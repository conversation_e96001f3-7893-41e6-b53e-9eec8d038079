# 1、EnhancedQueryBuilder 使用指南

## 概述

`EnhancedQueryBuilder` 是一个功能强大、灵活可配置的 GORM 查询构建器，专为简化复杂数据库查询而设计。它支持动态条件构建、类型自动转换、字段映射、分页排序等功能，特别适合构建 RESTful API 的查询接口。

## 核心特性

### 🚀 主要功能
- **动态查询条件构建** - 支持所有常见的 SQL 操作符
- **自动类型转换** - 智能转换查询参数类型
- **字段映射和验证** - 支持参数名到数据库列名的映射
- **灵活的配置模式** - 严格模式和宽松模式
- **完整的分页支持** - 内置分页功能
- **丰富的排序选项** - 多字段排序支持
- **关联查询支持** - JOIN、预加载等
- **错误处理机制** - 可配置的错误处理策略

### 📊 支持的操作符
| 操作符 | 说明 | 示例 |
|--------|------|------|
| `eq` | 等于 | `age = 25` |
| `neq` | 不等于 | `status != 'inactive'` |
| `like` | 模糊查询 | `name LIKE '%john%'` |
| `gt/gte` | 大于/大于等于 | `score > 80` |
| `lt/lte` | 小于/小于等于 | `age <= 30` |
| `in/not_in` | 包含/不包含 | `status IN ('active', 'pending')` |
| `between` | 范围查询 | `age BETWEEN 20 AND 30` |
| `is_null/not_null` | 空值检查 | `deleted_at IS NULL` |

### 🎯 支持的数据类型
- `string` - 字符串类型
- `int` - 整数类型
- `float` - 浮点数类型
- `bool` - 布尔类型
- `time/date` - 时间/日期类型
- `array` - 数组类型
- `complex` - 复杂类型

## 快速开始

### 基础使用

```go
// 创建查询构建器
builder := NewEnhancedQueryBuilder()

// 简单条件查询
var users []User
query := builder.
    AddCondition("status", "active").
    AddCondition("age", 25).
    AddSort("created_at", SortDESC).
    Build(db.Model(&User{}))

query.Find(&users)
```

### 字段注册和映射

```go
// 创建构建器并注册字段
builder := NewEnhancedQueryBuilder().
    SetAutoConvert(true).
    SetStrictMode(false)

// 注册字段定义
builder.RegisterFields(map[string]QueryField{
    "user_age": {
        Column:      "age",                    // 数据库列名
        Operation:   OpGte,                   // 默认操作符
        ValueType:   TypeInt,                 // 值类型
        AllowedOps:  []QueryOperator{OpEq, OpGt, OpGte}, // 允许的操作符
        Description: "用户年龄",
    },
    "username_like": {
        Column:      "username",
        Operation:   OpLike,
        ValueType:   TypeString,
        Description: "用户名模糊查询",
    },
})

// 使用注册的字段
query := builder.
    AddCondition("user_age", "25").        // 字符串自动转换为int
    AddCondition("username_like", "john"). // 自动添加通配符
    Build(db.Model(&User{}))
```

## 详细功能

### 1. 查询条件构建

#### 基本条件
```go
builder.AddCondition("field", value)                    // 使用默认操作符
builder.AddConditionWithOperator("field", OpLike, value) // 指定操作符
```

#### 特殊条件
```go
builder.AddLikeCondition("name", "john")                // LIKE查询，自动添加%
builder.AddRangeCondition("age", 20, 30)               // BETWEEN查询
builder.AddInCondition("status", []string{"active", "pending"}) // IN查询
```

#### 复杂条件示例
```go
builder.
    AddCondition("status", "active").
    AddRangeCondition("age", 18, 65).
    AddLikeCondition("username", "admin").
    AddInCondition("role", []string{"admin", "moderator"}).
    AddConditionWithOperator("score", OpGte, 80.0)
```

### 2. 排序和分组

```go
// 排序
builder.AddSort("created_at", SortDESC)
builder.AddSort("username", SortASC)

// 分组
builder.AddGroup("department")
builder.AddGroup("status")
```

### 3. 关联查询

```go
// JOIN查询
builder.AddJoin("LEFT JOIN profiles ON profiles.user_id = users.id")

// 预加载
builder.AddPreload("Profile")
builder.AddPreload("Orders")

// 选择字段
builder.AddSelect("id", "username", "email")
```

### 4. 分页功能

```go
// 设置分页
builder.SetPagination(1, 10) // 第1页，每页10条

// 或者分别设置
builder.SetPage(1).SetPageSize(10)

// 应用分页到查询
query := builder.Build(db.Model(&User{}))
query = builder.ApplyPagination(query)

// 获取总数（用于分页信息）
var total int64
builder.Build(db.Model(&User{})).Count(&total)
```

### 5. 配置选项

```go
builder := NewEnhancedQueryBuilder().
    SetStrictMode(true).    // 严格模式：只允许注册的字段
    SetAutoConvert(true).   // 自动类型转换
    SetSkipOnError(true)    // 遇到错误时跳过而不是panic
```

### 6. 时间处理

```go
// 注册时间字段
builder.RegisterField("created_date", QueryField{
    Column:    "created_at",
    Operation: OpBetween,
    ValueType: TypeDate,
})

// 使用字符串日期（自动转换）
builder.AddCondition("created_date", "2024-01-01")

// 或使用便捷的时间范围解析
params := map[string]interface{}{
    "start_time": "2024-01-01",
    "end_time":   "2024-12-31",
}
builder.ParseTimeRange(params, "start_time", "end_time")
```

## API 风格使用

特别适合构建 RESTful API 查询接口：

```go
// API 参数示例
func searchUsers(c *gin.Context) {
    // 获取查询参数
    params := make(map[string]interface{})
    params["username"] = c.Query("username")
    params["min_age"] = c.Query("min_age")
    params["max_age"] = c.Query("max_age")
    params["status"] = c.Query("status")
    params["page"] = c.DefaultQuery("page", "1")
    params["page_size"] = c.DefaultQuery("page_size", "10")
    
    // 创建查询构建器
    builder := NewEnhancedQueryBuilder().SetAutoConvert(true)
    
    // 注册字段映射
    builder.RegisterFields(map[string]QueryField{
        "username": {Column: "username", Operation: OpLike, ValueType: TypeString},
        "min_age":  {Column: "age", Operation: OpGte, ValueType: TypeInt},
        "max_age":  {Column: "age", Operation: OpLte, ValueType: TypeInt},
        "status":   {Column: "status", Operation: OpIn, ValueType: TypeArray},
    })
    
    // 构建查询
    for key, value := range params {
        if key == "page" {
            builder.SetPage(parseInt(value.(string)))
        } else if key == "page_size" {
            builder.SetPageSize(parseInt(value.(string)))
        } else {
            builder.AddCondition(key, value)
        }
    }
    
    // 执行查询
    var users []User
    var total int64
    
    // 获取总数
    builder.Build(db.Model(&User{})).Count(&total)
    
    // 获取数据
    query := builder.Build(db.Model(&User{}))
    query = builder.ApplyPagination(query)
    query.Find(&users)
    
    // 返回结果
    c.JSON(200, gin.H{
        "data":  users,
        "total": total,
        "page":  builder.Pagination.Page,
        "size":  builder.Pagination.PageSize,
    })
}
```

## 最佳实践

### 1. 字段注册策略
```go
// 为每个模型创建字段注册函数
func RegisterUserFields(builder *EnhancedQueryBuilder) {
    builder.RegisterFields(map[string]QueryField{
        "id":       {Column: "id", Operation: OpEq, ValueType: TypeInt},
        "username": {Column: "username", Operation: OpLike, ValueType: TypeString},
        "email":    {Column: "email", Operation: OpEq, ValueType: TypeString},
        "age_min":  {Column: "age", Operation: OpGte, ValueType: TypeInt},
        "age_max":  {Column: "age", Operation: OpLte, ValueType: TypeInt},
        "status":   {Column: "status", Operation: OpIn, ValueType: TypeArray},
        "active":   {Column: "is_active", Operation: OpEq, ValueType: TypeBool},
    })
}
```

### 2. 错误处理
```go
// 开发环境：使用严格模式，不跳过错误
if env == "development" {
    builder.SetStrictMode(true).SetSkipOnError(false)
}

// 生产环境：宽松模式，跳过错误
if env == "production" {
    builder.SetStrictMode(false).SetSkipOnError(true)
}

// 额外的实用示例函数
func demonstrateErrorHandling() {
	fmt.Println("\n=== 错误处理示例 ===")
	
	// 严格模式示例
	builder := NewEnhancedQueryBuilder().
		SetStrictMode(true).
		SetSkipOnError(false)
	
	// 注册字段
	builder.RegisterField("valid_field", QueryField{
		Column:      "test_column",
		Operation:   OpEq,
		ValueType:   TypeString,
		AllowedOps:  []QueryOperator{OpEq, OpLike},
		Description: "有效字段",
	})
	
	// 在严格模式下，这将触发panic（如果skipOnError为false）
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("捕获到错误: %v\n", r)
		}
	}()
	
	// 尝试使用未注册的字段
	builder.AddCondition("invalid_field", "test")
	
	fmt.Println("严格模式测试完成")
}
```

### 3. 复杂查询分解
```go
// 将复杂查询分解为多个步骤
// 步骤1: 构建基础查询条件
baseQuery := builder.AddCondition("status", "active").AddSort("created_at", SortDESC)

// 步骤2: 根据不同条件添加额外过滤
if someCondition {
    baseQuery.AddCondition("category", "electronics")
}

// 步骤3: 应用分页和执行查询
query := baseQuery.Build(db.Model(&Product{}))
query = baseQuery.ApplyPagination(query)
query.Find(&products)