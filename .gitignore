# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# 上传文件不同步
/static/uploads/*

# 过滤idea配置
/.idea

/.vscode

# 过滤src 数据
/src
/src.zip
/static/admin.zip

# 过滤日志文件夹所有文件
/logs/*

.DS_Store

main