package initialize

import (
	"gin/internal/models"
	"gorm.io/gorm"
)

// InitFrontMenu 初始化前台菜单数据
func InitFrontMenu(db *gorm.DB) error {
	// 检查是否已存在前台菜单数据
	var count int64
	if err := db.Model(&models.FrontMenu{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	frontMenus := []*models.FrontMenu{
		// 底部导航 - 移动端
		{
			Name:       "首页",
			Icon:       "/static/menu/home.png",
			ActiveIcon: "/static/menu/home-active.png",
			Mode:       models.FrontMenuModeMobile,
			Type:       models.FrontMenuTypeTabbar,
			Route:      "/",
			Sort:       1,
		}, {
			Name:       "我的",
			Icon:       "/static/menu/user.png",
			ActiveIcon: "/static/menu/user-active.png",
			Mode:       models.FrontMenuModeMobile,
			Type:       models.FrontMenuTypeTabbar,
			Route:      "/users",
			Sort:       5,
		},

		// 我的快捷菜单 - 移动端
		{
			Name:  "充值",
			Icon:  "/static/menu/deposit.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsersQuick,
			Route: "/wallets/deposit",
			Sort:  1,
		}, {
			Name:  "提现",
			Icon:  "/static/menu/withdraw.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsersQuick,
			Route: "/wallets/withdraw",
			Sort:  2,
		}, {
			Name:  "划转",
			Icon:  "/static/menu/transfer.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsersQuick,
			Route: "/wallets/transfer",
			Sort:  3,
		}, {
			Name:  "闪兑",
			Icon:  "/static/menu/swap.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsersQuick,
			Route: "/wallets/swap",
			Sort:  4,
		},

		// 首页快捷菜单 - 移动端
		{
			Name:  "充值",
			Icon:  "/static/menu/deposit.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeHomeQuick,
			Route: "/wallets/deposit",
			Sort:  1,
		}, {
			Name:  "提现",
			Icon:  "/static/menu/withdraw.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeHomeQuick,
			Route: "/wallets/withdraw",
			Sort:  2,
		}, {
			Name:  "帮助",
			Icon:  "/static/menu/helper.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeHomeQuick,
			Route: "/helps",
			Sort:  5,
		}, {
			Name:  "客服",
			Icon:  "/static/menu/service.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeHomeQuick,
			Route: "/helps/service",
			Sort:  5,
		},

		// 我的菜单导航 - 移动端
		{
			Name:  "钱包管理",
			Icon:  "/static/menu/wallet.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/wallets",
			Sort:  1,
		}, {
			Name:  "提现账户",
			Icon:  "/static/menu/withdraw-account.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/wallets/accounts",
			Sort:  2,
		}, {
			Name:  "账单明细",
			Icon:  "/static/menu/bill.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/wallets/bill",
			Sort:  3,
		}, {
			Name:  "邀请好友",
			Icon:  "/static/menu/invite.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/invite",
			Sort:  4,
		}, {
			Name:  "会员权益",
			Icon:  "/static/menu/level.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/level",
			Sort:  5,
		}, {
			Name:  "账户与安全",
			Icon:  "/static/menu/account-security.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/security",
			Sort:  6,
		}, {
			Name:  "消息中心",
			Icon:  "/static/menu/notice.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/notice",
			Sort:  7,
		}, {
			Name:  "设置",
			Icon:  "/static/menu/setting.png",
			Mode:  models.FrontMenuModeMobile,
			Type:  models.FrontMenuTypeUsers,
			Route: "/settings",
			Sort:  8,
		},

		// 顶部导航菜单 - 电脑端
		{
			Name:  "充值",
			Mode:  models.FrontMenuModeDesktop,
			Type:  models.FrontMenuTypeTabbar,
			Route: "/wallets/deposit",
			Sort:  1,
		},

		// 用户菜单导航 - 电脑端
		{
			Name:  "钱包管理",
			Icon:  "/static/menu/wallet.png",
			Mode:  models.FrontMenuModeDesktop,
			Type:  models.FrontMenuTypeUsers,
			Route: "/wallets",
			Sort:  1,
			Children: []*models.FrontMenu{
				{
					Name:  "订单列表",
					Icon:  "/static/menu/order.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/wallets",
					Sort:  1,
				}, {
					Name:  "划转订单",
					Icon:  "/static/menu/transfer.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/wallets/transfer/history",
					Sort:  2,
				}, {
					Name:  "闪兑订单",
					Icon:  "/static/menu/swap.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/wallets/swap/history",
					Sort:  3,
				}, {
					Name:  "账单明细",
					Icon:  "/static/menu/bill.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/wallets/bill",
					Sort:  4,
				}, {
					Name:  "提现账户",
					Icon:  "/static/menu/withdraw-account.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/wallets/accounts",
					Sort:  5,
				},
			},
		}, {
			Name:  "邀请好友",
			Icon:  "/static/menu/invite.png",
			Mode:  models.FrontMenuModeDesktop,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/invite",
			Sort:  2,
		}, {
			Name:  "会员权益",
			Icon:  "/static/menu/level.png",
			Mode:  models.FrontMenuModeDesktop,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/level",
			Sort:  3,
		}, {
			Name:  "账户与安全",
			Icon:  "/static/menu/account-security.png",
			Mode:  models.FrontMenuModeDesktop,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/security",
			Sort:  4,
			Children: []*models.FrontMenu{
				{
					Name:  "账户信息",
					Icon:  "/static/menu/account-info.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/users/info",
					Sort:  1,
				}, {
					Name:  "安全中心",
					Icon:  "/static/menu/safety.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/users/security",
					Sort:  2,
				}, {
					Name:  "实名认证",
					Icon:  "/static/menu/certification.png",
					Mode:  models.FrontMenuModeDesktop,
					Type:  models.FrontMenuTypeUsers,
					Route: "/users/verify",
					Sort:  3,
				},
			},
		}, {
			Name:  "消息中心",
			Icon:  "/static/menu/notice.png",
			Mode:  models.FrontMenuModeDesktop,
			Type:  models.FrontMenuTypeUsers,
			Route: "/users/notice",
			Sort:  5,
		},
	}
	if err := db.Create(&frontMenus).Error; err != nil {
		return err
	}
	return nil
}
