package initialize

import (
	"gin/internal/constant"
	"gin/internal/models"
	"gin/pkg/convert"
	"gorm.io/gorm"
)

// InitSetting 初始化设置数据
func InitSetting(db *gorm.DB) error {

	// 检查是否已存在角色数据
	var count int64
	if err := db.Model(&models.Setting{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	// 初始化设置数据
	settings := []*models.Setting{
		{
			GroupID: models.SettingGroupIDSite,
			Name:    "站点LOGO",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteLogo),
			Value:   "/static/logo.png",
		},
		{
			GroupID: models.SettingGroupIDSite,
			Name:    "站点favicon",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteFavicon),
			Value:   "/static/favicon.ico",
		},
		{
			GroupID: models.SettingGroupIDSite,
			Name:    "站点名称",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteName),
			Value:   "八戒网络科技",
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "项目时区",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteTimezone),
			Value:   "Asia/Shanghai",
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "轮播图",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingSiteBanner),
			Value: convert.JSONMarshal(constant.SettingBanner{
				Banner: []string{
					"/static/banner/1.png",
					"/static/banner/2.png",
					"/static/banner/3.png",
					"/static/banner/4.png",
					"/static/banner/5.png",
				},
				DesktopBanner: []string{
					"/static/banner/1.png",
					"/static/banner/2.png",
					"/static/banner/3.png",
					"/static/banner/4.png",
					"/static/banner/5.png",
				},
			}),
		},
		{
			GroupID: models.SettingGroupIDSite,
			Name:    "站点描述",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteDesc),
			Value:   models.SettingTipsSiteDesc,
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "站点版权",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteCopyright),
			Value:   models.SettingTipsSiteCopyright,
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "站点介绍",
			Type:    models.SettingTypeString,
			Field:   string(models.SettingSiteIntroduce),
			Value:   models.SettingTipsSiteIntroduce,
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "下载",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingSiteAppDownload),
			Value:   convert.JSONMarshal(constant.SettingAppDownload{}),
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "提现配置",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingSiteWithdraw),
			Value: convert.JSONMarshal(constant.SettingWithdraw{
				Account:   1,
				AuditNums: 1,
				Nums: constant.SettingWithdrawNums{
					Day: 1,
					Num: 1,
				},
			}),
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "站点社交",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingSiteSocial),
			Value: convert.JSONMarshal([]*constant.SettingSiteSocial{
				{Icon: "/static/icon/telegram.png", Link: "https://t.me/your_telegram_channel", Name: "Telegram"},
				{Icon: "/static/icon/twitter.png", Link: "https://x.com/your_twitter_account", Name: "Twitter"},
				{Icon: "/static/icon/facebook.png", Link: "https://www.facebook.com/your_facebook_page", Name: "Facebook"},
			}),
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "站点手续费",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingSiteFee),
			Value: convert.JSONMarshal(constant.SettingSiteFee{
				Transfer: 0, Swap: 0,
			}),
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "站点奖励配置",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingSiteReward),
			Value: convert.JSONMarshal(constant.SettingSiteReward{
				Invite: 0, Register: 0,
			}),
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "分销配置",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingDistribution),
			Value: convert.JSONMarshal([]*constant.SettingDistribution{
				{Product: 0, Earnings: 0}, {Product: 0, Earnings: 0}, {Product: 0, Earnings: 0},
			}),
		},
		{
			GroupID: models.SettingGroupIDSite,
			Name:    "提示词配置",
			Type:    models.SettingTypeString,
			Field:   models.SettingTipsUserFreeze,
			Value: convert.JSONMarshal(constant.SettingNoticeTips{
				UserFreeze: models.SettingTipsUserFreeze,
				UserScore:  models.SettingTipsUserScore,
				Notice:     models.SettingTipsNotice,
			}),
		},
		{
			GroupID: models.SettingGroupIDSite,
			Name:    "提示音配置",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingAudio),
			Value: convert.JSONMarshal(constant.SettingAudio{
				Audio: []string{},
			}),
		}, {
			GroupID: models.SettingGroupIDSite,
			Name:    "用户冻结状态配置",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingStatusUserFreeze),
			Value: convert.JSONMarshal(constant.SettingStatusUserFreeze{
				Status: []string{},
			}),
		},

		// ==================模版配置==================
		{
			GroupID: models.SettingGroupIDTemplate,
			Name:    "基础模版",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingTemplateBasic),
			Value: convert.JSONMarshal(constant.SettingBasicTemplate{
				Checkbox: []string{
					constant.SettingBasicTemplateCheckboxShowLevelTag,
					constant.SettingBasicTemplateCheckboxShowCertificationTag,
					constant.SettingBasicTemplateCheckboxShowScoreTag,
					constant.SettingBasicTemplateCheckboxShowChangePassword,
					constant.SettingBasicTemplateCheckboxShowChangeSecurityKey,
					constant.SettingBasicTemplateCheckboxWithdrawAccountDelete,
					constant.SettingBasicTemplateCheckboxWithdrawAccountUpdate,
					constant.SettingBasicTemplateCheckboxWithdrawAccountPassword,
					constant.SettingBasicTemplateCheckboxWithdrawPassword,
					constant.SettingBasicTemplateCheckboxTransferPassword,
					constant.SettingBasicTemplateCheckboxSwapPassword,
					constant.SettingBasicTemplateCheckboxBuyLevelPassword,
				},
			}),
		},
		{
			GroupID: models.SettingGroupIDTemplate,
			Name:    "登录模版",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingTemplateLogin),
			Value: convert.JSONMarshal(constant.SettingLoginTemplate{
				Background:        "/static/bg.jpg",
				DesktopBackground: "/static/bg.jpg",
				Tips:              models.SettingTipsLogin,
				ForgetURL:         "",
				Checkbox: []string{
					constant.SettingLoginTemplateCheckboxShowSiteName,
					constant.SettingLoginTemplateCheckboxShowSiteLogo,
					constant.SettingLoginTemplateCheckboxShowSwitchLanguage,
					constant.SettingLoginTemplateCheckboxShowRegister,
				},
			}),
		}, {
			GroupID: models.SettingGroupIDTemplate,
			Name:    "注册模版",
			Type:    models.SettingTypeObject,
			Field:   string(models.SettingTemplateRegister),
			Value: convert.JSONMarshal(constant.SettingRegisterTemplate{
				Background:        "/static/bg.jpg",
				DesktopBackground: "/static/bg.jpg",
				Tips:              models.SettingTipsRegister,
				AgreenmentURL:     "",
				PolicyURL:         "",
				Checkbox: []string{
					constant.SettingRegisterTemplateCheckboxShowSiteName,
					constant.SettingRegisterTemplateCheckboxShowSiteLogo,
					constant.SettingRegisterTemplateCheckboxShowSwitchLanguage,
					constant.SettingRegisterTemplateCheckboxSecurityKey,
				},
			}),
		},
	}

	if err := db.Create(&settings).Error; err != nil {
		return err
	}
	return nil
}
