package initialize

import (
	"gin/internal/models"
	"gorm.io/gorm"
)

// InitArticle 初始化文章数据
func InitArticle(db *gorm.DB) error {
	// 检查是否已存在文章数据
	var count int64
	if err := db.Model(&models.Article{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	articles := []*models.Article{
		{BaseModel: models.BaseModel{ID: 1}, Title: "欢迎使用 Gin 后台管理系统", Content: "欢迎使用 Gin 后台管理系统，这是一篇测试文章，你可以在这里发布你的文章。", Type: 1, Status: 1},
		{BaseModel: models.BaseModel{ID: 2}, Title: "如何使用 Gin 后台管理系统", Content: "这是一篇关于如何使用 Gin 后台管理系统的文章，你可以在这里找到相关的使用说明。", Type: 1, Status: 1},
	}

	if err := db.Create(&articles).Error; err != nil {
		return err
	}

	return nil
}
