package initialize

import (
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitAsset 初始化资产数据
func InitAsset(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Asset{}); err != nil {
		return err
	}

	// 检查是否已存在资产数据
	var count int64
	if err := db.Model(&models.Asset{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	assets := []*models.Asset{
		{
			Name:             "USD",
			SubtitleField:    "USD 美元",
			NameNative:       "USD",
			Symbol:           "USD",
			Icon:             "/static/currency/bank.png",
			Type:             models.AssetTypePlatform,
			Rate:             1,
			Sort:             1,
			Status:           models.AssetStatusEnabled,
			Decimals:         2,
			DescriptionField: "USD 是美元的代币",
		},
		{
			Name:             "USDT",
			SubtitleField:    "USDT 泰达币",
			NameNative:       "USDT",
			Symbol:           "USDT",
			Icon:             "/static/currency/usdt.png",
			Type:             models.AssetTypeCrypto,
			Rate:             1,
			Sort:             2,
			Status:           models.AssetStatusEnabled,
			Decimals:         6,
			DescriptionField: "USDT 是 Tether 公司推出的基于稳定价值货币美元（USD）的代币",
		},
	}

	if err := db.Create(&assets).Error; err != nil {
		return err
	}

	return nil
}
