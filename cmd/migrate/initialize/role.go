package initialize

import (
	"gin/internal/constant"
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitRole 初始化角色数据
func InitRole(db *gorm.DB) error {

	// 检查是否已存在角色数据
	var count int64
	if err := db.Model(&models.Role{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	// 初始化角色数据
	roles := initRoles()

	if err := db.Create(&roles).Error; err != nil {
		return err
	}
	return nil
}

func initRoles() []*models.Role {
	return []*models.Role{
		{Name: "超级管理员", Code: constant.InitialSuperManageRoleCode, Sort: 1, Remark: "系统超级管理员，拥有所有权限", DataScope: 1, IsSystem: true},
	}
}
