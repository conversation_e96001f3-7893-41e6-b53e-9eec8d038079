package initialize

import (
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitLanguage 初始化语言数据
func InitLanguage(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Language{}); err != nil {
		return err
	}

	// 检查是否已存在语言数据
	var count int64
	if err := db.Model(&models.Language{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	languages := []*models.Language{
		{Code: "zh-CN", Name: "简体中文", NameNative: "简体中文", Icon: "/static/country/china.png", Sort: 1},
		{Code: "zh-TW", Name: "繁体中文", NameNative: "繁體中文", Icon: "/static/country/taiwan.png", Sort: 2},
		{Code: "en-US", Name: "英语(美国)", NameNative: "English(US)", Icon: "/static/country/usa.png", Sort: 3},
		{Code: "en-GB", Name: "英语(英国)", NameNative: "English(UK)", Icon: "/static/country/uk.png", Sort: 4},
		{Code: "ja-JP", Name: "日语", NameNative: "日本語", Icon: "/static/country/japan.png", Sort: 5},
		{Code: "ko-KR", Name: "韩语", NameNative: "한국어", Icon: "/static/country/korea.png", Sort: 6},
		{Code: "fr-FR", Name: "法语(法国)", NameNative: "Français(FR)", Icon: "/static/country/france.png", Sort: 7},
		{Code: "de-DE", Name: "德语(德国)", NameNative: "Deutsch(DE)", Icon: "/static/country/germany.png", Sort: 8},
		{Code: "es-ES", Name: "西班牙语(西班牙)", NameNative: "Español(ES)", Icon: "/static/country/spain.png", Sort: 9},
		{Code: "it-IT", Name: "意大利语(意大利)", NameNative: "Italiano(IT)", Icon: "/static/country/italy.png", Sort: 10},
		{Code: "ru-RU", Name: "俄语(俄罗斯)", NameNative: "Русский(RU)", Icon: "/static/country/russia.png", Sort: 11},
	}

	if err := db.Create(&languages).Error; err != nil {
		return err
	}

	return nil
}
