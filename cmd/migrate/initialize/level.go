package initialize

import (
	"gin/internal/models"
	"gorm.io/gorm"
)

// InitLevel 初始化等级数据
func InitLevel(db *gorm.DB) error {

	// 检查是否已存在等级数据
	var count int64
	if err := db.Model(&models.Level{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	levels := []*models.Level{
		{BaseModel: models.BaseModel{ID: 1}, Name: "普通会员", Symbol: "member", Type: 1, Sort: 1, Amount: 0, Discount: 1, Days: 0, Status: 1},
		{BaseModel: models.BaseModel{ID: 2}, Name: "VIP会员", Symbol: "vip", Type: 1, Sort: 2, Amount: 100, Discount: 0.9, Days: 365, Status: 1},
	}

	if err := db.Create(&levels).Error; err != nil {
		return err
	}
	return nil
}
