package initialize

import (
	"errors"
	"fmt"
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitCountry 初始化国家数据
func InitCountry(db *gorm.DB) error {

	// 检查是否已存在国家表
	var count int64
	result := db.Model(&models.Country{}).Count(&count)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("检查国家表失败: %v", result.Error)
	}

	// 如果没有数据，创建默认国家
	if count == 0 {
		initData := []*models.Country{
			{NameZh: "中国", NameNative: "China", Code: "86", Icon: "/static/country/china.png", ISO2: "CN"},
			{NameZh: "美国", NameNative: "United States", Code: "1", Icon: "/static/country/usa.png", ISO2: "US"},
			{NameZh: "日本", NameNative: "Japan", Code: "81", Icon: "/static/country/japan.png", ISO2: "JP"},
			{NameZh: "韩国", NameNative: "South Korea", Code: "82", Icon: "/static/country/south_korea.png", ISO2: "KR"},
			{NameZh: "英国", NameNative: "United Kingdom", Code: "44", Icon: "/static/country/uk.png", ISO2: "GB"},
			{NameZh: "德国", NameNative: "Germany", Code: "49", Icon: "/static/country/germany.png", ISO2: "DE"},
			{NameZh: "法国", NameNative: "France", Code: "33", Icon: "/static/country/france.png", ISO2: "FR"},
			{NameZh: "加拿大", NameNative: "Canada", Code: "1", Icon: "/static/country/canada.png", ISO2: "CA"},
			{NameZh: "澳大利亚", NameNative: "Australia", Code: "61", Icon: "/static/country/australia.png", ISO2: "AU"},
			{NameZh: "新加坡", NameNative: "Singapore", Code: "65", Icon: "/static/country/singapore.png", ISO2: "SG"},
			{NameZh: "香港", NameNative: "Hong kong", Code: "852", Icon: "/static/country/hongkong.png", ISO2: "HK"},
			{NameZh: "台湾", NameNative: "Taiwan", Code: "886", Icon: "/static/country/taiwan.png", ISO2: "TW"},
			{NameZh: "澳门", NameNative: "Macao", Code: "853", Icon: "/static/country/macao.png", ISO2: "MO"},
			{NameZh: "印度", NameNative: "India", Code: "91", Icon: "/static/country/india.png", ISO2: "IN"},
			{NameZh: "俄罗斯", NameNative: "Russia", Code: "7", Icon: "/static/country/russia.png", ISO2: "RU"},
			{NameZh: "蒙古", NameNative: "Mongolia", Code: "976", Icon: "/static/country/mongolia.png", ISO2: "MN"},
			{NameZh: "朝鲜", NameNative: "North Korea", Code: "850", Icon: "/static/country/north_korea.png", ISO2: "KP"},
			{NameZh: "菲律宾", NameNative: "Philippines", Code: "63", Icon: "/static/country/philippines.png", ISO2: "PH"},
			{NameZh: "越南", NameNative: "Vietnam", Code: "84", Icon: "/static/country/vietnam.png", ISO2: "VN"},
			{NameZh: "老挝", NameNative: "Laos", Code: "856", Icon: "/static/country/laos.png", ISO2: "LA"},
			{NameZh: "柬埔寨", NameNative: "Cambodia", Code: "855", Icon: "/static/country/cambodia.png", ISO2: "KH"},
			{NameZh: "缅甸", NameNative: "Myanmar", Code: "95", Icon: "/static/country/myanmar.png", ISO2: "MM"},
			{NameZh: "泰国", NameNative: "Thailand", Code: "66", Icon: "/static/country/thailand.png", ISO2: "TH"},
			{NameZh: "马来西亚", NameNative: "Malaysia", Code: "60", Icon: "/static/country/malaysia.png", ISO2: "MY"},
			{NameZh: "文莱", NameNative: "Brunei", Code: "673", Icon: "/static/country/brunei.png", ISO2: "BN"},
			{NameZh: "印度尼西亚", NameNative: "Indonesia", Code: "62", Icon: "/static/country/indonesia.png", ISO2: "ID"},
			{NameZh: "尼泊尔", NameNative: "Nepal", Code: "977", Icon: "/static/country/nepal.png", ISO2: "NP"},
			{NameZh: "不丹", NameNative: "Bhutan", Code: "975", Icon: "/static/country/bhutan.png", ISO2: "BT"},
			{NameZh: "孟加拉国", NameNative: "Bangladesh", Code: "880", Icon: "/static/country/bangladesh.png", ISO2: "BD"},
			{NameZh: "巴基斯坦", NameNative: "Pakistan", Code: "92", Icon: "/static/country/pakistan.png", ISO2: "PK"},
			{NameZh: "斯里兰卡", NameNative: "SriLanka", Code: "94", Icon: "/static/country/sri_lanka.png", ISO2: "LK"},
			{NameZh: "马尔代夫", NameNative: "Maldives", Code: "960", Icon: "/static/country/maldives.png", ISO2: "MV"},
			{NameZh: "哈萨克斯坦", NameNative: "Kazakhstan", Code: "7", Icon: "/static/country/kazakhstan.png", ISO2: "KZ"},
			{NameZh: "乌兹别克斯坦", NameNative: "Uzbekistan", Code: "998", Icon: "/static/country/uzbekistan.png", ISO2: "UZ"},
			{NameZh: "土库曼斯坦", NameNative: "Turkmenistan", Code: "993", Icon: "/static/country/turkmenistan.png", ISO2: "TM"},
			{NameZh: "阿富汗", NameNative: "Afghanistan", Code: "93", Icon: "/static/country/afghanistan.png", ISO2: "AF", Sort: 99},
			{NameZh: "伊拉克", NameNative: "Iraq", Code: "964", Icon: "/static/country/iraq.png", ISO2: "IQ", Sort: 99},
			{NameZh: "叙利亚", NameNative: "Syria", Code: "963", Icon: "/static/country/syria.png", ISO2: "SY", Sort: 99},
			{NameZh: "黎巴嫩", NameNative: "Lebanon", Code: "961", Icon: "/static/country/lebanon.png", ISO2: "LB", Sort: 99},
			{NameZh: "以色列", NameNative: "Israel", Code: "972", Icon: "/static/country/israel.png", ISO2: "IL", Sort: 99},
			{NameZh: "巴勒斯坦", NameNative: "Palestine", Code: "970", Icon: "/static/country/palestine.png", ISO2: "PS", Sort: 99},
			{NameZh: "沙特阿拉伯", NameNative: "SaudiArabia", Code: "966", Icon: "/static/country/saudi_arabia.png", ISO2: "SA", Sort: 99},
			{NameZh: "巴林", NameNative: "Bahrain", Code: "973", Icon: "/static/country/bahrain.png", ISO2: "BH", Sort: 99},
			{NameZh: "科威特", NameNative: "Kuwait", Code: "965", Icon: "/static/country/kuwait.png", ISO2: "KW", Sort: 99},
			{NameZh: "阿拉伯联合酋长国", NameNative: "United Arab Emirates", Code: "971", Icon: "/static/country/united_arab_emirates.png", ISO2: "AE", Sort: 99},
			{NameZh: "阿曼", NameNative: "Oman", Code: "968", Icon: "/static/country/oman.png", ISO2: "OM", Sort: 99},
			{NameZh: "亚美尼亚", NameNative: "Armenia", Code: "374", Icon: "/static/country/armenia.png", ISO2: "AM", Sort: 99},
			{NameZh: "阿塞拜疆", NameNative: "Azerbaijan", Code: "994", Icon: "/static/country/azerbaijan.png", ISO2: "AZ", Sort: 99},
			{NameZh: "土耳其", NameNative: "Turkey", Code: "90", Icon: "/static/country/turkey.png", ISO2: "TR", Sort: 99},
			{NameZh: "塞浦路斯", NameNative: "Cyprus", Code: "357", Icon: "/static/country/cyprus.png", ISO2: "CY", Sort: 99},
			{NameZh: "阿尔巴尼亚", NameNative: "Albania", Code: "355", Icon: "/static/country/albania.png", ISO2: "AL", Sort: 99},
			{NameZh: "阿尔及利亚", NameNative: "Algeria", Code: "213", Icon: "/static/country/algeria.png", ISO2: "DZ", Sort: 99},
			{NameZh: "阿根廷", NameNative: "Argentina", Code: "54", Icon: "/static/country/argentina.png", ISO2: "AR", Sort: 99},
			{NameZh: "埃及", NameNative: "Egypt", Code: "20", Icon: "/static/country/egypt.png", ISO2: "EG", Sort: 99},
			{NameZh: "爱尔兰", NameNative: "Ireland", Code: "353", Icon: "/static/country/ireland.png", ISO2: "IE", Sort: 99},
			{NameZh: "安道尔", NameNative: "Andorra", Code: "376", Icon: "/static/country/andorra.png", ISO2: "AD", Sort: 99},
			{NameZh: "安哥拉", NameNative: "Angola", Code: "244", Icon: "/static/country/angola.png", ISO2: "AO", Sort: 99},
			{NameZh: "安圭拉", NameNative: "Anguilla", Code: "1-264", Icon: "/static/country/anguilla.png", ISO2: "AI", Sort: 99},
			{NameZh: "安提瓜和巴布达", NameNative: "Antigua and Barbuda", Code: "1-268", Icon: "/static/country/antigua_and_barbuda.png", ISO2: "AG", Sort: 99},
			{NameZh: "奥兰群岛", NameNative: "Åland Islands", Code: "358", Icon: "/static/country/aland_islands.png", ISO2: "AX", Sort: 99},
			{NameZh: "奥地利", NameNative: "Austria", Code: "43", Icon: "/static/country/austria.png", ISO2: "AT", Sort: 99},
			{NameZh: "巴巴多斯", NameNative: "Barbados", Code: "1-246", Icon: "/static/country/barbados.png", ISO2: "BB", Sort: 99},
			{NameZh: "巴布亚新几内亚国旗", NameNative: "Papua New Guinea", Code: "675", Icon: "/static/country/papua_new_guinea.png", ISO2: "PG", Sort: 99},
			{NameZh: "巴哈马", NameNative: "Bahamas", Code: "1-242", Icon: "/static/country/bahamas.png", ISO2: "BS", Sort: 99},
			{NameZh: "巴拉圭", NameNative: "Paraguay", Code: "595", Icon: "/static/country/paraguay.png", ISO2: "PY", Sort: 99},
			{NameZh: "巴拿马", NameNative: "Panama", Code: "507", Icon: "/static/country/panama.png", ISO2: "PA", Sort: 99},
			{NameZh: "巴西", NameNative: "Brazil", Code: "55", Icon: "/static/country/brazil.png", ISO2: "BR", Sort: 99},
			{NameZh: "白俄罗斯", NameNative: "Belarus", Code: "375", Icon: "/static/country/belarus.png", ISO2: "BY", Sort: 99},
			{NameZh: "百慕大", NameNative: "Bermuda", Code: "1-441", Icon: "/static/country/bermuda.png", ISO2: "BM", Sort: 99},
			{NameZh: "北马里亚纳群岛", NameNative: "Northern Mariana Islands", Code: "1-670", Icon: "/static/country/northern_mariana.png", ISO2: "MP", Sort: 99},
			{NameZh: "北马其顿", NameNative: "Republika Makedonija", Code: "389", Icon: "/static/country/republika_makedonija.png", ISO2: "MK", Sort: 99},
			{NameZh: "贝宁", NameNative: "Benin", Code: "229", Icon: "/static/country/benin.png", ISO2: "BJ", Sort: 99},
			{NameZh: "比利时", NameNative: "Belgium", Code: "32", Icon: "/static/country/belgium.png", ISO2: "BE", Sort: 99},
			{NameZh: "秘鲁", NameNative: "Peru", Code: "51", Icon: "/static/country/peru.png", ISO2: "PE", Sort: 99},
			{NameZh: "冰岛", NameNative: "Iceland", Code: "354", Icon: "/static/country/iceland.png", ISO2: "IS", Sort: 99},
			{NameZh: "波多黎各", NameNative: "Puerto Rico", Code: "1-939", Icon: "/static/country/puerto_rico.png", ISO2: "PR", Sort: 99},
			{NameZh: "波兰", NameNative: "Poland", Code: "48", Icon: "/static/country/poland.png", ISO2: "PL", Sort: 99},
			{NameZh: "波斯尼亚和黑塞哥维那", NameNative: "Bosnia and Herzegovina", Code: "287", Icon: "/static/country/bosnia_and_herzegovina.png", ISO2: "BA", Sort: 99},
			{NameZh: "玻利维亚", NameNative: "Bolivia", Code: "591", Icon: "/static/country/bolivia.png", ISO2: "BO", Sort: 99},
			{NameZh: "伯利兹", NameNative: "Belize", Code: "501", Icon: "/static/country/belize.png", ISO2: "BZ", Sort: 99},
			{NameZh: "布基纳法索", NameNative: "Burkina Faso", Code: "226", Icon: "/static/country/burkina_faso.png", ISO2: "BF", Sort: 99},
			{NameZh: "布隆迪", NameNative: "Burundi", Code: "257", Icon: "/static/country/burundi.png", ISO2: "BI", Sort: 99},
			{NameZh: "布维岛", NameNative: "Bouvet Island", Code: "359", Icon: "/static/country/bouvet_island.png", ISO2: "BV", Sort: 99},
			{NameZh: "赤道几内亚", NameNative: "Equatorial Guinea", Code: "240", Icon: "/static/country/equatorial_guinea.png", ISO2: "GQ", Sort: 99},
			{NameZh: "丹麦", NameNative: "Denmark", Code: "45", Icon: "/static/country/denmark.png", ISO2: "DK", Sort: 99},
			{NameZh: "东帝汶", NameNative: "East Timor, Timor-Leste", Code: "257", Icon: "/static/country/east_timor.png", ISO2: "TL", Sort: 99},
			{NameZh: "多哥", NameNative: "Togo", Code: "228", Icon: "/static/country/togo.png", ISO2: "TG", Sort: 99},
			{NameZh: "多米尼加共和国", NameNative: "Dominican Republic", Code: "1-809", Icon: "/static/country/dominican_republic.png", ISO2: "DO", Sort: 99},
			{NameZh: "多米尼克", NameNative: "Dominica", Code: "1-767", Icon: "/static/country/dominica.png", ISO2: "DM", Sort: 99},
			{NameZh: "厄瓜多尔", NameNative: "Ecuador", Code: "593", Icon: "/static/country/ecuador.png", ISO2: "EC", Sort: 99},
			{NameZh: "厄立特里亚", NameNative: "Eritrea", Code: "291", Icon: "/static/country/eritrea.png", ISO2: "ER", Sort: 99},
			{NameZh: "斐济", NameNative: "Fiji", Code: "679", Icon: "/static/country/fiji.png", ISO2: "FJ", Sort: 99},
			{NameZh: "芬兰", NameNative: "Finland", Code: "358", Icon: "/static/country/finland.png", ISO2: "FI", Sort: 99},
			{NameZh: "佛得角", NameNative: "Cape Verde", Code: "238", Icon: "/static/country/cape_verde.png", ISO2: "CV", Sort: 99},
			{NameZh: "福克兰群岛", NameNative: "Falkland Islands", Code: "500", Icon: "/static/country/falkland_islands.png", ISO2: "FK", Sort: 99},
			{NameZh: "冈比亚", NameNative: "Gambia", Code: "220", Icon: "/static/country/gambia.png", ISO2: "GM", Sort: 99},
			{NameZh: "刚果共和国", NameNative: "Congo, Republic of the", Code: "257", Icon: "/static/country/congo.png", ISO2: "CG", Sort: 99},
			{NameZh: "刚果民主共和国", NameNative: "Congo, Democratic Republic of the", Code: "243", Icon: "/static/country/congo_democratic.png", ISO2: "CD", Sort: 99},
			{NameZh: "哥伦比亚", NameNative: "Colombia", Code: "57", Icon: "/static/country/colombia.png", ISO2: "CO", Sort: 99},
			{NameZh: "哥斯达黎加", NameNative: "Costa Rica", Code: "506", Icon: "/static/country/costa_rica.png", ISO2: "CR", Sort: 99},
			{NameZh: "格林纳达", NameNative: "Grenada", Code: "1-473", Icon: "/static/country/grenada.png", ISO2: "GD", Sort: 99},
			{NameZh: "格陵兰", NameNative: "Greenland", Code: "299", Icon: "/static/country/greenland.png", ISO2: "GL", Sort: 99},
			{NameZh: "格鲁吉亚", NameNative: "Georgia", Code: "995", Icon: "/static/country/georgia.png", ISO2: "GE", Sort: 99},
			{NameZh: "根西", NameNative: "Guernsey", Code: "44-1481", Icon: "/static/country/guernsey.png", ISO2: "GG", Sort: 99},
			{NameZh: "古巴", NameNative: "Cuba", Code: "53", Icon: "/static/country/cuba.png", ISO2: "CU", Sort: 99},
			{NameZh: "瓜德罗普", NameNative: "Guadeloupe", Code: "590", Icon: "/static/country/gadeloupe.png", ISO2: "GP", Sort: 99},
			{NameZh: "关岛", NameNative: "Guam", Code: "1-671", Icon: "/static/country/guam.png", ISO2: "GU", Sort: 99},
			{NameZh: "圭亚那", NameNative: "Guyana", Code: "592", Icon: "/static/country/guyana.png", ISO2: "GY", Sort: 99},
			{NameZh: "海地", NameNative: "Haiti", Code: "509", Icon: "/static/country/haiti.png", ISO2: "HT", Sort: 99},
			{NameZh: "荷兰", NameNative: "Netherlands", Code: "31", Icon: "/static/country/netherlands.png", ISO2: "NL", Sort: 99},
			{NameZh: "赫德岛和麦克唐纳", NameNative: "Heard Island and McDonald Islands", Code: "", Icon: "/static/country/heard_island.png", ISO2: "HM", Sort: 99},
			{NameZh: "洪都拉斯", NameNative: "Honduras", Code: "504", Icon: "/static/country/honduras.png", ISO2: "HN", Sort: 99},
			{NameZh: "基里巴斯", NameNative: "Kiribati", Code: "686", Icon: "/static/country/kiribati.png", ISO2: "KI", Sort: 99},
			{NameZh: "吉布提", NameNative: "Djibouti", Code: "253", Icon: "/static/country/djibouti.png", ISO2: "DJ", Sort: 99},
			{NameZh: "吉尔吉斯斯坦", NameNative: "Kyrgyzstan", Code: "996", Icon: "/static/country/kyrgyzstan.png", ISO2: "KG", Sort: 99},
			{NameZh: "几内亚", NameNative: "Guinea", Code: "224", Icon: "/static/country/guinea.png", ISO2: "GN", Sort: 99},
			{NameZh: "加纳", NameNative: "Ghana", Code: "233", Icon: "/static/country/ghana.png", ISO2: "GH", Sort: 99},
			{NameZh: "加蓬", NameNative: "Gabon", Code: "241", Icon: "/static/country/gabon.png", ISO2: "GA", Sort: 99},
			{NameZh: "捷克", NameNative: "Czech Republic", Code: "420", Icon: "/static/country/czech.png", ISO2: "CZ", Sort: 99},
			{NameZh: "津巴布韦", NameNative: "Zimbabwe", Code: "263", Icon: "/static/country/zimbabwe.png", ISO2: "ZW", Sort: 99},
			{NameZh: "喀麦隆", NameNative: "Cameroon", Code: "237", Icon: "/static/country/cameroon.png", ISO2: "CM", Sort: 99},
			{NameZh: "卡塔尔", NameNative: "Qatar", Code: "974", Icon: "/static/country/qatar.png", ISO2: "QA", Sort: 99},
			{NameZh: "开曼群岛", NameNative: "Cayman Islands)", Code: "1-345", Icon: "/static/country/cayman_islands.png", ISO2: "KY", Sort: 99},
			{NameZh: "科科斯（基林）群岛", NameNative: "Cocos (Keeling) Islands", Code: "61", Icon: "/static/country/cocos_keeling.png", ISO2: "CC", Sort: 99},
			{NameZh: "科摩罗", NameNative: "Comoros", Code: "269", Icon: "/static/country/comoros.png", ISO2: "KM", Sort: 99},
			{NameZh: "科索沃", NameNative: "Kosovo", Code: "383", Icon: "/static/country/kosovo.png", ISO2: "XK", Sort: 99},
			{NameZh: "科特迪瓦", NameNative: "Côte d'Ivoire", Code: "225", Icon: "/static/country/ivory_coast.png", ISO2: "CI", Sort: 99},
			{NameZh: "克罗地亚", NameNative: "Croatia", Code: "385", Icon: "/static/country/croatia.png", ISO2: "HR", Sort: 99},
			{NameZh: "肯尼亚", NameNative: "Kenya", Code: "254", Icon: "/static/country/kenya.png", ISO2: "KE", Sort: 99},
			{NameZh: "库克群岛", NameNative: "Cook Islands", Code: "682", Icon: "/static/country/cook_islands.png", ISO2: "CK", Sort: 99},
			{NameZh: "库拉索", NameNative: "Curaçao", Code: "599", Icon: "/static/country/curacao.png", ISO2: "CW", Sort: 99},
			{NameZh: "拉脱维亚", NameNative: "Latvia", Code: "371", Icon: "/static/country/latvia.png", ISO2: "LV", Sort: 99},
			{NameZh: "莱索托", NameNative: "Lesotho", Code: "266", Icon: "/static/country/lesotho.png", ISO2: "LS", Sort: 99},
			{NameZh: "立陶宛", NameNative: "Lithuania", Code: "370", Icon: "/static/country/lithuania.png", ISO2: "LT", Sort: 99},
			{NameZh: "利比里亚", NameNative: "Liberia", Code: "231", Icon: "/static/country/liberia.png", ISO2: "LR", Sort: 99},
			{NameZh: "利比亚", NameNative: "Libya", Code: "218", Icon: "/static/country/libya.png", ISO2: "LY", Sort: 99},
			{NameZh: "列支敦斯登", NameNative: "Liechtenstein", Code: "423", Icon: "/static/country/liechtenstein.png", ISO2: "LI", Sort: 99},
			{NameZh: "留尼汪", NameNative: "Réunion", Code: "262", Icon: "/static/country/réunion.png", ISO2: "RE", Sort: 99},
			{NameZh: "卢森堡", NameNative: "Luxembourg", Code: "352", Icon: "/static/country/luxembourg.png", ISO2: "LU", Sort: 99},
			{NameZh: "卢旺达", NameNative: "Rwanda", Code: "250", Icon: "/static/country/rwanda.png", ISO2: "RW", Sort: 99},
			{NameZh: "罗马尼亚", NameNative: "Romania", Code: "40", Icon: "/static/country/romania.png", ISO2: "RO", Sort: 99},
			{NameZh: "马达加斯加", NameNative: "Madagascar", Code: "261", Icon: "/static/country/madagascar.png", ISO2: "MG", Sort: 99},
			{NameZh: "马恩岛", NameNative: "Isle of Man", Code: "44-1624", Icon: "/static/country/isle_of_man.png", ISO2: "IM", Sort: 99},
			{NameZh: "匈牙利", NameNative: "Hungary", Code: "36", Icon: "/static/country/hungary.png", ISO2: "HU", Sort: 99},
			{NameZh: "牙买加", NameNative: "Jamaica", Code: "1-876", Icon: "/static/country/jamaica.png", ISO2: "JM", Sort: 99},
			{NameZh: "也门", NameNative: "Yemen", Code: "967", Icon: "/static/country/yemen.png", ISO2: "YE", Sort: 99},
			{NameZh: "伊朗", NameNative: "Iran", Code: "98", Icon: "/static/country/iran.png", ISO2: "IR", Sort: 99},
			{NameZh: "意大利", NameNative: "Italy", Code: "39", Icon: "/static/country/italy.png", ISO2: "IT", Sort: 99},
			{NameZh: "英属维尔京群岛", NameNative: "British Virgin Islands", Code: "1-284", Icon: "/static/country/british_virgin_islands.png", ISO2: "VG", Sort: 99},
			{NameZh: "英属印度洋领地", NameNative: "British Indian Ocean Territory", Code: "246", Icon: "/static/country/british_indian_ocean_territory.png", ISO2: "IO", Sort: 99},
			{NameZh: "约旦", NameNative: "Jordan", Code: "962", Icon: "/static/country/jordan.png", ISO2: "JO", Sort: 99},
			{NameZh: "泽西", NameNative: "Jersey", Code: "44-1534", Icon: "/static/country/jersey.png", ISO2: "JE", Sort: 99},
			{NameZh: "乍得", NameNative: "Chad", Code: "235", Icon: "/static/country/chad.png", ISO2: "TD", Sort: 99},
			{NameZh: "直布罗陀", NameNative: "Gibraltar", Code: "350", Icon: "/static/country/gibraltar.png", ISO2: "GI", Sort: 99},
			{NameZh: "智利", NameNative: "Chile", Code: "56", Icon: "/static/country/chile.png", ISO2: "CL", Sort: 99},
			{NameZh: "中非共和国", NameNative: "Central African Republic", Code: "236", Icon: "/static/country/central_africa.png", ISO2: "CF", Sort: 99},
			{NameZh: "马耳他", NameNative: "Malta", Code: "44-1624", Icon: "/static/country/malta.png", ISO2: "MT", Sort: 99},
			{NameZh: "马拉维", NameNative: "Malawi", Code: "265", Icon: "/static/country/malawi.png", ISO2: "MW", Sort: 99},
			{NameZh: "马里", NameNative: "Mali", Code: "356", Icon: "/static/country/mali.png", ISO2: "ML", Sort: 99},
			{NameZh: "马绍尔群岛", NameNative: "Marshall Islands", Code: "692", Icon: "/static/country/marshall_islands.png", ISO2: "MH", Sort: 99},
			{NameZh: "马约特", NameNative: "Mayotte", Code: "262", Icon: "/static/country/mayotte.png", ISO2: "YT", Sort: 99},
			{NameZh: "马提尼克", NameNative: "Martinique", Code: "596", Icon: "/static/country/martinique.png", ISO2: "MQ", Sort: 99},
			{NameZh: "毛里求斯", NameNative: "Mauritius", Code: "230", Icon: "/static/country/mauritius.png", ISO2: "MU", Sort: 99},
			{NameZh: "毛里塔尼亚", NameNative: "Mauritania", Code: "222", Icon: "/static/country/mauritania.png", ISO2: "MR", Sort: 99},
			{NameZh: "美属萨摩亚", NameNative: "American Samoa", Code: "1-684", Icon: "/static/country/american_samoa.png", ISO2: "AS", Sort: 99},
			{NameZh: "蒙特内哥", NameNative: "Montenegro", Code: "382", Icon: "/static/country/montenegro.png", ISO2: "ME", Sort: 99},
			{NameZh: "蒙特塞拉特", NameNative: "Montserrat", Code: "1-664", Icon: "/static/country/montserrat.png", ISO2: "MS", Sort: 99},
			{NameZh: "密克罗尼西亚", NameNative: "Micronesia", Code: "691", Icon: "/static/country/micronesia.png", ISO2: "FM", Sort: 99},
			{NameZh: "摩尔多瓦", NameNative: "Moldova", Code: "373", Icon: "/static/country/moldova.png", ISO2: "MD", Sort: 99},
			{NameZh: "摩洛哥", NameNative: "Morocco", Code: "212", Icon: "/static/country/morocco.png", ISO2: "MA", Sort: 99},
			{NameZh: "摩纳哥", NameNative: "Monaco", Code: "377", Icon: "/static/country/monaco.png", ISO2: "MC", Sort: 99},
			{NameZh: "莫桑比克", NameNative: "Mozambique", Code: "258", Icon: "/static/country/mozambique.png", ISO2: "MZ", Sort: 99},
			{NameZh: "墨西哥", NameNative: "Mexico", Code: "52", Icon: "/static/country/mexico.png", ISO2: "MX", Sort: 99},
			{NameZh: "纳米比亚", NameNative: "Namibia", Code: "264", Icon: "/static/country/namibia.png", ISO2: "NA", Sort: 99},
			{NameZh: "奈及利亚", NameNative: "Nigeria", Code: "234", Icon: "/static/country/nigeria.png", ISO2: "NG", Sort: 99},
			{NameZh: "南非", NameNative: "South Africa", Code: "27", Icon: "/static/country/south_africa.png", ISO2: "ZA", Sort: 99},
			{NameZh: "南乔治亚与南桑威奇群岛", NameNative: "South Georgia and the South Sandwich Islands", Code: "44", Icon: "/static/country/south_georgia.png", ISO2: "GS", Sort: 99},
			{NameZh: "瑙鲁", NameNative: "Nauru", Code: "674", Icon: "/static/country/nauru.png", ISO2: "NR", Sort: 99},
			{NameZh: "尼加拉瓜", NameNative: "Nicaragua", Code: "505", Icon: "/static/country/nicaragua.png", ISO2: "NI", Sort: 99},
			{NameZh: "尼日尔", NameNative: "Niger", Code: "227", Icon: "/static/country/niger.png", ISO2: "NE", Sort: 99},
			{NameZh: "纽埃", NameNative: "Niue", Code: "683", Icon: "/static/country/niue.png", ISO2: "NU", Sort: 99},
			{NameZh: "挪威", NameNative: "Norway", Code: "47", Icon: "/static/country/norway.png", ISO2: "NO", Sort: 99},
			{NameZh: "帕劳", NameNative: "Palau", Code: "680", Icon: "/static/country/palau.png", ISO2: "PW", Sort: 99},
			{NameZh: "皮特凯恩群岛", NameNative: "Pitcairn Islands", Code: "64", Icon: "/static/country/pitcairn_islands.png", ISO2: "PN", Sort: 99},
			{NameZh: "葡萄牙", NameNative: "Portugal", Code: "351", Icon: "/static/country/portugal.png", ISO2: "PT", Sort: 99},
			{NameZh: "瑞典", NameNative: "Sweden", Code: "46", Icon: "/static/country/sweden.png", ISO2: "SE", Sort: 99},
			{NameZh: "瑞士", NameNative: "Switzerland", Code: "41", Icon: "/static/country/switzerland.png", ISO2: "CH", Sort: 99},
			{NameZh: "萨摩亚", NameNative: "Samoa", Code: "685", Icon: "/static/country/samoa.png", ISO2: "WS", Sort: 99},
			{NameZh: "塞尔维亚", NameNative: "Serbia", Code: "381", Icon: "/static/country/serbia.png", ISO2: "RS", Sort: 99},
			{NameZh: "塞拉利昂", NameNative: "Sierra Leone", Code: "232", Icon: "/static/country/sierra_leone.png", ISO2: "SL", Sort: 99},
			{NameZh: "塞内加尔", NameNative: "Senegal", Code: "221", Icon: "/static/country/senegal.png", ISO2: "SN", Sort: 99},
			{NameZh: "塞舌尔", NameNative: "Seychelles", Code: "248", Icon: "/static/country/seychelles.png", ISO2: "SC", Sort: 99},
			{NameZh: "赞比亚", NameNative: "Zambia", Code: "260", Icon: "/static/country/zambia.png", ISO2: "ZM", Sort: 99},
			{NameZh: "圣巴泰勒米", NameNative: "Saint Barthélemy", Code: "590", Icon: "/static/country/saint_barthelemy.png", ISO2: "BL", Sort: 99},
			{NameZh: "圣诞岛", NameNative: "Christmas Island", Code: "61-8-9164", Icon: "/static/country/christmas_island.png", ISO2: "CX", Sort: 99},
			{NameZh: "圣多美和普林西比", NameNative: "São Tomé and Príncipe", Code: "260", Icon: "/static/country/sao_tome_and_principe.png", ISO2: "ST", Sort: 99},
			{NameZh: "圣赫勒拿", NameNative: "Saint Helena", Code: "290", Icon: "/static/country/saint_helena.png", ISO2: "SH", Sort: 99},
			{NameZh: "圣马丁", NameNative: "Saint Martin", Code: "590", Icon: "/static/country/st_martin.png", ISO2: "MF", Sort: 99},
			{NameZh: "圣基茨和尼维斯", NameNative: "Saint Kitts and Nevis", Code: "1-869", Icon: "/static/country/saint_kitts_and_nevis.png", ISO2: "KN", Sort: 99},
			{NameZh: "圣卢西亚", NameNative: "Saint Lucia", Code: "1-758", Icon: "/static/country/saint_lucia.png", ISO2: "LC", Sort: 99},
			{NameZh: "圣马力诺", NameNative: "San Marino", Code: "378", Icon: "/static/country/san_marino.png", ISO2: "SM", Sort: 99},
			{NameZh: "圣皮埃尔和密克隆", NameNative: "Saint Pierre and Miquelon", Code: "508", Icon: "/static/country/zambia.png", ISO2: "PM", Sort: 99},
			{NameZh: "圣文森特和格林纳丁斯", NameNative: "Saint Vincent and the Grenadines", Code: "1-784", Icon: "/static/country/saint_vincent_and_the_grenadines.png", ISO2: "VC", Sort: 99},
			{NameZh: "斯威士兰", NameNative: "Kingdom of Eswatini", Code: "268", Icon: "/static/country/kingdom_of_eswatini.png", ISO2: "SZ", Sort: 99},
			{NameZh: "斯洛伐克", NameNative: "Slovakia", Code: "421", Icon: "/static/country/slovakia.png", ISO2: "SK", Sort: 99},
			{NameZh: "斯瓦尔巴群岛", NameNative: "Svalbard", Code: "47-79", Icon: "/static/country/svalbard.png", ISO2: "SJ", Sort: 99},
			{NameZh: "苏丹", NameNative: "Sudan", Code: "249", Icon: "/static/country/sudan.png", ISO2: "SD", Sort: 99},
			{NameZh: "苏里南", NameNative: "Suriname", Code: "597", Icon: "/static/country/suriname.png", ISO2: "SR", Sort: 99},
			{NameZh: "所罗门群岛", NameNative: "Solomon Islands", Code: "677", Icon: "/static/country/solomon_islands.png", ISO2: "SB", Sort: 99},
			{NameZh: "索马里", NameNative: "Somalia", Code: "252", Icon: "/static/country/somalia.png", ISO2: "SO", Sort: 99},
			{NameZh: "塔吉克斯坦", NameNative: "Tajikistan", Code: "992", Icon: "/static/country/tajikistan.png", ISO2: "TJ", Sort: 99},
			{NameZh: "坦桑尼亚", NameNative: "Tanzania", Code: "255", Icon: "/static/country/tanzania.png", ISO2: "TZ", Sort: 99},
			{NameZh: "汤加", NameNative: "Tonga", Code: "676", Icon: "/static/country/tonga.png", ISO2: "TO", Sort: 99},
			{NameZh: "特克斯和凯科斯群岛", NameNative: "Turks and Caicos Islands", Code: "1-649", Icon: "/static/country/turks_and_caicos_islands.png", ISO2: "TC", Sort: 99},
			{NameZh: "突尼斯", NameNative: "Tunisia", Code: "216", Icon: "/static/country/tunisia.png", ISO2: "TN", Sort: 99},
			{NameZh: "图瓦卢", NameNative: "Tuvalu", Code: "688", Icon: "/static/country/tuvalu.png", ISO2: "TV", Sort: 99},
			{NameZh: "托克劳", NameNative: "Tokelau", Code: "690", Icon: "/static/country/tokelau.png", ISO2: "TK", Sort: 99},
			{NameZh: "瓦利斯和富图纳", NameNative: "Wallis and Futuna", Code: "681", Icon: "/static/country/wallis_and_futuna.png", ISO2: "WF", Sort: 99},
			{NameZh: "瓦努阿图", NameNative: "Vanuatu", Code: "678", Icon: "/static/country/vanuatu.png", ISO2: "VU", Sort: 99},
			{NameZh: "危地马拉", NameNative: "Guatemala", Code: "502", Icon: "/static/country/guatemala.png", ISO2: "GT", Sort: 99},
			{NameZh: "委内瑞拉", NameNative: "Venezuela", Code: "58", Icon: "/static/country/venezuela.png", ISO2: "VE", Sort: 99},
			{NameZh: "乌干达", NameNative: "Uganda", Code: "256", Icon: "/static/country/uganda.png", ISO2: "UG", Sort: 99},
			{NameZh: "乌克兰", NameNative: "Ukraine", Code: "380", Icon: "/static/country/ukraine.png", ISO2: "UA", Sort: 99},
			{NameZh: "乌拉圭", NameNative: "Uruguay", Code: "598", Icon: "/static/country/uruguay.png", ISO2: "UY", Sort: 99},
			{NameZh: "西班牙", NameNative: "Spain", Code: "34", Icon: "/static/country/spain.png", ISO2: "ES", Sort: 99},
			{NameZh: "希腊", NameNative: "Greece", Code: "30", Icon: "/static/country/greece.png", ISO2: "GR", Sort: 99},
			{NameZh: "新西兰", NameNative: "New Zealand", Code: "64", Icon: "/static/country/new_zealand.png", ISO2: "NZ", Sort: 99},
		}

		if err := db.Create(initData).Error; err != nil {
			return fmt.Errorf("创建国家失败: %v", err)
		}

	}
	return nil
}
