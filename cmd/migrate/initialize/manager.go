package initialize

import (
	"gin/internal/constant"
	"gin/internal/models"
	"time"

	"gorm.io/gorm"
)

// InitManagers 初始化管理员数据
func InitManagers(db *gorm.DB) error {

	// 检查是否已存在管理员
	var count int64
	if err := db.Model(&models.Manager{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}
	// 初始化用户数据
	users := initManagers()

	// 批量创建用户
	if err := db.Create(&users).Error; err != nil {
		return err
	}

	return nil
}

func initManagers() []*models.Manager {
	users := []*models.Manager{
		{
			BaseModel:            models.BaseModel{ID: 1},
			Username:             constant.InitialSuperManagerUsername,
			Nickname:             "超级管理员",
			PasswordHash:         constant.InitialSuperManagerPassword,
			Avatar:               "/static/avatar/default_avatar.png",
			LastLoginIP:          "127.0.0.1",
			LastLoginTime:        time.Now(),
			LastPasswordChangeAt: time.Now(),
			ExpiredAt:            time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC),
		},
	}
	return users
}
