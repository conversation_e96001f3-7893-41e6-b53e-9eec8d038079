package initialize

import (
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitMenu 初始化菜单数据
func InitMenu(db *gorm.DB) error {

	// 检查是否已存在菜单数据
	var count int64
	if err := db.Model(&models.Menu{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	// 初始化菜单数据
	menus := initMenus()

	if err := db.Create(&menus).Error; err != nil {
		return err
	}
	return nil
}

func initMenus() []*models.Menu {
	return []*models.Menu{
		// 仪表盘目录
		{Name: "仪表盘", Code: "dashboard", Path: "/", Sort: 100, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/analytics", IsSystem: true, Meta: models.MenuMeta{Title: "仪表盘", Icon: "&#xe721;"}, Children: []*models.Menu{
			{Name: "分析页", Code: "dashboard:analysis:/dashboard/analysis", Path: "/dashboard/analysis", Sort: 1, Type: models.MenuTypeMenu, Component: "/dashboard/analysis", IsSystem: true, Meta: models.MenuMeta{Title: "分析页", Icon: "&#xe7cb;"}},
			{Name: "工作台", Code: "dashboard:console:/dashboard/console", Path: "/dashboard/console", Sort: 2, Type: models.MenuTypeMenu, Component: "/dashboard/console", IsSystem: true, Meta: models.MenuMeta{Title: "工作台", Icon: "&#xe7cd;"}},
		}},

		{Name: "用户管理", Code: "user", Path: "/users", Sort: 200, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/user/users", IsSystem: true, Meta: models.MenuMeta{Title: "用户管理", Icon: "&#xe60f;"}, Children: []*models.Menu{
			{Name: "用户列表", Code: "user:users:list:/user/users", Path: "/user/users", Sort: 1, Type: models.MenuTypeMenu, Component: "/user/users", IsSystem: true, Meta: models.MenuMeta{Title: "用户列表", Icon: "&#xe60f;"}},
			{Name: "用户认证", Code: "user:certification:/user/certification", Path: "/user/certification", Sort: 2, Type: models.MenuTypeMenu, Component: "/user/certification", IsSystem: true, Meta: models.MenuMeta{Title: "用户认证", Icon: "&#xe60f;"}},
			{Name: "用户通知", Code: "user:notice:list:/user/notice", Path: "/user/notice", Sort: 3, Type: models.MenuTypeMenu, Component: "/user/notice", IsSystem: true, Meta: models.MenuMeta{Title: "用户通知", Icon: "&#xe60f;"}},
			{Name: "用户等级", Code: "user:level:list:/user/level", Path: "/user/level", Sort: 4, Type: models.MenuTypeMenu, Component: "/user/level", IsSystem: true, Meta: models.MenuMeta{Title: "用户等级", Icon: "&#xe60f;"}},
			{Name: "提现账户", Code: "user:account:list:/user/account", Path: "/user/account", Sort: 5, Type: models.MenuTypeMenu, Component: "/user/account", IsSystem: true, Meta: models.MenuMeta{Title: "提现账户", Icon: "&#xe60f;"}},
			{Name: "通知列表", Code: "user:notice:list:/user/notice", Path: "/user/notice", Sort: 6, Type: models.MenuTypeMenu, Component: "/user/notice", IsSystem: true, Meta: models.MenuMeta{Title: "通知列表", Icon: "&#xe60f;"}},
		}},

		// 财务管理
		{Name: "财务管理", Code: "wallet", Path: "/wallet", Sort: 300, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/wallet/list", IsSystem: true, Meta: models.MenuMeta{Title: "财务管理", Icon: "&#xe60f;"}, Children: []*models.Menu{
			{Name: "用户资产", Code: "wallet:asset:list:/wallet/asset", Path: "/wallet/asset", Sort: 1, Type: models.MenuTypeMenu, Component: "/wallet/asset", IsSystem: true, Meta: models.MenuMeta{Title: "用户资产", Icon: "&#xe60f;"}},
			{Name: "提现订单", Code: "wallet:withdraw:list:/wallet/withdraw", Path: "/wallet/withdraw", Sort: 2, Type: models.MenuTypeMenu, Component: "/wallet/withdraw", IsSystem: true, Meta: models.MenuMeta{Title: "提现订单", Icon: "&#xe60f;"}},
			{Name: "充值订单", Code: "wallet:recharge:list:/wallet/deposit", Path: "/wallet/deposit", Sort: 3, Type: models.MenuTypeMenu, Component: "/wallet/deposit", IsSystem: true, Meta: models.MenuMeta{Title: "充值订单", Icon: "&#xe60f;"}},
			{Name: "兑换订单", Code: "wallet:exchange:list:/wallet/swap", Path: "/wallet/swap", Sort: 4, Type: models.MenuTypeMenu, Component: "/wallet/swap", IsSystem: true, Meta: models.MenuMeta{Title: "兑换订单", Icon: "&#xe60f;"}},
			{Name: "转账订单", Code: "wallet:transfer:list:/wallet/transfer", Path: "/wallet/transfer", Sort: 5, Type: models.MenuTypeMenu, Component: "/wallet/transfer", IsSystem: true, Meta: models.MenuMeta{Title: "转账订单", Icon: "&#xe60f;"}},
			{Name: "账单记录", Code: "wallet:bill:list:/wallet/bill", Path: "/wallet/bill", Sort: 6, Type: models.MenuTypeMenu, Component: "/wallet/bill", IsSystem: true, Meta: models.MenuMeta{Title: "账单记录", Icon: "&#xe60f;"}},
		}},

		// 产品管理
		{Name: "产品管理", Code: "product", Path: "/product", Sort: 400, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/product/list", IsSystem: true, Meta: models.MenuMeta{Title: "产品管理", Icon: "&#xe60f;"}, Children: []*models.Menu{
			{Name: "产品分类", Code: "product:category:/product/category", Path: "/product/category", Sort: 1, Type: models.MenuTypeMenu, Component: "/product/category", IsSystem: true, Meta: models.MenuMeta{Title: "产品分类", Icon: "&#xe60f;"}},
			{Name: "产品列表", Code: "product:list:/product/list", Path: "/product/list", Sort: 2, Type: models.MenuTypeMenu, Component: "/product/list", IsSystem: true, Meta: models.MenuMeta{Title: "产品列表", Icon: "&#xe60f;"}},
			{Name: "产品订单", Code: "product:order:/product/order", Path: "/product/order", Sort: 3, Type: models.MenuTypeMenu, Component: "/product/order", IsSystem: true, Meta: models.MenuMeta{Title: "产品订单", Icon: "&#xe60f;"}},
		}},

		// 后台管理
		{Name: "后台管理", Code: "manager", Path: "/manager", Sort: 500, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/manager/menu", IsSystem: true, Meta: models.MenuMeta{Title: "后台管理", Icon: "&#xe7b9;"}, Children: []*models.Menu{
			{Name: "菜单管理", Code: "manager:menu:list:/manager/menu", Path: "/manager/menu", Sort: 1, Type: models.MenuTypeMenu, Component: "/manager/menu", IsSystem: true, Meta: models.MenuMeta{Title: "菜单管理", Icon: "&#xe64e;"}},
			{Name: "管理列表", Code: "manager:manager:list:/manager/manager", Path: "/manager/manager", Sort: 2, Type: models.MenuTypeMenu, Component: "/manager/manager", IsSystem: true, Meta: models.MenuMeta{Title: "管理列表", Icon: "&#xe830;"}},
			{Name: "角色管理", Code: "manager:role:list:/manager/role", Path: "/manager/role", Sort: 3, Type: models.MenuTypeMenu, Component: "/manager/role", IsSystem: true, Meta: models.MenuMeta{Title: "角色管理", Icon: "&#xe74f;"}},
			{Name: "权限管理", Code: "manager:permission:list:/manager/permission", Path: "/manager/permission", Sort: 4, Type: models.MenuTypeMenu, Component: "/manager/permission", IsSystem: true, Meta: models.MenuMeta{Title: "权限管理", Icon: "&#xe89c;"}},
		}},

		// 系统配置
		{Name: "系统配置", Code: "system", Path: "/system", Sort: 600, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/system/config", IsSystem: true, Meta: models.MenuMeta{Title: "系统配置", Icon: "&#xe75c;"}, Children: []*models.Menu{
			{Name: "项目配置", Code: "system:setting:list:/system/setting", Path: "/system/setting", Sort: 1, Type: models.MenuTypeMenu, Component: "/system/setting", IsSystem: true, Meta: models.MenuMeta{Title: "项目配置", Icon: "&#xe60f;"}},
			{Name: "资产配置", Code: "system:asset:list:/system/asset", Path: "/system/asset", Sort: 2, Type: models.MenuTypeMenu, Component: "/system/asset", IsSystem: true, Meta: models.MenuMeta{Title: "资产配置", Icon: "&#xe60f;"}},
			{Name: "支付配置", Code: "system:pay:list:/system/payment", Path: "/system/payment", Sort: 3, Type: models.MenuTypeMenu, Component: "/system/payment", IsSystem: true, Meta: models.MenuMeta{Title: "支付配置", Icon: "&#xe60f;"}},
			{Name: "菜单配置", Code: "system:menu:list:/system/menu", Path: "/system/menu", Sort: 4, Type: models.MenuTypeMenu, Component: "/system/menu", IsSystem: true, Meta: models.MenuMeta{Title: "菜单配置", Icon: "&#xe60f;"}},
			{Name: "等级配置", Code: "system:level:list:/system/level", Path: "/system/level", Sort: 5, Type: models.MenuTypeMenu, Component: "/system/level", IsSystem: true, Meta: models.MenuMeta{Title: "等级配置", Icon: "&#xe60f;"}},
			{Name: "文章配置", Code: "system:article:list:/system/article", Path: "/system/article", Sort: 6, Type: models.MenuTypeMenu, Component: "/system/article", IsSystem: true, Meta: models.MenuMeta{Title: "文章配置", Icon: "&#xe60f;"}},
			{Name: "语言配置", Code: "system:language:list:/system/language", Path: "/system/language", Sort: 6, Type: models.MenuTypeMenu, Component: "/system/language", IsSystem: true, Meta: models.MenuMeta{Title: "语言配置", Icon: "&#xe60f;"}},
			{Name: "翻译配置", Code: "system:translation:list:/system/translation", Path: "/system/translation", Sort: 7, Type: models.MenuTypeMenu, Component: "/system/translation", IsSystem: true, Meta: models.MenuMeta{Title: "翻译配置", Icon: "&#xe60f;"}},
			{Name: "国家配置", Code: "system:country:list:/system/country", Path: "/system/country", Sort: 8, Type: models.MenuTypeMenu, Component: "/system/country", IsSystem: true, Meta: models.MenuMeta{Title: "国家配置", Icon: "&#xe60f;"}},
		}},

		// 系统监控
		{Name: "系统监控", Code: "monitor", Path: "/monitor", Sort: 700, Type: models.MenuTypeDirectory, Component: "/index/index", Redirect: "/monitor/server", IsSystem: true, Meta: models.MenuMeta{Title: "系统监控", Icon: "&#xe75c;"}, Children: []*models.Menu{
			{Name: "服务监控", Code: "monitor:server:list:/monitor/server", Path: "/monitor/server", Sort: 1, Type: models.MenuTypeMenu, Component: "/monitor/server", IsSystem: true, Meta: models.MenuMeta{Title: "服务监控", Icon: "&#xe6e1;"}},
			{Name: "登录日志", Code: "monitor:login-log:list:/monitor/loginLog", Path: "/monitor/loginLog", Sort: 2, Type: models.MenuTypeMenu, Component: "/monitor/loginLog", IsSystem: true, Meta: models.MenuMeta{Title: "登录日志", Icon: "&#xe6df;"}},
			{Name: "操作日志", Code: "monitor:operate-log:list:/monitor/operateLog", Path: "/monitor/operateLog", Sort: 3, Type: models.MenuTypeMenu, Component: "/monitor/operateLog", IsSystem: true, Meta: models.MenuMeta{Title: "操作日志", Icon: "&#xe722;"}},
		}},
	}
}
