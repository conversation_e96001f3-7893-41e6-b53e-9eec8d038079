package initialize

import (
	"gin/internal/constant"
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitTranslation 初始化翻译数据
func InitTranslation(db *gorm.DB) error {

	// 检查是否已存在数据
	var count int64
	if err := db.Model(&models.Translation{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil
	}

	var translations []*models.Translation

	validationTranslations := []*models.Translation{
		{Key: "validation.required", Module: "validation", Lang: models.LanguageDefault, Name: "必填", Value: "{{field}}不能为空", Status: models.TranslationTypeSystem},
		{Key: "validation.min", Module: "validation", Lang: models.LanguageDefault, Name: "最小长度", Value: "{{field}}长度不能小于{{min}}", Status: models.TranslationTypeSystem},
		{Key: "validation.max", Module: "validation", Lang: models.LanguageDefault, Name: "最大长度", Value: "{{field}}长度不能大于{{max}}", Status: models.TranslationTypeSystem},
		{Key: "validation.email", Module: "validation", Lang: models.LanguageDefault, Name: "邮箱", Value: "{{field}}必须是有效的邮箱地址", Status: models.TranslationTypeSystem},
		{Key: "validation.url", Module: "validation", Lang: models.LanguageDefault, Name: "URL", Value: "{{field}}必须是有效的URL地址", Status: models.TranslationTypeSystem},
		{Key: "validation.number", Module: "validation", Lang: models.LanguageDefault, Name: "数字", Value: "{{field}}必须是有效的数字", Status: models.TranslationTypeSystem},
		{Key: "validation.integer", Module: "validation", Lang: models.LanguageDefault, Name: "整数", Value: "{{field}}必须是有效的整数", Status: models.TranslationTypeSystem},
		{Key: "validation.float", Module: "validation", Lang: models.LanguageDefault, Name: "浮点数", Value: "{{field}}必须是有效的浮点数", Status: models.TranslationTypeSystem},
		{Key: "validation.boolean", Module: "validation", Lang: models.LanguageDefault, Name: "布尔值", Value: "{{field}}必须是有效的布尔值", Status: models.TranslationTypeSystem},
		{Key: "validation.date", Module: "validation", Lang: models.LanguageDefault, Name: "日期", Value: "{{field}}必须是有效的日期", Status: models.TranslationTypeSystem},
		{Key: "validation.datetime", Module: "validation", Lang: models.LanguageDefault, Name: "日期时间", Value: "{{field}}必须是有效的日期时间", Status: models.TranslationTypeSystem},
		{Key: "validation.time", Module: "validation", Lang: models.LanguageDefault, Name: "时间", Value: "{{field}}必须是有效的时间", Status: models.TranslationTypeSystem},
		{Key: "validation.datetime-local", Module: "validation", Lang: models.LanguageDefault, Name: "日期时间本地", Value: "{{field}}必须是有效的日期时间本地", Status: models.TranslationTypeSystem},
		{Key: "validation.date-local", Module: "validation", Lang: models.LanguageDefault, Name: "日期本地", Value: "{{field}}必须是有效的日期本地", Status: models.TranslationTypeSystem},
		{Key: "validation.time-local", Module: "validation", Lang: models.LanguageDefault, Name: "时间本地", Value: "{{field}}必须是有效的时间本地", Status: models.TranslationTypeSystem},
		{Key: "validation.datetime-local", Module: "validation", Lang: models.LanguageDefault, Name: "日期时间本地", Value: "{{field}}必须是有效的日期时间本地", Status: models.TranslationTypeSystem},
		{Key: "validation.date-local", Module: "validation", Lang: models.LanguageDefault, Name: "日期本地", Value: "{{field}}必须是有效的日期本地", Status: models.TranslationTypeSystem},
	}
	translations = append(translations, validationTranslations...)

	systemTranslations := []*models.Translation{
		{Key: constant.ErrSystemError.Error(), Module: "common", Name: "系统错误", Value: "系统错误", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrBalanceNotEnough.Error(), Module: "common", Name: "余额不足", Value: "余额不足", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrCaptchaIncorrect.Error(), Module: "common", Name: "验证码不正确", Value: "验证码不正确", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrInviteCodeExpired.Error(), Module: "common", Name: "邀请码已过期", Value: "邀请码已过期", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrInviteCodeNotExists.Error(), Module: "common", Name: "邀请码不存在", Value: "邀请码不存在", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrLevelNotExists.Error(), Module: "common", Name: "等级不存在", Value: "等级不存在", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrUserExists.Error(), Module: "common", Name: "用户已存在", Value: "用户已存在", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrUserNotExists.Error(), Module: "common", Name: "用户不存在", Value: "用户不存在", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrUserNotActive.Error(), Module: "common", Name: "用户未激活", Value: "用户未激活", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrUserDisabled.Error(), Module: "common", Name: "用户已禁用", Value: "用户已禁用", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrDepositAmountRange.Error(), Module: "common", Name: "充值金额范围错误", Value: "充值金额范围错误", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrDepositTimeRange.Error(), Module: "common", Name: "充值时间范围错误", Value: "充值时间范围错误", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrWithdrawAmountRange.Error(), Module: "common", Name: "提现金额范围错误", Value: "提现金额范围错误", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrWithdrawTimeRange.Error(), Module: "common", Name: "提现时间范围错误", Value: "提现时间范围错误", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrOldPasswordIncorrect.Error(), Module: "common", Name: "旧密码不正确", Value: "旧密码不正确", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrSecurityKeyIncorrect.Error(), Module: "common", Name: "支付密码错误", Value: "支付密码错误", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrAccountOrPasswordIncorrect.Error(), Module: "common", Name: "账户或密码不正确", Value: "账户或密码不正确", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: constant.ErrBindAccountOverLimit.Error(), Module: "common", Name: "绑定账户超过限制", Value: "绑定账户超过限制", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsUserFreeze, Module: "common", Name: "用户冻结提示词", Value: "用户已被冻结，请联系管理员", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsUserScore, Module: "common", Name: "用户信用分提示词", Value: "用户信用分不足，请联系管理员", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsNotice, Module: "common", Name: "公告提示词", Value: "公告提示词", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsRegister, Module: "common", Name: "注册提示词", Value: "欢迎注册，请先阅读并同意《用户协议》", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsLogin, Module: "common", Name: "登录提示词", Value: "欢迎登录，请先阅读并同意《用户协议》", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsSiteDesc, Module: "common", Name: "站点描述", Value: "欢迎使用我们的平台，我们提供最优质的服务", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsSiteCopyright, Module: "common", Name: "站点版权", Value: "版权所有，侵权必究", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
		{Key: models.SettingTipsSiteIntroduce, Module: "common", Name: "站点介绍", Value: "我们提供最优质的服务", Lang: models.LanguageDefault, Type: models.TranslationTypeSystem},
	}
	translations = append(translations, systemTranslations...)

	if err := db.Create(&translations).Error; err != nil {
		return err
	}
	return nil
}
