package migrate

import "gin/internal/models"

// GetAllModels 获取所有需要迁移的模型
func GetAllModels() []interface{} {
	return []interface{}{
		&models.Article{},
		&models.Asset{},
		&models.Category{},
		&models.Country{},
		&models.FrontMenu{},
		&models.Language{},
		&models.Level{},
		&models.LoginLog{},
		&models.Manager{},
		&models.ManagerNotice{},
		&models.Menu{},
		&models.MenuPermission{},
		&models.OperateLog{},
		&models.Order{},
		&models.Payment{},
		&models.Permission{},
		&models.Product{},
		&models.Role{},
		&models.Setting{},
		&models.Translation{},
		&models.User{},
		&models.UserAccount{},
		&models.UserAsset{},
		&models.UserBill{},
		&models.UserCertification{},
		&models.UserLevel{},
		&models.UserNotice{},
		&models.UserSetting{},
		&models.UserSwap{},
		&models.UserTransfer{},
		&models.UserWallet{},
	}
}
