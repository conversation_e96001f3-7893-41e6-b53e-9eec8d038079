package migrate

import (
	"fmt"
	"gin/cmd/migrate/initialize"
	"gin/cmd/migrate/migrate"
	"gin/cmd/migrate/wire"
	"gin/internal/infrastructure/config"
	"github.com/spf13/cobra"
)

// CmdMigrate 数据库迁移命令
var CmdMigrate = &cobra.Command{
	Use:   "migrate",
	Short: "数据库迁移管理",
	Long: `管理数据库迁移，包括创建表、升级数据库结构、初始化数据等操作。
支持的操作：
- up: 执行数据库迁移（创建表结构）
- init: 初始化数据（插入默认数据）`,
}

// migrateUpCmd 执行数据库迁移
var migrateUpCmd = &cobra.Command{
	Use:   "up",
	Short: "执行数据库表结构迁移",
	Long: `执行数据库迁移，创建或更新数据库表结构。

该命令会根据模型定义自动创建或更新数据库表，确保数据库结构是最新的。

示例:
  migrate up           # 执行所有迁移
  migrate up --dry-run # 预览迁移但不执行`,
	RunE: runMigrateUp,
}

// migrateInitCmd 初始化基础数据
var migrateInitCmd = &cobra.Command{
	Use:   "init",
	Short: "初始化基础数据",
	Long:  `初始化数据库基础数据，该命令会插入系统运行所需的基础数据。`,
	RunE:  runMigrateInit,
}

var (
	dryRun     bool
	configPath string
)

func init() {
	CmdMigrate.AddCommand(migrateUpCmd)
	CmdMigrate.AddCommand(migrateInitCmd)

	// 全局标志
	CmdMigrate.PersistentFlags().StringVarP(&configPath, "config", "c", "config/config.yaml", "配置文件路径")

	// 迁移相关标志
	migrateUpCmd.Flags().BoolVar(&dryRun, "dry-run", false, "预览迁移操作但不实际执行")
}

// runMigrateUp 执行数据库迁移
func runMigrateUp(cmd *cobra.Command, args []string) error {
	if dryRun {
		fmt.Println("预览模式 - 不会实际执行迁移")
		allModels := migrate.GetAllModels()
		fmt.Printf("将迁移 %d 个表\n", len(allModels))
		return nil
	}

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return err
	}

	// 初始化数据库连接
	app, err := wire.InitApp(cfg)
	if err != nil {
		return err
	}

	allModels := migrate.GetAllModels()

	for _, model := range allModels {
		if err = app.DB.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移失败: %v", err)
		}
		fmt.Printf("成功迁移表: %s\n", model)
	}

	return nil
}

// runMigrateInit 初始化基础数据
func runMigrateInit(cmd *cobra.Command, args []string) error {
	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return err
	}

	// 初始化数据库连接
	app, err := wire.InitApp(cfg)
	if err != nil {
		return err
	}

	// 迁移表结构
	if err = runMigrateUp(cmd, args); err != nil {
		return fmt.Errorf("迁移表结构失败: %v", err)
	}

	// 执行数据初始化
	if err = initialize.InitArticle(app.DB); err != nil {
		return err
	}
	if err = initialize.InitAsset(app.DB); err != nil {
		return err
	}
	if err = initialize.InitLevel(app.DB); err != nil {
		return err
	}
	if err = initialize.InitLanguage(app.DB); err != nil {
		return err
	}
	if err = initialize.InitManagers(app.DB); err != nil {
		return err
	}
	if err = initialize.InitTranslation(app.DB); err != nil {
		return err
	}
	if err = initialize.InitRole(app.DB); err != nil {
		return err
	}
	if err = initialize.InitMenu(app.DB); err != nil {
		return err
	}
	if err = initialize.InitCountry(app.DB); err != nil {
		return err
	}
	if err = initialize.InitFrontMenu(app.DB); err != nil {
		return err
	}
	if err = initialize.InitSetting(app.DB); err != nil {
		return err
	}

	return nil
}
