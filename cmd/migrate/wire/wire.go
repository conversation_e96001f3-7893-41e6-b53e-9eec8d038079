//go:build wireinject
// +build wireinject

package wire

import (
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/database"
	"github.com/google/wire"
	"gorm.io/gorm"
)

// ProvideDbConfig 数据库配置提供者
func ProvideDbConfig(cfg *config.Config) *config.DatabaseConfig {
	return &cfg.Database
}

// 基础设施提供者集合
var infrastructureSet = wire.NewSet(
	ProvideDbConfig,
	database.NewDatabase,
)

// InitApp 初始化App所需的所有依赖
func InitApp(cfg *config.Config) (*App, error) {
	panic(wire.Build(
		infrastructureSet,
		wire.Struct(new(App), "*"),
	))
}

// App 应用程序结构体
type App struct {
	DB *gorm.DB
}
