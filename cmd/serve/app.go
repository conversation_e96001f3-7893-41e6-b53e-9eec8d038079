package serve

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gin/cmd/serve/crontab"
	"gin/cmd/serve/wire"
	"gin/internal/handler"
	"gin/internal/infrastructure/config"
	"gin/internal/middleware"
	"gin/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LogoContent 应用程序启动Logo
var LogoContent = []byte(`
   ______ _          _____  ____            _____
  / _____|  \       |     \|    |     /\   /  ___)
 | |  ___| | |___   |  /\  ||   /     /  \ | (     
 | | |___|     ___) |     / |  (     / /\ \ \   \  
 | |_____|  | |     |  |\  \|   \   / /__\ \ ___) |
  \_______|  |_|     |__| \__|____| /_________(____/
`)

// App 应用程序结构体
type App struct {
	config *config.Config
	server *http.Server
	idApp  *wire.App
}

// CreateGinApp 创建并启动应用程序
func CreateGinApp() error {
	// 初始化应用
	app, err := initApp()
	if err != nil {
		log.Fatalf("初始化应用失败: %v", err)
		return err
	}

	// 启动服务器
	app.startServer()

	// 等待优雅关闭
	app.waitForShutdown()
	return nil
}

// initApp 初始化应用程序
func initApp() (*App, error) {
	// 加载配置文件
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		return nil, err
	}

	// 使用Wire初始化所有依赖
	diApp, err := wire.InitApp(cfg)
	if err != nil {
		return nil, err
	}

	// 设置 gin 模式
	gin.SetMode(cfg.Server.Mode)

	// 创建 HTTP 服务器
	engine := gin.New()

	engine.Static("static", "./static")

	// 安全中间件
	engine.Use(middleware.SecurityHeaders()) // 安全头

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(middleware.CorsMiddleware())
	engine.Use(middleware.LimitMiddleware(cfg))
	//engine.Use(middleware.ErrorHandler())

	// 使用新的AdminRouter构造函数
	adminRouter := handler.NewAdminHandler(engine, diApp.Logger, cfg, diApp.Casbin.GetEnforcer(), diApp.AdminHandler, diApp.PermissionRepo, diApp.MenuRepo, diApp.MenuPermissionRepo, diApp.OperateLogRepo)
	adminRouter.Register()

	// 使用新的WebRouter构造函数
	webRouter := handler.NewWebHandler(engine, diApp.Logger, cfg, diApp.WebHandler, diApp.OperateLogRepo)
	webRouter.Register()

	// 启动定时任务
	crontab.Start()

	srv := &http.Server{
		Addr:         cfg.Server.Port,
		Handler:      engine,
		ReadTimeout:  15 * time.Second, // 添加读取超时
		WriteTimeout: 15 * time.Second, // 添加写入超时
		IdleTimeout:  60 * time.Second, // 添加空闲超时
	}

	return &App{
		config: cfg,
		server: srv,
		idApp:  diApp,
	}, nil
}

// startServer 启动 HTTP 服务器
func (a *App) startServer() {
	go func() {
		fmt.Println(utils.Red(string(LogoContent)))
		a.idApp.Logger.Info("服务器正在启动", zap.String("port", a.config.Server.Port))
		if err := a.server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			a.idApp.Logger.Error("服务器启动失败", zap.Error(err))
		}
	}()
}

// waitForShutdown 等待服务器优雅关闭
func (a *App) waitForShutdown() {
	// 确保日志同步
	defer func() {
		_ = a.idApp.Logger.Sync()
		a.idApp.Redis.Close()
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	signalQuit := <-quit
	a.idApp.Logger.Info("接收到关闭信号", zap.String("signal", signalQuit.String()))

	// 设置关闭超时
	shutdownTimeout := 10 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
	defer cancel()

	// 关闭 HTTP 服务器
	a.idApp.Logger.Info("正在关闭服务器", zap.Duration("timeout", shutdownTimeout))
	if err := a.server.Shutdown(ctx); err != nil {
		a.idApp.Logger.Error("服务器关闭失败", zap.Error(err))
	}

	a.idApp.Logger.Info("服务器已成功关闭", zap.String("port", a.config.Server.Port))
}
