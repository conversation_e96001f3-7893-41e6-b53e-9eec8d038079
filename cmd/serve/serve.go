package serve

import (
	"github.com/spf13/cobra"
)

// CmdServe 启动服务器命令
var CmdServe = &cobra.Command{
	Use:   "serve",
	Short: "启动HTTP服务器",
	Long: `启动Gin RBAC HTTP服务器。

该命令将启动Web服务器并监听指定端口，提供REST API服务。
服务器支持优雅关闭，可以通过Ctrl+C或发送SIGTERM信号停止。

示例:
  gin-rbac serve                    # 使用默认配置启动
  gin-rbac serve --config config.yaml # 指定配置文件`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return CreateGinApp()
	},
}
