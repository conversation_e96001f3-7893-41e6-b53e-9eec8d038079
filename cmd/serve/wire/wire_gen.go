// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"gin/internal/handler"
	"gin/internal/handler/admin"
	"gin/internal/handler/web"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/captcha"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/database"
	"gin/internal/infrastructure/logger"
	"gin/internal/repository/repo"
	"gin/internal/service"
	"github.com/google/wire"
	"github.com/redis/rueidis"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// 初始化App所需的所有依赖
func InitApp(cfg *config.Config) (*App, error) {
	databaseConfig := ProvideDbConfig(cfg)
	db, err := database.NewDatabase(databaseConfig)
	if err != nil {
		return nil, err
	}
	iEnforcer, err := casbin.NewCasbin(db)
	if err != nil {
		return nil, err
	}
	redisConfig := ProvideRedisConfig(cfg)
	redisClient, err := cache.NewRedisClient(redisConfig)
	if err != nil {
		return nil, err
	}
	loggerConfig := ProvideLoggerConfig(cfg)
	loggerLogger, err := logger.NewLogger(loggerConfig)
	if err != nil {
		return nil, err
	}
	managerRepository := repo.NewManagerRepository(db)
	loginLogRepository := repo.NewLoginLogRepository(db)
	iLoginLogService := service.NewLoginLogService(loggerLogger, loginLogRepository)
	iManagerService := service.NewManagerService(loggerLogger, managerRepository, iLoginLogService, iEnforcer)
	managerHandler := admin.NewManagerHandler(redisClient, iManagerService)
	managerNoticeRepository := repo.NewManagerNoticeRepository(db)
	iManagerNoticeService := service.NewManagerNoticeService(managerNoticeRepository)
	managerNoticeHandler := admin.NewManagerNoticeHandler(iManagerNoticeService)
	menuRepository := repo.NewMenuRepository(db)
	menuPermissionRepository := repo.NewMenuPermissionRepository(db)
	permissionRepository := repo.NewPermissionRepository(db)
	iMenuService := service.NewMenuService(loggerLogger, menuRepository, menuPermissionRepository, permissionRepository, iEnforcer)
	menuHandler := admin.NewMenuHandler(loggerLogger, iMenuService, iManagerService)
	frontMenuRepository := repo.NewFrontMenuRepository(db)
	translationRepository := repo.NewTranslationRepository(db)
	iTranslationService := service.NewTranslationService(translationRepository, redisClient)
	iFrontMenuService := service.NewFrontMenuService(frontMenuRepository, iTranslationService)
	frontMenuHandler := admin.NewFrontMenuHandler(iFrontMenuService)
	iPermissionService := service.NewPermissionService(loggerLogger, permissionRepository, iEnforcer)
	permissionHandler := admin.NewPermissionHandler(loggerLogger, redisClient, iPermissionService)
	roleRepository := repo.NewRoleRepository(db)
	iRoleService := service.NewRoleService(loggerLogger, roleRepository, iPermissionService)
	roleHandler := admin.NewRoleHandler(iRoleService)
	levelRepository := repo.NewLevelRepository(db)
	iLevelService := service.NewLevelService(levelRepository, iTranslationService)
	levelHandler := admin.NewLevelHandler(iLevelService)
	monitorHandler := admin.NewMonitorHandler()
	loginLogHandler := admin.NewLoginLogHandler(iLoginLogService)
	operateLogRepository := repo.NewOperateLogRepository(db)
	iOperateLogService := service.NewOperateLogService(loggerLogger, operateLogRepository)
	operateLogHandler := admin.NewOperateLogHandler(iOperateLogService)
	countryRepository := repo.NewCountryRepository(db)
	iCountryService := service.NewCountryService(countryRepository)
	countryHandler := admin.NewCountryHandler(iCountryService)
	languageRepository := repo.NewLanguageRepository(db)
	iLanguageService := service.NewLanguageService(languageRepository)
	langHandler := admin.NewLangHandler(iLanguageService)
	translationHandler := admin.NewTranslationHandler(iTranslationService)
	uploadHandler := admin.NewUploadHandler()
	articleRepository := repo.NewArticleRepository(db)
	iArticleService := service.NewArticleService(articleRepository, languageRepository, iTranslationService)
	articleHandler := admin.NewArticleHandler(iArticleService)
	userRepository := repo.NewUserRepository(db)
	userAssetRepository := repo.NewUserAssetRepository(db)
	assetRepository := repo.NewAssetRepository(db)
	iUserService := service.NewUserService(userRepository, translationRepository, userAssetRepository, assetRepository)
	userHandler := admin.NewUserHandler(iUserService)
	userCertificationRepository := repo.NewUserCertificationRepository(db)
	iUserCertificationService := service.NewUserCertificationService(loggerLogger, userCertificationRepository, managerRepository, userRepository)
	userCertificationHandler := admin.NewUserCertificationHandler(iUserCertificationService)
	userAccountRepository := repo.NewUserAccountRepository(db)
	paymentRepository := repo.NewPaymentRepository(db)
	settingRepository := repo.NewSettingRepository(db)
	iUserAccountService := service.NewUserAccountService(loggerLogger, userRepository, assetRepository, userAccountRepository, paymentRepository, managerRepository, settingRepository, translationRepository)
	userAccountHandler := admin.NewUserAccountHandler(iUserAccountService)
	userLevelRepository := repo.NewUserLevelRepository(db)
	iUserLevelService := service.NewUserLevelService(userLevelRepository, levelRepository)
	userLevelHandler := admin.NewUserLevelHandler(iUserLevelService)
	userNoticeRepository := repo.NewUserNoticeRepository(db)
	iUserNoticeService := service.NewUserNoticeService(userNoticeRepository)
	userNoticeHandler := admin.NewUserNoticeHandler(iUserNoticeService)
	userBillRepository := repo.NewUserBillRepository(db)
	iUserBillService := service.NewUserBillService(loggerLogger, userBillRepository, managerRepository, translationRepository)
	iUserAssetService := service.NewUserAssetService(loggerLogger, userAssetRepository, userRepository, iUserBillService, managerRepository)
	userAssetHandler := admin.NewUserAssetHandler(iUserAssetService)
	userWalletRepository := repo.NewUserWalletRepository(db)
	iUserWalletService := service.NewUserWalletService(loggerLogger, userWalletRepository, assetRepository, managerRepository, userRepository, userAssetRepository, userAccountRepository, paymentRepository, translationRepository, settingRepository)
	userWalletHandler := admin.NewUserWalletHandler(iUserWalletService)
	userSwapRepository := repo.NewUserSwapRepository(db)
	iUserSwapService := service.NewUserSwapService(loggerLogger, userSwapRepository, managerRepository, userRepository, assetRepository, translationRepository, settingRepository)
	userSwapHandler := admin.NewUserSwapHandler(iUserSwapService)
	userTransferRepository := repo.NewUserTransferRepository(db)
	iUserTransferService := service.NewUserTransferService(loggerLogger, userTransferRepository, managerRepository, userRepository, translationRepository, settingRepository)
	userTransferHandler := admin.NewUserTransferHandler(iUserTransferService)
	userBillHandler := admin.NewUserBillHandler(iUserBillService)
	indexHandler := admin.NewIndexHandler()
	iAssetService := service.NewAssetService(assetRepository)
	assetHandler := admin.NewAssetHandler(iAssetService)
	iPaymentService := service.NewPaymentService(paymentRepository, translationRepository)
	paymentHandler := admin.NewPaymentHandler(iPaymentService)
	categoryRepository := repo.NewCategoryRepository(db)
	iCategoryService := service.NewCategoryService(categoryRepository)
	categoryHandler := admin.NewCategoryHandler(iCategoryService)
	productRepository := repo.NewProductRepository(db)
	iProductService := service.NewProductService(productRepository)
	productHandler := admin.NewProductHandler(iProductService)
	orderRepository := repo.NewOrderRepository(db)
	iOrderService := service.NewOrderService(orderRepository)
	orderHandler := admin.NewOrderHandler(iOrderService)
	iSettingService := service.NewSettingService(loggerLogger, settingRepository, translationRepository)
	settingHandler := admin.NewSettingHandler(iSettingService)
	adminHandler := &handler.AdminHandler{
		ManagerHandler:           managerHandler,
		ManagerNoticeHandler:     managerNoticeHandler,
		MenuHandler:              menuHandler,
		FrontMenuHandler:         frontMenuHandler,
		PermissionHandler:        permissionHandler,
		RoleHandler:              roleHandler,
		LevelHandler:             levelHandler,
		MonitorHandler:           monitorHandler,
		LoginLogHandler:          loginLogHandler,
		OperateLogHandler:        operateLogHandler,
		CountryHandler:           countryHandler,
		LangHandler:              langHandler,
		TranslationHandler:       translationHandler,
		UploadHandler:            uploadHandler,
		ArticleHandler:           articleHandler,
		UserHandler:              userHandler,
		UserCertificationHandler: userCertificationHandler,
		UserAccountHandler:       userAccountHandler,
		UserLevelHandler:         userLevelHandler,
		UserNoticeHandler:        userNoticeHandler,
		UserAssetHandler:         userAssetHandler,
		UserWalletHandler:        userWalletHandler,
		UserSwapHandler:          userSwapHandler,
		UserTransferHandler:      userTransferHandler,
		UserBillHandler:          userBillHandler,
		IndexHandler:             indexHandler,
		AssetHandler:             assetHandler,
		PaymentHandler:           paymentHandler,
		CategoryHandler:          categoryHandler,
		ProductHandler:           productHandler,
		OrderHandler:             orderHandler,
		SettingHandler:           settingHandler,
	}
	client := ProvideRueidisClient(redisClient)
	iCaptcha := captcha.NewCaptcha(cfg, client)
	webIndexHandler := web.NewIndexHandler(iSettingService, iCountryService, iLanguageService, iTranslationService, iFrontMenuService, iPaymentService, iArticleService, iManagerService, iCaptcha)
	webArticleHandler := web.NewArticleHandler(iArticleService)
	webUserHandler := web.NewUserHandler(iUserService)
	webUserLevelHandler := web.NewUserLevelHandler(iUserLevelService)
	webUserNoticeHandler := web.NewUserNoticeHandler(iUserNoticeService)
	webUserCertificationHandler := web.NewUserCertificationHandler(iUserCertificationService)
	webUserAccountHandler := web.NewUserAccountHandler(iUserAccountService)
	webUserWalletHandler := web.NewUserWalletHandler(iUserWalletService, iUserBillService)
	webUserSwapHandler := web.NewUserSwapHandler(iUserSwapService, iAssetService)
	webUserTransferHandler := web.NewUserTransferHandler(iUserTransferService, iAssetService)
	webHandler := &handler.WebHandler{
		IndexHandler:             webIndexHandler,
		ArticleHandler:           webArticleHandler,
		UserHandler:              webUserHandler,
		UserLevelHandler:         webUserLevelHandler,
		UserNoticeHandler:        webUserNoticeHandler,
		UserCertificationHandler: webUserCertificationHandler,
		UserAccountHandler:       webUserAccountHandler,
		UserWalletHandler:        webUserWalletHandler,
		UserSwapHandler:          webUserSwapHandler,
		UserTransferHandler:      webUserTransferHandler,
	}
	app := &App{
		DB:                 db,
		Casbin:             iEnforcer,
		Redis:              redisClient,
		AdminHandler:       adminHandler,
		WebHandler:         webHandler,
		PermissionRepo:     permissionRepository,
		MenuRepo:           menuRepository,
		MenuPermissionRepo: menuPermissionRepository,
		OperateLogRepo:     operateLogRepository,
		Logger:             loggerLogger,
	}
	return app, nil
}

// wire.go:

// ProvideConfig 数据库配置提供者
func ProvideDbConfig(cfg *config.Config) *config.DatabaseConfig {
	return &cfg.Database
}

// ProvideRedisConfig Redis 配置提供者
func ProvideRedisConfig(cfg *config.Config) *config.RedisConfig {
	return &cfg.Redis
}

// ProvideLoggerConfig 日志配置提供者
func ProvideLoggerConfig(cfg *config.Config) *config.LoggerConfig {
	return &cfg.Logger
}

// ProvideCaptchaConfig 验证码配置提供者
func ProvideCaptchaConfig(cfg *config.Config) *config.CaptchaConfig {
	return &cfg.Captcha
}

// ProvideRueidisClient 适配 *cache.RedisClient 到 rueidis.Client
func ProvideRueidisClient(rc *cache.RedisClient) rueidis.Client {
	if rc == nil {
		return nil
	}
	return rc.Client()
}

// 基础设施提供者集合
var infrastructureSet = wire.NewSet(
	ProvideDbConfig,
	ProvideRedisConfig,
	ProvideLoggerConfig,
	ProvideCaptchaConfig, database.NewDatabase, casbin.NewCasbin, cache.NewRedisClient, ProvideRueidisClient, logger.NewLogger, captcha.NewCaptcha,
)

// 仓库提供者集合
var repositorySet = wire.NewSet(repo.NewArticleRepository, repo.NewAssetRepository, repo.NewCategoryRepository, repo.NewCountryRepository, repo.NewFrontMenuRepository, repo.NewLanguageRepository, repo.NewLevelRepository, repo.NewLoginLogRepository, repo.NewManagerNoticeRepository, repo.NewManagerRepository, repo.NewMenuPermissionRepository, repo.NewMenuRepository, repo.NewOperateLogRepository, repo.NewOrderRepository, repo.NewPaymentRepository, repo.NewPermissionRepository, repo.NewProductRepository, repo.NewRoleRepository, repo.NewSettingRepository, repo.NewTranslationRepository, repo.NewUserAccountRepository, repo.NewUserAssetRepository, repo.NewUserBillRepository, repo.NewUserCertificationRepository, repo.NewUserLevelRepository, repo.NewUserNoticeRepository, repo.NewUserRepository, repo.NewUserSettingRepository, repo.NewUserSwapRepository, repo.NewUserTransferRepository, repo.NewUserWalletRepository)

// 服务提供者集合
var serviceSet = wire.NewSet(service.NewArticleService, service.NewAssetService, service.NewCountryService, service.NewFrontMenuService, service.NewLanguageService, service.NewLevelService, service.NewLoginLogService, service.NewManagerNoticeService, service.NewManagerService, service.NewMenuService, service.NewOperateLogService, service.NewPaymentService, service.NewPermissionService, service.NewRoleService, service.NewSettingService, service.NewTranslationService, service.NewUserAccountService, service.NewUserAssetService, service.NewUserBillService, service.NewUserCertificationService, service.NewUserLevelService, service.NewUserNoticeService, service.NewUserService, service.NewUserSettingService, service.NewUserSwapService, service.NewUserTransferService, service.NewUserWalletService, service.NewCategoryService, service.NewProductService, service.NewOrderService, service.NewWebSocketService)

// 处理器提供者集合
var handlerSet = wire.NewSet(admin.NewArticleHandler, admin.NewCountryHandler, admin.NewIndexHandler, admin.NewLangHandler, admin.NewLevelHandler, admin.NewLoginLogHandler, admin.NewManagerHandler, admin.NewManagerNoticeHandler, admin.NewMenuHandler, admin.NewMonitorHandler, admin.NewOperateLogHandler, admin.NewPermissionHandler, admin.NewRoleHandler, admin.NewTranslationHandler, admin.NewUploadHandler, admin.NewUserHandler, admin.NewUserLevelHandler, admin.NewUserNoticeHandler, admin.NewUserAssetHandler, admin.NewUserWalletHandler, admin.NewUserSwapHandler, admin.NewUserTransferHandler, admin.NewUserBillHandler, admin.NewSettingHandler, admin.NewFrontMenuHandler, admin.NewUserCertificationHandler, admin.NewUserAccountHandler, admin.NewAssetHandler, admin.NewPaymentHandler, admin.NewCategoryHandler, admin.NewProductHandler, admin.NewOrderHandler, web.NewArticleHandler, web.NewIndexHandler, web.NewUserAccountHandler, web.NewUserCertificationHandler, web.NewUserHandler, web.NewUserLevelHandler, web.NewUserNoticeHandler, web.NewUserSwapHandler, web.NewUserTransferHandler, web.NewUserWalletHandler, wire.Struct(new(handler.AdminHandler), "*"), wire.Struct(new(handler.WebHandler), "*"))

// App 应用程序结构体
type App struct {
	DB                 *gorm.DB
	Casbin             casbin.IEnforcer
	Redis              *cache.RedisClient
	AdminHandler       *handler.AdminHandler
	WebHandler         *handler.WebHandler
	PermissionRepo     repo.PermissionRepository
	MenuRepo           repo.MenuRepository
	MenuPermissionRepo repo.MenuPermissionRepository
	OperateLogRepo     repo.OperateLogRepository
	Logger             logger.Logger
}
