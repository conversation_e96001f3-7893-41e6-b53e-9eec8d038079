//go:build wireinject
// +build wireinject

package wire

import (
	"gin/internal/handler"
	"gin/internal/handler/admin"
	"gin/internal/handler/web"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/captcha"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/database"
	"gin/internal/infrastructure/logger"
	"gin/internal/repository/repo"
	"gin/internal/service"

	"github.com/google/wire"
	"github.com/redis/rueidis"
	"gorm.io/gorm"
)

// ProvideConfig 数据库配置提供者
func ProvideDbConfig(cfg *config.Config) *config.DatabaseConfig {
	return &cfg.Database
}

// ProvideRedisConfig Redis 配置提供者
func ProvideRedisConfig(cfg *config.Config) *config.RedisConfig {
	return &cfg.Redis
}

// ProvideLoggerConfig 日志配置提供者
func ProvideLoggerConfig(cfg *config.Config) *config.LoggerConfig {
	return &cfg.Logger
}

// ProvideCaptchaConfig 验证码配置提供者
func ProvideCaptchaConfig(cfg *config.Config) *config.CaptchaConfig {
	return &cfg.Captcha
}

// ProvideRueidisClient 适配 *cache.RedisClient 到 rueidis.Client
func ProvideRueidisClient(rc *cache.RedisClient) rueidis.Client {
	if rc == nil {
		return nil
	}
	return rc.Client()
}

// 基础设施提供者集合
var infrastructureSet = wire.NewSet(
	ProvideDbConfig,
	ProvideRedisConfig,
	ProvideLoggerConfig,
	ProvideCaptchaConfig,
	database.NewDatabase,
	casbin.NewCasbin,
	cache.NewRedisClient,
	ProvideRueidisClient,
	logger.NewLogger,
	captcha.NewCaptcha,
)

// 仓库提供者集合
var repositorySet = wire.NewSet(
	repo.NewArticleRepository,
	repo.NewAssetRepository,
	repo.NewCategoryRepository,
	repo.NewCountryRepository,
	repo.NewFrontMenuRepository,
	repo.NewLanguageRepository,
	repo.NewLevelRepository,
	repo.NewLoginLogRepository,
	repo.NewManagerNoticeRepository,
	repo.NewManagerRepository,
	repo.NewMenuPermissionRepository,
	repo.NewMenuRepository,
	repo.NewOperateLogRepository,
	repo.NewOrderRepository,
	repo.NewPaymentRepository,
	repo.NewPermissionRepository,
	repo.NewProductRepository,
	repo.NewRoleRepository,
	repo.NewSettingRepository,
	repo.NewTranslationRepository,
	repo.NewUserAccountRepository,
	repo.NewUserAssetRepository,
	repo.NewUserBillRepository,
	repo.NewUserCertificationRepository,
	repo.NewUserLevelRepository,
	repo.NewUserNoticeRepository,
	repo.NewUserRepository,
	repo.NewUserSettingRepository,
	repo.NewUserSwapRepository,
	repo.NewUserTransferRepository,
	repo.NewUserWalletRepository,
)

// 服务提供者集合
var serviceSet = wire.NewSet(
	service.NewArticleService,
	service.NewAssetService,
	service.NewCountryService,
	service.NewFrontMenuService,
	service.NewLanguageService,
	service.NewLevelService,
	service.NewLoginLogService,
	service.NewManagerNoticeService,
	service.NewManagerService,
	service.NewMenuService,
	service.NewOperateLogService,
	service.NewPaymentService,
	service.NewPermissionService,
	service.NewRoleService,
	service.NewSettingService,
	service.NewTranslationService,
	service.NewUserAccountService,
	service.NewUserAssetService,
	service.NewUserBillService,
	service.NewUserCertificationService,
	service.NewUserLevelService,
	service.NewUserNoticeService,
	service.NewUserService,
	service.NewUserSettingService,
	service.NewUserSwapService,
	service.NewUserTransferService,
	service.NewUserWalletService,
	service.NewCategoryService,
	service.NewProductService,
	service.NewOrderService,
	service.NewWebSocketService,
)

// 处理器提供者集合
var handlerSet = wire.NewSet(
	admin.NewArticleHandler,
	admin.NewCountryHandler,
	admin.NewIndexHandler,
	admin.NewLangHandler,
	admin.NewLevelHandler,
	admin.NewLoginLogHandler,
	admin.NewManagerHandler,
	admin.NewManagerNoticeHandler,
	admin.NewMenuHandler,
	admin.NewMonitorHandler,
	admin.NewOperateLogHandler,
	admin.NewPermissionHandler,
	admin.NewRoleHandler,
	admin.NewTranslationHandler,
	admin.NewUploadHandler,
	admin.NewUserHandler,
	admin.NewUserLevelHandler,
	admin.NewUserNoticeHandler,
	admin.NewUserAssetHandler,
	admin.NewUserWalletHandler,
	admin.NewUserSwapHandler,
	admin.NewUserTransferHandler,
	admin.NewUserBillHandler,
	admin.NewSettingHandler,
	admin.NewFrontMenuHandler,
	admin.NewUserCertificationHandler,
	admin.NewUserAccountHandler,
	admin.NewAssetHandler,
	admin.NewPaymentHandler,
	admin.NewCategoryHandler,
	admin.NewProductHandler,
	admin.NewOrderHandler,
	web.NewArticleHandler,
	web.NewIndexHandler,
	web.NewUserAccountHandler,
	web.NewUserCertificationHandler,
	web.NewUserHandler,
	web.NewUserLevelHandler,
	web.NewUserNoticeHandler,
	web.NewUserSwapHandler,
	web.NewUserTransferHandler,
	web.NewUserWalletHandler,
	wire.Struct(new(handler.AdminHandler), "*"),
	wire.Struct(new(handler.WebHandler), "*"),
)

// 初始化App所需的所有依赖
func InitApp(cfg *config.Config) (*App, error) {
	panic(wire.Build(
		infrastructureSet,
		repositorySet,
		serviceSet,
		handlerSet,
		wire.Struct(new(App), "*"),
	))
}

// App 应用程序结构体
type App struct {
	DB                 *gorm.DB
	Casbin             casbin.IEnforcer
	Redis              *cache.RedisClient
	AdminHandler       *handler.AdminHandler
	WebHandler         *handler.WebHandler
	PermissionRepo     repo.PermissionRepository
	MenuRepo           repo.MenuRepository
	MenuPermissionRepo repo.MenuPermissionRepository
	OperateLogRepo     repo.OperateLogRepository
	Logger             logger.Logger
}
