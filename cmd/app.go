package cmd

import (
	"context"
	"errors"
	"gin/internal/api/admin"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/database"
	"gin/internal/infrastructure/initialize"
	"gin/internal/infrastructure/logger"
	"gin/internal/middleware"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// App 应用程序结构体
type App struct {
	config *config.Config
	server *http.Server
}

// CreateGinApp 创建并启动应用程序
func CreateGinApp() {
	// 初始化应用
	app, err := initApp()
	if err != nil {
		log.Fatalf("初始化应用失败: %v", err)
	}

	// 启动服务器
	app.startServer()

	// 等待优雅关闭
	app.waitForShutdown()
}

// initApp 初始化应用程序
func initApp() (*App, error) {
	// 加载配置文件
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		return nil, err
	}

	// 初始化日志
	if err = logger.InitLogger(&cfg.Logger); err != nil {
		return nil, err
	}

	// 设置 gin 模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化基础设施
	if err = initInfrastructure(cfg); err != nil {
		return nil, err
	}

	// 创建 HTTP 服务器
	engine := setupGinEngine(cfg)
	srv := &http.Server{
		Addr:         cfg.Server.Port,
		Handler:      engine,
		ReadTimeout:  15 * time.Second, // 添加读取超时
		WriteTimeout: 15 * time.Second, // 添加写入超时
		IdleTimeout:  60 * time.Second, // 添加空闲超时
	}

	return &App{
		config: cfg,
		server: srv,
	}, nil
}

// initInfrastructure 初始化基础设施
func initInfrastructure(cfg *config.Config) error {
	// 初始化数据库连接
	db, err := database.NewDatabase(&cfg.Database)
	if err != nil {
		logger.Error("数据库连接失败", zap.Error(err))
		return err
	}

	// 初始化 Redis 连接
	if err = cache.InitRedisClient(&cfg.Redis); err != nil {
		logger.Error("Redis 初始化失败", zap.Error(err))
		return err
	}

	// 检查 Redis 健康状态
	if !cache.CheckHealth(context.Background()) {
		logger.Error("Redis 连接不可用")
		return errors.New("redis 连接不可用")
	}

	// 初始化 Casbin
	if err = casbin.InitCasbin(db); err != nil {
		logger.Error("初始化 Casbin 失败", zap.Error(err))
		return err
	}

	// 初始化测试数据
	initializer := initialize.NewDataInitializer(db, casbin.GetEnforcer())
	if err = initializer.InitAll(); err != nil {
		logger.Error("初始化测试数据失败", zap.Error(err))
		return err
	}

	// 记录权限信息
	perms := casbin.GetEnforcer().GetPermissionsForUserInDomain("guest", "*")
	logger.Info("Guest 用户权限", zap.Any("permissions", perms))

	return nil
}

// setupGinEngine 设置 Gin 引擎和中间件
func setupGinEngine(cfg *config.Config) *gin.Engine {
	// 创建 Gin 引擎
	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(middleware.CorsMiddleware())
	engine.Use(middleware.LimitMiddleware(cfg))
	engine.Use(middleware.ErrorHandler())

	// 注册路由
	admin.RegisterAdminRoutes(engine)

	return engine
}

// startServer 启动 HTTP 服务器
func (a *App) startServer() {
	go func() {
		logger.Info("服务器正在启动", zap.String("port", a.config.Server.Port))
		if err := a.server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Error("服务器启动失败", zap.Error(err))
		}
	}()
}

// waitForShutdown 等待服务器优雅关闭
func (a *App) waitForShutdown() {
	// 确保日志同步
	defer func() {
		_ = logger.Log.Sync()
		cache.Close() // 关闭 Redis 连接
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	signal := <-quit
	logger.Info("接收到关闭信号", zap.String("signal", signal.String()))

	// 设置关闭超时
	shutdownTimeout := 10 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
	defer cancel()

	// 关闭 HTTP 服务器
	logger.Info("正在关闭服务器", zap.Duration("timeout", shutdownTimeout))
	if err := a.server.Shutdown(ctx); err != nil {
		logger.Error("服务器关闭失败", zap.Error(err))
	}

	logger.Info("服务器已成功关闭", zap.String("port", a.config.Server.Port))
}
