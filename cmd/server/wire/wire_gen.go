// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"gin/internal/handler"
	"gin/internal/handler/admin"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/database"
	"gin/internal/repository"
	"gin/internal/service"
	"github.com/google/wire"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// 初始化App所需的所有依赖
func InitApp(cfg *config.Config) (*App, error) {
	databaseConfig := ProvideDbConfig(cfg)
	db, err := database.NewDatabase(databaseConfig)
	if err != nil {
		return nil, err
	}
	iEnforcer, err := casbin.NewCasbin(db)
	if err != nil {
		return nil, err
	}
	redisConfig := ProvideRedisConfig(cfg)
	redisClient, err := cache.NewRedisClient(redisConfig)
	if err != nil {
		return nil, err
	}
	indexHandler := admin.NewIndexHandler()
	managerRepo := repository.NewManagerRepository(db)
	loginLogRepo := repository.NewLoginLogRepository(db)
	iLoginLogService := service.NewLoginLogService(loginLogRepo)
	iManagerService := service.NewManagerService(managerRepo, iLoginLogService)
	managerHandler := admin.NewManagerHandler(redisClient, iManagerService)
	permissionRepo := repository.NewPermissionRepository(db)
	iPermissionService := service.NewPermissionService(permissionRepo)
	tenantRepo := repository.NewTenantRepository(db)
	iTenantService := service.NewTenantService(tenantRepo)
	permissionHandler := admin.NewPermissionHandler(redisClient, iEnforcer, iPermissionService, iManagerService, iTenantService)
	adminHandler := &handler.AdminHandler{
		IndexHandler:      indexHandler,
		ManagerHandler:    managerHandler,
		PermissionHandler: permissionHandler,
	}
	app := &App{
		DB:      db,
		Casbin:  iEnforcer,
		Redis:   redisClient,
		Handler: adminHandler,
	}
	return app, nil
}

// wire.go:

// ProvideConfig 数据库配置提供者
func ProvideDbConfig(cfg *config.Config) *config.DatabaseConfig {
	return &cfg.Database
}

// ProvideRedisConfig Redis 配置提供者
func ProvideRedisConfig(cfg *config.Config) *config.RedisConfig {
	return &cfg.Redis
}

// 基础设施提供者集合
var infrastructureSet = wire.NewSet(
	ProvideDbConfig,
	ProvideRedisConfig, database.NewDatabase, casbin.NewCasbin, cache.NewRedisClient,
)

// 仓库提供者集合
var repositorySet = wire.NewSet(repository.NewRoleRepository, repository.NewManagerRepository, repository.NewLoginLogRepository, repository.NewTenantRepository, repository.NewPermissionRepository, repository.NewPositionRepository)

// 服务提供者集合
var serviceSet = wire.NewSet(service.NewRoleService, service.NewManagerService, service.NewLoginLogService, service.NewPermissionService, service.NewTenantService, service.NewPositionService)

// 处理器提供者集合
var handlerSet = wire.NewSet(admin.NewIndexHandler, admin.NewManagerHandler, admin.NewRoleHandler, admin.NewPermissionHandler, wire.Struct(new(handler.AdminHandler), "*"))

// App 应用程序结构体
type App struct {
	DB      *gorm.DB
	Casbin  casbin.IEnforcer // 修改为接口类型
	Redis   *cache.RedisClient
	Handler *handler.AdminHandler
}
