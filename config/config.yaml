server:
  port: ":8080"
  mode: "debug"
  log_level: "info"

logger:
  level: "info"           # 日志级别：debug, info, warn, error
  format: "console"       # 日志格式：json, console
  filename: "logs/app.log" # 日志文件路径
  max_size: 100          # 每个日志文件的最大大小(MB)
  max_age: 30            # 日志文件保留天数
  max_backups: 10        # 保留的旧日志文件最大数量
  compress: true         # 是否压缩旧日志文件
  console: true          # 是否同时输出到控制台
  show_line: true        # 是否显示调用行号

database:
  host: "localhost"
  port: "3306"
  user: "root"
  password: "12!@qwAS"
  dbname: "lfgin"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600  # 连接最大生命周期(秒)
  conn_max_idle_time: 1800 # 空闲连接最大生命周期(秒)
  log_level: "info"       # 日志级别：silent, error, warn, info
  slow_threshold: 200     # 慢查询阈值(毫秒)

jwt:
  secret: "your-secret-key"
  expire_time: 24

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 15
  pool_size: 100
  min_idle_conns: 10

middleware:
  limiter:
    max: 50          # 速率限制最大数 1秒限制
    expiration: 10   # 速率限制过期 秒数