server:
  port: ":8000"
  mode: "debug"
  log_level: "info"

# 日志配置
logger:
  level: "info"           # 日志级别：debug, info, warn, error
  format: "text"       # 日志格式：json, text
  output: "both"          # 输出目标：stdout(控制台), file(文件), both(控制台和文件)
  add_source: true        # 是否添加源码信息
  time_format: "2006/01/02 - 15:04:05.000"  # 时间格式
  caller_skip: 1          # 调用者跳过的帧数
  development: false      # 是否为开发模式
  max_buffer: 1024       # 最大缓冲区大小
  file:
    filename: "logs/app.log" # 日志文件路径
    max_size: 100          # 每个日志文件的最大大小(MB)
    max_age: 30            # 日志文件保留天数
    max_backups: 10        # 保留的旧日志文件最大数量
    compress: true         # 是否压缩旧日志文件
    show_line: true        # 是否显示调用行号
    local_time: true       # 是否使用本地时间

# 数据库配置
database:
  host: "localhost"
  port: "3306"
  user: "root"
  password: "12!@qwAS"
  dbname: "lfgin"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600  # 连接最大生命周期(秒)
  conn_max_idle_time: 1800 # 空闲连接最大生命周期(秒)
  log_level: "silent"      # 日志级别：silent, error, warn, info
  slow_threshold: 200     # 慢查询阈值(毫秒)

jwt:
  secret: "your-secret-key"
  expire_time: 24

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 15
  pool_size: 100
  min_idle_conns: 10

middleware:
  limiter:
    max: 50          # 速率限制最大数 1秒限制
    expiration: 10   # 速率限制过期 秒数

# 验证码配置
captcha:
  type: "digit"            # 验证码类型：digit(数字), string(字符串), chinese(中文), math(数学)
  height: 80               # 图片高度
  width: 240               # 图片宽度
  length: 5                # 验证码长度
  noise_count: 1          # 干扰线数量 (字符串、中文、数学验证码)
  source: "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"  # 字符源 (字符串验证码)
  bg_color: "255,255,255" # 背景颜色 (RGB格式)
  store_type: "memory"    # 存储类型：memory(内存), redis(Redis)
  expiration: 300         # 验证码过期时间(秒)