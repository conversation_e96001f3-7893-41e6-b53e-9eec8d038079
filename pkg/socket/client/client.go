package client

import (
	"context"
	"errors"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// SocketClient WebSocket客户端
type SocketClient struct {
	addr              atomic.Pointer[string]                   // 连接地址
	heartbeatTime     atomic.Int32                             // 心跳时间（秒）
	heartbeatEnabled  atomic.Bool                              // 是否启用心跳
	reconnectTime     atomic.Int32                             // 重连时间（秒）
	mu                sync.RWMutex                             // 锁
	messageHandler    func(msg []byte) error                   // 消息处理函数
	beforeConnectFunc func(client SocketClientInterface) error // 连接前处理函数
	afterConnectFunc  func(client SocketClientInterface) error // 连接后处理函数
	connMu            sync.Mutex                               // 连接操作锁
	conn              *websocket.Conn                          // WebSocket连接
	isConnected       atomic.Bool                              // 连接状态
	ctx               context.Context                          // 上下文
	cancel            context.CancelFunc                       // 取消函数
	heartbeatTicker   atomic.Pointer[time.Ticker]              // 心跳定时器
	sendChan          chan []byte                              // 发送消息队列
	sendBinary<PERSON>han    chan []byte                              // 发送二进制消息队列
	reconnecting      atomic.Bool                              // 重连状态标志
}

// NewSocketClient 创建新的连接对象
func NewSocketClient(addr string) SocketClientInterface {
	ctx, cancel := context.WithCancel(context.Background())

	client := &SocketClient{
		ctx:            ctx,
		cancel:         cancel,
		sendChan:       make(chan []byte, sendChanSize),
		sendBinaryChan: make(chan []byte, sendChanSize),
		messageHandler: func(msg []byte) error {
			return nil
		},
	}

	// 设置默认值
	client.addr.Store(&addr)
	client.heartbeatTime.Store(30)
	client.heartbeatEnabled.Store(true) // 默认启用心跳
	client.reconnectTime.Store(10)

	return client
}

// SetAddr 设置连接地址
func (c *SocketClient) SetAddr(addr string) SocketClientInterface {
	c.addr.Store(&addr)
	return c
}

// SetHeartbeatTime 设置心跳时间
func (c *SocketClient) SetHeartbeatTime(second int) SocketClientInterface {
	c.heartbeatTime.Store(int32(second))
	return c
}

// SetHeartbeatEnabled 设置是否启用心跳
func (c *SocketClient) SetHeartbeatEnabled(enabled bool) SocketClientInterface {
	c.heartbeatEnabled.Store(enabled)
	// 如果当前连接中且禁用心跳，停止当前的心跳
	if !enabled && c.isConnected.Load() {
		if ticker := c.heartbeatTicker.Load(); ticker != nil {
			ticker.Stop()
			c.heartbeatTicker.Store(nil)
		}
	}
	// 如果启用心跳且当前已连接，启动心跳
	if enabled && c.isConnected.Load() {
		go c.startHeartbeat()
	}
	return c
}

// SetReconnectTime 设置重连时间
func (c *SocketClient) SetReconnectTime(second int) SocketClientInterface {
	c.reconnectTime.Store(int32(second))
	return c
}

// SetWebSocketMessageFunc 设置消息处理函数
func (c *SocketClient) SetWebSocketMessageFunc(fun func(msg []byte) error) SocketClientInterface {
	c.mu.Lock()
	c.messageHandler = fun
	c.mu.Unlock()
	return c
}

// SetBeforeConnectFunc 设置连接前处理函数
func (c *SocketClient) SetBeforeConnectFunc(fun func(client SocketClientInterface) error) SocketClientInterface {
	c.mu.Lock()
	c.beforeConnectFunc = fun
	c.mu.Unlock()
	return c
}

// SetAfterConnectFunc 设置连接后处理函数
func (c *SocketClient) SetAfterConnectFunc(fun func(client SocketClientInterface) error) SocketClientInterface {
	c.mu.Lock()
	c.afterConnectFunc = fun
	c.mu.Unlock()
	return c
}

// Connect 连接WebSocket
func (c *SocketClient) Connect() SocketClientInterface {
	c.connMu.Lock()
	defer c.connMu.Unlock()

	if c.isConnected.Load() {
		return c
	}

	// 执行连接前处理
	c.mu.RLock()
	beforeConnectFunc := c.beforeConnectFunc
	c.mu.RUnlock()

	if beforeConnectFunc != nil {
		if err := beforeConnectFunc(c); err != nil {
			return c
		}
	}

	// 建立WebSocket连接
	dialer := &websocket.Dialer{
		HandshakeTimeout:  handshakeTimeout * time.Second,
		EnableCompression: true, // 启用压缩
	}

	addr := c.addr.Load()
	conn, _, err := dialer.Dial(*addr, nil)
	if err != nil {
		// 启动重连
		go c.reconnect()
		return c
	}

	// 配置连接参数
	conn.SetReadLimit(maxMessageSize)
	_ = conn.SetReadDeadline(time.Now().Add(time.Duration(readTimeout) * time.Second))
	conn.SetPongHandler(func(string) error {
		_ = conn.SetReadDeadline(time.Now().Add(time.Duration(readTimeout) * time.Second))
		return nil
	})

	c.conn = conn
	c.isConnected.Store(true)

	// 启动各种处理协程
	go c.handleMessages()
	go c.handleSendQueue()
	go c.startHeartbeat()

	// 执行连接后处理
	c.mu.RLock()
	afterConnectFunc := c.afterConnectFunc
	c.mu.RUnlock()

	if afterConnectFunc != nil {
		if err = afterConnectFunc(c); err != nil {
			// 连接后处理失败，关闭连接
			_ = c.Close()
		}
	}

	return c
}

// SendMessage 发送消息（非阻塞）
func (c *SocketClient) SendMessage(data []byte) error {
	if !c.isConnected.Load() {
		return errors.New("connection is not active")
	}

	// 复制数据避免竞态条件
	msg := make([]byte, len(data))
	copy(msg, data)

	select {
	case c.sendChan <- msg:
		return nil
	default:
		return errors.New("send queue is full")
	}
}

// SendPing 发送Ping消息
func (c *SocketClient) SendPing() error {
	c.connMu.Lock()
	conn := c.conn
	connected := c.isConnected.Load()
	c.connMu.Unlock()

	if !connected || conn == nil {
		return errors.New("connection is not active")
	}

	err := conn.SetWriteDeadline(time.Now().Add(time.Duration(writeTimeout) * time.Second))
	if err != nil {
		return err
	}
	return conn.WriteMessage(websocket.PingMessage, []byte{})
}

// SendBinary 发送二进制消息（非阻塞）
func (c *SocketClient) SendBinary(data []byte) error {
	if !c.isConnected.Load() {
		return errors.New("connection is not active")
	}

	// 复制数据避免竞态条件
	msg := make([]byte, len(data))
	copy(msg, data)

	select {
	case c.sendBinaryChan <- msg:
		return nil
	default:
		return errors.New("send binary queue is full")
	}
}

// Close 关闭连接
func (c *SocketClient) Close() error {
	c.cancel()

	c.connMu.Lock()
	defer c.connMu.Unlock()

	c.isConnected.Store(false)

	// 停止心跳
	if ticker := c.heartbeatTicker.Load(); ticker != nil {
		ticker.Stop()
		c.heartbeatTicker.Store(nil)
	}

	// 关闭连接
	if c.conn != nil {
		err := c.conn.Close()
		c.conn = nil
		return err
	}

	return nil
}

// IsConnected 检查连接状态
func (c *SocketClient) IsConnected() bool {
	return c.isConnected.Load()
}

// IsHeartbeatEnabled 检查心跳是否启用
func (c *SocketClient) IsHeartbeatEnabled() bool {
	return c.heartbeatEnabled.Load()
}

// handleMessages 处理接收消息
func (c *SocketClient) handleMessages() {
	defer c.reconnect()

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			if !c.isConnected.Load() {
				return
			}

			c.connMu.Lock()
			conn := c.conn
			c.connMu.Unlock()

			if conn == nil {
				return
			}

			_, msg, err := conn.ReadMessage()
			if err != nil {
				return
			}

			// 处理接收到的消息
			c.mu.RLock()
			handler := c.messageHandler
			c.mu.RUnlock()

			if handler != nil {
				// 在独立的goroutine中处理消息，避免阻塞读取
				go func(message []byte) {
					defer func() {
						if r := recover(); r != nil {
							// 处理panic，避免程序崩溃
						}
					}()
					_ = handler(message)
				}(msg)
			}
		}
	}
}

// handleSendQueue 处理发送消息队列
func (c *SocketClient) handleSendQueue() {
	defer c.reconnect()

	for {
		select {
		case <-c.ctx.Done():
			return
		case msg := <-c.sendChan:
			if !c.isConnected.Load() {
				continue
			}
			if err := c.writeMessage(websocket.TextMessage, msg); err != nil {
				return
			}
		case msg := <-c.sendBinaryChan:
			if !c.isConnected.Load() {
				continue
			}
			if err := c.writeMessage(websocket.BinaryMessage, msg); err != nil {
				return
			}
		}
	}
}

// writeMessage 内部写入消息方法
func (c *SocketClient) writeMessage(messageType int, data []byte) error {
	c.connMu.Lock()
	conn := c.conn
	c.connMu.Unlock()

	if conn == nil {
		return errors.New("connection is nil")
	}

	err := conn.SetWriteDeadline(time.Now().Add(time.Duration(writeTimeout) * time.Second))
	if err != nil {
		return err
	}
	return conn.WriteMessage(messageType, data)
}

// startHeartbeat 启动心跳
func (c *SocketClient) startHeartbeat() {
	// 检查是否启用心跳
	if !c.heartbeatEnabled.Load() {
		return
	}

	heartbeatTime := c.heartbeatTime.Load()
	if heartbeatTime <= 0 {
		return
	}

	// 防止重复启动心跳
	if c.heartbeatTicker.Load() != nil {
		return
	}

	ticker := time.NewTicker(time.Duration(heartbeatTime) * time.Second)
	c.heartbeatTicker.Store(ticker)

	defer func() {
		ticker.Stop()
		c.heartbeatTicker.Store(nil)
	}()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			// 检查连接状态和心跳是否仍然启用
			if !c.isConnected.Load() || !c.heartbeatEnabled.Load() {
				return
			}

			if err := c.SendPing(); err != nil {
				return
			}
		}
	}
}

// reconnect 重新连接
func (c *SocketClient) reconnect() {
	// 防止重复重连
	if !c.reconnecting.CompareAndSwap(false, true) {
		return
	}
	defer c.reconnecting.Store(false)

	c.connMu.Lock()
	c.isConnected.Store(false)
	if c.conn != nil {
		_ = c.conn.Close()
		c.conn = nil
	}
	// 停止心跳
	if ticker := c.heartbeatTicker.Load(); ticker != nil {
		ticker.Stop()
		c.heartbeatTicker.Store(nil)
	}
	c.connMu.Unlock()

	// 清空发送队列中的过期消息
	c.drainSendQueues()

	// 延时重连
	reconnectTime := c.reconnectTime.Load()
	timer := time.NewTimer(time.Duration(reconnectTime) * time.Second)
	defer timer.Stop()

	select {
	case <-c.ctx.Done():
		return
	case <-timer.C:
		c.Connect()
	}
}

// drainSendQueues 清空发送队列
func (c *SocketClient) drainSendQueues() {
	// 非阻塞清空队列，避免内存泄漏
	for {
		select {
		case <-c.sendChan:
		default:
			goto drainBinary
		}
	}

drainBinary:
	for {
		select {
		case <-c.sendBinaryChan:
		default:
			return
		}
	}
}
