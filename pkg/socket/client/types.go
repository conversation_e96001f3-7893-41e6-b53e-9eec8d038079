package client

const (
	// 性能相关常量
	sendChanSize     = 1000    // 发送队列大小
	writeTimeout     = 10      // 写入超时时间（秒）
	readTimeout      = 60      // 读取超时时间（秒）
	handshakeTimeout = 45      // 握手超时时间（秒）
	maxMessageSize   = 1 << 20 // 最大消息大小 1MB
)

// SocketClientInterface WebSocket客户端接口
type SocketClientInterface interface {
	// SetAddr 设置地址
	SetAddr(addr string) SocketClientInterface
	// SetHeartbeatTime 设置心跳时间
	SetHeartbeatTime(second int) SocketClientInterface
	// SetHeartbeatEnabled 设置是否启用心跳
	SetHeartbeatEnabled(enabled bool) SocketClientInterface
	// SetReconnectTime 设置重连时间
	SetReconnectTime(second int) SocketClientInterface
	// SetWebSocketMessageFunc 设置WebSocket消息处理函数
	SetWebSocketMessageFunc(fun func(msg []byte) error) SocketClientInterface
	// SetBeforeConnectFunc 设置连接前处理函数
	SetBeforeConnectFunc(fun func(client SocketClientInterface) error) SocketClientInterface
	// SetAfterConnectFunc 设置连接后处理函数
	SetAfterConnectFunc(fun func(client SocketClientInterface) error) SocketClientInterface
	// Connect 连接
	Connect() SocketClientInterface
	// Close 关闭
	Close() error
	// IsConnected 是否连接
	IsConnected() bool
	// IsHeartbeatEnabled 检查心跳是否启用
	IsHeartbeatEnabled() bool
	// SendMessage 发送消息
	SendMessage(data []byte) error
	// SendPing 发送心跳
	SendPing() error
	// SendBinary 发送二进制数据
	SendBinary(data []byte) error
}
