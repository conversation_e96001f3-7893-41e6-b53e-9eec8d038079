package handler

import (
	"context"
	"github.com/goccy/go-json"
	"sync"
	"sync/atomic"
	"time"
)

// Hub WebSocket连接管理中心
type Hub struct {
	// 连接管理 - 使用sync.Map提高并发性能
	connections sync.Map // 所有连接
	userConnMap sync.Map // ownerID -> []*Connection 映射

	// 消息通道
	broadcast chan []byte
	// 注册连接
	register chan *Connection
	// 注销连接
	unregister chan *Connection

	// 统计信息
	connectionCount int64 // 原子操作的连接计数
	maxConnections  int64

	// 配置
	config *Config

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
}

// Config Hub配置
type Config struct {
	MaxConnections    int           `yaml:"max_connections"`
	HeartbeatInterval time.Duration `yaml:"heartbeat_interval"`
	ReadTimeout       time.Duration `yaml:"read_timeout"`
	WriteTimeout      time.Duration `yaml:"write_timeout"`
	BufferSize        int           `yaml:"buffer_size"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		MaxConnections:    1000,
		HeartbeatInterval: 54 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
		BufferSize:        256,
	}
}

// NewHub 创建新的Hub
func NewHub(config *Config) *Hub {
	if config == nil {
		config = DefaultConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	hub := &Hub{
		broadcast:      make(chan []byte, config.BufferSize),
		register:       make(chan *Connection, 100),
		unregister:     make(chan *Connection, 100),
		maxConnections: int64(config.MaxConnections),
		config:         config,
		ctx:            ctx,
		cancel:         cancel,
	}

	// 启动Hub主循环
	go hub.run()

	return hub
}

// RegisterConnection 注册连接
func (h *Hub) RegisterConnection(conn *Connection) error {
	currentCount := atomic.LoadInt64(&h.connectionCount)
	if currentCount >= h.maxConnections {
		return ErrMaxClientsReached
	}

	select {
	case h.register <- conn:
		return nil
	case <-h.ctx.Done():
		return ErrConnectionClosed
	default:
		return ErrSendBufferFull
	}
}

// UnregisterConnection 注销连接
func (h *Hub) UnregisterConnection(conn *Connection) {
	select {
	case h.unregister <- conn:
	case <-h.ctx.Done():
	default:
		h.removeConnection(conn)
	}
}

// GetConnectionCount 获取连接数量
func (h *Hub) GetConnectionCount() int64 {
	return atomic.LoadInt64(&h.connectionCount)
}

// GetConnectionsByOwnerID 获取拥有者的所有连接
func (h *Hub) GetConnectionsByOwnerID(ownerID uint) []*Connection {
	if connections, ok := h.userConnMap.Load(ownerID); ok {
		return connections.([]*Connection)
	}
	return nil
}

// Broadcast 广播消息
func (h *Hub) Broadcast(message Message) error {
	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return err
	}

	select {
	case h.broadcast <- jsonMessage:
		return nil
	case <-h.ctx.Done():
		return ErrConnectionClosed
	default:
		return ErrSendBufferFull
	}
}

// SendToUser 向指定拥有者的所有连接发送消息
func (h *Hub) SendToUser(ownerID uint, message Message) error {
	connections := h.GetConnectionsByOwnerID(ownerID)
	if len(connections) == 0 {
		return ErrConnectionClosed
	}

	var lastErr error
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			lastErr = err
		}
	}
	return lastErr
}

// GetStats 获取基础统计信息
func (h *Hub) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"active_connections": atomic.LoadInt64(&h.connectionCount),
		"max_connections":    h.maxConnections,
	}
}

// run Hub主循环
func (h *Hub) run() {
	defer h.cleanup()

	for {
		select {
		case conn := <-h.register:
			h.addConnection(conn)

		case conn := <-h.unregister:
			h.removeConnection(conn)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case <-h.ctx.Done():
			return
		}
	}
}

// addConnection 添加连接
func (h *Hub) addConnection(conn *Connection) {
	h.connections.Store(conn, true)

	// 更新拥有者连接映射
	if conn.GetOwnerID() > 0 {
		h.updateUserConnections(conn.GetOwnerID(), conn, true)
	}

	atomic.AddInt64(&h.connectionCount, 1)
}

// removeConnection 移除连接
func (h *Hub) removeConnection(conn *Connection) {
	if _, loaded := h.connections.LoadAndDelete(conn); loaded {
		// 更新拥有者连接映射
		if conn.GetOwnerID() > 0 {
			h.updateUserConnections(conn.GetOwnerID(), conn, false)
		}

		conn.Close()
		atomic.AddInt64(&h.connectionCount, -1)
	}
}

// updateUserConnections 更新拥有者连接映射
func (h *Hub) updateUserConnections(ownerID uint, conn *Connection, add bool) {
	var newConnections []*Connection

	if existing, ok := h.userConnMap.Load(ownerID); ok {
		existingConnections := existing.([]*Connection)

		if add {
			newConnections = append(existingConnections, conn)
		} else {
			for _, c := range existingConnections {
				if c.GetID() != conn.GetID() {
					newConnections = append(newConnections, c)
				}
			}
		}
	} else if add {
		newConnections = []*Connection{conn}
	}

	if len(newConnections) == 0 {
		h.userConnMap.Delete(ownerID)
	} else {
		h.userConnMap.Store(ownerID, newConnections)
	}
}

// broadcastMessage 广播消息
func (h *Hub) broadcastMessage(message []byte) {
	var failedConnections []*Connection

	h.connections.Range(func(key, value interface{}) bool {
		conn := key.(*Connection)

		select {
		case conn.send <- message:
			// 发送成功
		default:
			// 发送失败，标记为待清理
			failedConnections = append(failedConnections, conn)
		}
		return true
	})

	// 清理失败的连接
	for _, conn := range failedConnections {
		h.removeConnection(conn)
	}
}

// Shutdown 优雅关闭
func (h *Hub) Shutdown(timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	h.cancel()

	done := make(chan struct{})
	go func() {
		h.cleanup()
		close(done)
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// cleanup 清理资源
func (h *Hub) cleanup() {
	h.connections.Range(func(key, value interface{}) bool {
		conn := key.(*Connection)
		conn.Close()
		return true
	})
}
