package handler

import (
	"github.com/goccy/go-json"
	"log/slog"
	"time"

	"github.com/gorilla/websocket"
)

// MessageHandler 消息处理接口，由业务层实现
type MessageHandler interface {
	HandleMessage(conn *Connection, msg Message) error
}

// Connection 表示一个WebSocket连接
type Connection struct {
	hub      *Hub            // 所属hub
	conn     *websocket.Conn // WebSocket连接对象
	send     chan []byte     // 发送消息通道
	ID       string          // 连接唯一标识
	OwnerID  uint            // 拥有者ID（可选，可以是用户、客户端或会话ID等，由业务层设置）
	JoinTime time.Time       // 加入时间
	handler  MessageHandler  // 消息处理器
	isActive bool            // 连接是否活跃
}

// NewConnection 创建新的连接
func NewConnection(hub *Hub, conn *websocket.Conn, id string, handler MessageHandler) *Connection {
	return &Connection{
		hub:      hub,
		conn:     conn,
		send:     make(chan []byte, 256),
		ID:       id,
		JoinTime: time.Now(),
		handler:  handler,
		isActive: true,
	}
}

// SetOwnerID 设置拥有者ID（由业务层调用）
func (c *Connection) SetOwnerID(ownerID uint) {
	c.OwnerID = ownerID
}

// GetOwnerID 获取拥有者ID
func (c *Connection) GetOwnerID() uint {
	return c.OwnerID
}

// GetID 获取连接ID
func (c *Connection) GetID() string {
	return c.ID
}

// IsActive 检查连接是否活跃
func (c *Connection) IsActive() bool {
	return c.isActive
}

// SendMessage 发送消息到客户端
func (c *Connection) SendMessage(message Message) error {
	if !c.isActive {
		return ErrConnectionClosed
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	select {
	case c.send <- data:
		return nil
	default:
		return ErrSendBufferFull
	}
}

// Close 关闭连接
func (c *Connection) Close() {
	if c.isActive {
		c.isActive = false
		close(c.send)
		_ = c.conn.Close()
	}
}

// readPump 处理从客户端读取消息
func (c *Connection) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.Close()
	}()

	// 使用配置中的读取超时
	readTimeout := c.hub.config.ReadTimeout
	err := c.conn.SetReadDeadline(time.Now().Add(readTimeout))
	if err != nil {
		slog.Error("failed to set read deadline", "error", err, "connectionID", c.ID)
		return
	}

	// 设置pong处理器，处理心跳响应
	c.conn.SetPongHandler(func(string) error {
		err := c.conn.SetReadDeadline(time.Now().Add(readTimeout))
		if err != nil {
			slog.Error("failed to extend read deadline", "error", err, "connectionID", c.ID)
		}
		return err
	})

	for {
		if !c.isActive {
			break
		}

		_, messageBytes, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				slog.Error("websocket read error", "error", err, "connectionID", c.ID)
			} else {
				slog.Debug("websocket connection closed", "connectionID", c.ID, "error", err)
			}
			break
		}

		// 解析消息
		var msg Message
		if err = json.Unmarshal(messageBytes, &msg); err != nil {
			slog.Error("websocket unmarshal error", "error", err, "connectionID", c.ID)
			continue
		}

		// 处理心跳消息
		if msg.Type == MessageTypeHeartbeat {
			// 收到心跳，延长读取超时
			err = c.conn.SetReadDeadline(time.Now().Add(readTimeout))
			if err != nil {
				slog.Error("failed to extend read deadline on heartbeat", "error", err, "connectionID", c.ID)
				return
			}
			// 发送pong响应
			if err := c.conn.WriteMessage(websocket.PongMessage, []byte{}); err != nil {
				slog.Error("failed to send pong", "error", err, "connectionID", c.ID)
				return
			}
			continue
		}

		// 委托给业务层处理
		if c.handler != nil {
			if err := c.handler.HandleMessage(c, msg); err != nil {
				slog.Error("message handler error", "error", err, "connectionID", c.ID)
			}
		}
	}
}

// writePump 处理向客户端写入消息
func (c *Connection) writePump() {
	// 使用配置中的心跳间隔
	heartbeatInterval := c.hub.config.HeartbeatInterval
	ticker := time.NewTicker(heartbeatInterval)
	defer func() {
		ticker.Stop()
		c.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			// 设置写入超时
			writeTimeout := c.hub.config.WriteTimeout
			_ = c.conn.SetWriteDeadline(time.Now().Add(writeTimeout))

			if !ok {
				// 通道已关闭
				_ = c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				slog.Error("failed to get next writer", "error", err, "connectionID", c.ID)
				return
			}
			_, _ = w.Write(message)

			if err := w.Close(); err != nil {
				slog.Error("failed to close writer", "error", err, "connectionID", c.ID)
				return
			}

		case <-ticker.C:
			// 设置写入超时
			writeTimeout := c.hub.config.WriteTimeout
			_ = c.conn.SetWriteDeadline(time.Now().Add(writeTimeout))

			// 发送ping心跳
			if err := c.conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				slog.Error("failed to send ping", "error", err, "connectionID", c.ID)
				return
			}
		}
	}
}

// Start 启动连接的读写循环
func (c *Connection) Start() {
	go c.writePump()
	go c.readPump()
}
