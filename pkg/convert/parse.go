package convert

import (
	"github.com/goccy/go-json"
	"strconv"
)

// StringToUint 将字符串转换为 uint64 类型
func StringToUint(s string) uint {
	if s == "" {
		return 0
	}
	val, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		return 0
	}
	return uint(val)
}

// JSONMarshal 将对象序列化
func JSONMarshal(v interface{}) string {
	jsonStr, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(jsonStr)
}
