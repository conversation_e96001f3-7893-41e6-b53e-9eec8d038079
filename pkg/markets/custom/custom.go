package custom

import (
	"context"
	"errors"
	"fmt"
	"gin/pkg/markets/types"
	"github.com/redis/rueidis"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
)

// Custom 实现Markets接口
type Custom struct {
	client         rueidis.Client                                                       // Redis客户端
	marketsData    *types.MarketsData                                                   // 市场数据
	storage        *types.KlineStorage                                                  // 存储
	tickers        []string                                                             // 订阅的tickers
	messageHandler func(platform types.PlatformSymbol, channel string, msg interface{}) // 订阅消息的转发方法
}

// NewCustom 创建一个新的Custom实例
func NewCustom(client rueidis.Client) types.Markets {
	return &Custom{
		storage: types.NewKlineStorage(client),
		client:  client,
	}
}

// InitSubscribes 初始化订阅 CTM-USDT@1.38
func (c *Custom) InitSubscribes(subscribes []string) types.Markets {
	for _, subscribe := range subscribes {
		symbolList := strings.Split(subscribe, "@")
		if len(symbolList) != 2 {
			continue
		}

		// 添加到订阅列表
		c.tickers = append(c.tickers, subscribe)

		// 获取价格
		symbolPrice, err := strconv.ParseFloat(symbolList[1], 64)
		if err != nil {
			continue
		}
		// 涨幅百分之8
		maxSymbolPrice := symbolPrice * 1.08

		// 生成 32天数据  1分钟的k线数据 共 46080条数据
		klines := types.NewCustomKline(symbolList[0]).GenerateKline(60, symbolPrice, maxSymbolPrice, time.Now().Add(-time.Hour*24*30), time.Now().Add(time.Hour*24*2))
		c.storage.InitKlines(context.Background(), klines)
	}
	return c
}

// Subscribe 订阅
func (c *Custom) Subscribe(subscribe []*types.Subscribe) error {
	return nil
}

// Unsubscribe 取消订阅
func (c *Custom) Unsubscribe(subscribe []*types.Subscribe) error {
	return nil
}

// SetMessageHandler 设置消息处理方法
func (c *Custom) SetMessageHandler(messageHandler func(platform types.PlatformSymbol, channel string, msg interface{})) types.Markets {
	c.messageHandler = messageHandler
	return c
}

// SetData 设置市场数据
func (c *Custom) SetData(data *types.MarketsData) types.Markets {
	c.marketsData = data
	return c
}

func (c *Custom) Start() error {
	// 启动定时任务
	crontab := cron.New()
	// 每秒更新一次
	crontab.AddFunc("@every 1s", c.crontabUpdateTickers)

	// 10分钟更新一次
	crontab.AddFunc("@every 10m", c.updateKline)

	crontab.Start()
	return nil
}

func (c *Custom) GetTickers() (*types.Tickers, error) {
	return nil, nil
}

// GetTicker 获取指定产品行情信息
func (c *Custom) GetTicker(symbol string) (*types.Ticker, error) {

	return nil, nil
}

// GetKline 获取K线数据
func (c *Custom) GetKline(symbol string, bar types.LineBar, limit int) ([]*types.Kline, error) {
	return c.storage.GetKlines(context.Background(), symbol, bar, int64(limit))
}

// GetBooks 获取深度数据
func (c *Custom) GetBooks(symbol string, depth int) (*types.Books, error) {
	books := &types.Books{
		Asks: make([][]string, 0),
		Bids: make([][]string, 0),
		Ts:   strconv.FormatInt(time.Now().UnixMilli(), 10),
	}

	ticker, err := c.storage.GetLatestTicker(context.Background(), symbol)
	if err != nil {
		return books, err
	}

	books = types.GenerateRandomBooks(ticker, depth)
	books.Ts = strconv.FormatInt(time.Now().UnixMilli(), 10)
	return books, nil
}

// GetTrades 获取交易数据
func (c *Custom) GetTrades(symbol string, limit int) ([]*types.Trade, error) {
	trades := make([]*types.Trade, 0)

	ticker, err := c.storage.GetLatestTicker(context.Background(), symbol)
	if err != nil {
		return trades, err
	}

	trades = types.GenerateRandomTrades(ticker, limit)
	return trades, nil
}

// UpdateTargetPrice 更新目标价格
func (c *Custom) UpdateTargetPrice(symbol string, targetTime time.Time, targetPrice float64) error {
	beforeTime := time.Now().Truncate(time.Minute)
	klines, err := c.storage.GetLatestKlineBeforeTime(context.Background(), symbol, beforeTime.Unix())
	if err != nil {
		return err
	}

	var endKline *types.Kline
	// 如果数据为空, 那么重新加载32天数据, 准备下次在执行
	if len(klines) == 0 {
		c.LoadInitKlines(symbol)
		return errors.New("数据为空, 重新加载32天数据, 再次尝试执行")
	}
	endKline = klines[len(klines)-1]

	// 删除当前时间之后的数据
	err = c.storage.DeleteKlinesAfterTime(context.Background(), symbol, time.Now().Truncate(time.Minute).Unix())
	if err != nil {
		return err
	}

	// 生成新的数据
	currentPrice := endKline.OpenPrice
	klines = types.NewCustomKline(symbol).GenerateKline(60, currentPrice, targetPrice, beforeTime, targetTime)
	return c.storage.AddKlines(context.Background(), symbol, klines)
}

// LoadInitKlines 加载初始化K线数据 - 不会删除其他数据
func (c *Custom) LoadInitKlines(symbol string) error {
	for _, subscribe := range c.tickers {
		symbolList := strings.Split(subscribe, "@")
		if len(symbolList) != 2 {
			continue
		}

		if symbolList[0] != symbol {
			continue
		}

		// 获取价格
		symbolPrice, err := strconv.ParseFloat(symbolList[1], 64)
		if err != nil {
			return err
		}

		// 涨幅百分之8
		maxSymbolPrice := symbolPrice * 1.08

		// 生成 32天数据  1分钟的k线数据 共 46080条数据
		klines := types.NewCustomKline(symbolList[0]).GenerateKline(60, symbolPrice, maxSymbolPrice, time.Now().Add(-time.Hour*24*30), time.Now().Add(time.Hour*24*2))
		err = c.storage.AddKlines(context.Background(), symbolList[0], klines)
		if err != nil {
			return err
		}
	}
	return nil
}

// crontabUpdateTickers 定时更新 tickers 数据
func (c *Custom) crontabUpdateTickers() {
	for _, subscribe := range c.tickers {
		symbolList := strings.Split(subscribe, "@")
		if len(symbolList) != 2 {
			continue
		}

		symbolTiker, err := c.storage.GetLatestTicker(context.Background(), symbolList[0])
		if err != nil {
			continue
		}

		if c.messageHandler != nil {
			// 推送行情数据
			books := types.GenerateRandomBooks(symbolTiker, rand.Intn(20)+1)
			c.messageHandler(types.PlatformSymbolCustom, SubscribeChannelBooks, books)

			// 推送交易数据
			trades := types.GenerateRandomTrades(symbolTiker, rand.Intn(10)+1)
			c.messageHandler(types.PlatformSymbolCustom, SubscribeChannelTrades, trades)

			// 发送tickers数据
			// 生成最新价格, 随机加减 +- 0.01-0.09
			randomFloat := rand.Float64()
			if randomFloat > 0.5 {
				maxRange, minRange := types.GetPriceRange(symbolTiker.Last)
				symbolTiker.Last = symbolTiker.Last + symbolTiker.Last*types.RandomFloat64(minRange, maxRange, 6)
			} else {
				maxRange, minRange := types.GetPriceRange(symbolTiker.Last)
				symbolTiker.Last = symbolTiker.Last - symbolTiker.Last*types.RandomFloat64(minRange, maxRange, 6)
			}
			symbolTiker.Ts = time.Now().Unix()
			c.messageHandler(types.PlatformSymbolCustom, SubscribeChannelTickers, symbolTiker)
		}
	}
}

// updateKline 更新K线数据
func (c *Custom) updateKline() {
	for _, subscribe := range c.tickers {
		symbolList := strings.Split(subscribe, "@")
		if len(symbolList) != 2 {
			continue
		}

		// 删除 30 天之前到数据
		c.storage.DeleteKlinesBeforeTime(context.Background(), symbolList[0], time.Now().Add(-time.Hour*24*30).Unix())

		// 如果数据不足当前时间1天后到数据, 那么执行生成1天后到数据
		beforeTime := time.Now().Truncate(time.Minute)
		klines, err := c.storage.GetLatestKlineBeforeTime(context.Background(), symbolList[0], beforeTime.Unix())
		if err != nil {
			fmt.Println("获取", symbolList[0], "数据失败", err)
			continue
		}

		// 如果数据不足当前时间1天后到数据, 那么执行生成1天后到数据
		if len(klines) < 1440 && len(klines) > 0 {
			endKline := klines[len(klines)-1]
			minPrice, maxPrice := types.GetPriceRange(endKline.OpenPrice)
			targetPrice := endKline.OpenPrice + endKline.OpenPrice*types.RandomFloat64(minPrice, maxPrice, 6)
			startTime := time.Unix(endKline.CreatedAt, 0)
			klines = types.NewCustomKline(symbolList[0]).GenerateKline(60, endKline.OpenPrice, targetPrice, startTime, time.Now().Add(time.Hour*24).Truncate(time.Minute))
			_ = c.storage.AddKlines(context.Background(), symbolList[0], klines)
		}
	}

}
