package trading

const (
	SubscribeChannelTickers = "tickers" // 行情
	SubscribeChannelBooks   = "books"   // 深度
	SubscribeChannelTrades  = "trades"  // 成交
)

// SocketIOHandshake 握手信息
type SocketIOHandshake struct {
	SID          string   `json:"sid"`
	Upgrades     []string `json:"upgrades"`
	PingInterval int      `json:"pingInterval"`
	PingTimeout  int      `json:"pingTimeout"`
	MaxPayload   int      `json:"maxPayload"`
}

// KlineResult 响应数据
type KlineResult struct {
	CacheTime      string        `json:"cacheTime"`
	Agr            string        `json:"agr"`
	Span           string        `json:"span"`
	AllowIntraday  bool          `json:"allow_intraday"`
	AllowInterval  string        `json:"allow_interval"`
	IsIntraday     bool          `json:"isIntraday"`
	ChartFrequency string        `json:"chartFrequency"`
	Series         []KlineSeries `json:"series"`
}

// KlineSeries 产品行情信息
type KlineSeries struct {
	Symbol               string  `json:"symbol"`
	Name                 string  `json:"name"`
	Shortname            string  `json:"shortname"`
	FullName             string  `json:"full_name"`
	Data                 [][]any `json:"data"` // using any to support different types in the nested arrays
	Unit                 string  `json:"unit"`
	Decimals             int     `json:"decimals"`
	Frequency            string  `json:"frequency"`
	Type                 string  `json:"type"`
	AllowedCandles       bool    `json:"allowed_candles"`
	SupportedResolutions any     `json:"supported_resolutions"`
	Timezone             any     `json:"timezone"`
	HasDaily             bool    `json:"has_daily"`
	AllowedInterval      any     `json:"allowed_interval"`
	Value                any     `json:"value"`
	ConvertedValue       any     `json:"converted_value"`
	Last                 any     `json:"last"`
	Ticker               any     `json:"ticker"`
	Description          any     `json:"description"`
	HasWeeklyAndMonthly  bool    `json:"has_weekly_and_monthly"`
	HasNoVolume          bool    `json:"has_no_volume"`
	HasIntraday          bool    `json:"has_intraday"`
	Industry             any     `json:"industry"`
	Minmov               any     `json:"minmov"`
	Sector               any     `json:"sector"`
	Pricescale           float64 `json:"pricescale"`
	Minmov2              float64 `json:"minmov2"`
	IeconomicsUrl        any     `json:"ieconomics_url"`
	ChartType            any     `json:"chart_type"`
}
