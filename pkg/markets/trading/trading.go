package trading

import (
	"bytes"
	"compress/gzip"
	"compress/zlib"
	"encoding/base64"
	"fmt"
	"gin/pkg/markets/types"
	"gin/pkg/net"
	"gin/pkg/socket/client"
	"github.com/goccy/go-json"
	"io"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/redis/rueidis"

	"github.com/robfig/cron/v3"
	"golang.org/x/crypto/nacl/secretbox"
)

// Trading 实现Markets接口
// 采集 外汇数据｜期货数据 - 模拟操作
// 官网 https://tradingeconomics.com
type Trading struct {
	wssURL         string                                                               // 公共WebSocket URL
	restURL        string                                                               // 公共REST URL
	client         rueidis.Client                                                       // Redis客户端
	DecryptKey     string                                                               // 解密密钥
	DecryptNonce   string                                                               // 解密非对称
	wsClient       client.SocketClientInterface                                         // WebSocket客户端
	subscribes     []*types.Subscribe                                                   // 订阅列表
	marketsData    *types.MarketsData                                                   // 市场数据
	messageHandler func(platform types.PlatformSymbol, channel string, msg interface{}) // 订阅消息的转发方法
}

// NewTrading 创建一个新的Trading实例
func NewTrading(client rueidis.Client) types.Markets {
	return &Trading{
		wssURL:         "wss://live.tradingeconomics.com/socket.io",
		restURL:        "https://live.tradingeconomics.com",
		client:         client,
		DecryptKey:     "j9ONifjoKzxt7kmfYTdKK/5vve0b9Y1UCj/n50jr8d8=",
		DecryptNonce:   "Ipp9HNSfVBUntqFK7PrtofYaOPV312xy",
		wsClient:       nil,
		marketsData:    &types.MarketsData{},
		messageHandler: nil,
	}
}

// Subscribe 订阅数据
func (t *Trading) Subscribe(subscribe []*types.Subscribe) error {
	// TODO...
	return nil
}

// Unsubscribe 取消订阅
func (t *Trading) Unsubscribe(subscribe []*types.Subscribe) error {
	// TODO...
	return nil
}

// UpdateTargetPrice 更新目标价格
func (t *Trading) UpdateTargetPrice(symbol string, targetTime time.Time, targetPrice float64) error {
	return nil
}

// SetMessageHandler 设置订阅消息处理函数
func (t *Trading) SetMessageHandler(messageHandler func(platform types.PlatformSymbol, channel string, msg interface{})) types.Markets {
	t.messageHandler = messageHandler
	return t
}

// SetData 设置数据
func (t *Trading) SetData(data *types.MarketsData) types.Markets {
	t.marketsData = data
	return t
}

// Start 启动
func (t *Trading) Start() error {
	t.wsClient = client.NewSocketClient(t.wssURL)
	t.wsClient.SetBeforeConnectFunc(func(client client.SocketClientInterface) error {
		resp, err := net.NewHttpClient(t.restURL).Get("/socket.io/?key=rain&url=%2Fcommodities&EIO=4&transport=polling")
		if err != nil {
			return err
		}
		handshake := &SocketIOHandshake{}
		err = t.messageJson(string(resp.Body), handshake)
		if err != nil {
			return err
		}

		// 请求获取 sid  重新设置 socket url 地址
		query := url.Values{}
		query.Set("key", "rain")
		query.Set("url", "/commodities")
		query.Set("EIO", "4")
		query.Set("transport", "websocket")
		query.Set("sid", handshake.SID)
		_ = t.wsClient.SetAddr(t.wssURL + "/?" + query.Encode())

		return nil
	})

	t.wsClient.SetAfterConnectFunc(func(client client.SocketClientInterface) error {
		return client.SendMessage([]byte("2probe"))
	})

	// 设置消息的处理方法
	t.wsClient.SetWebSocketMessageFunc(func(msg []byte) error {
		isData := len(msg) < 46
		numb := t.extractNumbers(msg)
		switch {
		case numb == 3 && isData:
			_ = t.wsClient.SendMessage([]byte("5"))
			_ = t.wsClient.SendMessage([]byte("40"))
		case numb == 2 && isData:
			_ = t.wsClient.SendMessage([]byte("3"))
		case numb == 40 && isData:
			subscribes := []any{"subscribe", map[string][]string{"s": t.marketsData.Subscribes}}
			subscribesBytes, _ := json.Marshal(subscribes)
			_ = t.wsClient.SendMessage(append([]byte("42"), subscribesBytes...))
		default:
			v, err := t.decryptMessage(msg)
			if err != nil {
				return err
			}

			decompressed, err := t.decompressMessageZlib(v)
			if err != nil {
				return err
			}

			// 解析数据
			ticker := &Ticker{}
			err = json.Unmarshal(decompressed, ticker)
			if err != nil {
				return err
			}

			// 发送消息处理
			msgTicker := ticker.ToTicker(t.client)
			if t.messageHandler != nil {
				t.messageHandler(types.PlatformSymbolTrading, SubscribeChannelTickers, msgTicker)
			}
		}
		return nil
	})

	// 启动定时任务
	c := cron.New()
	// 定时更新 tickers 数据
	c.AddFunc("@every 1s", t.crontabUpdateTickers)
	c.Start()

	// 启动WebSocket客户端
	t.wsClient.Connect()
	return nil
}

// GetTickers 获取所有产品行情信息
func (t *Trading) GetTickers() (*types.Tickers, error) {
	cacheTickers := types.GetCacheTickers(t.client, tradingTickerCacheKey)
	if cacheTickers == nil {
		return &types.Tickers{Data: make([]*types.Ticker, 0)}, nil
	}
	return cacheTickers, nil
}

// GetTicker 获取指定产品行情信息
func (t *Trading) GetTicker(symbol string) (*types.Ticker, error) {

	return nil, nil
}

// GetKline 获取K线数据
func (t *Trading) GetKline(symbol string, bar types.LineBar, limit int) ([]*types.Kline, error) {
	params := map[string]string{
		"interval": string(bar),         // 1m, 5m, 15m, 1h, 1d, 1w
		"n":        strconv.Itoa(limit), // 数量
		"ohlc":     "1",                 // k线图类型 0 1
		"key":      "20240229:nazare",   // 加密key
	}

	// 请求数据
	resp, err := net.NewHttpClient("https://d3ii0wo49og5mi.cloudfront.net").SetParams(params).Get("/markets/" + symbol)
	if err != nil {
		return nil, err
	}

	return t.klineConversion(bar, symbol, resp.Body)
}

// klineConversion 转换数据
func (t *Trading) klineConversion(bar types.LineBar, symbol string, tmpVal []byte) ([]*types.Kline, error) {
	vStr := strings.Trim(string(tmpVal), "\"")
	decryptData, err := t.klineDataMagic(vStr, "tradingeconomics-charts-core-api-key")
	if err != nil {
		return nil, err
	}

	// 解析数据
	rawResult := KlineResult{}
	err = json.Unmarshal(decryptData, &rawResult)
	if err != nil {
		return nil, err
	}

	kline := make([]*types.Kline, 0)
	for _, datum := range rawResult.Series[0].Data {
		// 过滤第一个存在nil的数据
		if datum[0] == nil {
			continue
		}

		// 生成随机的交易金额和交易数量
		volume, amount := t.generateRandomVolume(bar, datum[4].(float64))
		kline = append(kline, &types.Kline{
			Symbol:     symbol,
			OpenPrice:  datum[4].(float64),
			HighPrice:  datum[5].(float64),
			LowsPrice:  datum[6].(float64),
			ClosePrice: datum[7].(float64),
			Vol:        volume,
			Amount:     amount,
			CreatedAt:  int64(datum[0].(float64)),
		})
	}

	return kline, err
}

// klineDataMagic 数据魔法
func (t *Trading) klineDataMagic(b64Data, dk string) ([]byte, error) {
	// 1. Base64 解码
	decodedData, err := base64.StdEncoding.DecodeString(b64Data)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// 2. 按字节进行异或操作
	keyBytes := []byte(dk)
	for i := 0; i < len(decodedData); i++ {
		decodedData[i] ^= keyBytes[i%len(keyBytes)]
	}

	// 3. 解压数据
	decompressedData, err := t.klineDecompressGzip(decodedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress encryptionData: %v", err)
	}

	return decompressedData, nil
}

// 解压
func (t *Trading) klineDecompressGzip(compressedData []byte) ([]byte, error) {
	// 使用 gzip.NewReader 创建解压读取器
	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %v", err)
	}
	defer func(reader *gzip.Reader) {
		_ = reader.Close()
	}(reader)

	// 使用 bytes.Buffer 来存储解压后的数据
	var decompressedData bytes.Buffer
	_, err = io.Copy(&decompressedData, reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress encryptionData: %v", err)
	}

	// 返回解压后的字节数据
	return decompressedData.Bytes(), nil
}

// GetBooks 获取深度数据
func (t *Trading) GetBooks(symbol string, depth int) (*types.Books, error) {
	books := &types.Books{
		Asks: make([][]string, 0),
		Bids: make([][]string, 0),
		Ts:   strconv.FormatInt(time.Now().UnixMilli(), 10),
	}
	cacheTickers := types.GetCacheTickers(t.client, tradingTickerCacheKey)
	if cacheTickers == nil {
		return books, nil
	}

	_, ticker := cacheTickers.GetSymbol(symbol)
	if ticker == nil {
		return books, nil
	}

	fmt.Println("ticker", ticker)
	books = types.GenerateRandomBooks(ticker, depth)
	books.Ts = strconv.FormatInt(time.Now().UnixMilli(), 10)
	return books, nil
}

// GetTrades 获取交易数据
func (t *Trading) GetTrades(symbol string, limit int) ([]*types.Trade, error) {
	trades := make([]*types.Trade, 0)
	cacheTickers := types.GetCacheTickers(t.client, tradingTickerCacheKey)
	if cacheTickers == nil {
		return trades, nil
	}

	_, ticker := cacheTickers.GetSymbol(symbol)
	if ticker == nil {
		return trades, nil
	}

	trades = types.GenerateRandomTrades(ticker, limit)
	return trades, nil
}

// extractNumbers 提取数字
func (t *Trading) extractNumbers(b []byte) int {
	var val []byte
	for _, v := range b {
		if 48 <= v && v <= 57 {
			val = append(val, v)
			continue
		}
		break
	}
	parseInt, err := strconv.ParseInt(string(val), 10, 64)
	if err != nil {
		return -1
	}
	return int(parseInt)
}

// messageJson 解析 Socket.IO 响应中的 JSON 数据
func (t *Trading) messageJson(rawJson string, v interface{}) error {
	jsonStart := strings.Index(rawJson, "{")
	if jsonStart == -1 {
		return fmt.Errorf("无效的 Socket.IO 响应格式: 没有找到 JSON")
	}

	jsonStr := rawJson[jsonStart:]
	err := json.Unmarshal([]byte(jsonStr), v)
	if err != nil {
		return err
	}
	return nil
}

// decryptMessage 解密消息
func (t *Trading) decryptMessage(ciphertext []byte) ([]byte, error) {
	decodedKey, err := base64.StdEncoding.DecodeString(t.DecryptKey)
	if err != nil {
		return nil, err
	}

	decodedNonce, err := base64.StdEncoding.DecodeString(t.DecryptNonce)
	if err != nil {
		return nil, err
	}

	decrypted, ok := secretbox.Open(nil, ciphertext, (*[24]byte)(decodedNonce), (*[32]byte)(decodedKey))
	if !ok {
		return nil, fmt.Errorf("解密消息失败")
	}

	return decrypted, nil
}

// decompressZlib 解压数据
func (t *Trading) decompressMessageZlib(data []byte) ([]byte, error) {
	reader, err := zlib.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer func(reader io.ReadCloser) {
		_ = reader.Close()
	}(reader)
	return io.ReadAll(reader)
}

// generateRandomVolume 生成随机交易金额
func (t *Trading) generateRandomVolume(bar types.LineBar, price float64) (float64, float64) {
	// 根据时间间隔设置最大交易金额
	var maxAmount float64
	switch bar {
	case types.Bar1m:
		maxAmount = 10000 // 1分钟最大100,000金额
	case types.Bar5m:
		maxAmount = 50000 // 5分钟最大500,000金额
	case types.Bar15m:
		maxAmount = 150000 // 15分钟最大1,500,000金额
	case types.Bar1h:
		maxAmount = 500000 // 1小时最大5,000,000金额
	case types.Bar1d:
		maxAmount = 5000000 // 1天最大50,000,000金额
	case types.Bar1w:
		maxAmount = 20000000 // 1周最大200,000,000金额
	default:
		maxAmount = 100000 // 默认最大100万金额
	}

	// 生成随机金额 (在最大金额的10%-100%之间)
	minAmount := maxAmount * 0.1
	randomAmount := minAmount + (maxAmount-minAmount)*rand.Float64()

	// 根据当前价格计算交易数量
	volume := randomAmount / price

	// 返回交易数量和交易金额
	return volume, randomAmount
}

// crontabUpdateTickers 定时更新 tickers 数据
func (t *Trading) crontabUpdateTickers() {
	cacheTickers := types.GetCacheTickers(t.client, tradingTickerCacheKey)
	if cacheTickers == nil {
		return
	}

	nowTime := time.Now()
	for _, ticker := range cacheTickers.Data {
		volume, amount := t.generateRandomVolume("1m", ticker.Last)
		ticker.VolCcy24h += amount
		ticker.Vol24h += volume
		ticker.Ts = nowTime.Unix()

		// 生成最新价格, 随机加减 +- 0.01-0.09
		randomFloat := rand.Float64()
		if randomFloat > 0.5 {
			maxRange, minRange := types.GetPriceRange(ticker.Last)
			ticker.Last = ticker.Last + ticker.Last*types.RandomFloat64(minRange, maxRange, 6)
		} else {
			maxRange, minRange := types.GetPriceRange(ticker.Last)
			ticker.Last = ticker.Last - ticker.Last*types.RandomFloat64(minRange, maxRange, 6)
		}

		// 如果当前时间 00:00:01 - 00:00:05 那么重置交易量和交易额
		if nowTime.Hour() == 0 && nowTime.Minute() == 0 {
			if nowTime.Second() > 0 && nowTime.Second() < 5 {
				ticker.High24h = 0
				ticker.Low24h = 0
				ticker.Vol24h = 0
				ticker.VolCcy24h = 0
			}
		}

		// 发送消息处理
		if t.messageHandler != nil {
			// 推送行情数据
			t.messageHandler(types.PlatformSymbolTrading, SubscribeChannelTickers, ticker)

			// 推送深度数据 1-20 档 随机生成
			books := types.GenerateRandomBooks(ticker, rand.Intn(20)+1)
			t.messageHandler(types.PlatformSymbolTrading, SubscribeChannelBooks, books)

			// 推送交易数据
			trades := types.GenerateRandomTrades(ticker, rand.Intn(10)+1)
			t.messageHandler(types.PlatformSymbolTrading, SubscribeChannelTrades, trades)
		}
	}

	// 设置缓存数据
	_ = types.SetCacheTickers(t.client, tradingTickerCacheKey, cacheTickers)
}
