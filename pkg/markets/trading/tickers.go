package trading

import (
	"gin/pkg/markets/types"
	"github.com/redis/rueidis"
	"time"
)

// 缓存 trading tickers 行情数据
var tradingTickerCacheKey = "markets:trading:tickers"

// Ticker 行情数据
type Ticker struct {
	S      string  `json:"s"`
	P      float64 `json:"p"`
	Nch    float64 `json:"nch"`
	Pch    float64 `json:"pch"`
	Dt     int64   `json:"dt"`
	Odt    int64   `json:"odt"`
	Type   string  `json:"type"`
	State  string  `json:"state"`
	Dstate string  `json:"dstate"`
}

// ToTicker 转换为类型，优先从缓存获取数据
func (t *Ticker) ToTicker(client rueidis.Client) *types.Ticker {
	// 从缓存获取数据
	cacheTickers := types.GetCacheTickers(client, tradingTickerCacheKey)
	var oldTicker *types.Ticker

	// 如果能获取到缓存中的数据，那么使用缓存的数据 - 定时任务 1s 更新一次交易数量
	var high24h, low24h, vol24h, volCcy24h float64
	if cacheTickers != nil {
		_, oldTicker = cacheTickers.GetSymbol(t.S)
		if oldTicker != nil {
			high24h = oldTicker.High24h
			low24h = oldTicker.Low24h
			vol24h = oldTicker.Vol24h
			volCcy24h = oldTicker.VolCcy24h
		}
	}

	// 设置最高价格
	if t.P > high24h {
		high24h = t.P
	}

	// 设置最低价格
	if t.P < low24h || low24h == 0 {
		low24h = t.P
	}

	// 转换为类型
	newTime := time.UnixMilli(t.Dt)
	ticker := &types.Ticker{
		Symbol:    t.S,
		Last:      t.P,
		LastSz:    t.P,
		High24h:   high24h,
		Low24h:    low24h,
		Vol24h:    vol24h,
		VolCcy24h: volCcy24h,
		Open24h:   t.P - t.Nch,
		Ts:        newTime.Unix(),
	}

	// 设置缓存数据
	_ = types.SetCacheTicker(client, tradingTickerCacheKey, ticker)
	return ticker
}
