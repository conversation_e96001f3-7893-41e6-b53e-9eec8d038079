package types

import (
	"math"
	"math/rand"
	"time"
)

// Kline K线图数据
type Kline struct {
	Symbol     string  `json:"symbol"`     // 交易对标识
	OpenPrice  float64 `json:"openPrice"`  // 开盘价格
	HighPrice  float64 `json:"highPrice"`  // 最高价格
	LowsPrice  float64 `json:"lowsPrice"`  // 最低价格
	ClosePrice float64 `json:"closePrice"` // 收盘价格
	Vol        float64 `json:"vol"`        // 交易量
	Amount     float64 `json:"amount"`     // 成交额
	CreatedAt  int64   `json:"createdAt"`  // 开盘时间
}

// CustomKline 自定义K线图数据
type CustomKline struct {
	symbol string
}

// NewCustomKline 创建一个新的CustomKline实例
func NewCustomKline(symbol string) *CustomKline {
	return &CustomKline{
		symbol: symbol,
	}
}

// GenerateKline 生成K线图数据 间隔时间(秒) 开始价格 结束价格 开始时间 结束时间
func (c *CustomKline) GenerateKline(interval int64, startPrice, endPrice float64, startTime, endTime time.Time) []*Kline {
	// 去掉秒，用00代替
	startTime = startTime.Truncate(time.Minute)
	endTime = endTime.Truncate(time.Minute)

	rows := int((endTime.Unix() - startTime.Unix()) / interval)
	bridgePrices := c.GenerateBrownianBridge(startPrice, endPrice, rows, 10)
	return c.GenerateBridgeKline(startTime, interval, rows, bridgePrices)
}

// GenerateBridgeKline 生成布朗桥K线图数据
func (c *CustomKline) GenerateBridgeKline(startTime time.Time, interval int64, limit int, bridgePrices []float64) []*Kline {
	var kLines []*Kline

	for i := 0; i < limit; i++ {
		openPrice := bridgePrices[i]
		closePrice := bridgePrices[i+1]

		// 生成合理的最高价和最低价
		// 根据价格范围动态调整波动比例，确保K线图看起来合理
		maxRange, minRange := GetPriceRange(openPrice)

		highPrice := openPrice + RandomFloat64(minRange, maxRange, 6)
		lowPrice := openPrice - RandomFloat64(minRange, maxRange, 6)

		// 确保最低价不会小于0
		if lowPrice < 0 {
			lowPrice = openPrice * 0.1
		}

		// 模拟成交量
		volume := RandomFloat64(100, 1100, 2)

		// 添加到 K 线数据
		kLines = append(kLines, &Kline{
			Symbol:     c.symbol,
			CreatedAt:  startTime.Unix(),
			OpenPrice:  openPrice,
			ClosePrice: closePrice,
			LowsPrice:  lowPrice,
			HighPrice:  highPrice,
			Vol:        volume,
			Amount:     RoundToDecimalPlaces(volume*openPrice, 4),
		})
		startTime = startTime.Add(time.Duration(interval) * time.Second)
	}

	return kLines
}

// GenerateBrownianBridge 生成布朗桥价格路径，确保价格在 [min(start, end), max(start, end)] 范围内 volatility 波动率 steps 步长
func (c *CustomKline) GenerateBrownianBridge(startPrice, endPrice float64, steps int, volatility float64) []float64 {
	// 确定价格范围
	minPrice := math.Min(startPrice, endPrice)
	maxPrice := math.Max(startPrice, endPrice)

	prices := make([]float64, steps+1)
	prices[0] = math.Max(math.Min(startPrice, maxPrice), minPrice)   // 确保起始价格在范围内
	prices[steps] = math.Max(math.Min(endPrice, maxPrice), minPrice) // 确保结束价格在范围内

	for i := 1; i < steps; i++ {
		// 时间点比例
		t := float64(i) / float64(steps)

		// 期望价格线性插值
		expected := startPrice + t*(endPrice-startPrice)

		// 生成随机偏差，符合波动率
		// 这里使用标准正态分布乘以波动率和预定义的步长
		deviation := rand.NormFloat64() * RandomFloat64(volatility/10, volatility, 2) * (endPrice - startPrice) / float64(steps)

		// 当前价格 = 期望价格 + 偏差
		prices[i] = expected + deviation

		// 确保价格在 [minPrice, maxPrice] 范围内
		prices[i] = math.Max(math.Min(prices[i], maxPrice), minPrice)
	}

	// 为确保价格连续性，可以对价格进行平滑处理
	for i := 1; i < len(prices); i++ {
		// 简单的线性插值以确保价格不会出现剧烈跳变
		prices[i] = (prices[i-1] + prices[i]) / 2

		// 再次确保价格在 [minPrice, maxPrice] 范围内
		prices[i] = math.Max(math.Min(prices[i], maxPrice), minPrice)
	}

	// 确保最后价格严格等于 end
	if endPrice > 0 {
		prices[steps] = endPrice
	}

	return prices
}
