package types

// PlatformSymbol 交易所平台
type PlatformSymbol string

const (
	// PlatformSymbolOkex 交易所平台
	PlatformSymbolOkex PlatformSymbol = "okex"
	// PlatformSymbolTrading Trading交易平台
	PlatformSymbolTrading PlatformSymbol = "trading"
	// PlatformSymbolCustom 自定义平台
	PlatformSymbolCustom PlatformSymbol = "custom"

	// SubscribeChannelOkex 订阅通道 - okex
	SubscribeChannelOkex = "okex:market:data"
	// SubscribeChannelTrading 订阅通道 - trading
	SubscribeChannelTrading = "trading:market:data"
	// SubscribeChannelCustom 订阅通道 - custom
	SubscribeChannelCustom = "custom:market:data"
)

// Subscribe 同一订阅数据格式
type Subscribe struct {
	Channel string // 订阅通道
	Symbol  string // 交易对标识
}
