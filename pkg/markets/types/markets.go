package types

import (
	"time"
)

// Markets 市场接口
type Markets interface {
	// Subscribe 订阅数据
	Subscribe(subscribe []*Subscribe) error
	// Unsubscribe 取消订阅
	Unsubscribe(subscribe []*Subscribe) error
	// SetMessageHandler 设置订阅消息处理函数
	SetMessageHandler(messageHandler func(platform PlatformSymbol, channel string, msg interface{})) Markets
	// UpdateTargetPrice 更新目标价格
	UpdateTargetPrice(symbol string, targetTime time.Time, targetPrice float64) error
	// SetData 设置数据
	SetData(data *MarketsData) Markets
	// Start 启动市场
	Start() error
	// GetTickers 获取所有产品行情信息
	GetTickers() (*Tickers, error)
	// GetTicker 获取指定产品行情信息
	GetTicker(symbol string) (*Ticker, error)
	// GetKline 获取K线数据
	GetKline(symbol string, bar LineBar, limit int) ([]*Kline, error)
	// GetBooks 获取深度数据
	GetBooks(symbol string, depth int) (*Books, error)
	// GetTrades 获取交易数据
	GetTrades(symbol string, limit int) ([]*Trade, error)
}

// MarketsData 市场数据
type MarketsData struct {
	Subscribes []string `json:"subscribes"` // 订阅列表
}
