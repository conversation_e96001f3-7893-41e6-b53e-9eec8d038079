package types

import (
	"math"
	"math/rand"
	"time"
)

// randomFloat64 生成指定范围内的随机float64数字，保留指定位小数
// 作用：生成指定范围内的随机浮点数，用于模拟价格和数量的随机变化
// 参数：
//   - min: 最小值
//   - max: 最大值
//   - decimalPlaces: 小数位数
//
// 返回：指定范围内的随机浮点数
func RandomFloat64(min, max float64, decimalPlaces int) float64 {
	// 确保min小于max
	if min > max {
		min, max = max, min
	}

	// 生成随机float64
	random := min + rand.Float64()*(max-min)

	// 计算四舍五入的倍数
	roundMultiplier := math.Pow10(decimalPlaces)

	// 保留指定位小数
	return math.Round(random*roundMultiplier) / roundMultiplier
}

// randomNumericString 生成指定长度的数字字符串
// 作用：生成指定长度的数字字符串，用于模拟交易ID等标识符
// 参数：
//   - length: 字符串长度
//
// 返回：指定长度的数字字符串
func RandomNumericString(length int) string {
	if length <= 0 {
		return ""
	}

	const digits = "0123456789"
	b := make([]byte, length)

	// 使用当前时间作为种子
	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)

	// 第一个字符单独处理以避免前导零 - 确保ID的有效性
	b[0] = digits[r.Intn(9)+1] // 1-9

	// 处理剩余字符
	for i := 1; i < length; i++ {
		b[i] = digits[r.Intn(10)] // 0-9
	}

	return string(b)
}

// RoundToDecimalPlaces 四舍五入到指定小数位数
// 作用：对浮点数进行四舍五入，保留指定的小数位数
// 参数：
//   - val: 需要四舍五入的浮点数
//   - precision: 保留的小数位数
//
// 返回：四舍五入后的浮点数
func RoundToDecimalPlaces(val float64, precision uint) float64 {
	ratio := math.Pow(10, float64(precision))
	return math.Round(val*ratio) / ratio
}

// 获取价格涨幅百分比
func GetPriceRange(basePrice float64) (float64, float64) {
	var maxRange, minRange float64

	if basePrice >= 10000 {
		// 高价币种：0.5%-2%的波动
		maxRange = basePrice * 0.0008
		minRange = basePrice * 0.00001
	} else if basePrice >= 1000 {
		// 高价币种：0.5%-2%的波动
		maxRange = basePrice * 0.003
		minRange = basePrice * 0.0001
	} else if basePrice >= 100 {
		// 中价币种：1%-3%的波动
		maxRange = basePrice * 0.008
		minRange = basePrice * 0.001
	} else if basePrice >= 1 {
		// 中价币种：1%-3%的波动
		maxRange = basePrice * 0.01
		minRange = basePrice * 0.001
	} else {
		// 超低价币种：2%-0.3%的波动
		maxRange = basePrice * 0.02
		minRange = basePrice * 0.003
	}

	return maxRange, minRange
}
