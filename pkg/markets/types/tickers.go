package types

import (
	"context"
	"encoding/json"

	"github.com/redis/rueidis"
)

// Tickers 所有产品行情信息
type Tickers struct {
	Data []*Ticker `json:"data"`
}

// Ticker 产品行情信息
type Ticker struct {
	Symbol    string  `json:"symbol"`    // 产品标识
	Last      float64 `json:"last"`      // 最新成交价
	LastSz    float64 `json:"lastSz"`    // 最新成交的数量
	Open24h   float64 `json:"open24h"`   // 24小时开盘价
	High24h   float64 `json:"high24h"`   // 24小时最高价
	Low24h    float64 `json:"low24h"`    // 24小时最低价
	VolCcy24h float64 `json:"volCcy24h"` // 24小时成交额
	Vol24h    float64 `json:"vol24h"`    // 24小时成交量
	Ts        int64   `json:"ts"`        // 数据产生时间
}

// GetSymbol 获取指定symbol的ticker数据
func (t *Tickers) GetSymbol(symbol string) (int, *Ticker) {
	for i, v := range t.Data {
		if v.Symbol == symbol {
			return i, v
		}
	}
	return -1, nil
}

// GetLastPrice 获取最新成交价
func (t *Tickers) GetLastPrice(symbol string) float64 {
	for _, v := range t.Data {
		if v.Symbol == symbol {
			return v.Last
		}
	}
	return 0
}

// GetCacheTickers 获取缓存数据
func GetCacheTickers(client rueidis.Client, redisKey string) *Tickers {
	resp := client.Do(context.Background(), client.B().Get().Key(redisKey).Build())
	data, err := resp.ToString()
	if err != nil || data == "" {
		return nil
	}

	var tickers Tickers
	err = json.Unmarshal([]byte(data), &tickers)
	if err != nil {
		return nil
	}

	return &tickers
}

// SetCacheTicker 设置缓存数据
func SetCacheTicker(client rueidis.Client, redisKey string, ticker *Ticker) error {
	oldTickers := GetCacheTickers(client, redisKey)
	if oldTickers == nil {
		oldTickers = &Tickers{Data: []*Ticker{}}
		oldTickers.Data = append(oldTickers.Data, ticker)
	} else {
		index, _ := oldTickers.GetSymbol(ticker.Symbol)
		if index > -1 {
			oldTickers.Data[index] = ticker
		}
	}

	data, err := json.Marshal(oldTickers)
	if err != nil {
		return err
	}

	return client.Do(context.Background(), client.B().Set().Key(redisKey).Value(string(data)).Build()).Error()
}

// SetCacheTickers 设置缓存数据
func SetCacheTickers(client rueidis.Client, redisKey string, tickers *Tickers) error {
	data, err := json.Marshal(tickers)
	if err != nil {
		return err
	}

	return client.Do(context.Background(), client.B().Set().Key(redisKey).Value(string(data)).Build()).Error()
}
