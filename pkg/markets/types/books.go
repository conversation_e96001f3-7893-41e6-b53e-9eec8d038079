package types

import (
	"math/rand"
	"strconv"
)

// Books 深度数据
type Books struct {
	Asks [][]string `json:"asks"` // 卖方深度
	Bids [][]string `json:"bids"` // 买方深度
	Ts   string     `json:"ts"`   // 时间戳
}

// GenerateRandomBooks 生成随机深度数据
func GenerateRandomBooks(ticker *Ticker, depth int) *Books {
	books := &Books{
		Asks: make([][]string, 0),
		Bids: make([][]string, 0),
	}

	askPrice := ticker.Last
	bidPrice := ticker.Last
	for i := 0; i < depth; i++ {
		askPrice += generatePriceChange(askPrice, true)
		bidPrice += generatePriceChange(bidPrice, false)
		askPriceStr := strconv.FormatFloat(askPrice, 'f', 2, 64)
		bidPriceStr := strconv.FormatFloat(bidPrice, 'f', 2, 64)

		// 根据价格生成相应的数量：价格越高数量越小，价格越低数量越大
		askAmount := generateAmountByPrice(ticker.Last)
		bidAmount := generateAmountByPrice(ticker.Last)
		askAmountStr := strconv.FormatFloat(askAmount, 'f', 6, 64)
		bidAmountStr := strconv.FormatFloat(bidAmount, 'f', 6, 64)

		books.Asks = append(books.Asks, []string{
			askPriceStr,
			askAmountStr,
		})

		books.Bids = append(books.Bids, []string{
			bidPriceStr,
			bidAmountStr,
		})
	}
	return books
}

// generateAmountByPrice 根据价格生成数量
func generateAmountByPrice(price float64) float64 {
	var minMoney float64 = 100
	var maxMoney float64 = 10000

	minAmount := minMoney / price
	maxAmount := maxMoney / price

	amount := minAmount + rand.Float64()*(maxAmount-minAmount)
	return float64(amount)
}

// generatePriceChange 生成限制涨幅的价格变化量
// 作用：生成买盘或卖盘价格的变化量，如果变化量超过30则自动降低百分比
// 参数：
//   - basePrice: 基准价格
//   - isAsk: true为买盘（向上），false为卖盘（向下）
//
// 返回：价格变化量（正数表示增加，负数表示减少）
func generatePriceChange(basePrice float64, isAsk bool) float64 {
	// 定义百分比层级：1%, 0.1%, 0.01%, 0.001%
	percentLevels := []float64{0.01, 0.001, 0.0001, 0.00001}

	// 最大允许变化量
	maxChange := 30.0

	var priceChange float64

	// 从最高百分比开始尝试，如果变化量超过30则降低到下一级
	for _, percent := range percentLevels {
		priceChange = basePrice * percent

		// 如果变化量不超过30，使用当前百分比
		if priceChange <= maxChange {
			break
		}
	}

	// 如果所有百分比都超过30，使用最后一个（最小的）
	if priceChange > maxChange {
		selectedPercent := percentLevels[len(percentLevels)-1]
		priceChange = basePrice * selectedPercent
	}

	// 添加一些随机性，在选定百分比范围内随机
	randomFactor := 0.5 + rand.Float64()*0.5 // 0.5 到 1.0 之间的随机因子
	priceChange = priceChange * randomFactor

	if isAsk {
		// 买盘：返回正数变化量（价格向上）
		return priceChange
	} else {
		// 卖盘：返回负数变化量（价格向下）
		return -priceChange
	}
}
