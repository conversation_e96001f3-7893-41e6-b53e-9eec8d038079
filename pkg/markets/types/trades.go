package types

import (
	"math/rand"
	"strconv"
	"time"
)

// Trade 交易数据
type Trade struct {
	InstID  string `json:"instId"`  // 产品ID
	TradeID string `json:"tradeId"` // 交易ID
	Px      string `json:"px"`      // 价格
	Sz      string `json:"sz"`      // 数量
	Side    string `json:"side"`    // 方向
	Ts      string `json:"ts"`      // 时间戳
	Count   string `json:"count"`   // 成交笔数
}

// GenerateRandomTrades 生成随机交易数据
func GenerateRandomTrades(ticker *Ticker, limit int) []*Trade {
	trades := make([]*Trade, 0)

	for i := 0; i < limit; i++ {
		side := "sell"
		px := ticker.Last + generatePriceChange(ticker.Last, false)
		if rand.Intn(2) == 0 {
			side = "buy"
			px = ticker.Last + generatePriceChange(ticker.Last, true)
		}
		trades = append(trades, &Trade{
			InstID:  ticker.Symbol,
			TradeID: RandomNumericString(12),
			Px:      strconv.FormatFloat(px, 'f', 2, 64),
			Sz:      strconv.FormatFloat(generateAmountByPrice(ticker.Last), 'f', 6, 64),
			Side:    side,
			Ts:      strconv.FormatInt(time.Now().UnixMilli(), 10),
			Count:   strconv.FormatInt(int64(rand.Intn(100)), 10),
		})
	}
	return trades
}
