package types

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/rueidis"
)

// KlineStorage K线数据存储服务
type KlineStorage struct {
	client rueidis.Client
}

// NewKlineStorage 创建K线数据存储服务
func NewKlineStorage(client rueidis.Client) *KlineStorage {
	return &KlineStorage{
		client: client,
	}
}

// StoreKline 存储K线数据
func (ks *KlineStorage) InitKlines(ctx context.Context, klines []*Kline) error {
	if len(klines) == 0 {
		return nil
	}

	// 如果存在数据, 则不更新
	allKlines, err := ks.GetAllKlines(ctx, klines[0].Symbol)
	if err != nil {
		return fmt.Errorf("get all klines failed: %w", err)
	}
	if len(allKlines) > 0 {
		return nil
	}

	redisKey := ks.getKey(klines[0].Symbol)

	// 使用 DoMulti 进行批量操作
	cmds := make(rueidis.Commands, 0, len(klines))
	for _, kline := range klines {
		// 将Kline序列化为JSON字符串
		klineData, err := json.Marshal(kline)
		if err != nil {
			return fmt.Errorf("marshal kline failed: %w", err)
		}

		// 使用时间戳作为score，JSON数据作为member
		cmds = append(cmds, ks.client.B().Zadd().Key(redisKey).ScoreMember().ScoreMember(float64(kline.CreatedAt), string(klineData)).Build())
	}

	for _, result := range ks.client.DoMulti(ctx, cmds...) {
		if err := result.Error(); err != nil {
			return err
		}
	}
	return nil
}

// AddKlines 添加K线数据
func (ks *KlineStorage) AddKlines(ctx context.Context, symbol string, klines []*Kline) error {
	if len(klines) == 0 {
		return nil
	}

	// 如果存在数据, 则不更新
	allKlines, err := ks.GetAllKlines(ctx, symbol)
	if err != nil {
		return fmt.Errorf("get all klines failed: %w", err)
	}

	// 如果数据存在, 那么不添加
	hasKlineFunc := func(kline *Kline) bool {
		for _, k := range allKlines {
			if k.CreatedAt == kline.CreatedAt {
				return true
			}
		}
		return false
	}

	key := ks.getKey(symbol)
	cmds := make(rueidis.Commands, 0, len(klines))
	for _, kline := range klines {
		if hasKlineFunc(kline) {
			continue
		}

		// 将Kline序列化为JSON字符串
		klineData, err := json.Marshal(kline)
		if err != nil {
			return fmt.Errorf("marshal kline failed: %w", err)
		}

		cmds = append(cmds, ks.client.B().Zadd().Key(key).ScoreMember().ScoreMember(float64(kline.CreatedAt), string(klineData)).Build())
	}

	for _, result := range ks.client.DoMulti(ctx, cmds...) {
		if err := result.Error(); err != nil {
			return err
		}
	}
	return nil
}

// GetKlines 获取指定交易对的K线数据
func (ks *KlineStorage) GetKlines(ctx context.Context, symbol string, bar LineBar, limit int64) ([]*Kline, error) {
	// 根据bar类型计算时间间隔（秒）
	var interval int64
	switch bar {
	case Bar1m:
		interval = 60
	case Bar5m:
		interval = 5 * 60
	case Bar15m:
		interval = 15 * 60
	case Bar1h:
		interval = 60 * 60
	case Bar1d:
		interval = 24 * 60 * 60
	case Bar1w:
		interval = 7 * 24 * 60 * 60
	default:
		return nil, fmt.Errorf("unsupported bar type: %s", bar)
	}

	// 获取这些时间间隔的开始时间 全部数据
	startTime := time.Now().Add(-time.Duration(interval*limit) * time.Second).Truncate(time.Minute)
	endTime := time.Now().Truncate(time.Minute)
	latestKlines, err := ks.GetLatestKlinesOptimized(ctx, symbol, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("get latest kline failed: %w", err)
	}

	// 按时间间隔分组
	timeGroups := make(map[int64][]*Kline)
	timeGroupKeys := make([]int64, 0)
	for i := 0; i < len(latestKlines); i++ {
		groupTime := latestKlines[i].CreatedAt - latestKlines[i].CreatedAt%interval
		timeGroups[groupTime] = append(timeGroups[groupTime], latestKlines[i])
		if len(timeGroups[groupTime]) == 1 {
			timeGroupKeys = append(timeGroupKeys, groupTime)
		}
	}

	// 聚合每个时间组的K线数据
	newKlines := make([]*Kline, 0)
	for _, groupTime := range timeGroupKeys {
		newKlines = append(newKlines, ks.mergeKlines(timeGroups[groupTime], groupTime))
	}
	return newKlines, nil
}

// mergeKlines 合并多个K线数据为一个
func (ks *KlineStorage) mergeKlines(klines []*Kline, groupTime int64) *Kline {
	if len(klines) == 0 {
		return nil
	}

	// 使用第一个K线的基本信息
	merged := &Kline{
		Symbol:     klines[0].Symbol,
		OpenPrice:  klines[0].OpenPrice,  // 开盘价：第一个K线的开盘价
		HighPrice:  klines[0].HighPrice,  // 最高价：所有K线中的最高价
		LowsPrice:  klines[0].LowsPrice,  // 最低价：所有K线中的最低价
		ClosePrice: klines[0].ClosePrice, // 收盘价：最后一个K线的收盘价
		Vol:        0,                    // 成交量：所有K线成交量之和
		Amount:     0,                    // 成交额：所有K线成交额之和
		CreatedAt:  groupTime,            // 时间：分组时间
	}

	// 计算聚合值
	for _, kline := range klines {
		// 更新最高价和最低价
		if kline.HighPrice > merged.HighPrice {
			merged.HighPrice = kline.HighPrice
		}
		if kline.LowsPrice < merged.LowsPrice {
			merged.LowsPrice = kline.LowsPrice
		}

		// 累加成交量和成交额
		merged.Vol += kline.Vol
		merged.Amount += kline.Amount
	}

	// 更新收盘价为最后一个K线的收盘价
	merged.ClosePrice = klines[len(klines)-1].ClosePrice

	return merged
}

// GetLatestKlinesOptimized 获取最新的N条数据（优化版本）
func (ks *KlineStorage) GetLatestKlinesOptimized(ctx context.Context, symbol string, startTime, endTime time.Time) ([]*Kline, error) {
	key := ks.getKey(symbol)

	// 使用ZREVRANGEBYSCORE，从+inf到-inf，限制数量
	result, err := ks.client.Do(ctx, ks.client.B().Zrevrangebyscore().Key(key).Max(strconv.FormatInt(endTime.Unix(), 10)).Min(strconv.FormatInt(startTime.Unix(), 10)).Build()).AsStrSlice()
	if err != nil {
		return nil, fmt.Errorf("get latest klines failed: %w", err)
	}

	var klines []*Kline
	for i := 0; i < len(result); i++ {
		var kline Kline
		if err := json.Unmarshal([]byte(result[i]), &kline); err != nil {
			continue
		}
		klines = append(klines, &kline)
	}

	return klines, nil
}

// GetLatestKline 获取指定交易对的最大时间戳（最新数据的时间）
func (ks *KlineStorage) GetLatestKline(ctx context.Context, symbol string) (*Kline, error) {
	key := ks.getKey(symbol)

	// 使用ZREVRANGEBYSCORE获取最大的score（时间戳）
	result, err := ks.client.Do(ctx, ks.client.B().Zrevrangebyscore().Key(key).Max("+inf").Min("-inf").Withscores().Limit(0, 1).Build()).AsZScores()
	if err != nil {
		return nil, fmt.Errorf("get max timestamp failed: %w", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("no kline data found for symbol: %s", symbol)
	}

	// 解析JSON数据
	var kline Kline
	if err := json.Unmarshal([]byte(result[0].Member), &kline); err != nil {
		return nil, fmt.Errorf("unmarshal kline data failed: %w", err)
	}

	return &kline, nil
}

// GetLatestKlineBeforeTime 获取小于指定时间的数据
func (ks *KlineStorage) GetLatestKlineBeforeTime(ctx context.Context, symbol string, beforeTime int64) ([]*Kline, error) {
	var klines []*Kline
	key := ks.getKey(symbol)
	result, err := ks.client.Do(ctx, ks.client.B().Zrevrangebyscore().Key(key).Max("+inf").Min(fmt.Sprintf("%d", beforeTime)).Withscores().Build()).AsZScores()
	if err != nil {
		return nil, fmt.Errorf("get latest kline before time failed: %w", err)
	}
	if len(result) == 0 {
		return klines, nil
	}

	for _, item := range result {
		var kline Kline
		if err := json.Unmarshal([]byte(item.Member), &kline); err != nil {
			continue
		}
		klines = append(klines, &kline)
	}

	return klines, nil
}

// GetLatestKlineBeforeNow 获取小于当前时间的最新一条数据
func (ks *KlineStorage) GetLatestTicker(ctx context.Context, symbol string) (*Ticker, error) {
	key := ks.getKey(symbol)

	// 获取今日0点时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()
	currentTime := time.Now().Truncate(time.Minute).Unix()

	// 使用ZREVRANGEBYSCORE获取小于当前时间的最大数据
	result, err := ks.client.Do(ctx, ks.client.B().Zrevrangebyscore().Key(key).Max(fmt.Sprintf("%d", currentTime)).Min(fmt.Sprintf("%d", startTime)).Withscores().Build()).AsZScores()
	if err != nil {
		return nil, fmt.Errorf("get latest kline before now failed: %w", err)
	}
	if len(result) == 0 {
		return nil, fmt.Errorf("no kline data found before current time for symbol: %s", symbol)
	}

	ticker := &Ticker{}
	for i := 0; i < len(result); i++ {
		var kline *Kline
		if err := json.Unmarshal([]byte(result[i].Member), &kline); err != nil {
			continue
		}

		// 如果第一个数据，则设置开盘价
		if i == 0 {
			ticker.Symbol = symbol
			ticker.Open24h = kline.OpenPrice
			ticker.High24h = kline.HighPrice
			ticker.Low24h = kline.LowsPrice
			ticker.VolCcy24h = kline.Vol
			ticker.Vol24h = kline.Amount
			ticker.Ts = kline.CreatedAt
		} else {
			ticker.Last = kline.ClosePrice
			ticker.LastSz = kline.Vol

			if kline.HighPrice > ticker.High24h {
				ticker.High24h = kline.HighPrice
			}
			if kline.LowsPrice < ticker.Low24h {
				ticker.Low24h = kline.LowsPrice
			}
			ticker.VolCcy24h += kline.Vol
			ticker.Vol24h += kline.Amount
		}
	}

	return ticker, nil
}

// DeleteKlinesBeforeTime 删除指定时间之前的K线数据
func (ks *KlineStorage) DeleteKlinesBeforeTime(ctx context.Context, symbol string, beforeTime int64) error {
	key := ks.getKey(symbol)

	// 删除指定时间之前的数据
	_, err := ks.client.Do(ctx, ks.client.B().Zremrangebyscore().Key(key).Min("0").Max(fmt.Sprintf("%d", beforeTime)).Build()).AsInt64()
	return err
}

// DeleteKlinesAfterTime 删除指定时间之后的K线数据
func (ks *KlineStorage) DeleteKlinesAfterTime(ctx context.Context, symbol string, afterTime int64) error {
	key := ks.getKey(symbol)

	// 删除指定时间之后的数据，使用 +inf 表示正无穷
	_, err := ks.client.Do(ctx, ks.client.B().Zremrangebyscore().Key(key).Min(fmt.Sprintf("%d", afterTime)).Max("+inf").Build()).AsInt64()
	return err
}

// GetAllKlines 获取指定交易对的所有K线数据
func (ks *KlineStorage) GetAllKlines(ctx context.Context, symbol string) ([]*Kline, error) {
	key := ks.getKey(symbol)

	// 使用ZRANGEBYSCORE获取所有成员和分数，从负无穷到正无穷
	result, err := ks.client.Do(ctx, ks.client.B().Zrangebyscore().Key(key).Min("-inf").Max("+inf").Withscores().Build()).AsZScores()
	if err != nil {
		return nil, fmt.Errorf("get all klines failed: %w", err)
	}

	var klines []*Kline
	for _, item := range result {
		var kline Kline
		if err := json.Unmarshal([]byte(item.Member), &kline); err != nil {
			continue
		}
		klines = append(klines, &kline)
	}

	return klines, nil
}

// getKey 生成Redis key
func (ks *KlineStorage) getKey(symbol string) string {
	return fmt.Sprintf("custom_kline:%s", symbol)
}
