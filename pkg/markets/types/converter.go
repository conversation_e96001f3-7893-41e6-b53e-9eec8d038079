package types

import (
	"fmt"
	"strconv"
)

// MapConverter map数据转换器
type MapConverter struct {
	data   map[string]string
	errors []error
}

// NewMapConverter 创建转换器
func NewMapConverter(data map[string]string) *MapConverter {
	return &MapConverter{
		data:   data,
		errors: make([]error, 0),
	}
}

// GetFloat64 获取float64值
func (c *MapConverter) GetFloat64(key string) float64 {
	value, exists := c.data[key]
	if !exists {
		c.errors = append(c.errors, fmt.Errorf("key '%s' not found", key))
		return 0
	}

	result, err := strconv.ParseFloat(value, 64)
	if err != nil {
		c.errors = append(c.errors, fmt.Errorf("parse float64 '%s': %w", key, err))
		return 0
	}

	return result
}

// GetInt64 获取int64值
func (c *MapConverter) GetInt64(key string) int64 {
	value, exists := c.data[key]
	if !exists {
		c.errors = append(c.errors, fmt.<PERSON><PERSON><PERSON>("key '%s' not found", key))
		return 0
	}

	result, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		c.errors = append(c.errors, fmt.Errorf("parse int64 '%s': %w", key, err))
		return 0
	}

	return result
}

// GetString 获取字符串值
func (c *MapConverter) GetString(key string) string {
	return c.data[key]
}

// HasErrors 检查是否有错误
func (c *MapConverter) HasErrors() bool {
	return len(c.errors) > 0
}

// GetErrors 获取所有错误
func (c *MapConverter) GetErrors() []error {
	return c.errors
}

// GetFirstError 获取第一个错误
func (c *MapConverter) GetFirstError() error {
	if len(c.errors) > 0 {
		return c.errors[0]
	}
	return nil
}
