package okex

import "encoding/json"

const (
	SubscribeChannelTickers = "tickers" // 行情
	SubscribeChannelBooks   = "books"   // 深度
	SubscribeChannelTrades  = "trades"  // 成交
)

// SubscribeReq 订阅请求
type SubscribeReq struct {
	Op   string `json:"op"`
	Args []*Arg `json:"args"`
}

// SubscribeRes 订阅结果
type SubscribeRes struct {
	Arg  *Arg            `json:"arg"`
	Data json.RawMessage `json:"data"`
}

// Arg 订阅参数
type Arg struct {
	InstId  string `json:"instId"`
	Channel string `json:"channel"`
}

// Resp 响应
type Resp struct {
	Code string          `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}
