package okex

import (
	"errors"
	"fmt"
	"gin/pkg/markets/types"
	"gin/pkg/net"
	"gin/pkg/socket/client"
	"strconv"
	"time"

	"github.com/goccy/go-json"
)

// Okex 实现Markets接口
type Okex struct {
	wwsURL         string                                                               // 公共WebSocket URL
	restURL        string                                                               // 公共REST URL
	wsClient       client.SocketClientInterface                                         // WebSocket客户端
	messageHandler func(platform types.PlatformSymbol, channel string, msg interface{}) // 订阅消息的转发方法
}

// NewOkex 创建一个新的Okex实例
func NewOkex() types.Markets {
	return &Okex{
		wwsURL:         "wss://ws.okx.com/ws/v5/public",
		restURL:        "https://www.okx.com",
		wsClient:       nil,
		messageHandler: nil,
	}
}

// Subscribe 订阅数据
func (o *Okex) Subscribe(subscribe []*types.Subscribe) error {
	if o.wsClient == nil {
		return errors.New("websocket client is not connected")
	}
	args := make([]*Arg, 0)
	for _, v := range subscribe {
		args = append(args, &Arg{
			InstId:  v.Symbol,
			Channel: v.Channel,
		})
	}

	jsonBytes, err := json.Marshal(&SubscribeReq{
		Op:   "subscribe",
		Args: args,
	})
	if err != nil {
		return err
	}

	// 发送订阅消息
	return o.wsClient.SendMessage(jsonBytes)
}

// Unsubscribe 取消订阅
func (o *Okex) Unsubscribe(subscribe []*types.Subscribe) error {
	if o.wsClient == nil {
		return errors.New("websocket client is not connected")
	}
	args := make([]*Arg, len(subscribe))
	for _, v := range subscribe {
		args = append(args, &Arg{
			InstId:  v.Symbol,
			Channel: v.Channel,
		})
	}

	jsonBytes, err := json.Marshal(&SubscribeReq{
		Op:   "unsubscribe",
		Args: args,
	})
	if err != nil {
		return err
	}

	// 发送取消订阅消息
	return o.wsClient.SendMessage(jsonBytes)
}

// UpdateTargetPrice 更新目标价格
func (o *Okex) UpdateTargetPrice(symbol string, targetTime time.Time, targetPrice float64) error {
	return nil
}

// SetMessageHandler 设置订阅消息处理函数
func (o *Okex) SetMessageHandler(messageHandler func(platform types.PlatformSymbol, channel string, msg interface{})) types.Markets {
	o.messageHandler = messageHandler
	return o
}

// SetData 设置数据
func (o *Okex) SetData(_ *types.MarketsData) types.Markets {
	return o
}

// Start 启动市场
func (o *Okex) Start() error {
	o.wsClient = client.NewSocketClient(o.wwsURL)

	// 设置消息的处理方法
	o.wsClient.SetWebSocketMessageFunc(func(msg []byte) error {
		subscribeResult := &SubscribeRes{}
		err := json.Unmarshal(msg, subscribeResult)
		if err != nil {
			return err
		}
		switch subscribeResult.Arg.Channel {
		case SubscribeChannelTickers:
			// 行情
			var rawData []map[string]string
			err = json.Unmarshal(subscribeResult.Data, &rawData)
			if err == nil && len(rawData) > 0 {
				converter := types.NewMapConverter(rawData[0])
				data := &types.Ticker{}
				data.Last = converter.GetFloat64("last")
				data.LastSz = converter.GetFloat64("lastSz")
				data.Open24h = converter.GetFloat64("open24h")
				data.High24h = converter.GetFloat64("high24h")
				data.Low24h = converter.GetFloat64("low24h")
				data.Vol24h = converter.GetFloat64("vol24h")
				data.VolCcy24h = converter.GetFloat64("volCcy24h")
				data.Ts = converter.GetInt64("ts")
				data.Symbol = converter.GetString("instId")

				// 如果数据不为空, 那么传递 redis 通道统一处理数据
				if o.messageHandler != nil {
					o.messageHandler(types.PlatformSymbolOkex, subscribeResult.Arg.Channel, data)
				}
			}
		case SubscribeChannelBooks:
			// 深度
			data := make([]*types.Books, 0)
			err = json.Unmarshal(subscribeResult.Data, &data)
			if err == nil && len(data) > 0 {
				// 如果数据不为空, 那么传递 redis 通道统一处理数据
				if o.messageHandler != nil {
					o.messageHandler(types.PlatformSymbolOkex, subscribeResult.Arg.Channel, data)
				}
			}
		case SubscribeChannelTrades:
			// 成交
			data := make([]*types.Trade, 0)
			err = json.Unmarshal(subscribeResult.Data, &data)
			if err == nil && len(data) > 0 {
				// 如果数据不为空, 那么传递 redis 通道统一处理数据
				if o.messageHandler != nil {
					o.messageHandler(types.PlatformSymbolOkex, subscribeResult.Arg.Channel, data)
				}
			}
		}
		return nil
	})

	// 启动WebSocket客户端
	o.wsClient.Connect()
	return nil
}

// GetTickers 获取所有产品行情信息
func (o *Okex) GetTickers() (*types.Tickers, error) {
	params := map[string]string{
		"instType": "SPOT",
	}

	// 返回数据
	res := struct {
		Code string              `json:"code"`
		Msg  string              `json:"msg"`
		Data []map[string]string `json:"data"`
	}{}
	resp, err := net.NewHttpClient(o.restURL).SetParams(params).Get("/api/v5/market/tickers")
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp.Body, &res)
	if err != nil {
		return nil, err
	}

	if res.Code != "0" {
		return nil, errors.New(res.Msg)
	}

	data := &types.Tickers{Data: make([]*types.Ticker, 0, len(res.Data))}

	for _, v := range res.Data {
		converter := types.NewMapConverter(v)
		ticker := &types.Ticker{
			Symbol:    converter.GetString("instId"),
			Last:      converter.GetFloat64("last"),
			LastSz:    converter.GetFloat64("lastSz"),
			Open24h:   converter.GetFloat64("open24h"),
			High24h:   converter.GetFloat64("high24h"),
			Low24h:    converter.GetFloat64("low24h"),
			Vol24h:    converter.GetFloat64("vol24h"),
			VolCcy24h: converter.GetFloat64("volCcy24h"),
			Ts:        converter.GetInt64("ts"),
		}

		data.Data = append(data.Data, ticker)
	}

	return data, nil
}

// GetTicker 获取指定产品行情信息
func (o *Okex) GetTicker(symbol string) (*types.Ticker, error) {
	params := map[string]string{
		"instId": symbol,
	}

	res := struct {
		Code string              `json:"code"`
		Msg  string              `json:"msg"`
		Data []map[string]string `json:"data"`
	}{}
	resp, err := net.NewHttpClient(o.restURL).SetParams(params).Get("/api/v5/market/ticker")
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp.Body, res)
	if err != nil {
		return nil, err
	}

	if res.Code != "0" {
		return nil, errors.New(res.Msg)
	}

	converter := types.NewMapConverter(res.Data[0])

	return &types.Ticker{
		Symbol:    converter.GetString("instId"),
		Last:      converter.GetFloat64("last"),
		LastSz:    converter.GetFloat64("lastSz"),
		Open24h:   converter.GetFloat64("open24h"),
		High24h:   converter.GetFloat64("high24h"),
		Low24h:    converter.GetFloat64("low24h"),
		Vol24h:    converter.GetFloat64("vol24h"),
		VolCcy24h: converter.GetFloat64("volCcy24h"),
		Ts:        converter.GetInt64("ts"),
	}, nil
}

// GetKline 获取K线数据
func (o *Okex) GetKline(symbol string, bar types.LineBar, limit int) ([]*types.Kline, error) {
	params := map[string]string{
		"instId": symbol,
		"bar":    string(bar),
		"limit":  strconv.Itoa(limit),
	}

	res := &Resp{}
	resp, err := net.NewHttpClient(o.restURL).SetParams(params).Get("/api/v5/market/candles")
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp.Body, res)
	if err != nil {
		return nil, err
	}

	// 如果返回码不是0, 则返回错误
	if res.Code != "0" {
		return nil, errors.New(res.Msg)
	}

	// 将数据转换为[][]string
	var rawData [][]string
	err = json.Unmarshal(res.Data, &rawData)
	if err != nil {
		return nil, err
	}

	// 处理数据
	data := make([]*types.Kline, 0, len(rawData))
	for _, rows := range rawData {
		if len(rows) < 8 {
			continue
		}

		converter := types.NewMapConverter(map[string]string{
			"timestamp": rows[0],
			"open":      rows[1],
			"high":      rows[2],
			"low":       rows[3],
			"close":     rows[4],
			"volume":    rows[6],
			"amount":    rows[7],
		})

		kline := &types.Kline{
			Symbol:     symbol,
			OpenPrice:  converter.GetFloat64("open"),
			HighPrice:  converter.GetFloat64("high"),
			LowsPrice:  converter.GetFloat64("low"),
			ClosePrice: converter.GetFloat64("close"),
			Vol:        converter.GetFloat64("volume"),
			Amount:     converter.GetFloat64("amount"),
			CreatedAt:  converter.GetInt64("timestamp") / 1000,
		}

		data = append(data, kline)
	}

	return data, nil
}

// GetBooks 获取深度数据
func (o *Okex) GetBooks(symbol string, depth int) (*types.Books, error) {
	params := map[string]string{
		"instId": symbol,
		"sz":     strconv.Itoa(depth),
	}

	// 返回数据
	res := &Resp{}
	resp, err := net.NewHttpClient(o.restURL).SetParams(params).Get("/api/v5/market/books")
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp.Body, res)
	if err != nil {
		return nil, err
	}

	// 如果返回码不是0, 则返回错误
	if res.Code != "0" {
		return nil, errors.New(res.Msg)
	}

	// 将数据转换为[]*types.Books
	var rawData []*types.Books
	err = json.Unmarshal(res.Data, &rawData)
	if err != nil {
		return nil, err
	}

	if len(rawData) == 0 {
		return nil, fmt.Errorf("no books data returned")
	}

	return rawData[0], nil
}

// GetTrades 获取交易数据
func (o *Okex) GetTrades(symbol string, limit int) ([]*types.Trade, error) {
	params := map[string]string{
		"instId": symbol,
		"limit":  strconv.Itoa(limit),
	}

	// 返回数据
	res := &Resp{}
	resp, err := net.NewHttpClient(o.restURL).SetParams(params).Get("/api/v5/market/trades")
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp.Body, res)
	if err != nil {
		return nil, err
	}

	// 如果返回码不是0, 则返回错误
	if res.Code != "0" {
		return nil, errors.New(res.Msg)
	}

	// 将数据转换为[]*types.Trade
	var rawData []*types.Trade
	err = json.Unmarshal(res.Data, &rawData)
	if err != nil {
		return nil, err
	}

	return rawData, nil
}
