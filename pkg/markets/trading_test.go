package markets

import (
	"fmt"
	"gin/pkg/markets/trading"
	"gin/pkg/markets/types"
	"github.com/goccy/go-json"
	"github.com/redis/rueidis"
	"testing"
	"time"
)

func TestTradingWebsocket(t *testing.T) {
	// 外汇数据｜期货数据 - 到中国时间 周六 05:00

	client, err := rueidis.NewClient(rueidis.ClientOption{
		InitAddress:      []string{fmt.Sprintf("%s:%d", "localhost", 6379)},
		SelectDB:         15,
		ConnWriteTimeout: time.Second * 5,
		DisableCache:     true, // 禁用客户端缓存
	})

	if err != nil {
		panic(err)
	}

	trading := trading.NewTrading(client)
	trading.SetData(&types.MarketsData{
		//Subscribes: []string{"eurusd:cur", "gbpusd:cur", "audusd:cur", "nzdusd:cur", "usdjpy:cur", "usdcny:cur", "usdchf:cur", "usdmxn:cur", "xauusd:cur"},
		Subscribes: []string{"BTCUSD:CUR"},
	})

	trading.SetMessageHandler(func(platform types.PlatformSymbol, channel string, msg interface{}) {
		fmt.Println("平台:", platform, "通道:", channel, "数据:", msg)
	})

	// 阻塞
	trading.Start()
	select {}
}

// 测试深度数据
func TestTrading(t *testing.T) {
	// 初始化Redis
	client, err := rueidis.NewClient(rueidis.ClientOption{
		InitAddress:      []string{fmt.Sprintf("%s:%d", "localhost", 6379)},
		SelectDB:         15,
		ConnWriteTimeout: time.Second * 5,
		DisableCache:     true, // 禁用客户端缓存
	})
	if err != nil {
		panic(err)
	}

	trading := trading.NewTrading(client)
	kline, err := trading.GetKline("BTCUSD:CUR", "1h", 100)
	if err != nil {
		panic(err)
	}

	// 打印数据
	for _, v := range kline {
		fmt.Println(v)
	}

	// 获取深度数据
	books, err := trading.GetBooks("BTCUSD:CUR", 100)
	if err != nil {
		panic(err)
	}

	jsonBytes, _ := json.Marshal(books)
	fmt.Println("books", string(jsonBytes))

	// 获取交易数据
	trades, err := trading.GetTrades("BTCUSD:CUR", 100)
	if err != nil {
		panic(err)
	}

	jsonBytes, _ = json.Marshal(trades)
	fmt.Println("trades", string(jsonBytes))
}
