package markets

import (
	"fmt"
	"gin/pkg/markets/okex"
	"gin/pkg/markets/types"
	"testing"
)

// TestOkexWebsocket 测试Okex的WebSocket连接
func TestOkexWebsocket(t *testing.T) {
	okx := okex.NewOkex()

	// 设置消息处理器来捕获数据
	okx.SetMessageHandler(func(platform types.PlatformSymbol, channel string, msg interface{}) {
		fmt.Printf("收到数据 - 平台: %v, 频道: %s\n", platform, channel)
		fmt.Println(msg)
	})

	// 启动连接
	err := okx.Start()
	if err != nil {
		fmt.Printf("启动Okex失败: %v", err)
	}

	// 订阅数据
	err = okx.Subscribe([]*types.Subscribe{
		{Channel: okex.SubscribeChannelTickers, Symbol: "BTC-USDT"},
		{Channel: okex.SubscribeChannelBooks, Symbol: "BTC-USDT"},
		{Channel: okex.SubscribeChannelTrades, Symbol: "BTC-USDT"},
	})
	if err != nil {
		fmt.Printf("订阅数据失败: %v", err)
	}

	select {}
}

// TestOkex 测试 okex
func TestOkex(t *testing.T) {
	okx := okex.NewOkex()

	// 获取行情
	tickers, err := okx.GetTickers()
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("BTC-USDT 行情", tickers.GetLastPrice("BTC-USDT"))

	// 获取K线数据
	kline, err := okx.GetKline("BTC-USDT", "1m", 100)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("BTC-USDT 1m K线", kline)

	// 获取深度
	books, err := okx.GetBooks("BTC-USDT", 100)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("BTC-USDT 深度", books)

	// 获取交易
	trades, err := okx.GetTrades("BTC-USDT", 100)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("BTC-USDT 交易", trades)
}
