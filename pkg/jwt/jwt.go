package jwt

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

const (
	// TokenBlacklistPrefix 令牌黑名单前缀
	TokenBlacklistPrefix = "token:blacklist:"
	// TokenWhitelistPrefix 令牌白名单前缀
	TokenWhitelistPrefix = "token:whitelist:"
	// ContextUserKey 上下文中用户信息的键
	ContextUserKey = "user"
	// DefaultTokenExpiration 默认令牌过期时间
	DefaultTokenExpiration = 24 * time.Hour
)

// TokenClaims JWT声明结构体
type TokenClaims struct {
	AdminId  uint64 `json:"adminId"`
	UserId   uint64 `json:"userId"`
	IsAdmin  bool   `json:"isAdmin"`
	TenantId uint64 `json:"tenantId"`
	TokenId  string `json:"tokenId"` // 用于标识token，便于缓存管理
	jwt.RegisteredClaims
}

var jwtSecret = []byte("your-secret-key") // 实际项目中应该从配置文件读取

// GenerateToken 生成JWT令牌
func GenerateToken(userId, tenantId uint64, expiration time.Duration) (string, string, error) {
	if expiration == 0 {
		expiration = DefaultTokenExpiration
	}

	// 生成唯一的TokenID
	tokenId := uuid.NewString()

	claims := TokenClaims{
		UserId:   userId,
		TenantId: tenantId,
		TokenId:  tokenId,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(jwtSecret)

	return tokenString, tokenId, err
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrSignatureInvalid
}
