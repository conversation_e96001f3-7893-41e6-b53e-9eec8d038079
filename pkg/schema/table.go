package schema

import "github.com/google/uuid"

// Table 表格配置结构体
type Table struct {
	Key                string                 `json:"key"`                // 标识
	URL                string                 `json:"url"`                // 数据源URL
	UpdateURL          string                 `json:"update_url"`         // 更新数据源URL
	Class              string                 `json:"class"`              // 自定义类名
	SearchForm         *Form                  `json:"searchForm"`         // 搜索表单配置
	InitialSearchState map[string]interface{} `json:"initialSearchState"` // 搜索表单配置
	Toolbar            []*Dialog              `json:"toolbar"`            // 工具栏
	Actions            []*Dialog              `json:"actions"`            // 操作栏
	Columns            []*TableColumn         `json:"columns"`            // 表格列配置
	PaginationKey      Pagination             `json:"paginationKey"`      // 分页配置

}

// NewTable 创建新的表格配置
func NewTable(url string) *Table {
	return &Table{
		Key:                uuid.New().String(),
		URL:                url,
		SearchForm:         &Form{},
		InitialSearchState: make(map[string]interface{}),
		Toolbar:            make([]*Dialog, 0),
		Actions:            make([]*Dialog, 0),
		Columns:            make([]*TableColumn, 0),
		PaginationKey: Pagination{
			Current: "page",
			Size:    "pageSize",
			OrderBy: "orderBy",
			Order:   "order",
		},
	}
}

// AddSearchField 添加搜索字段
func (t *Table) AddSearchField(field ...*FormItem) *Table {
	t.SearchForm.Fields = append(t.SearchForm.Fields, field...)
	t.InitialSearchState = make(map[string]interface{})
	for _, f := range field {
		t.InitialSearchState[f.Prop] = ""
	}
	return t
}

func (t *Table) SetInitialSearchState(state map[string]interface{}) *Table {
	t.InitialSearchState = state
	return t
}

// AddColumn 添加表格列
func (t *Table) AddColumn(column ...*TableColumn) *Table {
	t.Columns = append(t.Columns, column...)
	return t
}

// AddToolbar 添加工具栏
func (t *Table) AddToolbar(dialog ...*Dialog) *Table {
	t.Toolbar = append(t.Toolbar, dialog...)
	return t
}

// AddAction 添加操作栏
func (t *Table) AddAction(dialog ...*Dialog) *Table {
	t.Actions = append(t.Actions, dialog...)
	return t
}

// SetSearchFieldAttrs 设置搜索字段属性
func (t *Table) SetSearchFieldAttrs(field string, attrKey string, attrVal interface{}) *Table {
	ResetFieldAttrs(t.SearchForm.Fields, field, attrKey, attrVal)
	return t
}
