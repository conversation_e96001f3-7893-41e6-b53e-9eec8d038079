package schema

import "fmt"

type Form struct {
	ID     string      `json:"id"`     // 唯一标识
	Label  string      `json:"label"`  // 表单标题
	Class  string      `json:"class"`  // 自定义类名
	Fields []*FormItem `json:"fields"` // 表单字段
}

type FormItemType string

const (
	// 搜索特有
	FormItemTypeInput         FormItemType = "input"         // 输入框
	FormItemTypeSelect        FormItemType = "select"        // 下拉选择框
	FormItemTypeRadio         FormItemType = "radio"         // 单选框
	FormItemTypeCheckbox      FormItemType = "checkbox"      // 多选框
	FormItemTypeDate          FormItemType = "date"          // 日期选择框
	FormItemTypeDateTime      FormItemType = "datetime"      // 日期时间选择框
	FormItemTypeDateRange     FormItemType = "daterange"     // 日期范围选择框
	FormItemTypeDateTimeRange FormItemType = "datetimerange" // 日期时间范围选择框
	FormItemTypeMonth         FormItemType = "month"         // 月份选择框
	FormItemTypeMonthRange    FormItemType = "monthrange"    // 月份范围选择框
	FormItemTypeYear          FormItemType = "year"          // 年份选择框
	FormItemTypeYearRange     FormItemType = "yearrange"     // 年份范围选择框
	FormItemTypeWeek          FormItemType = "week"          // 周选择框
	FormItemTypeTime          FormItemType = "time"          // 时间选择框
	FormItemTypeTimeRange     FormItemType = "timerange"     // 时间范围选择框
	// 通用
	FormItemTypeSwitch    FormItemType = "switch"    // 开关选择框
	FormItemTypeTextarea  FormItemType = "textarea"  // 文本域
	FormItemTypeMarkdown  FormItemType = "markdown"  //  markdown 编辑器
	FormItemTypeUpload    FormItemType = "upload"    // 上传组件
	FormItemTypeRate      FormItemType = "rate"      // 评分组件
	FormItemTypeSlider    FormItemType = "slider"    // 滑动选择器
	FormItemTypeColor     FormItemType = "color"     // 颜色选择器
	FormItemTypeTransfer  FormItemType = "transfer"  // 穿梭选择器
	FormItemTypeTree      FormItemType = "tree"      // 树选择器
	FormItemTypeCascader  FormItemType = "cascader"  // 级联选择器
	FormItemTypeNumber    FormItemType = "number"    // 数字选择器
	FormItemTypeTranslate FormItemType = "translate" // 翻译组件
	FormItemTypeImage     FormItemType = "image"     // 图片上传组件
	FormItemTypeFile      FormItemType = "file"      // 文件上传组件

)

// FormItem 表单项
type FormItem struct {
	Label    string                 `json:"label"`    // 标题
	Prop     string                 `json:"prop"`     // 映射
	Type     FormItemType           `json:"type"`     // 类型
	Disabled bool                   `json:"disabled"` // 是否禁用
	Required bool                   `json:"required"` // 是否必填
	Config   map[string]interface{} `json:"config"`   // 额外配置项
	Rules    []*ValidationRule      `json:"rules,omitempty"`
	Options  []*Option              `json:"options,omitempty"`
	Span     int                    `json:"span"`           // 每列的宽度
	Show     *ShowCondition         `json:"show,omitempty"` // 条件显示配置
}

// ShowCondition 显示条件配置
type ShowCondition struct {
	Type      string      `json:"type"`      // "field", "expression" 等
	Field     string      `json:"field"`     // 依赖的字段名
	Value     interface{} `json:"value"`     // 期望的值
	Operator  string      `json:"operator"`  // 比较操作符: "==", "!=", "in", "not_in" 等
	Operation string      `json:"operation"` // 操作类型: "add", "edit", "both"
}

// ValidationRule 验证规则 - 增强版本
type ValidationRule struct {
	Required bool   `json:"required,omitempty"`
	Message  string `json:"message"`
	Trigger  string `json:"trigger"`
	Min      int    `json:"min,omitempty"`
	Max      int    `json:"max,omitempty"`
	Len      int    `json:"len,omitempty"`     // 精确长度
	Pattern  string `json:"pattern,omitempty"` // 正则表达式模式
	Type     string `json:"type,omitempty"`    // 数据类型验证: email, url, number 等
}

// AddSearchFormItem 添加搜索表单项
func AddSearchFormItem(label, prop string) *FormItem {
	return &FormItem{
		Label: label,
		Prop:  prop,
	}
}

// SetConfig 设置表单项额外配置项
func (f *FormItem) SetConfig(config map[string]interface{}) *FormItem {
	f.Config = config
	return f
}

// SetSelect 设置下拉选择框选项
func (f *FormItem) SetSelect(options []*Option) *FormItem {
	f.Config = map[string]interface{}{
		"clearable":   true,
		"placeholder": "请选择",
	}
	f.Type = "select"
	f.Options = options
	return f
}

// SetRadio 设置单选框选项
func (f *FormItem) SetRadio(options []*Option) *FormItem {
	f.Config = map[string]interface{}{
		"clearable":   true,
		"placeholder": "请选择",
	}
	f.Type = "radio"
	f.Options = options
	return f
}

// SetCheckbox 设置多选框选项
func (f *FormItem) SetCheckbox(options []*Option) *FormItem {
	f.Config = map[string]interface{}{
		"clearable":   true,
		"placeholder": "请选择",
	}
	f.Type = "checkbox"
	f.Options = options
	return f
}

// SetDateRange 设置日期范围选择器
func (f *FormItem) SetDateRange() *FormItem {
	f.Config = map[string]interface{}{
		"type":        "daterange",
		"format":      "YYYY-MM-DD",
		"valueFormat": "YYYY-MM-DD",
	}
	f.Type = FormItemTypeDateRange
	return f
}

// ResetFieldAttrs 重置字段属性
func ResetFieldAttrs(formFields []*FormItem, field string, attrKey string, attrVal interface{}) {
	for _, item := range formFields {
		fmt.Println(item)
		switch item.Type {
		case FormItemTypeInput, FormItemTypeSelect, FormItemTypeRadio, FormItemTypeCheckbox:
			if item.Prop == field {
				switch attrKey {
				case "id":
					item.Config["id"] = attrVal.(string)
				case "label":
					item.Label = attrVal.(string)
				case "field":
					item.Prop = attrVal.(string)
				case "class":
					item.Config["class"] = attrVal.(string)
				case "type":
					item.Type = FormItemType(attrVal.(string))
				case "value":
					item.Config["value"] = attrVal
				case "placeholder":
					item.Config["placeholder"] = attrVal.(string)
				case "disabled":
					item.Config["disabled"] = attrVal.(bool)
				case "multiple":
					item.Config["multiple"] = attrVal.(bool)
				case "searchable":
					item.Config["searchable"] = attrVal.(bool)
				case "size":
					item.Config["size"] = attrVal.(string)
				case "span":
					item.Span = attrVal.(int)
				case "start_content":
					item.Config["startContent"] = attrVal.(string)
				case "end_content":
					item.Config["endContent"] = attrVal.(string)
				case "options":
					item.Options = attrVal.([]*Option)
				case "help":
					item.Config["help"] = attrVal.(string)
				case "variant":
					item.Config["variant"] = attrVal.(string)
				}
			}
		}
	}
}
