package schema

import "github.com/google/uuid"

type DialogType string

const (
	DialogTypeTable   DialogType = "table"   // 表格
	DialogTypeForm    DialogType = "form"    // 表单
	DialogTypeSetting DialogType = "setting" // 设置

	DialogAnimationTypeFade  = "fade"  // 淡入淡出
	DialogAnimationTypeSlide = "slide" // 滑动
	DialogAnimationTypeZoom  = "zoom"  // 缩放
)

// Dialog 对话框组件
type Dialog struct {
	ID         string        `json:"id"`         // 唯一标识
	Title      string        `json:"title"`      // 标题
	Width      string        `json:"width"`      // 宽度
	Fullscreen string        `json:"fullscreen"` // 是否为全屏
	Modal      bool          `json:"modal"`      // 是否需要遮罩层
	Draggable  bool          `json:"draggable"`  // 启用可拖拽功能
	Content    string        `json:"content"`    // 内容
	IsHtml     bool          `json:"is_html"`    // 是否是 HTML 内容
	Class      string        `json:"class"`      // 自定义 CSS 类名
	Style      string        `json:"style"`      // 自定义 CSS 样式
	Type       DialogType    `json:"type"`       // 类型
	URL        string        `json:"url"`        // 请求URL
	Button     Button        `json:"button"`     // 按钮
	Query      []*QueryField `json:"query"`      // GET参数
	Form       *Form         `json:"form"`       // 表单
	Action     []*Button     `json:"action"`     // 操作
}

// NewDialog 创建新的对话框
func NewDialog(title string, url string) *Dialog {
	return &Dialog{
		ID:    uuid.New().String(),
		Title: title,
		URL:   url,
		Query: make([]*QueryField, 0),
		Form: &Form{
			Fields: make([]*FormItem, 0),
		},
		Type: DialogTypeForm,
		Button: Button{
			Label: title,
			Color: ButtonColorPrimary,
			Size:  ButtonSizeDefault,
		},
		Action: []*Button{
			{
				Label:  "提交",
				Type:   ButtonTypeSubmit,
				Method: ButtonMethodPost,
				Size:   ButtonSizeDefault,
				Color:  ButtonColorPrimary,
				URL:    url,
			},
		},
	}
}

// BuildForm 构建表单
func (d *Dialog) BuildForm(s interface{}) *Dialog {
	d.Form.Fields = BuildForm(s)
	return d
}

// SetInputAttrs 设置输入框属性
func (d *Dialog) SetInputAttrs(field string, attrKey string, attrVal interface{}) *Dialog {
	ResetFieldAttrs(d.Form.Fields, field, attrKey, attrVal)
	return d
}
