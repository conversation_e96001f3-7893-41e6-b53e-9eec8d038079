package schema

const (
	ButtonColorDefault   = "default"   // 默认
	ButtonColorPrimary   = "primary"   // 主色
	ButtonColorSecondary = "secondary" // 次色
	ButtonColorSuccess   = "success"   // 成功
	ButtonColorDanger    = "danger"    // 危险
	ButtonColorWarning   = "warning"   // 警告

	ButtonSizeSmall   = "small"   // 小
	ButtonSizeDefault = "default" // 中
	ButtonSizeLarge   = "large"   // 大

	ButtonTypeSubmit = "submit" // 提交

	// Button methods
	ButtonMethodGet    = "GET"    // 获取
	ButtonMethodPost   = "POST"   // 提交
	ButtonMethodPut    = "PUT"    // 更新
	ButtonMethodDelete = "DELETE" // 删除
)

// Button 按钮
type Button struct {
	Label    string `json:"label"`    // 按钮文本
	Color    string `json:"color"`    // 按钮颜色
	Size     string `json:"size"`     // 按钮大小
	Class    string `json:"class"`    // 自定义类名
	Disabled bool   `json:"disabled"` // 是否禁用
	Type     string `json:"type"`     // 按钮类型
	Method   string `json:"method"`   // 按钮方法
	URL      string `json:"url"`      // 按钮链接
	Show     string `json:"show"`     // 是否显示
}

// NewButton 创建按钮
func NewButton(label string, url string) *Button {
	return &Button{
		Label:  label,
		URL:    url,
		Size:   ButtonSizeSmall,
		Method: ButtonMethodPost,
		Color:  ButtonColorPrimary,
		Type:   ButtonTypeSubmit,
	}
}

// SetColor 设置按钮颜色
func (b *Button) SetColor(color string) *Button {
	b.Color = color
	return b
}
