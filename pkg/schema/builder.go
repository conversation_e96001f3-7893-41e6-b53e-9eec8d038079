package schema

import (
	"reflect"
	"strconv"
	"strings"
)

// BuildForm 构建表单
func BuildForm(s interface{}) []*FormItem {
	result := make([]*FormItem, 0)

	// 如果是指针， 则获取指针的类型
	t := reflect.TypeOf(s)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	// 如果不是结构体, 那么返回空数组
	if t.Kind() != reflect.Struct {
		return result
	}

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		// 获取字段类型
		fieldType := field.Type.Kind()
		if fieldType == reflect.Ptr {
			fieldType = field.Type.Elem().Kind()
		}

		// 获取views标签
		viewsTag := field.Tag.Get("views")
		if viewsTag == "" {
			continue
		}

		// 解析views标签
		formItem := ParseViewsTag(field)
		if formItem != nil {
			result = append(result, formItem)
		}
	}

	return result
}

// GetTagKey 获取tag的key值
func GetTagKey(tag string, key string) string {
	parts := strings.Split(tag, ";")
	for _, part := range parts {
		kv := strings.Split(part, ":")
		if len(kv) == 2 {
			if kv[0] == key {
				return kv[1]
			}
		}
	}
	return ""
}

// ParseViewsTag 解析views标签并返回FormItem
func ParseViewsTag(field reflect.StructField) *FormItem {
	viewsTag := field.Tag.Get("views")
	if viewsTag == "" {
		return nil
	}

	// 获取字段名
	fieldName := field.Tag.Get("form")
	if fieldName == "" {
		fieldName = field.Tag.Get("json")
	}
	if fieldName == "" {
		fieldName = strings.ToLower(field.Name)
	}

	// 创建FormItem
	formItem := &FormItem{
		Prop:   fieldName,
		Label:  GetTagKey(viewsTag, "label"),
		Type:   FormItemType(GetTagKey(viewsTag, "type")),
		Config: make(map[string]interface{}),
	}

	// 设置默认配置
	switch formItem.Type {
	case "input":
		formItem.Config["clearable"] = true
		formItem.Config["placeholder"] = "请输入" + formItem.Label
	case "select":
		formItem.Config["clearable"] = true
		formItem.Config["placeholder"] = "请选择" + formItem.Label
		// 解析options
		optionsStr := GetTagKey(viewsTag, "options")
		if optionsStr != "" {
			formItem.Options = parseOptions(optionsStr)
		}
	case "radio":
		// 解析options
		optionsStr := GetTagKey(viewsTag, "options")
		if optionsStr != "" {
			formItem.Options = parseOptions(optionsStr)
		}
	case "checkbox":
		// 解析options
		optionsStr := GetTagKey(viewsTag, "options")
		if optionsStr != "" {
			formItem.Options = parseOptions(optionsStr)
		}
	case "switch":
		// 开关类型不需要特殊处理
	case "date", "datetime", "time":
		formItem.Config["clearable"] = true
		formItem.Config["placeholder"] = "请选择" + formItem.Label
		// 如果是日期范围
		if GetTagKey(viewsTag, "range") == "true" {
			formItem.Config["type"] = formItem.Type + "range"
			formItem.Config["format"] = "YYYY-MM-DD"
			formItem.Config["valueFormat"] = "YYYY-MM-DD"
		}
	}

	// 解析其他配置
	for _, part := range strings.Split(viewsTag, ";") {
		kv := strings.Split(part, ":")
		if len(kv) == 2 && kv[0] != "label" && kv[0] != "type" && kv[0] != "options" && kv[0] != "range" {
			// 尝试转换为数字或布尔值
			if val, err := strconv.Atoi(kv[1]); err == nil {
				formItem.Config[kv[0]] = val
			} else if val, err := strconv.ParseFloat(kv[1], 64); err == nil {
				formItem.Config[kv[0]] = val
			} else if val, err := strconv.ParseBool(kv[1]); err == nil {
				formItem.Config[kv[0]] = val
			} else {
				formItem.Config[kv[0]] = kv[1]
			}
		}
	}

	// 处理必填项
	bindingTag := field.Tag.Get("binding")
	if strings.Contains(bindingTag, "required") {
		formItem.Required = true
		if formItem.Rules == nil {
			formItem.Rules = make([]*ValidationRule, 0)
		}
		formItem.Rules = append(formItem.Rules, &ValidationRule{
			Required: true,
			Message:  formItem.Label + "不能为空",
			Trigger:  "blur",
		})
	}

	return formItem
}

// parseOptions 解析选项字符串为Option数组
func parseOptions(optionsStr string) []*Option {
	options := make([]*Option, 0)
	optionParts := strings.Split(optionsStr, ",")

	for _, part := range optionParts {
		kv := strings.Split(part, "=")
		if len(kv) == 2 {
			// 尝试将值转换为数字
			var value interface{} = kv[0]
			if intVal, err := strconv.Atoi(kv[0]); err == nil {
				value = intVal
			} else if floatVal, err := strconv.ParseFloat(kv[0], 64); err == nil {
				value = floatVal
			} else if boolVal, err := strconv.ParseBool(kv[0]); err == nil {
				value = boolVal
			}

			options = append(options, &Option{
				Label: kv[1],
				Value: value,
			})
		}
	}

	return options
}
