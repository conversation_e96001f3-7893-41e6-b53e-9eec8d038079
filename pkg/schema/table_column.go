package schema

type TableColumnType string

const (
	TableColumnTypeText     TableColumnType = "text"     // 文本
	TableColumnTypeDate     TableColumnType = "date"     // 日期
	TableColumnTypeImage    TableColumnType = "image"    // 图片
	TableColumnTypeImages   TableColumnType = "images"   // 图片组
	TableColumnTypeSelect   TableColumnType = "select"   // 下拉框
	TableColumnTypeCheckbox TableColumnType = "checkbox" // 复选框
	TableColumnTypeToggle   TableColumnType = "toggle"   // 开关
	TableColumnTypeHtml     TableColumnType = "html"     // HTML
	TableColumnTypeCopy     TableColumnType = "copy"     // 复制
	TableColumnTypeRows     TableColumnType = "rows"     // 多行文本
	TableColumnTypeTag      TableColumnType = "tag"      // 标签
	TableColumnTypeLink     TableColumnType = "link"     // 链接
	TableColumnTypeRating   TableColumnType = "rating"   // 评分
)

// TableColumn 表格列
type TableColumn struct {
	Type                TableColumnType `json:"type"`                // 类型
	Label               string          `json:"label"`               // 标题
	Prop                string          `json:"prop"`                // 映射
	Width               interface{}     `json:"width"`               // 宽度
	MinWidth            interface{}     `json:"minWidth"`            // 最小宽度
	Fixed               interface{}     `json:"fixed"`               // 列是否固定在左侧或者右侧(left, right)
	Sortable            bool            `json:"sortable"`            // 对应列是否可以排序
	ShowOverflowTooltip bool            `json:"showOverflowTooltip"` // 当内容过长被隐藏时显示 tooltip
	Slot                string          `json:"slot"`                // 插槽名称
	UseSlot             bool            `json:"useSlot"`             // 是否使用插槽
	SlotName            string          `json:"slotName"`            // 自定义插槽名称
	Options             interface{}     `json:"options"`             // 选项
}

// NewTableColumn 创建新的表格列
func NewTableColumn(label string, prop string) *TableColumn {
	return &TableColumn{
		Label: label,
		Prop:  prop,
	}
}

// SetImage 设置列类型为图片
func (t *TableColumn) SetImage() *TableColumn {
	t.Type = TableColumnTypeImage
	return t
}

// SetSelect 设置下拉框
func (t *TableColumn) SetSelect(options []*Option) *TableColumn {
	t.Options = options
	t.Type = TableColumnTypeSelect
	return t
}

// SetCheckbox 设置复选框
func (t *TableColumn) SetCheckbox(options []*Option) *TableColumn {
	t.Options = options
	t.Type = TableColumnTypeCheckbox
	return t
}

// SetRows 设置多行文本
func (t *TableColumn) SetRows() *TableColumn {
	t.Type = TableColumnTypeRows
	return t
}

// SetTag 设置标签
func (t *TableColumn) SetTag() *TableColumn {
	t.Type = TableColumnTypeTag
	return t
}

// SetHtml 设置HTML
func (t *TableColumn) SetHtml() *TableColumn {
	t.Type = TableColumnTypeHtml
	return t
}

// SetCopy 设置复制
func (t *TableColumn) SetCopy() *TableColumn {
	t.Type = TableColumnTypeCopy
	return t
}

// SetLink 设置链接
func (t *TableColumn) SetLink() *TableColumn {
	t.Type = TableColumnTypeLink
	return t
}

// SetRating 设置评分
func (t *TableColumn) SetRating() *TableColumn {
	t.Type = TableColumnTypeRating
	return t
}

// SetSwitch 设置开关
func (t *TableColumn) SetSwitch(options *SwitchOption) *TableColumn {
	t.Options = options
	t.Type = TableColumnTypeToggle
	return t
}

// SetWidth 设置列宽
func (t *TableColumn) SetWidth(width int) *TableColumn {
	t.Width = width
	return t
}

// SetMinWidth 设置最小列宽
func (t *TableColumn) SetMinWidth(minWidth int) *TableColumn {
	t.MinWidth = minWidth
	return t
}

// SetFixed 设置固定列
func (t *TableColumn) SetFixed(fixed string) *TableColumn {
	t.Fixed = fixed
	return t
}

// SetSortable 设置是否可排序
func (t *TableColumn) SetSortable(sortable bool) *TableColumn {
	t.Sortable = sortable
	return t
}

// SetShowOverflowTooltip 设置是否显示溢出提示
func (t *TableColumn) SetShowOverflowTooltip(showOverflowTooltip bool) *TableColumn {
	t.ShowOverflowTooltip = showOverflowTooltip
	return t
}

// SetSlot 设置插槽名称
func (t *TableColumn) SetSlot(slot string) *TableColumn {
	t.Slot = slot
	return t
}
