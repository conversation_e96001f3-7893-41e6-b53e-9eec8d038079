package schema

// QueryField 表示一个GET 参数
type QueryField struct {
	Field   string      `json:"field"`   // 字段名
	Mapping string      `json:"mapping"` // 映射字段名
	Value   interface{} `json:"value"`   // 值
}

// Option 选项
type Option struct {
	Label    string      `json:"label"`
	Value    interface{} `json:"value"`
	Disabled bool        `json:"disabled"`
	Children []*Option   `json:"children"`
}

// SwitchOption 开关
type SwitchOption struct {
	ActiveText   string      `json:"activeText"`
	InactiveText string      `json:"inactiveText"`
	TrueValue    interface{} `json:"trueValue"`
	FalseValue   interface{} `json:"falseValue"`
}

// Pagination 分页
type Pagination struct {
	Current string `json:"current"`
	Size    string `json:"size"`
	OrderBy string `json:"orderBy"`
	Order   string `json:"order"`
}
