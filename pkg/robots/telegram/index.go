package telegram

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"sync"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// Config 机器人配置
type Config struct {
	Token      string        `json:"token"`
	ProxyUrl   string        `json:"proxy_url,omitempty"`
	Timeout    time.Duration `json:"timeout,omitempty"`
	Workers    int           `json:"workers,omitempty"`
	BufferSize int           `json:"buffer_size,omitempty"`
	Debug      bool          `json:"debug,omitempty"`
}

// Handler 消息处理器函数类型
type Handler func(context.Context, *models.Update) error

// Bot Telegram 机器人
type Bot struct {
	bot        *bot.Bot
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	updateCh   chan *models.Update
	handlers   []Handler
	middleware []Handler
	client     *http.Client
}

// New 创建新的 Telegram 机器人
func New(conf *Config) *Bot {
	ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt)

	// 设置默认值
	if conf.Timeout == 0 {
		conf.Timeout = 30 * time.Second
	}
	if conf.Workers == 0 {
		conf.Workers = 4
	}
	if conf.BufferSize == 0 {
		conf.BufferSize = 100
	}

	return &Bot{
		ctx:      ctx,
		cancel:   cancel,
		updateCh: make(chan *models.Update, conf.BufferSize),
		client:   createHTTPClient(conf),
	}
}

// Use 添加中间件
func (b *Bot) Use(middleware Handler) *Bot {
	b.middleware = append(b.middleware, middleware)
	return b
}

// OnUpdate 添加更新处理器
func (b *Bot) OnUpdate(handler Handler) *Bot {
	b.handlers = append(b.handlers, handler)
	return b
}

// OnCommand 添加命令处理器
func (b *Bot) OnCommand(command string, handler Handler) *Bot {
	return b.OnUpdate(func(ctx context.Context, update *models.Update) error {
		if update.Message != nil && update.Message.Text != "" {
			if len(update.Message.Text) > 0 && update.Message.Text[0] == '/' {
				cmd := update.Message.Text[1:]
				if cmd == command {
					return handler(ctx, update)
				}
			}
		}
		return nil
	})
}

// OnText 添加文本消息处理器
func (b *Bot) OnText(handler Handler) *Bot {
	return b.OnUpdate(func(ctx context.Context, update *models.Update) error {
		if update.Message != nil && update.Message.Text != "" {
			return handler(ctx, update)
		}
		return nil
	})
}

// SendMessage 发送消息
func (b *Bot) SendMessage(chatID int64, text string, markup models.ReplyMarkup) error {
	_, err := b.bot.SendMessage(b.ctx, &bot.SendMessageParams{
		ChatID:      chatID,
		Text:        text,
		ReplyMarkup: markup,
	})
	return err
}

// SendMessageHTML 发送 HTML 格式消息
func (b *Bot) SendMessageHTML(chatID int64, text string, markup models.ReplyMarkup) error {
	_, err := b.bot.SendMessage(b.ctx, &bot.SendMessageParams{
		ChatID:      chatID,
		Text:        text,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: markup,
	})
	return err
}

// SendPhoto 发送图片
func (b *Bot) SendPhoto(chatID int64, photo models.InputFile, caption string) error {
	_, err := b.bot.SendPhoto(b.ctx, &bot.SendPhotoParams{
		ChatID:  chatID,
		Photo:   photo,
		Caption: caption,
	})
	return err
}

// EditMessage 编辑消息
func (b *Bot) EditMessage(chatID int64, messageID int, text string, markup models.ReplyMarkup) error {
	_, err := b.bot.EditMessageText(b.ctx, &bot.EditMessageTextParams{
		ChatID:      chatID,
		MessageID:   messageID,
		Text:        text,
		ReplyMarkup: markup,
	})
	return err
}

// DeleteMessage 删除消息
func (b *Bot) DeleteMessage(chatID int64, messageID int) error {
	_, err := b.bot.DeleteMessage(b.ctx, &bot.DeleteMessageParams{
		ChatID:    chatID,
		MessageID: messageID,
	})
	return err
}

// Start 启动机器人
func (b *Bot) Start(conf *Config) error {
	opts := []bot.Option{
		bot.WithDefaultHandler(b.handleUpdate),
		bot.WithHTTPClient(conf.Timeout, b.client),
	}

	if conf.Debug {
		opts = append(opts, bot.WithDebug())
	}

	var err error
	b.bot, err = bot.New(conf.Token, opts...)
	if err != nil {
		return err
	}

	// 启动工作协程池
	b.startWorkers(conf.Workers)

	// 启动机器人
	go b.bot.Start(b.ctx)

	// 等待关闭信号
	<-b.ctx.Done()
	return nil
}

// Close 关闭机器人
func (b *Bot) Close() error {
	b.cancel()
	close(b.updateCh)
	b.wg.Wait()
	return nil
}

// GetMe 获取机器人信息
func (b *Bot) GetMe() (*models.User, error) {
	return b.bot.GetMe(b.ctx)
}

// 内部方法

func (b *Bot) handleUpdate(ctx context.Context, _ *bot.Bot, update *models.Update) {
	select {
	case b.updateCh <- update:
	case <-ctx.Done():
	default:
		// 缓冲区满时丢弃更新（防止阻塞）
	}
}

func (b *Bot) startWorkers(workers int) {
	for i := 0; i < workers; i++ {
		b.wg.Add(1)
		go b.worker()
	}
}

func (b *Bot) worker() {
	defer b.wg.Done()

	for {
		select {
		case update, ok := <-b.updateCh:
			if !ok {
				return
			}
			b.processUpdate(update)
		case <-b.ctx.Done():
			return
		}
	}
}

func (b *Bot) processUpdate(update *models.Update) {
	ctx := b.ctx

	// 执行中间件
	for _, middleware := range b.middleware {
		if err := middleware(ctx, update); err != nil {
			return // 中间件返回错误时停止处理
		}
	}

	// 执行处理器
	for _, handler := range b.handlers {
		if err := handler(ctx, update); err != nil {
			// 记录错误但继续执行其他处理器
			fmt.Printf("Handler error: %v\n", err)
		}
	}
}

func createHTTPClient(conf *Config) *http.Client {
	transport := &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
	}

	if conf.ProxyUrl != "" {
		if proxyURL, err := url.Parse(conf.ProxyUrl); err == nil {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}

	return &http.Client{
		Transport: transport,
		Timeout:   conf.Timeout,
	}
}
