package ethereum

import (
	"gin/pkg/blockchain/interfaces"
	"math/big"
	"time"
)

// TransferHandler 转账处理函数类型
type TransferHandler func(event *interfaces.TokenTransferEvent)

// GasInfo Gas信息
type GasInfo struct {
	GasPrice     *big.Int `json:"gas_price"`
	GasLimit     uint64   `json:"gas_limit"`
	EstimatedGas uint64   `json:"estimated_gas"`
}

// Config 配置结构
type Config struct {
	RPCURL         string        `json:"rpc_url"`
	MaxConcurrency int64         `json:"max_concurrency"`
	RetryAttempts  int           `json:"retry_attempts"`
	ProcessDelay   time.Duration `json:"process_delay"`
	GasLimit       uint64        `json:"gas_limit"`
	GasPrice       *big.Int      `json:"gas_price"`
}
