package ethereum

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"gin/pkg/blockchain/interfaces"
	"github.com/ethereum/go-ethereum"
	"log"
	"math/big"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/rpc"
	"golang.org/x/sync/semaphore"
)

// EthProvider 以太坊客户端，实现Blockchain接口
type EthProvider struct {
	client    *ethclient.Client
	rpcClient *rpc.Client
	ctx       context.Context
	cancel    context.CancelFunc
	rpcURL    string

	// 监听相关
	monitors  map[string]*AddressMonitor
	monitorMu sync.RWMutex

	// 配置
	config Config

	// 连接状态
	connected bool
	connMu    sync.RWMutex
}

// AddressMonitor 地址监听器
type AddressMonitor struct {
	addresses map[common.Address]bool
	handlers  []TransferHandler
	stopChan  chan struct{}
	running   bool
	mu        sync.RWMutex
}

// DefaultConfig 默认配置
func DefaultConfig() Config {
	return Config{
		MaxConcurrency: 10,
		RetryAttempts:  3,
		ProcessDelay:   200 * time.Millisecond,
		GasLimit:       21000,
		GasPrice:       big.NewInt(20000000000),
	}
}

// NewEthereumClient 创建新的以太坊客户端
func NewEthereumClient(rpcURL string) (*EthProvider, error) {
	return NewEthereumClientWithConfig(rpcURL, DefaultConfig())
}

// NewEthereumClientWithConfig 使用配置创建以太坊客户端
func NewEthereumClientWithConfig(rpcURL string, config Config) (*EthProvider, error) {
	ctx, cancel := context.WithCancel(context.Background())

	client := &EthProvider{
		rpcURL:    rpcURL,
		config:    config,
		ctx:       ctx,
		cancel:    cancel,
		monitors:  make(map[string]*AddressMonitor),
		connected: false,
	}

	if err := client.Connect(ctx); err != nil {
		cancel()
		return nil, err
	}

	return client, nil
}

// Connect 连接到以太坊节点
func (ec *EthProvider) Connect(ctx context.Context) error {
	ec.connMu.Lock()
	defer ec.connMu.Unlock()

	if ec.connected {
		return nil
	}

	client, err := ethclient.DialContext(ctx, ec.rpcURL)
	if err != nil {
		return fmt.Errorf("连接以太坊节点失败: %v", err)
	}

	rpcClient, err := rpc.DialContext(ctx, ec.rpcURL)
	if err != nil {
		client.Close()
		return fmt.Errorf("连接RPC客户端失败: %v", err)
	}

	ec.client = client
	ec.rpcClient = rpcClient
	ec.connected = true

	log.Printf("成功连接到以太坊节点: %s", ec.rpcURL)
	return nil
}

// Disconnect 断开连接
func (ec *EthProvider) Disconnect() error {
	ec.connMu.Lock()
	defer ec.connMu.Unlock()

	if !ec.connected {
		return nil
	}

	// 停止所有监听器
	ec.monitorMu.Lock()
	for _, monitor := range ec.monitors {
		monitor.Stop()
	}
	ec.monitors = make(map[string]*AddressMonitor)
	ec.monitorMu.Unlock()

	// 取消上下文
	ec.cancel()

	// 关闭连接
	if ec.client != nil {
		ec.client.Close()
	}
	if ec.rpcClient != nil {
		ec.rpcClient.Close()
	}

	ec.connected = false
	log.Println("已断开以太坊节点连接")
	return nil
}

// IsConnected 检查连接状态
func (ec *EthProvider) IsConnected(_ context.Context) bool {
	ec.connMu.RLock()
	defer ec.connMu.RUnlock()
	return ec.connected && ec.client != nil
}

// IsHealthy 检查连接健康状态
func (ec *EthProvider) IsHealthy(ctx context.Context) bool {
	if !ec.IsConnected(ctx) {
		return false
	}

	// 尝试获取最新区块号
	_, err := ec.client.BlockNumber(ctx)
	return err == nil
}

// TransferNative 原生币转账
func (ec *EthProvider) TransferNative(ctx context.Context, senderPrivateKeyHex, toAddress string, amount *big.Int) (string, error) {
	if !common.IsHexAddress(toAddress) {
		return "", fmt.Errorf("无效的接收地址: %s", toAddress)
	}

	// 解析私钥
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(senderPrivateKeyHex, "0x"))
	if err != nil {
		return "", fmt.Errorf("无效的私钥: %v", err)
	}

	// 获取发送方地址
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", fmt.Errorf("无法获取公钥")
	}

	fromAddress := crypto.PubkeyToAddress(*publicKeyECDSA)

	// 获取nonce
	nonce, err := ec.client.PendingNonceAt(ctx, fromAddress)
	if err != nil {
		return "", fmt.Errorf("获取nonce失败: %v", err)
	}

	// 获取gas价格
	gasPrice, err := ec.GetGasPrice(ctx)
	if err != nil {
		return "", fmt.Errorf("获取gas价格失败: %v", err)
	}

	// 估算gas
	gasLimit, err := ec.EstimateGas(ctx, fromAddress.Hex(), toAddress, "", amount)
	if err != nil {
		return "", fmt.Errorf("估算gas失败: %v", err)
	}

	hexToAddress := common.HexToAddress(toAddress)

	// 构建原生币交易
	tx := types.NewTx(&types.LegacyTx{
		Nonce:    nonce,
		GasPrice: gasPrice,
		Gas:      gasLimit, // ETH 转账固定 Gas Limit
		To:       &hexToAddress,
		Value:    amount,
		Data:     nil, // 没有额外数据
	})

	chainID, err := ec.client.ChainID(ctx)
	if err != nil {
		return "", fmt.Errorf("获取链ID失败: %v", err)
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", fmt.Errorf("签名交易失败: %v", err)
	}

	// 发送交易
	err = ec.client.SendTransaction(ctx, signedTx)
	if err != nil {
		return "", fmt.Errorf("发送交易失败: %v", err)
	}

	return signedTx.Hash().Hex(), nil
}

// TransferToken 代币转账
func (ec *EthProvider) TransferToken(ctx context.Context, senderPrivateKeyHex, toAddress, tokenContractAddress string, amount *big.Int) (string, error) {
	// ERC20 transfer方法签名: transfer(address,uint256)
	methodID := crypto.Keccak256([]byte("transfer(address,uint256)"))[:4]

	// 编码参数
	toAddr := common.HexToAddress(toAddress)
	amountBytes := amount.Bytes()

	// 填充到32字节
	paddedAmount := make([]byte, 32)
	copy(paddedAmount[32-len(amountBytes):], amountBytes)

	// 填充地址到32字节
	paddedAddr := make([]byte, 32)
	copy(paddedAddr[12:], toAddr.Bytes())

	// 组合数据
	data := append(methodID, paddedAddr...)
	data = append(data, paddedAmount...)

	// 使用TransferNative发送交易，但data参数包含代币转账数据
	return ec.TransferNative(ctx, senderPrivateKeyHex, tokenContractAddress, big.NewInt(0))
}

// GetNativeBalance 获取原生币余额
func (ec *EthProvider) GetNativeBalance(ctx context.Context, address string) (*big.Int, error) {
	if !common.IsHexAddress(address) {
		return nil, fmt.Errorf("无效的地址: %s", address)
	}

	balance, err := ec.client.BalanceAt(ctx, common.HexToAddress(address), nil)
	if err != nil {
		return nil, fmt.Errorf("获取余额失败: %v", err)
	}

	return balance, nil
}

// GetTokenBalance 获取代币余额
func (ec *EthProvider) GetTokenBalance(ctx context.Context, address, tokenContractAddress string) (*big.Int, error) {
	// ERC20 balanceOf方法签名
	methodID := crypto.Keccak256([]byte("balanceOf(address)"))[:4]

	// 编码地址参数
	addr := common.HexToAddress(address)
	paddedAddr := make([]byte, 32)
	copy(paddedAddr[12:], addr.Bytes())

	data := append(methodID, paddedAddr...)

	toAddress := common.HexToAddress(tokenContractAddress)
	// 调用合约
	result, err := ec.client.CallContract(ctx, ethereum.CallMsg{
		To:   &toAddress,
		Data: data,
	}, nil)

	if err != nil {
		return nil, fmt.Errorf("调用合约失败: %v", err)
	}

	// 解析结果
	balance := new(big.Int).SetBytes(result)
	return balance, nil
}

// GetMultipleBalances 批量获取余额
func (ec *EthProvider) GetMultipleBalances(ctx context.Context, addresses []string) ([]interfaces.AddressBalance, error) {
	var balances []interfaces.AddressBalance

	// 使用信号量限制并发
	sem := semaphore.NewWeighted(ec.config.MaxConcurrency)
	var mu sync.Mutex
	var wg sync.WaitGroup

	for _, addr := range addresses {
		wg.Add(1)
		go func(address string) {
			defer wg.Done()

			if err := sem.Acquire(ctx, 1); err != nil {
				return
			}
			defer sem.Release(1)

			balance, err := ec.GetNativeBalance(ctx, address)
			if err != nil {
				log.Printf("获取地址 %s 余额失败: %v", address, err)
				return
			}

			mu.Lock()
			balances = append(balances, interfaces.AddressBalance{
				Address: address,
				Balance: balance,
				Symbol:  "ETH",
				Decimal: 18,
			})
			mu.Unlock()
		}(addr)
	}

	wg.Wait()
	return balances, nil
}

// GetTransactionStatus 获取交易状态
func (ec *EthProvider) GetTransactionStatus(ctx context.Context, txHash string) (bool, error) {
	receipt, err := ec.client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		return false, fmt.Errorf("获取交易收据失败: %v", err)
	}

	return receipt.Status == 1, nil
}

// GetTransactionReceipt 获取交易收据
func (ec *EthProvider) GetTransactionReceipt(ctx context.Context, txHash string) (*interfaces.TransactionReceipt, error) {
	hash := common.HexToHash(txHash)

	// 1. 获取交易收据 (receipt)
	receipt, err := ec.client.TransactionReceipt(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("获取交易收据失败: %w", err) // 使用 %w 包装错误以便于 Unwrap
	}

	// 2. 获取交易本身 (transaction)
	tx, isPending, err := ec.client.TransactionByHash(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("获取交易详情失败: %w", err)
	}
	if isPending { // 如果交易还在 Pending 状态，通常不应该有 Receipt
		return nil, fmt.Errorf("交易仍在挂起状态，无法获取完整收据信息: %s", txHash)
	}

	chainID, err := ec.client.ChainID(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取链ID失败: %w", err)
	}

	senderAddress, err := types.Sender(types.NewEIP155Signer(chainID), tx)
	if err != nil {
		return nil, fmt.Errorf("无法从交易中获取发送方地址: %w", err)
	}

	return &interfaces.TransactionReceipt{
		TxHash:      receipt.TxHash.Hex(),
		BlockNumber: receipt.BlockNumber.Uint64(),
		Status:      receipt.Status == types.ReceiptStatusSuccessful,
		GasUsed:     receipt.GasUsed,
		From:        senderAddress.Hex(),
		To:          tx.To().Hex(),
		Value:       tx.Value().String(),
	}, nil
}

// GetTransactionByHash 根据哈希获取交易
func (ec *EthProvider) GetTransactionByHash(ctx context.Context, txHash string) (map[string]interface{}, error) {
	tx, isPending, err := ec.client.TransactionByHash(ctx, common.HexToHash(txHash))
	if err != nil {
		return nil, fmt.Errorf("获取交易失败: %v", err)
	}

	result := map[string]interface{}{
		"hash":      tx.Hash().Hex(),
		"nonce":     tx.Nonce(),
		"gas_price": tx.GasPrice().String(),
		"gas_limit": tx.Gas(),
		"value":     tx.Value().String(),
		"pending":   isPending,
	}

	if tx.To() != nil {
		result["to"] = tx.To().Hex()
	}

	return result, nil
}

// GetGasPrice 获取gas价格
func (ec *EthProvider) GetGasPrice(ctx context.Context) (*big.Int, error) {
	gasPrice, err := ec.client.SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取gas价格失败: %v", err)
	}

	return gasPrice, nil
}

// EstimateGas 估算gas
func (ec *EthProvider) EstimateGas(ctx context.Context, from, to, data string, value *big.Int) (uint64, error) {
	var toAddr *common.Address
	if to != "" {
		addr := common.HexToAddress(to)
		toAddr = &addr
	}

	var dataBytes []byte
	if data != "" {
		dataBytes = common.FromHex(data)
	}

	msg := ethereum.CallMsg{
		From:  common.HexToAddress(from),
		To:    toAddr,
		Value: value,
		Data:  dataBytes,
	}

	gasLimit, err := ec.client.EstimateGas(ctx, msg)
	if err != nil {
		return 0, fmt.Errorf("估算gas失败: %v", err)
	}

	return gasLimit, nil
}

// GetGasInfo 获取完整的gas信息（以太坊特有方法）
func (ec *EthProvider) GetGasInfo(ctx context.Context, from, to, data string, value *big.Int) (*GasInfo, error) {
	gasPrice, err := ec.GetGasPrice(ctx)
	if err != nil {
		return nil, err
	}

	estimatedGas, err := ec.EstimateGas(ctx, from, to, data, value)
	if err != nil {
		return nil, err
	}

	return &GasInfo{
		GasPrice:     gasPrice,
		GasLimit:     ec.config.GasLimit,
		EstimatedGas: estimatedGas,
	}, nil
}

// GetLatestBlock 获取最新区块
func (ec *EthProvider) GetLatestBlock(ctx context.Context) (*interfaces.BlockInfo, error) {
	header, err := ec.client.HeaderByNumber(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("获取最新区块失败: %v", err)
	}

	return &interfaces.BlockInfo{
		Number:    header.Number.Uint64(),
		Hash:      header.Hash().Hex(),
		Timestamp: header.Time,
		TxCount:   0, // 需要额外获取
	}, nil
}

// GetBlockByNumber 根据区块号获取区块
func (ec *EthProvider) GetBlockByNumber(ctx context.Context, blockNumber uint64) (*interfaces.BlockInfo, error) {
	block, err := ec.client.BlockByNumber(ctx, big.NewInt(int64(blockNumber)))
	if err != nil {
		return nil, fmt.Errorf("获取区块失败: %v", err)
	}

	return &interfaces.BlockInfo{
		Number:    block.Number().Uint64(),
		Hash:      block.Hash().Hex(),
		Timestamp: block.Time(),
		TxCount:   len(block.Transactions()),
	}, nil
}

// GetBlockByHash 根据区块哈希获取区块
func (ec *EthProvider) GetBlockByHash(ctx context.Context, blockHash string) (*interfaces.BlockInfo, error) {
	block, err := ec.client.BlockByHash(ctx, common.HexToHash(blockHash))
	if err != nil {
		return nil, fmt.Errorf("获取区块失败: %v", err)
	}

	return &interfaces.BlockInfo{
		Number:    block.Number().Uint64(),
		Hash:      block.Hash().Hex(),
		Timestamp: block.Time(),
		TxCount:   len(block.Transactions()),
	}, nil
}

// ListenForNewBlocks 监听新区块
func (ec *EthProvider) ListenForNewBlocks(ctx context.Context) (<-chan string, error) {
	headers := make(chan *types.Header)
	sub, err := ec.client.SubscribeNewHead(ctx, headers)
	if err != nil {
		return nil, fmt.Errorf("订阅新区块失败: %v", err)
	}

	blockChan := make(chan string)

	go func() {
		defer sub.Unsubscribe()
		defer close(blockChan)

		for {
			select {
			case err := <-sub.Err():
				log.Printf("区块监听错误: %v", err)
				return
			case header := <-headers:
				blockChan <- header.Hash().Hex()
			case <-ctx.Done():
				return
			}
		}
	}()

	return blockChan, nil
}

// ListenForTokenTransfers 监听代币转账
func (ec *EthProvider) ListenForTokenTransfers(ctx context.Context, tokenContractAddress string) (<-chan interfaces.TokenTransferEvent, error) {
	// ERC20 Transfer事件签名
	transferEventSignature := crypto.Keccak256([]byte("Transfer(address,address,uint256)"))

	query := ethereum.FilterQuery{
		Addresses: []common.Address{common.HexToAddress(tokenContractAddress)},
		Topics: [][]common.Hash{
			{common.BytesToHash(transferEventSignature)},
		},
	}

	logs := make(chan types.Log)
	sub, err := ec.client.SubscribeFilterLogs(ctx, query, logs)
	if err != nil {
		return nil, fmt.Errorf("订阅代币转账事件失败: %v", err)
	}

	eventChan := make(chan interfaces.TokenTransferEvent)

	go func() {
		defer sub.Unsubscribe()
		defer close(eventChan)

		for {
			select {
			case err := <-sub.Err():
				log.Printf("代币转账监听错误: %v", err)
				return
			case contractLog := <-logs:
				// 解析Transfer事件
				if len(contractLog.Topics) >= 3 {
					from := common.HexToAddress(contractLog.Topics[1].Hex()[26:]) // 移除padding
					to := common.HexToAddress(contractLog.Topics[2].Hex()[26:])
					value := new(big.Int).SetBytes(contractLog.Data)

					event := interfaces.TokenTransferEvent{
						From:   from.Hex(),
						To:     to.Hex(),
						Value:  value,
						TxHash: contractLog.TxHash.Hex(),
						Block:  contractLog.BlockNumber,
					}

					eventChan <- event
				}
			case <-ctx.Done():
				return
			}
		}
	}()

	return eventChan, nil
}

// ListenForAddressTransfers 监听地址转账
func (ec *EthProvider) ListenForAddressTransfers(ctx context.Context, address string) (<-chan interfaces.TokenTransferEvent, error) {
	return ec.ListenForMultipleAddresses(ctx, []string{address})
}

// ListenForMultipleAddresses 监听多个地址的转账
func (ec *EthProvider) ListenForMultipleAddresses(ctx context.Context, addresses []string) (<-chan interfaces.TokenTransferEvent, error) {
	// 创建或获取监听器
	monitorKey := strings.Join(addresses, ",")

	ec.monitorMu.Lock()
	monitor, exists := ec.monitors[monitorKey]
	if !exists {
		monitor = &AddressMonitor{
			addresses: make(map[common.Address]bool),
			handlers:  make([]TransferHandler, 0),
			stopChan:  make(chan struct{}),
			running:   false,
		}

		// 添加地址
		for _, addr := range addresses {
			if common.IsHexAddress(addr) {
				monitor.addresses[common.HexToAddress(addr)] = true
			}
		}

		ec.monitors[monitorKey] = monitor
	}
	ec.monitorMu.Unlock()

	// 启动监听
	if !monitor.running {
		if err := monitor.Start(ec); err != nil {
			return nil, err
		}
	}

	// 创建事件通道
	eventChan := make(chan interfaces.TokenTransferEvent)

	// 添加处理器
	monitor.AddHandler(func(event *interfaces.TokenTransferEvent) {
		select {
		case eventChan <- *event:
		case <-ctx.Done():
		}
	})

	return eventChan, nil
}

// WeiToEther 将Wei转换为Ether
func (ec *EthProvider) WeiToEther(wei *big.Int) string {
	ether := new(big.Float).SetInt(wei)
	ether.Quo(ether, big.NewFloat(1e18))
	return ether.Text('f', 6)
}

// EtherToWei 将Ether转换为Wei
func (ec *EthProvider) EtherToWei(ether string) (*big.Int, error) {
	etherFloat, ok := new(big.Float).SetString(ether)
	if !ok {
		return nil, fmt.Errorf("无效的ether值: %s", ether)
	}

	weiFloat := new(big.Float).Mul(etherFloat, big.NewFloat(1e18))
	weiInt := new(big.Int)
	weiFloat.Int(weiInt)

	return weiInt, nil
}

// AddHandler 添加转账处理器
func (am *AddressMonitor) AddHandler(handler TransferHandler) {
	am.mu.Lock()
	defer am.mu.Unlock()
	am.handlers = append(am.handlers, handler)
}

// Start 启动监听
func (am *AddressMonitor) Start(client *EthProvider) error {
	am.mu.Lock()
	defer am.mu.Unlock()

	if am.running {
		return nil
	}

	am.running = true

	go func() {
		headers := make(chan *types.Header)
		sub, err := client.client.SubscribeNewHead(client.ctx, headers)
		if err != nil {
			log.Printf("订阅新区块失败: %v", err)
			return
		}
		defer sub.Unsubscribe()

		for {
			select {
			case err := <-sub.Err():
				log.Printf("监听错误: %v", err)
				return
			case header := <-headers:
				go func(blockNum uint64) {
					time.Sleep(client.config.ProcessDelay)
					am.processBlock(client, blockNum)
				}(header.Number.Uint64())
			case <-am.stopChan:
				return
			case <-client.ctx.Done():
				return
			}
		}
	}()

	return nil
}

// Stop 停止监听
func (am *AddressMonitor) Stop() {
	am.mu.Lock()
	defer am.mu.Unlock()

	if !am.running {
		return
	}

	am.running = false
	close(am.stopChan)
}

// processBlock 处理区块中的交易
func (am *AddressMonitor) processBlock(client *EthProvider, blockNumber uint64) {
	block, err := client.client.BlockByNumber(client.ctx, big.NewInt(int64(blockNumber)))
	if err != nil {
		log.Printf("获取区块 %d 失败: %v", blockNumber, err)
		return
	}

	// 使用信号量限制并发
	sem := semaphore.NewWeighted(client.config.MaxConcurrency)
	var wg sync.WaitGroup

	for _, tx := range block.Transactions() {
		// 检查是否为监听地址的交易
		if tx.To() == nil || !am.isMonitoredAddress(*tx.To()) {
			continue
		}

		wg.Add(1)
		go func(transaction *types.Transaction) {
			defer wg.Done()

			if err := sem.Acquire(client.ctx, 1); err != nil {
				return
			}
			defer sem.Release(1)

			// 获取交易收据
			receipt, err := client.getTransactionReceiptWithRetry(transaction.Hash(), client.config.RetryAttempts)
			if err != nil {
				log.Printf("获取交易收据失败: %v", err)
				return
			}

			// 处理交易
			am.processTransaction(client, transaction, receipt, block)
		}(tx)
	}

	wg.Wait()
}

// isMonitoredAddress 检查是否为监听地址
func (am *AddressMonitor) isMonitoredAddress(address common.Address) bool {
	am.mu.RLock()
	defer am.mu.RUnlock()
	return am.addresses[address]
}

// processTransaction 处理交易
func (am *AddressMonitor) processTransaction(client *EthProvider, tx *types.Transaction, receipt *types.Receipt, block *types.Block) {
	// 获取发送方地址
	from, err := types.Sender(types.NewEIP155Signer(big.NewInt(1)), tx) // 使用链ID 1作为默认值
	if err != nil {
		log.Printf("获取发送方地址失败: %v", err)
		return
	}

	event := &interfaces.TokenTransferEvent{
		From:   from.Hex(),
		To:     tx.To().Hex(),
		Value:  tx.Value(),
		TxHash: tx.Hash().Hex(),
		Block:  receipt.BlockNumber.Uint64(),
	}

	// 调用所有处理器
	am.mu.RLock()
	handlers := make([]TransferHandler, len(am.handlers))
	copy(handlers, am.handlers)
	am.mu.RUnlock()

	for _, handler := range handlers {
		go handler(event)
	}
}

// getTransactionReceiptWithRetry 带重试的获取交易收据
func (ec *EthProvider) getTransactionReceiptWithRetry(txHash common.Hash, maxRetries int) (*types.Receipt, error) {
	var receipt *types.Receipt
	var err error

	for i := 0; i < maxRetries; i++ {
		receipt, err = ec.client.TransactionReceipt(ec.ctx, txHash)
		if err == nil {
			return receipt, nil
		}

		// 如果是临时错误，等待后重试
		if strings.Contains(err.Error(), "internal error") || strings.Contains(err.Error(), "rate limit") {
			waitTime := time.Duration(i+1) * 100 * time.Millisecond
			log.Printf("获取交易收据失败，%v 后重试 (第%d次): %v", waitTime, i+1, err)
			time.Sleep(waitTime)
			continue
		}

		break
	}

	return nil, err
}
