package tron

import (
	"gin/pkg/blockchain/interfaces"
	"math/big"
	"time"
)

// TransferHandler 转账处理函数类型
type TransferHandler func(event *interfaces.TokenTransferEvent)

// EnergyInfo 能量信息
type EnergyInfo struct {
	EnergyUsed      uint64   `json:"energy_used"`
	EnergyLimit     uint64   `json:"energy_limit"`
	EstimatedEnergy uint64   `json:"estimated_energy"`
	FrozenBalance   *big.Int `json:"frozen_balance"`
}

// Config 配置结构
type Config struct {
	RPCURL         string        `json:"rpc_url"`
	MaxConcurrency int64         `json:"max_concurrency"`
	RetryAttempts  int           `json:"retry_attempts"`
	ProcessDelay   time.Duration `json:"process_delay"`
	EnergyLimit    uint64        `json:"energy_limit"`
	FrozenBalance  *big.Int      `json:"frozen_balance"`
}

// TronAccount 波场账户信息
type TronAccount struct {
	Address       string   `json:"address"`
	Balance       *big.Int `json:"balance"`
	Energy        uint64   `json:"energy"`
	FrozenBalance *big.Int `json:"frozen_balance"`
	AccountType   string   `json:"account_type"`
}

// TronTransaction 波场交易信息
type TronTransaction struct {
	TxID            string   `json:"tx_id"`
	BlockNumber     uint64   `json:"block_number"`
	Timestamp       uint64   `json:"timestamp"`
	From            string   `json:"from"`
	To              string   `json:"to"`
	Value           *big.Int `json:"value"`
	EnergyUsed      uint64   `json:"energy_used"`
	Status          bool     `json:"status"`
	ContractAddress string   `json:"contract_address,omitempty"`
}
