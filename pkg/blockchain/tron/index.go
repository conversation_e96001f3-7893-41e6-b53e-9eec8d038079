package tron

import (
	"context"
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	interfaces2 "gin/pkg/blockchain/interfaces"
	"github.com/goccy/go-json"
	"math/big"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// TronBlockchain 波场区块链实现
type TronBlockchain struct {
	config     *Config
	client     *http.Client
	connected  bool
	mu         sync.RWMutex
	grpcClient *client.GrpcClient
}

// NewTronBlockchain 创建新的波场区块链实例
func NewTronBlockchain(config *Config) interfaces2.Blockchain {
	return &TronBlockchain{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Connect 连接到波场网络
func (t *TronBlockchain) Connect(ctx context.Context) error {
	t.mu.Lock()
	defer t.mu.Unlock()

	// 初始化 gRPC 客户端
	grpcClient := client.NewGrpcClient(t.config.RPCURL)

	// 设置 gRPC 连接选项 - 使用不安全的连接（适用于测试）
	err := grpcClient.Start(grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return fmt.Errorf("failed to create TRON client: %w", err)
	}

	t.grpcClient = grpcClient
	t.connected = true
	return nil
}

// Disconnect 断开连接
func (t *TronBlockchain) Disconnect() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.grpcClient != nil {
		t.grpcClient.Stop()
	}
	t.connected = false
	return nil
}

// IsConnected 检查连接状态
func (t *TronBlockchain) IsConnected(ctx context.Context) bool {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.connected
}

// IsHealthy 检查连接健康状态
func (t *TronBlockchain) IsHealthy(ctx context.Context) bool {
	if !t.IsConnected(ctx) {
		return false
	}

	// 尝试获取最新区块来检查连接
	_, err := t.GetLatestBlock(ctx)
	return err == nil
}

// TransferNative TRX 转账
func (t *TronBlockchain) TransferNative(ctx context.Context, senderPrivateKeyHex, toAddress string, amount *big.Int) (string, error) {
	if !t.IsConnected(ctx) {
		return "", fmt.Errorf("not connected to TRON network")
	}

	// 解析私钥
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(senderPrivateKeyHex, "0x"))
	if err != nil {
		return "", fmt.Errorf("invalid private key: %w", err)
	}

	// 获取发送方地址
	senderAddress := address.PubkeyToAddress(privateKey.PublicKey)

	// 创建交易
	tx, err := t.grpcClient.Transfer(senderAddress.String(), toAddress, amount.Int64())
	if err != nil {
		return "", fmt.Errorf("failed to create transfer transaction: %w", err)
	}

	// 签名并广播交易
	txID, err := t.signAndBroadcast(tx, privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign and broadcast transaction: %w", err)
	}

	return txID, nil
}

// TransferToken TRC20 代币转账
func (t *TronBlockchain) TransferToken(ctx context.Context, senderPrivateKeyHex, toAddress, tokenContractAddress string, amount *big.Int) (string, error) {
	if !t.IsConnected(ctx) {
		return "", fmt.Errorf("not connected to TRON network")
	}

	// 解析私钥
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(senderPrivateKeyHex, "0x"))
	if err != nil {
		return "", fmt.Errorf("invalid private key: %w", err)
	}

	// 获取发送方地址
	senderAddress := address.PubkeyToAddress(privateKey.PublicKey)

	// 创建 TRC20 转账交易
	tx, err := t.grpcClient.TRC20Send(senderAddress.String(), toAddress, tokenContractAddress, amount, int64(t.config.EnergyLimit))
	if err != nil {
		return "", fmt.Errorf("failed to create TRC20 transfer transaction: %w", err)
	}

	// 签名并广播交易
	txID, err := t.signAndBroadcast(tx, privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign and broadcast transaction: %w", err)
	}

	return txID, nil
}

// GetNativeBalance 获取 TRX 余额
func (t *TronBlockchain) GetNativeBalance(ctx context.Context, addressStr string) (*big.Int, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取账户信息
	account, err := t.grpcClient.GetAccount(addressStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	return big.NewInt(account.Balance), nil
}

// GetTokenBalance 获取 TRC20 代币余额
func (t *TronBlockchain) GetTokenBalance(ctx context.Context, addressStr, tokenContractAddress string) (*big.Int, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取 TRC20 代币余额
	balance, err := t.grpcClient.TRC20ContractBalance(addressStr, tokenContractAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get TRC20 balance: %w", err)
	}

	return balance, nil
}

// GetMultipleBalances 批量获取余额
func (t *TronBlockchain) GetMultipleBalances(ctx context.Context, addresses []string) ([]interfaces2.AddressBalance, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	var balances []interfaces2.AddressBalance
	for _, addr := range addresses {
		balance, err := t.GetNativeBalance(ctx, addr)
		if err != nil {
			return nil, fmt.Errorf("failed to get balance for %s: %w", addr, err)
		}

		balances = append(balances, interfaces2.AddressBalance{
			Address: addr,
			Balance: balance,
			Symbol:  "TRX",
			Decimal: 6,
		})
	}

	return balances, nil
}

// GetTransactionStatus 查询交易状态
func (t *TronBlockchain) GetTransactionStatus(ctx context.Context, txHash string) (bool, error) {
	if !t.IsConnected(ctx) {
		return false, fmt.Errorf("not connected to TRON network")
	}

	// 获取交易信息
	tx, err := t.grpcClient.GetTransactionByID(txHash)
	if err != nil {
		return false, fmt.Errorf("failed to get transaction: %w", err)
	}

	return tx.Ret[0].ContractRet == core.Transaction_Result_SUCCESS, nil
}

// GetTransactionReceipt 获取交易收据
func (t *TronBlockchain) GetTransactionReceipt(ctx context.Context, txHash string) (*interfaces2.TransactionReceipt, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取交易信息
	tx, err := t.grpcClient.GetTransactionByID(txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// 获取交易收据
	receipt, err := t.grpcClient.GetTransactionInfoByID(txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// 解析交易参数
	var fromAddr, toAddr string
	var amount int64

	if len(tx.RawData.Contract) > 0 {
		contract := tx.RawData.Contract[0]
		if contract.Type == core.Transaction_Contract_TransferContract {
			transferContract := &core.TransferContract{}
			if err := contract.Parameter.UnmarshalTo(transferContract); err == nil {
				fromAddr = address.Address(transferContract.OwnerAddress).String()
				toAddr = address.Address(transferContract.ToAddress).String()
				amount = transferContract.Amount
			}
		}
	}

	receiptInfo := &interfaces2.TransactionReceipt{
		TxHash:      txHash,
		BlockNumber: uint64(receipt.BlockNumber),
		Status:      tx.Ret[0].ContractRet == core.Transaction_Result_SUCCESS,
		GasUsed:     uint64(receipt.Receipt.EnergyUsage),
		From:        fromAddr,
		To:          toAddr,
		Value:       fmt.Sprintf("%d", amount),
	}

	return receiptInfo, nil
}

// GetTransactionByHash 根据哈希获取交易详情
func (t *TronBlockchain) GetTransactionByHash(ctx context.Context, txHash string) (map[string]interface{}, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取交易信息
	tx, err := t.grpcClient.GetTransactionByID(txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// 转换为 map
	txData, err := json.Marshal(tx)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal transaction: %w", err)
	}

	var result map[string]interface{}
	err = json.Unmarshal(txData, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal transaction: %w", err)
	}

	return result, nil
}

// GetLatestBlock 获取最新区块信息
func (t *TronBlockchain) GetLatestBlock(ctx context.Context) (*interfaces2.BlockInfo, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取最新区块
	block, err := t.grpcClient.GetNowBlock()
	if err != nil {
		return nil, fmt.Errorf("failed to get latest block: %w", err)
	}

	return &interfaces2.BlockInfo{
		Number:    uint64(block.BlockHeader.RawData.Number),
		Hash:      hex.EncodeToString(block.Blockid),
		Timestamp: uint64(block.BlockHeader.RawData.Timestamp),
		TxCount:   len(block.Transactions),
	}, nil
}

// GetBlockByNumber 根据区块号获取区块信息
func (t *TronBlockchain) GetBlockByNumber(ctx context.Context, blockNumber uint64) (*interfaces2.BlockInfo, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取指定区块
	block, err := t.grpcClient.GetBlockByNum(int64(blockNumber))
	if err != nil {
		return nil, fmt.Errorf("failed to get block by number: %w", err)
	}

	return &interfaces2.BlockInfo{
		Number:    uint64(block.BlockHeader.RawData.Number),
		Hash:      hex.EncodeToString(block.Blockid),
		Timestamp: uint64(block.BlockHeader.RawData.Timestamp),
		TxCount:   len(block.Transactions),
	}, nil
}

// GetBlockByHash 根据区块哈希获取区块信息
func (t *TronBlockchain) GetBlockByHash(ctx context.Context, blockHash string) (*interfaces2.BlockInfo, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	// 获取指定区块
	block, err := t.grpcClient.GetBlockByID(blockHash)
	if err != nil {
		return nil, fmt.Errorf("failed to get block by hash: %w", err)
	}

	return &interfaces2.BlockInfo{
		Number:    uint64(block.BlockHeader.RawData.Number),
		Hash:      hex.EncodeToString(block.BlockHeader.RawData.ParentHash),
		Timestamp: uint64(block.BlockHeader.RawData.Timestamp),
		TxCount:   len(block.Transactions),
	}, nil
}

// ListenForNewBlocks 监听新区块
func (t *TronBlockchain) ListenForNewBlocks(ctx context.Context) (<-chan string, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	blockChan := make(chan string, 100)

	go func() {
		defer close(blockChan)

		var lastBlockNumber int64 = 0
		ticker := time.NewTicker(3 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				block, err := t.grpcClient.GetNowBlock()
				if err != nil {
					continue
				}

				currentBlockNumber := block.BlockHeader.RawData.Number
				if currentBlockNumber > lastBlockNumber {
					lastBlockNumber = currentBlockNumber
					blockHash := hex.EncodeToString(block.Blockid)
					select {
					case blockChan <- blockHash:
					default:
						// 通道已满，跳过
					}
				}
			}
		}
	}()

	return blockChan, nil
}

// ListenForTokenTransfers 监听特定代币的转账事件
func (t *TronBlockchain) ListenForTokenTransfers(ctx context.Context, tokenContractAddress string) (<-chan interfaces2.TokenTransferEvent, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	eventChan := make(chan interfaces2.TokenTransferEvent, 100)

	go func() {
		defer close(eventChan)

		// 这里需要实现 TRC20 转账事件监听
		// 由于波场的 gRPC 客户端限制，这里提供一个基础实现
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				// 这里应该实现实际的 TRC20 转账事件监听
				// 由于波场 SDK 的限制，这里只是占位实现
			}
		}
	}()

	return eventChan, nil
}

// ListenForAddressTransfers 监听特定地址的转账事件
func (t *TronBlockchain) ListenForAddressTransfers(ctx context.Context, addressStr string) (<-chan interfaces2.TokenTransferEvent, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	eventChan := make(chan interfaces2.TokenTransferEvent, 100)

	go func() {
		defer close(eventChan)

		// 监听地址转账事件
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				// 这里应该实现实际的地址转账事件监听
				// 由于波场 SDK 的限制，这里只是占位实现
			}
		}
	}()

	return eventChan, nil
}

// ListenForMultipleAddresses 监听多个地址的转账事件
func (t *TronBlockchain) ListenForMultipleAddresses(ctx context.Context, addresses []string) (<-chan interfaces2.TokenTransferEvent, error) {
	if !t.IsConnected(ctx) {
		return nil, fmt.Errorf("not connected to TRON network")
	}

	eventChan := make(chan interfaces2.TokenTransferEvent, 100)

	go func() {
		defer close(eventChan)

		// 监听多个地址转账事件
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				// 这里应该实现实际的多地址转账事件监听
				// 由于波场 SDK 的限制，这里只是占位实现
			}
		}
	}()

	return eventChan, nil
}

// signAndBroadcast 签名并广播交易
func (t *TronBlockchain) signAndBroadcast(tx *api.TransactionExtention, privateKey *ecdsa.PrivateKey) (string, error) {
	// 签名交易
	signedTx, err := t.signTransaction(tx.Transaction, privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// 广播交易
	result, err := t.grpcClient.Broadcast(signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to broadcast transaction: %w", err)
	}

	if result.Code != api.Return_SUCCESS {
		return "", fmt.Errorf("transaction failed: %s", string(result.Message))
	}

	return common.BytesToHexString(tx.GetTxid()), nil
}

// signTransaction 签名交易
func (t *TronBlockchain) signTransaction(tx *core.Transaction, privateKey *ecdsa.PrivateKey) (*core.Transaction, error) {
	// 这里需要实现交易签名
	// 由于波场 SDK 的复杂性，这里提供一个基础实现
	// 实际使用时需要根据波场 SDK 的具体 API 来实现

	// 暂时返回原始交易，实际需要签名
	return tx, nil
}
