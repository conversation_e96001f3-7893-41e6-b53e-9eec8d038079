package interfaces

import (
	"context"
	"math/big"
)

// Blockchain 定义了区块链操作的通用接口
type Blockchain interface {
	// TransferNative 原生币转账
	// senderPrivateKeyHex: 发送方私钥的十六进制字符串
	// toAddress: 接收方地址
	// amount: 转账金额（以链的最小单位表示，如 wei 或 lamports）
	// 返回交易哈希和错误
	TransferNative(ctx context.Context, senderPrivateKeyHex, toAddress string, amount *big.Int) (string, error)

	// TransferToken 代币转账
	// senderPrivateKeyHex: 发送方私钥的十六进制字符串
	// toAddress: 接收方地址
	// tokenContractAddress: 代币合约地址
	// amount: 转账金额（以代币的最小单位表示，需考虑代币小数位数）
	// 返回交易哈希和错误
	TransferToken(ctx context.Context, senderPrivateKeyHex, toAddress, tokenContractAddress string, amount *big.Int) (string, error)

	// GetNativeBalance 获取地址的原生币余额
	// address: 要查询的地址
	// 返回余额和错误
	GetNativeBalance(ctx context.Context, address string) (*big.Int, error)

	// GetTokenBalance 获取地址的代币余额
	// address: 要查询的地址
	// tokenContractAddress: 代币合约地址
	// 返回余额和错误
	GetTokenBalance(ctx context.Context, address, tokenContractAddress string) (*big.Int, error)

	// GetMultipleBalances 批量获取多个地址的余额
	// addresses: 要查询的地址列表
	// 返回地址余额列表和错误
	GetMultipleBalances(ctx context.Context, addresses []string) ([]AddressBalance, error)

	// GetTransactionStatus 查询交易状态
	// txHash: 交易哈希
	// 返回交易是否成功、错误信息（如果交易失败）
	GetTransactionStatus(ctx context.Context, txHash string) (bool, error)

	// GetTransactionReceipt 获取交易收据
	// txHash: 交易哈希
	// 返回交易收据和错误
	GetTransactionReceipt(ctx context.Context, txHash string) (*TransactionReceipt, error)

	// GetTransactionByHash 根据哈希获取交易详情
	// txHash: 交易哈希
	// 返回交易详情和错误
	GetTransactionByHash(ctx context.Context, txHash string) (map[string]interface{}, error)

	// GetLatestBlock 获取最新区块信息
	// 返回最新区块信息和错误
	GetLatestBlock(ctx context.Context) (*BlockInfo, error)

	// GetBlockByNumber 根据区块号获取区块信息
	// blockNumber: 区块号
	// 返回区块信息和错误
	GetBlockByNumber(ctx context.Context, blockNumber uint64) (*BlockInfo, error)

	// GetBlockByHash 根据区块哈希获取区块信息
	// blockHash: 区块哈希
	// 返回区块信息和错误
	GetBlockByHash(ctx context.Context, blockHash string) (*BlockInfo, error)

	// ListenForNewBlocks 监听新区块
	// 返回一个通道，用于接收新区块事件
	ListenForNewBlocks(ctx context.Context) (<-chan string, error)

	// ListenForTokenTransfers 监听特定代币的转账事件
	// tokenContractAddress: 要监听的代币合约地址
	// 返回一个通道，用于接收转账事件（包含from, to, value等信息）
	ListenForTokenTransfers(ctx context.Context, tokenContractAddress string) (<-chan TokenTransferEvent, error)

	// ListenForAddressTransfers 监听特定地址的转账事件
	// address: 要监听的地址
	// 返回一个通道，用于接收转账事件
	ListenForAddressTransfers(ctx context.Context, address string) (<-chan TokenTransferEvent, error)

	// ListenForMultipleAddresses 监听多个地址的转账事件
	// addresses: 要监听的地址列表
	// 返回一个通道，用于接收转账事件
	ListenForMultipleAddresses(ctx context.Context, addresses []string) (<-chan TokenTransferEvent, error)

	// IsConnected 检查连接状态
	// 返回是否已连接
	IsConnected(ctx context.Context) bool

	// Connect 连接到区块链节点
	// 返回连接错误
	Connect(ctx context.Context) error

	// Disconnect 断开连接
	// 返回断开连接错误
	Disconnect() error

	// IsHealthy 检查连接健康状态
	// 返回连接是否健康
	IsHealthy(ctx context.Context) bool
}
