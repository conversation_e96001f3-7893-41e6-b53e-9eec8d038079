package interfaces

import (
	"math/big"
)

// TokenTransferEvent 代币转账事件
type TokenTransferEvent struct {
	From   string   `json:"from"`
	To     string   `json:"to"`
	Value  *big.Int `json:"value"`
	TxHash string   `json:"tx_hash"`
	Block  uint64   `json:"block"`
}

// TransactionReceipt 交易收据
type TransactionReceipt struct {
	TxHash      string `json:"tx_hash"`
	BlockNumber uint64 `json:"block_number"`
	Status      bool   `json:"status"`
	GasUsed     uint64 `json:"gas_used"`
	From        string `json:"from"`
	To          string `json:"to"`
	Value       string `json:"value"`
}

// BlockInfo 区块信息
type BlockInfo struct {
	Number    uint64 `json:"number"`
	Hash      string `json:"hash"`
	Timestamp uint64 `json:"timestamp"`
	TxCount   int    `json:"tx_count"`
}

// AddressBalance 地址余额信息
type AddressBalance struct {
	Address string   `json:"address"`
	Balance *big.Int `json:"balance"`
	Symbol  string   `json:"symbol"`
	Decimal int      `json:"decimal"`
}
