package net

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"

	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
)

var (
	ip2region *xdb.Searcher
	once      sync.Once
	disabled  bool // 标记是否禁用ip2region功能
)

// findProjectRoot 查找项目根目录
func findProjectRoot() string {
	// 1. 优先使用环境变量
	if root := os.Getenv("PROJECT_ROOT"); root != "" {
		return root
	}

	// 2. 从当前工作目录开始查找
	dir, err := os.Getwd()
	if err == nil {
		if root := searchProjectRoot(dir); root != "" {
			return root
		}
	}

	// 3. 从当前文件位置开始查找
	_, filename, _, ok := runtime.Caller(0)
	if ok {
		dir := filepath.Dir(filename)
		if root := searchProjectRoot(dir); root != "" {
			return root
		}
	}

	return ""
}

// searchProjectRoot 在指定目录向上查找项目根目录
func searchProjectRoot(startDir string) string {
	dir := startDir
	for {
		// 查找标识文件
		markers := []string{"go.mod", "go.sum", "config", ".git"}
		for _, marker := range markers {
			if _, err := os.Stat(filepath.Join(dir, marker)); err == nil {
				// 进一步确认是否有config目录和ip2region.xdb
				configPath := filepath.Join(dir, "config", "ip2region.xdb")
				if _, err := os.Stat(configPath); err == nil {
					return dir
				}
			}
		}

		// 到达系统根目录
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}
	return ""
}

// getDbPath 获取数据库文件路径
func getDbPath() string {
	// 1. 优先使用环境变量指定的路径
	if path := os.Getenv("IP2REGION_DB_PATH"); path != "" {
		return path
	}

	// 2. 查找项目根目录
	root := findProjectRoot()
	if root == "" {
		return ""
	}

	return filepath.Join(root, "config", "ip2region.xdb")
}

// 初始化 ip2region（懒加载模式）
func init() {
	once.Do(func() {
		// 在测试环境下可以通过环境变量禁用
		if os.Getenv("DISABLE_IP2REGION") == "true" {
			disabled = true
			return
		}

		dbPath := getDbPath()
		if dbPath == "" {
			fmt.Printf("警告: 无法找到 ip2region 数据库文件，IP地理位置功能将被禁用\n")
			disabled = true
			return
		}

		cb, err := xdb.LoadContentFromFile(dbPath)
		if err != nil {
			fmt.Printf("警告: 加载 ip2region 数据库失败 (%s): %v，IP地理位置功能将被禁用\n", dbPath, err)
			disabled = true
			return
		}

		searcher, err := xdb.NewWithBuffer(cb)
		if err != nil {
			fmt.Printf("警告: 初始化 ip2region 失败: %v，IP地理位置功能将被禁用\n", err)
			disabled = true
			return
		}

		ip2region = searcher
	})
}

// ResolveIPRegion 获取 IP 地理位置
func ResolveIPRegion(ip string) string {
	// 如果功能被禁用，返回默认值
	if disabled || ip2region == nil {
		return "未知位置"
	}

	// 过滤 IPv6 和内网地址
	if ip == "localhost" || ip == "::1" || strings.Contains(ip, ":") || strings.HasPrefix(ip, "127.") {
		return "内网地址"
	}

	region, err := ip2region.SearchByStr(ip)
	if err != nil {
		return "未知位置"
	}

	parts := strings.Split(region, "|")
	if len(parts) < 5 {
		return "未知位置"
	}

	// 只保留有效的地区信息
	var location []string
	for _, part := range []string{parts[0], parts[2], parts[3]} {
		if part != "0" && part != "内网IP" && part != "XX" {
			location = append(location, part)
		}
	}

	if len(location) == 0 {
		return "未知位置"
	}
	return strings.Join(location, " ")
}

// IsEnabled 检查ip2region功能是否启用
func IsEnabled() bool {
	return !disabled && ip2region != nil
}
