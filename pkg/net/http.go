package net

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/goccy/go-json"
)

// HttpClient HTTP客户端
type HttpClient struct {
	client  *http.Client
	headers map[string]string
	params  url.Values
	baseURL string
}

// Response HTTP响应包装
type Response struct {
	Body       []byte
	StatusCode int
	Headers    http.Header
	resp       *http.Response
}

// NewHttpClient 创建新的HTTP客户端
func NewHttpClient(baseURL string) *HttpClient {
	return &HttpClient{
		client:  &http.Client{Timeout: 30 * time.Second},
		headers: make(map[string]string),
		baseURL: strings.TrimSuffix(baseURL, "/"),
		params:  url.Values{},
	}
}

// SetTimeout 设置超时时间
func (h *HttpClient) SetTimeout(timeout time.Duration) *HttpClient {
	h.client.Timeout = timeout
	return h
}

// SetHeader 设置请求头
func (h *HttpClient) SetHeader(key, value string) *HttpClient {
	h.headers[key] = value
	return h
}

// SetParams 设置查询参数
func (h *HttpClient) SetParams(params map[string]string) *HttpClient {
	// 清空现有参数
	h.params = url.Values{}
	for k, v := range params {
		h.params.Set(k, v)
	}
	return h
}

// AddParam 添加查询参数
func (h *HttpClient) AddParam(key, value string) *HttpClient {
	h.params.Add(key, value)
	return h
}

// SetProxy 设置代理
func (h *HttpClient) SetProxy(proxy string) error {
	if proxy == "" {
		h.client.Transport = nil
		return nil
	}

	proxyURL, err := url.Parse(proxy)
	if err != nil {
		return fmt.Errorf("invalid proxy URL: %w", err)
	}

	h.client.Transport = &http.Transport{Proxy: http.ProxyURL(proxyURL)}
	return nil
}

// buildURL 构建完整URL
func (h *HttpClient) buildURL(endpoint string) string {
	endpoint = strings.TrimPrefix(endpoint, "/")
	fullURL := h.baseURL + "/" + endpoint

	if len(h.params) > 0 {
		fullURL += "?" + h.params.Encode()
	}
	return fullURL
}

// request 执行HTTP请求
func (h *HttpClient) request(ctx context.Context, method, endpoint string, body io.Reader) (*Response, error) {
	req, err := http.NewRequestWithContext(ctx, method, h.buildURL(endpoint), body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	for k, v := range h.headers {
		req.Header.Set(k, v)
	}

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return &Response{
		Body:       bodyBytes,
		StatusCode: resp.StatusCode,
		Headers:    resp.Header,
		resp:       resp,
	}, nil
}

// Get 发送GET请求
func (h *HttpClient) Get(endpoint string) (*Response, error) {
	return h.GetWithContext(context.Background(), endpoint)
}

// GetWithContext 发送带上下文的GET请求
func (h *HttpClient) GetWithContext(ctx context.Context, endpoint string) (*Response, error) {
	return h.request(ctx, "GET", endpoint, nil)
}

// Post 发送POST请求
func (h *HttpClient) Post(endpoint string, data []byte) (*Response, error) {
	return h.PostWithContext(context.Background(), endpoint, data)
}

// PostWithContext 发送带上下文的POST请求
func (h *HttpClient) PostWithContext(ctx context.Context, endpoint string, data []byte) (*Response, error) {
	// 临时设置Content-Type，不影响客户端状态
	oldContentType := h.headers["Content-Type"]
	h.headers["Content-Type"] = "application/json"

	resp, err := h.request(ctx, "POST", endpoint, bytes.NewBuffer(data))

	// 恢复原来的Content-Type
	if oldContentType == "" {
		delete(h.headers, "Content-Type")
	} else {
		h.headers["Content-Type"] = oldContentType
	}

	return resp, err
}

// ToString 转换为字符串
func (r *Response) ToString() string {
	return string(r.Body)
}

// ToStruct 转换为结构体
func (r *Response) ToStruct(val interface{}) error {
	return json.Unmarshal(r.Body, val)
}

// IsOK 检查是否成功（2xx状态码）
func (r *Response) IsOK() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}
