package utils

import (
	"math/rand"
	"strconv"
	"strings"
	"time"
)

// Randomizer 通用随机生成器
type Randomizer struct {
	rand    *rand.Rand
	charset string
}

// NewRandomizer 创建随机生成器
func NewRandomizer() *Randomizer {
	return &Randomizer{
		rand:    rand.New(rand.NewSource(time.Now().UnixNano())),
		charset: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
	}
}

// String 生成随机字符串
func (r *Randomizer) String(length int) string {
	result := make([]byte, length)
	for i := range result {
		result[i] = r.charset[r.rand.Intn(len(r.charset))]
	}
	return string(result)
}

// Int 生成随机整数 [min, max]
func (r *Randomizer) Int(min, max int) int {
	if min > max {
		min, max = max, min
	}
	return r.rand.Intn(max-min+1) + min
}

// Float 生成随机浮点数 [min, max]
func (r *Randomizer) Float(min, max float64) float64 {
	if min > max {
		min, max = max, min
	}
	return min + r.rand.Float64()*(max-min)
}

// OrderID 生成订单编号（时间戳 + 随机数）
func (r *Randomizer) OrderID(prefix string) string {
	timestamp := time.Now().Format("20060102150405") // 精确到秒
	randomNum := strconv.Itoa(r.Int(100000, 999999)) // 6位随机数
	if prefix != "" {
		return strings.ToUpper(prefix) + timestamp + randomNum
	}
	return timestamp + randomNum
}
