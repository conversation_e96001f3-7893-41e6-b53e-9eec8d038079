package utils

import (
	"time"
)

// ParseDuration 统一处理时间解析
func ParseDuration(value string) time.Duration {
	duration, err := time.ParseDuration(value)
	if err != nil {
		return 5 * time.Second // 默认返回 5 秒
	}
	return duration
}

// HasStringSlice 判断字符串切片是否包含指定值
func HasStringSlice(slice []string, value string) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}
