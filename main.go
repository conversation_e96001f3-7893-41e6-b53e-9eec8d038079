package main

import (
	"fmt"
	"gin/cmd/migrate"
	"gin/cmd/serve"
	"github.com/spf13/cobra"
	"os"
)

var (
	// 构建信息，通过ldflags在编译时注入
	version   = "1.0.0"
	buildTime = "unknown"
	gitCommit = "unknown"
	goVersion = "unknown"
)

func main() {
	// rootCmd 根命令
	var rootCmd = &cobra.Command{
		Use:     "lfyh",
		Short:   "Gin RBAC 权限管理系统",
		Long:    `一个基于 Gin 框架的高性能 RBAC (基于角色的访问控制) 系统。`,
		Version: version,
	}

	// 设置注解信息
	if rootCmd.Annotations == nil {
		rootCmd.Annotations = make(map[string]string)
	}
	rootCmd.Annotations["BuildTime"] = buildTime
	rootCmd.Annotations["GitCommit"] = gitCommit
	rootCmd.Annotations["GoVersion"] = goVersion

	InitCLI(rootCmd)

	// 如果没有命令行参数，默认启动HTTP服务器
	if len(os.Args) == 1 {
		err := serve.CreateGinApp()
		if err != nil {
			_, _ = fmt.Fprintf(os.Stderr, "启动服务器时出错: %v\n", err)
			os.Exit(1)
		}
		return
	}

	// Execute 执行根命令
	if err := rootCmd.Execute(); err != nil {
		_, _ = fmt.Fprintf(os.Stderr, "执行命令时出错: %v\n", err)
		os.Exit(1)
	}

}

// InitCLI 初始化CLI命令结构
func InitCLI(rootCmd *cobra.Command) {
	// 添加全局标志
	//rootCmd.PersistentFlags().StringP("config", "c", "", "配置文件路径 (默认查找 ./config/config.yaml)")
	rootCmd.PersistentFlags().BoolP("verbose", "v", false, "启用详细日志输出")
	rootCmd.PersistentFlags().StringP("log-level", "l", "info", "日志级别 (debug, info, warn, error)")

	// 设置版本信息模板
	rootCmd.SetVersionTemplate(`{{.Name}} 版本信息:
  版本: {{.Version}}
  构建时间: {{if ne .Root.Annotations.BuildTime ""}}{{.Root.Annotations.BuildTime}}{{else}}unknown{{end}}
  Git提交: {{if ne .Root.Annotations.GitCommit ""}}{{.Root.Annotations.GitCommit}}{{else}}unknown{{end}}
  Go版本: {{if ne .Root.Annotations.GoVersion ""}}{{.Root.Annotations.GoVersion}}{{else}}unknown{{end}}
`)

	// 添加子命令
	rootCmd.AddCommand(migrate.CmdMigrate)
	rootCmd.AddCommand(serve.CmdServe)
}
