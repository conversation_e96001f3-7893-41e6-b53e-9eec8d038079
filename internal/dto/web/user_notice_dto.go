package web

import "gin/internal/dto/common"

// UserNoticeListRequest 用户通知列表请求
type UserNoticeListRequest struct {
	// Priority 优先级
	Priority *int8 `form:"priority"`
	// Type 类型
	Type *int8 `form:"type"`
	// IsRead 是否已读
	IsRead *bool `form:"isRead"`
	// 分页参数
	common.Pagination
}

// UserNoticeResponse 用户通知响应
type UserNoticeResponse struct {
	// ID 用户通知ID
	ID uint `json:"id"`
	// SenderID 发送者ID
	SenderID uint `json:"senderId"`
	// Title 通知标题
	Title string `json:"title"`
	// Summary 通知摘要
	Summary string `json:"summary"`
	// Content 通知内容
	Content string `json:"content"`
	// Priority 通知优先级
	Priority int8 `json:"priority"`
	// Status 通知状态
	Status int8 `json:"status"`
	// Type 通知类型
	Type int8 `json:"type"`
	// Route 通知路由
	Route string `json:"route"`
	// ReadAt 阅读时间
	ReadAt string `json:"readAt"`
	// IsRead 是否已读
	IsRead bool `json:"isRead"`
}
