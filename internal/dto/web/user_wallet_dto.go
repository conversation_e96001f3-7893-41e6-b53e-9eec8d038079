package web

import (
	"gin/internal/dto/common"
	"time"
)

// WalletListReq 钱包列表参数
type WalletListReq struct {
	AssetID *uint `json:"asset_id" form:"asset_id"` // 资产ID
	Type    *int8 `json:"type" form:"type"`         // 类型
	Status  *int8 `json:"status" form:"status"`     // 状态

	// 分页参数
	common.Pagination
}

// WalletOrderRes 钱包订单结果
type WalletOrderRes struct {
	ID            uint      `json:"id"`             // 订单ID
	AssetID       uint      `json:"asset_id"`       // 资产ID
	OrderSN       string    `json:"order_sn"`       // 订单编号
	Amount        float64   `json:"amount"`         // 金额
	ArrivalAmount float64   `json:"arrival_amount"` // 到账金额
	FixedFee      float64   `json:"fixed_fee"`      // 固定手续费
	RateFee       float64   `json:"rate_fee"`       // 手续费(%)
	Type          int8      `json:"type"`           // 类型
	Status        int8      `json:"status"`         // 状态
	Reason        string    `json:"reason"`         // 原因
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`     // 更新时间
}

// WalletBillListReq 钱包账单列表参数
type WalletBillListReq struct {
	// 查询参数
	AssetID   *uint   `json:"asset_id" form:"asset_id"`     // 资产ID
	Types     *string `json:"types" form:"types"`           // 类型 用 “,” 逗号分隔
	StartTime *string `json:"start_time" form:"start_time"` // 开始时间
	EndTime   *string `json:"end_time" form:"end_time"`     // 结束时间

	// 分页参数
	common.Pagination
}

// WalletBillRes 钱包账单结果
type WalletBillRes struct {
	ID        uint      `json:"id"`         // ID
	AssetID   uint      `json:"asset_id"`   // 资产ID
	Asset     AssetRes  `json:"asset"`      // 资产
	Amount    float64   `json:"amount"`     // 金额
	Fee       float64   `json:"fee"`        // 手续费
	Balance   float64   `json:"balance"`    // 余额
	Type      int8      `json:"type"`       // 类型
	Name      string    `json:"name"`       // 名称
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// WalletDepositReq 钱包充值参数
type WalletDepositReq struct {
	SecurityKey string  `json:"security_key" form:"security_key"`                // 安全密钥
	Proof       string  `json:"proof" form:"proof"`                              // 凭证
	PaymentID   uint    `json:"payment_id" form:"payment_id" binding:"required"` // 支付ID
	Amount      float64 `json:"amount" form:"amount" binding:"required,gt=0"`    // 金额
	AssetID     uint    `json:"asset_id" form:"asset_id" binding:"required"`     // 资产ID
}

// WalletWithdrawReq 钱包提现参数
type WalletWithdrawReq struct {
	SecurityKey string  `json:"security_key" form:"security_key"`                // 安全密钥
	AccountID   uint    `json:"account_id" form:"account_id" binding:"required"` // 账户ID
	Amount      float64 `json:"amount" form:"amount" binding:"required,gt=0"`    // 金额
}
