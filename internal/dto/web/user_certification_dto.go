package web

import "time"

// UserCertificationRes 用户认证结果
type UserCertificationRes struct {
	ID        uint      `json:"id"`         // ID
	RealName  string    `json:"real_name"`  // 证件姓名
	IDNumber  string    `json:"id_number"`  // 证件号码
	Photo1    string    `json:"photo1"`     // 证件照正面
	Photo2    string    `json:"photo2"`     // 证件照反面
	Photo3    string    `json:"photo3"`     // 手持证件照
	Address   string    `json:"address"`    // 详细地址
	Type      int8      `json:"type"`       // 类型
	Status    int8      `json:"status"`     // 状态
	Reason    string    `json:"reason"`     // 拒绝原因
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// UserCertificationReq 用户认证参数
type UserCertificationReq struct {
	Type     int8   `json:"type" form:"type" binding:"required"`           // 类型
	RealName string `json:"real_name" form:"real_name" binding:"required"` // 证件姓名
	IDNumber string `json:"id_number" form:"id_number" binding:"required"` // 证件号码
	Photo1   string `json:"photo1" form:"photo1" binding:"required"`       // 证件照正面
	Photo2   string `json:"photo2" form:"photo2" binding:"required"`       // 证件照反面
	Photo3   string `json:"photo3" form:"photo3"`                          // 手持证件照
	Address  string `json:"address" form:"address" binding:"required"`     // 详细地址
}
