package web

// AssetRes 用户资产返回
type AssetRes struct {
	ID         uint    `json:"id"`          // 资产ID
	Name       string  `json:"name"`        // 名称
	NameNative string  `json:"name_native"` // 货币名称
	Subtitle   string  `json:"subtitle"`    // 副标题
	Symbol     string  `json:"symbol"`      // 符号
	Icon       string  `json:"icon"`        // 图标
	Rate       float64 `json:"rate"`        // 汇率
	Decimals   int8    `json:"decimals"`    // 小数位数
}
