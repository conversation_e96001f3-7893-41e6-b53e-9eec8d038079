package web

// LevelResponse 系统等级响应
type LevelResponse struct {
	ID          uint    `json:"id"`
	Name        string  `json:"name"`
	Icon        string  `json:"icon"`
	Symbol      string  `json:"symbol"`
	Type        int8    `json:"type"`
	Sort        int     `json:"sort"`
	Amount      float64 `json:"amount"`
	Discount    float64 `json:"discount"`
	Days        int     `json:"days"`
	Status      int8    `json:"status"`
	Description string  `json:"description"`
}

// CreateUserLevelRequest 创建用户等级请求
type CreateUserLevelRequest struct {
	// LevelID 等级ID
	LevelID uint `json:"levelId" binding:"required"`
}
