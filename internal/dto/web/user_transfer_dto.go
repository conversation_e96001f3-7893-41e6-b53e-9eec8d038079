package web

import (
	"gin/internal/dto/common"
	"time"
)

const (
	UserTransferTypeUserID    = 1 // 用户ID
	UserTransferTypeUsername  = 2 // 用户名
	UserTransferTypeEmail     = 3 // 邮箱
	UserTransferTypeTelephone = 4 // 手机号码
)

// cUserTransferListReq 用户转账列表请求
type UserTransferListReq struct {
	common.Pagination
}

// UserTransferListRes 用户转账列表返回
type UserTransferListRes struct {
	ID            uint          `json:"id"`              // ID
	ReceiveUserID uint          `json:"receive_user_id"` // 接收用户ID
	AssetID       uint          `json:"asset_id"`        // 资产ID
	Receiver      UserSmallInfo `json:"receiver"`        // 接收用户
	Asset         AssetRes      `json:"asset"`           // 资产
	Amount        float64       `json:"amount"`          // 转账金额
	FixedFee      float64       `json:"fixed_fee"`       // 固定手续费
	RateFee       float64       `json:"rate_fee"`        // 手续费
	ActualAmount  float64       `json:"actual_amount"`   // 实际到账金额
	Status        int8          `json:"status"`          // 状态
	Reason        string        `json:"reason"`          // 原因
	CreatedAt     time.Time     `json:"created_at"`      // 创建时间
	UpdatedAt     time.Time     `json:"updated_at"`      // 更新时间
}

// UserTransferCreateReq 用户转账创建请求
type UserTransferCreateReq struct {
	SecurityKey string      `json:"security_key" form:"security_key"`             // 安全密钥
	Type        int8        `json:"type" form:"type" binding:"required"`          // 账户类型
	Receiver    interface{} `json:"receiver" form:"receiver" binding:"required"`  // 接收者
	AssetID     uint        `json:"asset_id" form:"asset_id" binding:"required"`  // 资产ID
	Amount      float64     `json:"amount" form:"amount" binding:"required,gt=0"` // 转账金额
}
