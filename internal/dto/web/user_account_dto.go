package web

// UserAccountListReq 用户账户列表参数
type UserAccountListReq struct {
	AssetID *uint `json:"asset_id" form:"asset_id"` // 资产ID
}

// UserAccountCreateReq 用户账户创建参数
type UserAccountCreateReq struct {
	AssetID   uint        `json:"asset_id" form:"asset_id" binding:"required"`     // 资产ID
	PaymentID uint        `json:"payment_id" form:"payment_id" binding:"required"` // 支付ID
	Data      interface{} `json:"data" form:"data" binding:"required"`             // 数据
}

// UserAccountUpdateReq 用户账户更新参数
type UserAccountUpdateReq struct {
	SecurityKey string      `json:"security_key" form:"security_key"`    // 安全密钥
	Data        interface{} `json:"data" form:"data" binding:"required"` // 数据
}

// UserAccountDeleteReq 用户账户删除参数
type UserAccountDeleteReq struct {
	SecurityKey string `json:"security_key" form:"security_key"` // 安全密钥
}

// UserAccountListRes 用户账户列表结果
type UserAccountListRes struct {
	ID        uint        `json:"id"`         // 账户ID
	AssetID   uint        `json:"asset_id"`   // 资产ID
	Icon      string      `json:"icon"`       // 图标
	Name      string      `json:"name"`       // 资产名称
	PaymentID uint        `json:"payment_id"` // 支付ID
	Type      int8        `json:"type"`       // 类型
	Data      interface{} `json:"data"`       // 数据
}
