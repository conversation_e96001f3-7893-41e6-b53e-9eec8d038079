package web

import "time"

const (
	UpdatePasswordTypeLogin    = 1 // 登录密码
	UpdatePasswordTypeSecurity = 2 // 支付密码
	BindingTypeEmail           = 1 // 邮箱
	BindingTypeTelephone       = 2 // 手机号码
)

// UpdateReq 更新参数
type UpdateReq struct {
	Avatar      *string `json:"avatar" form:"avatar"`           // 头像
	Nickname    *string `json:"nickname" form:"nickname"`       // 昵称
	Sex         *int8   `json:"sex" form:"sex"`                 // 性别
	Birthday    *string `json:"birthday" form:"birthday"`       // 生日
	Description *string `json:"description" form:"description"` // 个性签名
}

// UpdatePasswordReq 更新密码参数
type UpdatePasswordReq struct {
	OldPassword string `json:"old_password" form:"old_password" binding:"required"` // 旧密码
	NewPassword string `json:"new_password" form:"new_password" binding:"required"` // 新密码
}

// BindingEmailReq 绑定邮箱参数
type BindingEmailReq struct {
	Code  string `json:"code" form:"code" binding:"required"`   // 验证码
	Email string `json:"email" form:"email" binding:"required"` // 邮箱
}

// BindingTelephoneReq 绑定手机号码参数
type BindingTelephoneReq struct {
	Country   string `json:"country" form:"country" binding:"required"`     // 国家代码
	Code      string `json:"code" form:"code" binding:"required"`           // 验证码
	Telephone string `json:"telephone" form:"telephone" binding:"required"` // 手机号码
}

// LoginReq 登陆参数
type LoginReq struct {
	Type          int8   `json:"type" form:"type" binding:"required"`         // 类型 1:用户名 2:手机号码 3:邮箱
	CountryID     uint   `json:"country_id" form:"country_id"`                // 国家ID
	Username      string `json:"username" form:"username" binding:"required"` // 用户名
	Password      string `json:"password" form:"password" binding:"required"` // 密码
	CaptchaID     string `json:"captcha_id" form:"captcha_id"`                // 验证码ID
	CaptchaAnswer string `json:"captcha_answer" form:"captcha_answer"`        // 验证码答案
}

// RegisterReq 注册参数
type RegisterReq struct {
	Type          int8   `json:"type" form:"type" binding:"required"`         // 类型 1:用户名 2:手机号码 3:邮箱
	CountryID     uint   `json:"country_id" form:"country_id"`                // 国家ID
	Username      string `json:"username" form:"username" binding:"required"` // 用户名
	Password      string `json:"password" form:"password" binding:"required"` // 密码
	SecurityKey   string `json:"security_key" form:"security_key"`            // 密钥
	InviteCode    string `json:"invite_code" form:"invite_code"`              // 邀请码
	Nickname      string `json:"nickname" form:"nickname"`                    // 昵称
	Sex           int8   `json:"sex" form:"sex"`                              // 性别
	Birthday      string `json:"birthday" form:"birthday"`                    // 生日
	CaptchaID     string `json:"captcha_id" form:"captcha_id"`                // 验证码ID
	CaptchaAnswer string `json:"captcha_answer" form:"captcha_answer"`        // 验证码答案
}

// LoginRes 登录结果
type LoginRes struct {
	Token string      `json:"token"` // 令牌
	User  UserInfoRes `json:"user"`  // 用户信息
}

// UserInfoRes 用户信息结果
type UserInfoRes struct {
	ID            uint                  `json:"id"`            // ID
	Username      string                `json:"username"`      // 用户名
	Nickname      string                `json:"nickname"`      // 昵称
	Avatar        string                `json:"avatar"`        // 头像
	Sex           int8                  `json:"sex"`           // 性别
	Email         string                `json:"email"`         // 邮箱
	Telephone     string                `json:"telephone"`     // 手机号码
	Score         int32                 `json:"score"`         // 信用分
	MinScore      int32                 `json:"min_score"`     // 最低信用分
	Birthday      *time.Time            `json:"birthday"`      // 生日
	InviteCode    string                `json:"invite_code"`   // 邀请码
	Description   string                `json:"description"`   // 描述
	LevelID       uint                  `json:"level_id"`      // 等级ID
	Status        int8                  `json:"status"`        // 状态
	UserLevel     UserInfoUserLevel     `json:"user_level"`    // 用户等级
	Certification UserInfoCertification `json:"certification"` // 认证
}

// UserSmallInfo 用户小信息
type UserSmallInfo struct {
	ID       uint   `json:"id"`       // ID
	Username string `json:"username"` // 用户名
	Nickname string `json:"nickname"` // 昵称
	Avatar   string `json:"avatar"`   // 头像
}

// UserInfoUserLevel 用户等级
type UserInfoUserLevel struct {
	Icon      string    `json:"icon"`       // 图标
	Symbol    string    `json:"symbol"`     // 符号
	ExpiredAt time.Time `json:"expired_at"` // 过期时间
}

// UserInfoCertification 用户认证
type UserInfoCertification struct {
	ID        uint      `json:"id"`         // ID
	Status    int8      `json:"status"`     // 状态
	Reason    string    `json:"reason"`     // 原因
	CreatedAt time.Time `json:"created_at"` // 时间
}

// InviteListRes 邀请列表结果
type InviteListRes struct {
	Award  float64          `json:"award"`  // 奖励
	Number int64            `json:"number"` // 人数
	Items  []InviteListItem `json:"items"`  // 列表
}

// InviteListItem 邀请列表项
type InviteListItem struct {
	UID       uint      `json:"uid"`        // UID
	Avatar    string    `json:"avatar"`     // 头像
	Status    int8      `json:"status"`     // 状态
	CreatedAt time.Time `json:"created_at"` // 时间
}
