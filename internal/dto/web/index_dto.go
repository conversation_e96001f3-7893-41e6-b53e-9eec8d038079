package web

import "gin/internal/constant"

// HelpersResult 帮助中心结果
type HelpersResult struct {
	Socials []*constant.SettingSiteSocial `json:"socials"` // 社交
	Items   []*ArticleRes                 `json:"items"`   // 帮助中心列表
}

// WalletPaymentsRes 钱包支付结果
type WalletPaymentsRes struct {
	Payments []*WalletPayment `json:"payments"` // 支付列表
}

// WalletPayment 钱包支付
type WalletPayment struct {
	ID           uint        `json:"id"`             // 支付ID
	AssetID      uint        `json:"asset_id"`       // 资产ID
	Name         string      `json:"name"`           // 支付名称
	Subtitle     string      `json:"subtitle"`       // 支付副标题
	Icon         string      `json:"icon"`           // 支付图标
	Type         int8        `json:"type"`           // 支付类型
	MinAmount    float64     `json:"min_amount"`     // 最小金额
	MaxAmount    float64     `json:"max_amount"`     // 最大金额
	FixedFee     float64     `json:"fixed_fee"`      // 固定手续费
	RateFee      float64     `json:"rate_fee"`       // 手续费(%)
	IsProof      bool        `json:"is_proof"`       // 是否需要凭证
	IsRedirectCS bool        `json:"is_redirect_cs"` // 是否跳转客服
	Data         interface{} `json:"data"`           // 数据
}

// InitsResult 初始化结果
type InitsResult struct {
	Name         string              `json:"name"`         // 站点名称
	Logo         string              `json:"logo"`         // 站点logo
	Favicon      string              `json:"favicon"`      // 站点favicon
	Keywords     string              `json:"keywords"`     // 站点关键词
	Copyright    string              `json:"copyright"`    // 站点版权
	Introduce    string              `json:"introduce"`    // 站点介绍
	Service      InitsService        `json:"service"`      // 客服信息
	Settings     InitsSettings       `json:"settings"`     // 站点设置
	Languages    []*InitsLanguages   `json:"languages"`    // 语言列表
	Countrys     []*InitsCountrys    `json:"countrys"`     // 国家列表
	Translations []*InitsTranslation `json:"translations"` // 翻译列表
	Menus        *InitsMenus         `json:"menus"`        // 菜单列表
}

// InitsService 初始化服务
type InitsService struct {
	Name string `json:"name"` // 名称
	Link string `json:"link"` // 链接
	Icon string `json:"icon"` // 图标
}

// InitsSettings 初始化设置
type InitsSettings struct {
	Basic    *constant.SettingBasicTemplate    `json:"basic"`    // 基础设置
	Login    *constant.SettingLoginTemplate    `json:"login"`    // 登录设置
	Register *constant.SettingRegisterTemplate `json:"register"` // 注册设置
}

// HomeRes 首页结果
type HomeRes struct {
	Banner       *constant.SettingBanner       `json:"banner"`       // 轮播图
	Notices      []*ArticleNotContentRes       `json:"notices"`      // 公告
	Announcement *constant.SettingNoticeTips   `json:"announcement"` // 公告提示词
	Socials      []*constant.SettingSiteSocial `json:"socials"`      // 社交
}

// InitsLanguages 初始化语言
type InitsLanguages struct {
	Icon   string `json:"icon"`   // 语言图标
	Label  string `json:"label"`  // 名称
	Locale string `json:"locale"` // 语言区域设置(如:zh-CN,en-US)
}

// InitsCountrys 初始化国家
type InitsCountrys struct {
	ID    uint   `json:"id"`    // 国家ID
	Icon  string `json:"icon"`  // 国旗图标
	Label string `json:"label"` // 名称
	ISO2  string `json:"iso2"`  // ISO 3166-1 alpha-2代码
	Code  string `json:"code"`  // 国家区号
}

// InitsTranslation 初始化翻译
type InitsTranslation struct {
	Label string `json:"label"` // 名称
	Value string `json:"value"` // 值
}

// InitsMenus 初始化菜单
type InitsMenus struct {
	Tabbar        []*InitsMenuItems `json:"tabbar"`         // 底部导航栏
	Users         []*InitsMenuItems `json:"users"`          // 用户菜单
	UsersQuick    []*InitsMenuItems `json:"users_quick"`    // 用户快捷菜单
	HomeQuick     []*InitsMenuItems `json:"home_quick"`     // 首页快捷菜单
	DesktopNavbar []*InitsMenuItems `json:"desktop_navbar"` // 桌面端导航栏
	DesktopUsers  []*InitsMenuItems `json:"desktop_users"`  // 桌面端用户菜单
}

// InitsMenuItems 初始化菜单项
type InitsMenuItems struct {
	Label      string            `json:"label"`       // 名称
	Subtitle   string            `json:"subtitle"`    // 副标题
	Icon       string            `json:"icon"`        // 图标
	ActiveIcon string            `json:"active_icon"` // 激活图标
	Route      string            `json:"route"`       // 路由
	Children   []*InitsMenuItems `json:"children"`    // 子菜单
}
