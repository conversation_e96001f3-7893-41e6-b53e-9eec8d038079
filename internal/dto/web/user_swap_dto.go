package web

import (
	"gin/internal/dto/common"
	"time"
)

// UserSwapListReq 用户闪兑列表请求
type UserSwapListReq struct {
	common.Pagination
}

// UserSwapListRes 用户闪兑列表返回
type UserSwapListRes struct {
	ID             uint      `json:"id"`               // ID
	SendAssetID    uint      `json:"send_asset_id"`    // 发送资产ID
	ReceiveAssetID uint      `json:"receive_asset_id"` // 接收资产ID
	SendAsset      AssetRes  `json:"send_asset"`       // 发送资产
	ReceiveAsset   AssetRes  `json:"receive_asset"`    // 接收资产
	SendAmount     float64   `json:"send_amount"`      // 发送金额
	ReceiveAmount  float64   `json:"receive_amount"`   // 接收金额
	Rate           float64   `json:"rate"`             // 汇率
	FixedFee       float64   `json:"fixed_fee"`        // 固定手续费
	RateFee        float64   `json:"rate_fee"`         // 手续费
	Type           int8      `json:"type"`             // 类型
	Status         int8      `json:"status"`           // 状态
	Reason         string    `json:"reason"`           // 原因
	CreatedAt      time.Time `json:"created_at"`       // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`       // 更新时间
}

// UserSwapCreateReq 用户闪兑创建请求
type UserSwapCreateReq struct {
	SecurityKey    string  `json:"security_key" form:"security_key"`                            // 安全密钥
	SendAssetID    uint    `json:"send_asset_id" form:"send_asset_id" binding:"required"`       // 发送资产ID
	ReceiveAssetID uint    `json:"receive_asset_id" form:"receive_asset_id" binding:"required"` // 接收资产ID
	Amount         float64 `json:"amount" form:"amount" binding:"required,gt=0"`                // 闪兑金额
}
