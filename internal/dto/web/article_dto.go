package web

import (
	"gin/internal/dto/common"
	"time"
)

// ArticleListRequest 文章列表请求
type ArticleListRequest struct {
	// 分页参数
	common.Pagination
}

// ArticleDetailRequest 文章详情请求
type ArticleDetailRequest struct {
	// ID 文章ID
	ID uint `json:"id" form:"id" binding:"required"`
}

// ArticleNotContentRes 文章没有内容结果
type ArticleNotContentRes struct {
	ID        uint      `json:"id"`         // ID
	Icon      string    `json:"icon"`       // 封面
	Route     string    `json:"route"`      // 路由
	Title     string    `json:"title"`      // 标题
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// ArticleRes 文章响应
type ArticleRes struct {
	// ID 文章ID
	ID uint `json:"id"`
	// Icon 图标
	Icon string `json:"icon"`
	// Title 标题
	Title string `json:"title"`
	// Content 内容
	Content string `json:"content"`
	// Route 路由
	Route string `json:"route"`
	// Sort 排序
	Sort int `json:"sort"`
	// Views 浏览量
	Views int `json:"views"`
	// Type 类型
	Type int8 `json:"type"`
	// IsTop 是否置顶
	IsTop bool `json:"isTop"`
	// IsHot 是否热门
	IsHot bool `json:"isHot"`
	// Status 状态
	Status int8 `json:"status"`
	// CreatedAt 创建时间
	CreatedAt string `json:"createdAt"`
}
