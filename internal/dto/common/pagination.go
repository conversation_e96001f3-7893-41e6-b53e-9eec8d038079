package common

// Pagination 通用分页请求参数
type Pagination struct {
	Page     int    `json:"page" form:"page" binding:"min=1"`           // 当前页码
	PageSize int    `json:"pageSize" form:"pageSize" binding:"min=1"`   // 每页数量
	OrderBy  string `json:"orderBy" form:"orderBy" binding:"omitempty"` // 排序字段
	Order    string `json:"order" form:"order" binding:"omitempty"`     // 排序方式：asc/desc
}

// GetPage 获取页码，默认为1
func (p *Pagination) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

// GetPageSize 获取每页数量，默认为10，最大为100
func (p *Pagination) GetPageSize() int {
	if p.PageSize <= 0 {
		return 10
	}
	if p.PageSize > 100 {
		return 100
	}
	return p.PageSize
}

// GetOffset 获取偏移量
func (p *Pagination) GetOffset() int {
	return (p.GetPage() - 1) * p.GetPageSize()
}

// GetLimit 获取限制数量
func (p *Pagination) GetLimit() int {
	return p.GetPageSize()
}

// GetOrderBy 获取排序字段，默认为id
func (p *Pagination) GetOrderBy() string {
	if p.OrderBy == "" {
		return "id"
	}
	return p.OrderBy
}

// GetOrder 获取排序方式，默认为desc
func (p *Pagination) GetOrder() string {
	if p.Order != "asc" && p.Order != "desc" {
		return "desc"
	}
	return p.Order
}

// GetOrderSQL 获取排序SQL
func (p *Pagination) GetOrderSQL() string {
	return p.GetOrderBy() + " " + p.GetOrder()
}
