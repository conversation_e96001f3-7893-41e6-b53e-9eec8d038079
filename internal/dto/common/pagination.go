package common

// Pagination 通用分页请求参数
type Pagination struct {
	Page     int    `json:"page" form:"page"`         // 当前页码
	PageSize int    `json:"pageSize" form:"pageSize"` // 每页数量
	OrderBy  string `json:"orderBy" form:"orderBy"`   // 排序字段
	Order    string `json:"order" form:"order"`       // 排序方式：asc/desc
}

// GetPage 获取页码，默认为1
func (p *Pagination) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

// GetPageSize 获取每页数量，默认为10，最大为100
func (p *Pagination) GetPageSize() int {
	if p.PageSize <= 0 {
		return 10
	}
	if p.PageSize > 100 {
		return 100
	}
	return p.PageSize
}

// GetOffset 获取偏移量
func (p *Pagination) GetOffset() int {
	return (p.GetPage() - 1) * p.GetPageSize()
}

// GetLimit 获取限制数量
func (p *Pagination) GetLimit() int {
	return p.GetPageSize()
}

// GetOrderBy 获取排序字段，默认为id
func (p *Pagination) GetOrderBy() string {
	if p.OrderBy == "" {
		return "id"
	}
	return p.OrderBy
}

// GetOrder 获取排序方式，默认为desc
func (p *Pagination) GetOrder() string {
	if p.Order != "asc" && p.Order != "desc" {
		return "desc"
	}
	return p.Order
}

// GetOrderSQL 获取排序SQL
func (p *Pagination) GetOrderSQL() string {
	return p.GetOrderBy() + " " + p.GetOrder()
}

// PageResult 分页结果
type PageResult struct {
	List     interface{} `json:"list"`     // 数据列表
	Total    int64       `json:"total"`    // 总数量
	Page     int         `json:"page"`     // 当前页码
	PageSize int         `json:"pageSize"` // 每页数量
	Pages    int         `json:"pages"`    // 总页数
}

// NewPageResult 创建分页结果
func NewPageResult(list interface{}, total int64, pagination *Pagination) *PageResult {
	pages := 0
	if pagination.GetPageSize() > 0 {
		pages = int((total + int64(pagination.GetPageSize()) - 1) / int64(pagination.GetPageSize()))
	}
	return &PageResult{
		List:     list,
		Total:    total,
		Page:     pagination.GetPage(),
		PageSize: pagination.GetPageSize(),
		Pages:    pages,
	}
}
