package admin

// PositionController 岗位控制器
//type PositionController struct {
//	positionService service.PositionService
//}
//
//// NewPositionController 创建岗位控制器
//func NewPositionController() *PositionController {
//	return &PositionController{
//		//positionService: service.NewPositionService(),
//	}
//}

// GetPositions 获取岗位列表（分页）
//func (c *PositionController) GetPositions(ctx *context.CustomContext) {
//	// 解析分页参数
//	pagination := &common.Pagination{}
//	if err := ctx.ShouldBindQuery(pagination); err != nil {
//		logger.Error("解析分页参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "解析分页参数失败", err.Error())
//		return
//	}
//
//	// 获取岗位列表
//	result, err := c.positionService.GetPositionsWithPagination(pagination)
//	if err != nil {
//		logger.Error("获取岗位列表失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, "获取岗位列表失败")
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, result)
//}
//
//// GetPositionByID 根据ID获取岗位
//func (c *PositionController) GetPositionByID(ctx *context.CustomContext) {
//	// 解析ID参数
//	idStr := ctx.Param("id")
//	id, err := strconv.ParseUint(idStr, 10, 64)
//	if err != nil {
//		logger.Error("解析ID参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "无效的ID参数", err.Error())
//		return
//	}
//
//	// 获取岗位
//	position, err := c.positionService.GetPositionByID(id)
//	if err != nil {
//		logger.Error("获取岗位失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, "获取岗位失败")
//		return
//	}
//
//	if position == nil {
//		common.AbortNotFound(ctx.Context, "岗位不存在")
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, position)
//}
//
//// CreatePosition 创建岗位
//func (c *PositionController) CreatePosition(ctx *context.CustomContext) {
//	// 解析请求参数
//	var position models.Position
//	if err := ctx.ShouldBindJSON(&position); err != nil {
//		logger.Error("解析请求参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "解析请求参数失败", err.Error())
//		return
//	}
//
//	// 设置创建人
//	if ctx.Claims != nil {
//		position.CreatedBy = ctx.Claims.UserID
//		position.UpdatedBy = ctx.Claims.UserID
//		position.TenantID = ctx.Claims.TenantID
//	}
//
//	// 创建岗位
//	err := c.positionService.CreatePosition(&position)
//	if err != nil {
//		logger.Error("创建岗位失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, err.Error())
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, position)
//}
//
//// UpdatePosition 更新岗位
//func (c *PositionController) UpdatePosition(ctx *context.CustomContext) {
//	// 解析ID参数
//	idStr := ctx.Param("id")
//	id, err := strconv.ParseUint(idStr, 10, 64)
//	if err != nil {
//		logger.Error("解析ID参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "无效的ID参数", err.Error())
//		return
//	}
//
//	// 解析请求参数
//	var position models.Position
//	if err := ctx.ShouldBindJSON(&position); err != nil {
//		logger.Error("解析请求参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "解析请求参数失败", err.Error())
//		return
//	}
//
//	// 设置ID和更新人
//	position.ID = id
//	if ctx.Claims != nil {
//		position.UpdatedBy = ctx.Claims.UserID
//	}
//
//	// 更新岗位
//	err = c.positionService.UpdatePosition(&position)
//	if err != nil {
//		logger.Error("更新岗位失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, err.Error())
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, position)
//}
//
//// DeletePosition 删除岗位
//func (c *PositionController) DeletePosition(ctx *context.CustomContext) {
//	// 解析ID参数
//	idStr := ctx.Param("id")
//	id, err := strconv.ParseUint(idStr, 10, 64)
//	if err != nil {
//		logger.Error("解析ID参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "无效的ID参数", err.Error())
//		return
//	}
//
//	// 删除岗位
//	err = c.positionService.DeletePosition(id)
//	if err != nil {
//		logger.Error("删除岗位失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, err.Error())
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, nil)
//}
//
//// GetPositionsByDeptID 根据部门ID获取岗位
//func (c *PositionController) GetPositionsByDeptID(ctx *context.CustomContext) {
//	// 解析部门ID参数
//	deptIDStr := ctx.Query("deptId")
//	deptID, err := strconv.ParseUint(deptIDStr, 10, 64)
//	if err != nil {
//		logger.Error("解析部门ID参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "无效的部门ID参数", err.Error())
//		return
//	}
//
//	// 获取岗位列表
//	positions, err := c.positionService.GetPositionsByDeptID(deptID)
//	if err != nil {
//		logger.Error("获取部门岗位失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, "获取部门岗位失败")
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, positions)
//}
//
//// BatchDeletePositions 批量删除岗位
//func (c *PositionController) BatchDeletePositions(ctx *context.CustomContext) {
//	// 解析请求参数
//	var ids []uint64
//	if err := ctx.ShouldBindJSON(&ids); err != nil {
//		logger.Error("解析请求参数失败", zap.Error(err))
//		common.AbortBadRequest(ctx.Context, "解析请求参数失败", err.Error())
//		return
//	}
//
//	// 批量删除岗位
//	err := c.positionService.BatchDeletePositions(ids)
//	if err != nil {
//		logger.Error("批量删除岗位失败", zap.Error(err))
//		common.AbortInternalServerError(ctx.Context, "批量删除岗位失败")
//		return
//	}
//
//	// 返回成功响应
//	common.Success(ctx.Context, nil)
//}
