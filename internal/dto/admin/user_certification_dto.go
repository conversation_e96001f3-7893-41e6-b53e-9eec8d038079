package admin

import "gin/internal/dto/common"

// UserCertificationListReq 用户认证列表查询参数
type UserCertificationListReq struct {
	// 用户ID
	UserID *uint `json:"user_id" form:"user_id" views:"label:用户;searchable"`
	// 证件姓名
	RealName *string `json:"real_name" form:"real_name" views:"label:证件姓名"`
	// 证件号码
	IDNumber *string `json:"id_number" form:"id_number" views:"label:证件号码"`
	// 类型
	Type *int8 `json:"type" form:"type" views:"label:类型;type:select"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`

	// 分页请求
	common.Pagination
}

// UserCertificationCreateReq 用户认证创建请求
type UserCertificationCreateReq struct {
	// 用户ID
	UserID uint `json:"user_id" form:"user_id" binding:"required" views:"label:用户;searchable"`
	// 类型
	Type int8 `json:"type" form:"type" binding:"required" views:"label:类型;type:select"`
	// 证件姓名
	RealName string `json:"real_name" form:"real_name" binding:"required" views:"label:证件姓名"`
	// 证件号码
	IDNumber string `json:"id_number" form:"id_number" binding:"required" views:"label:证件号码"`
	// 证件照正面
	Photo1 string `json:"photo1" form:"photo1" binding:"required" views:"label:正面照;type:image"`
	// 证件照反面
	Photo2 string `json:"photo2" form:"photo2" binding:"required" views:"label:反面照;type:image"`
}

// UserCertificationUpdateReq 用户认证更新请求
type UserCertificationUpdateReq struct {
	// 证件照正面
	Photo1 *string `json:"photo1" form:"photo1" views:"label:正面照;type:image"`
	// 证件照反面
	Photo2 *string `json:"photo2" form:"photo2" views:"label:反面照;type:image"`
	// 证件姓名
	RealName *string `json:"real_name" form:"real_name" views:"label:证件姓名"`
	// 证件号码
	IDNumber *string `json:"id_number" form:"id_number" views:"label:证件号码"`
	// 详细地址
	Address *string `json:"address" form:"address" views:"label:详细地址"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// 拒绝原因
	Reason *string `json:"reason" form:"reason" views:"label:拒绝原因"`
}

// UserCertificationStatusReq 用户认证状态审核
type UserCertificationStatusReq struct {
	// 状态
	Status int8 `json:"status" form:"status" binding:"required" views:"label:状态;type:select"`
	// 拒绝原因
	Reason *string `json:"reason" form:"reason" views:"label:拒绝原因;type:textarea"`
}
