package admin

import "gin/internal/dto/common"

// UserAssetListReq 用户资产列表查询参数
type UserAssetListReq struct {
	// 管理ID
	ManagerID *uint `json:"manager_id" form:"manager_id" views:"label:管理;searchable"`
	// 用户ID
	UserID *uint `json:"user_id" form:"user_id" views:"label:用户;searchable"`
	// 资产ID
	AssetID *uint `json:"asset_id" form:"asset_id" views:"label:资产;type:select"`
	// 分页参数
	common.Pagination
}

// UserAssetCreateReq 用户资产创建请求
type UserAssetCreateReq struct {
	// 用户ID
	UserID uint `json:"user_id" form:"user_id" views:"label:用户;searchable"`
	// 资产ID
	AssetID uint `json:"asset_id" form:"asset_id" views:"label:资产;type:select"`
}

// UserAssetOperationsReq 用户资产余额更新请求
type UserAssetOperationsReq struct {
	// 账单类型
	Type int8 `json:"type" form:"type" binding:"required" views:"label:类型;type:select"`
	// 金额
	Amount float64 `json:"amount" form:"amount" binding:"required" views:"label:金额;type:number"`
	// 是否写入账单
	WriteBill bool `json:"write_bill" form:"write_bill" views:"label:是否写入账单;type:toggle"`
}

// UserAssetUpdateReq 用户资产更新请求
type UserAssetUpdateReq struct {
	// 可用金额
	AvailableAmount *float64 `json:"available_amount" form:"available_amount" views:"label:可用金额;type:number"`
	// 冻结金额
	FrozenAmount *float64 `json:"frozen_amount" form:"frozen_amount" views:"label:冻结金额;type:number"`
	// 收益金额
	ProfitAmount *float64 `json:"profit_amount" form:"profit_amount" views:"label:收益金额;type:number"`
	// 充值总金额
	SumDepositAmount *float64 `json:"sum_deposit_amount" form:"sum_deposit_amount" views:"label:充值总金额;type:number"`
	// 提现总金额
	SumWithdrawAmount *float64 `json:"sum_withdraw_amount" form:"sum_withdraw_amount" views:"label:提现总金额;type:number"`
}
