package admin

import "gin/internal/dto/common"

// OrderCreateReq 创建订单请求
type OrderCreateReq struct {
	ProductID uint    `json:"product_id" binding:"required"`
	UserID    uint    `json:"user_id" binding:"required"`
	Amount    float64 `json:"amount" binding:"required,min=0"`
}

// OrderUpdateReq 更新订单请求
type OrderUpdateReq struct {
	Status *int8 `json:"status" binding:"omitempty,oneof=1 2 3 4 5"`
}

// OrderListReq 获取订单列表请求
type OrderListReq struct {
	ProductID *uint `json:"product_id" form:"product_id"`
	UserID    *uint `json:"user_id" form:"user_id"`
	Status    *int  `json:"status" form:"status"`
	common.Pagination
}
