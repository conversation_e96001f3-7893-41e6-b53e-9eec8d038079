package admin

import (
	"github.com/shirou/gopsutil/v4/disk"
	"time"
)

type MonitorSearchReq struct {
	SystemSes   *SystemSes       `json:"systemSes"`
	StorageSes  []disk.UsageStat `json:"storageSes"`
	CpuUsage    *CpuUsage        `json:"cpuUsage"`
	MemoryUsage *MemUsage        `json:"memoryUsage"`
}

type SystemSes struct {
	SysComputerName interface{} `json:"sysComputerName"`
	SysOsName       interface{} `json:"sysOsName"`
	SysComputerIp   interface{} `json:"sysComputerIp"`
	SysOsArch       interface{} `json:"sysOsArch"`
	GoName          interface{} `json:"goName"`
	GoVersion       interface{} `json:"goVersion"`
	GoStartTime     time.Time   `json:"goStartTime"`
	GoRunTime       interface{} `json:"goRunTime"`
}

type CpuUsage struct {
	CpuNum   int     `json:"cpuNum"`
	CpuUsed  float64 `json:"cpuUsed"`
	CpuAvg5  float64 `json:"cpuAvg5"`
	CpuAvg15 float64 `json:"cpuAvg15"`
}

type MemUsage struct {
	MemTotal uint64  `json:"memTotal"`
	MemUsed  uint64  `json:"memUsed"`
	MemFree  uint64  `json:"memFree"`
	MemUsage float64 `json:"memUsage"`
}

type GoUsage struct {
	GoTotal uint64  `json:"goTotal"`
	GoUsed  uint64  `json:"goUsed"`
	GoFree  uint64  `json:"goFree"`
	GoUsage float64 `json:"goUsage"`
}
