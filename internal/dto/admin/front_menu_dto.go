package admin

import "gin/internal/dto/common"

// FrontMenuListReq 获取前台菜单列表请求
type FrontMenuListReq struct {
	// ParentID 父级ID
	ParentID *uint `form:"parentId"`
	// Name 菜单名称
	Name *string `form:"name"`
	// Subtitle 菜单副标题
	Subtitle *string `form:"subtitle"`
	// Route 菜单路由
	Route *string `form:"route"`
	// Sort 菜单排序
	Sort *int8 `form:"sort"`
	// Mode 菜单模式
	Mode *int8 `form:"mode"`
	// Type 菜单类型
	Type *int8 `form:"type"`
	// Status 菜单状态
	Status *int8 `form:"status"`
	// 分页参数
	common.Pagination
}

// FrontMenuUpdateReq 更新前台菜单请求
type FrontMenuUpdateReq struct {
	// ParentID 父级ID
	ParentID *uint `json:"parentId" binding:"omitempty"`
	// Name 菜单名称
	Name *string `json:"name" binding:"omitempty,max=255"`
	// Subtitle 菜单副标题
	Subtitle *string `json:"subtitle" binding:"omitempty,max=255"`
	// Icon 菜单图标
	Icon *string `json:"icon" binding:"omitempty,max=255"`
	// ActiveIcon 菜单激活图标
	ActiveIcon *string `json:"activeIcon" binding:"omitempty,max=255"`
	// Route 菜单路由
	Route *string `json:"route" binding:"omitempty,max=255"`
	// Sort 菜单排序
	Sort *int8 `json:"sort" binding:"omitempty,min=0"`
	// Mode 菜单模式
	Mode *int8 `json:"mode" binding:"omitempty,oneof=1 2"`
	// Type 菜单类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1"`
	// Status 菜单状态
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
}
