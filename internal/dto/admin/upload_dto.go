package admin

import (
	"time"
)

// UploadResponse 文件上传响应
type UploadResponse struct {
	FileName   string    `json:"fileName"`   // 原始文件名
	Size       int64     `json:"size"`       // 文件大小（字节）
	URL        string    `json:"url"`        // 访问URL
	UploadTime time.Time `json:"uploadTime"` // 上传时间
}

// MultiUploadResponse 多文件上传响应
type MultiUploadResponse struct {
	Success      []UploadResponse `json:"success"`      // 成功上传的文件
	Failed       []UploadError    `json:"failed"`       // 失败的文件
	SuccessCount int              `json:"successCount"` // 成功数量
	FailedCount  int              `json:"failedCount"`  // 失败数量
}

// UploadError 上传错误信息
type UploadError struct {
	FileName string `json:"fileName"` // 文件名
	Error    string `json:"error"`    // 错误信息
}

// UploadConfig 上传配置
type UploadConfig struct {
	MaxFileSize  int64    `json:"maxFileSize"`  // 最大文件大小（字节）
	AllowedTypes []string `json:"allowedTypes"` // 允许的文件类型
}
