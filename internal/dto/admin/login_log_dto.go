package admin

import (
	"gin/internal/dto/common"
)

// LoginLogListRequest 获取登录日志列表请求参数
type LoginLogListRequest struct {
	// Username 用户名
	Username *string ` form:"username" json:"username"`
	// IP 登录IP
	IP *string `form:"ip" json:"IP"`
	// Status 登录状态
	Status *int8 `form:"status" json:"status"`
	// Type 访问类型
	Type *int8 `form:"type" json:"type"`
	// UserID 用户ID
	UserID *uint `form:"userId" json:"userID"`
	// ManagerID 管理员ID
	ManagerID *uint `form:"managerId" json:"managerID"`
	// OS 操作系统
	OS *string `form:"os" json:"OS"`
	// Device 设备类型
	Device *string `form:"device" json:"device"`
	// StartTime 开始时间
	StartTime *string `form:"startTime" json:"startTime"`
	// EndTime 结束时间
	EndTime *string `form:"endTime" json:"endTime"`

	common.Pagination `json:"common.Pagination"`
}

// LoginLogBatchDeleteRequest 批量删除登录日志请求参数
type LoginLogBatchDeleteRequest struct {
	IDs []uint `json:"ids" binding:"required"`
}
