package admin

import "gin/internal/dto/common"

// CountryCreateRequest 创建国家请求
type CountryCreateRequest struct {
	// NameZh 中文名称
	NameZh string `json:"nameZh" binding:"required,max=100"`
	// NameNative 本地语言名称
	NameNative string `json:"nameNative" binding:"required,max=100"`
	// Icon 国旗图标URL
	Icon string `json:"icon" binding:"required,max=255"`
	// ISO2 ISO 3166-1 alpha-2代码
	ISO2 string `json:"iso2" binding:"required,len=2"`
	// Sort 排序
	Sort int `json:"sort" binding:"required,min=0"`
	// Code 国家区号
	Code string `json:"code" binding:"required,max=10"`
}

// CountryUpdateRequest 更新国家请求
type CountryUpdateRequest struct {
	// NameZh 中文名称
	NameZh *string `json:"nameZh" binding:"omitempty,max=100"`
	// NameNative 本地语言名称
	NameNative *string `json:"nameNative" binding:"omitempty,max=100"`
	// Icon 国旗图标URL
	Icon *string `json:"icon" binding:"omitempty,max=255"`
	// ISO2 ISO 3166-1 alpha-2代码
	ISO2 *string `json:"iso2" binding:"omitempty,len=2"`
	// Sort 排序
	Sort *int `json:"sort" binding:"omitempty,min=0"`
	// Code 国家区号
	Code *string `json:"code" binding:"omitempty,max=10"`
}

// CountryListRequest 获取国家列表请求
type CountryListRequest struct {
	// NameZh 中文名称
	NameZh *string `json:"nameZh" form:"nameZh"`
	// NameNative 本地语言名称
	NameNative *string `json:"nameNative" form:"nameNative"`
	// ISO2 ISO 3166-1 alpha-2代码
	ISO2 *string `json:"iso2" form:"iso2"`
	// Code 国家区号
	Code *string `json:"code" form:"code"`
	// Status 状态
	Status *int8 `json:"status" form:"status" binding:"omitempty,oneof=1 0"`
	// 分页参数
	common.Pagination
}

// CountryBatchDeleteRequest 批量删除国家请求
type CountryBatchDeleteRequest struct {
	// IDs 国家ID列表
	IDs []uint `json:"ids" binding:"required"`
}
