package admin

import "gin/internal/dto/common"

// OperateLogListRequest 获取操作日志列表请求参数
type OperateLogListRequest struct {
	// UserID 用户ID
	UserID *uint `form:"userId" json:"userID"`
	// ManagerID 管理员ID
	ManagerID *uint `form:"managerId" json:"managerID"`
	// Username 用户名
	Username *string `form:"username" json:"username"`
	// Module 操作模块
	Module *string `form:"module" json:"module"`
	// Type 操作类型
	Type *int8 `form:"type" json:"type"`
	// Mode 操作模式
	Mode *int8 `form:"mode" json:"mode"`
	// StartTime 开始时间
	StartTime *string `form:"startTime" json:"startTime"`
	// EndTime 结束时间
	EndTime *string `form:"endTime" json:"endTime"`

	common.Pagination `json:"common.Pagination"`
}

// OperateLogBatchDeleteRequest 批量删除操作日志请求参数
type OperateLogBatchDeleteRequest struct {
	IDs []uint `json:"ids" binding:"required"`
}
