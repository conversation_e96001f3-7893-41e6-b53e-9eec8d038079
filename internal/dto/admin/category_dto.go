package admin

import "gin/internal/dto/common"

// CategoryCreateReq 创建分类请求
type CategoryCreateReq struct {
	Name string `json:"name" binding:"required,max=100"`
	Sort uint16 `json:"sort" binding:"required,min=0"`
}

// CategoryUpdateReq 更新分类请求
type CategoryUpdateReq struct {
	Name *string `json:"name" binding:"omitempty,max=100"`
	Sort *uint16 `json:"sort" binding:"omitempty,min=0"`
}

// CategoryListReq 获取分类列表请求
type CategoryListReq struct {
	Name *string `json:"name" form:"name"`
	Sort *int    `json:"sort" form:"sort"`
	common.Pagination
}
