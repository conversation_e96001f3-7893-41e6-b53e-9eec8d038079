package admin

import "gin/internal/dto/common"

// UserWalletListReq 用户钱包列表查询参数
type UserWalletListReq struct {
	// 管理ID
	ManagerID *uint `json:"manager_id" form:"manager_id" views:"label:管理;searchable"`
	// 用户ID
	UserID *uint `json:"user_id" form:"user_id" views:"label:用户;searchable"`
	// 资产ID
	AssetID *uint `json:"asset_id" form:"asset_id" views:"label:资产;type:select"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// StartTime 开始时间
	StartTime *string `json:"start_time" form:"start_time" views:"label:开始时间;type:date"`
	// EndTime 结束时间
	EndTime *string `json:"end_time" form:"end_time" views:"label:结束时间;type:date"`

	// 分页请求
	common.Pagination
}

// UserWalletCreateReq 用户钱包创建请求
type UserWalletCreateReq struct {
	// 用户ID
	UserID uint `json:"user_id" form:"user_id" binding:"required" views:"label:用户;searchable"`
	// 资产ID
	AssetID uint `json:"asset_id" form:"asset_id" binding:"required" views:"label:资产;type:select"`
	// 金额
	Amount float64 `json:"amount" form:"amount" binding:"required,gt=0" views:"label:金额;type:number"`
}

// UserWalletUpdateReq 用户钱包更新请求
type UserWalletUpdateReq struct {
	// 凭证
	Proof *string `json:"proof" form:"proof" views:"label:凭证;type:image"`
	// 金额
	Amount *float64 `json:"amount" form:"amount" views:"label:金额;type:number;row:4"`
	// 固定手续费
	FixedFee *float64 `json:"fixed_fee" form:"fixed_fee" views:"label:固定手续费;type:number;row:4"`
	// 手续费(%)
	RateFee *float64 `json:"rate_fee" form:"rate_fee" views:"label:手续费(%);type:number;row:4"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// 时间
	CreatedAt *string `json:"created_at" form:"created_at" views:"label:时间;type:datetime;row:6"`
	// 完成时间
	UpdatedAt *string `json:"updated_at" form:"updated_at" views:"label:完成时间;type:datetime;row:6"`
	// 数据
	Data *UserWalletData `json:"data" form:"data" views:"label:数据"`
	// 原因
	Reason *string `json:"reason" form:"reason" views:"label:原因;type:textarea"`
}

// UserWalletData 用户钱包数据
type UserWalletData struct {
	Name   string `json:"name" form:"name" views:"label:名称;row:6"`
	CardNo string `json:"card_no" form:"card_no" views:"label:卡号;row:6"`
}

// UserWalletStatusReq 用户钱包状态更新请求
type UserWalletStatusReq struct {
	// 状态
	Status int8 `json:"status" form:"status" binding:"required" views:"label:状态;type:select"`
	// 拒绝原因
	Reason string `json:"reason" form:"reason" views:"label:拒绝原因;type:textarea"`
}
