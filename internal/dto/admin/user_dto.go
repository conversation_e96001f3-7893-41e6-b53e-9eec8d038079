package admin

import "gin/internal/dto/common"

// UserListRequest 用户列表请求
type UserListRequest struct {
	// Username 用户名
	Username *string `json:"username" form:"username" binding:"omitempty,max=60"`
	// 昵称
	Nickname *string `json:"nickname" form:"nickname" binding:"omitempty,max=50"`
	// 手机号
	Mobile *string `json:"mobile" form:"mobile" binding:"omitempty,max=20"`
	// 邮箱
	Email *string `json:"email" form:"email" binding:"omitempty,max=100"`
	// 性别
	Gender *int8 `json:"gender" form:"gender" binding:"omitempty,oneof=0 1 2"`
	// 用户类型
	Type *int8 `json:"type" form:"type" binding:"omitempty,oneof=1 2"`
	// 状态
	Status *int8 `json:"status" form:"status" binding:"omitempty,oneof=10 -1"`
	// 开始时间
	StartTime *string `json:"startTime" form:"start_time" binding:"omitempty"`
	// 结束时间
	EndTime *string `json:"endTime" form:"end_time" binding:"omitempty"`
	// 分页参数
	common.Pagination
}

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	// 用户名
	Username string `json:"username" binding:"required,max=60"`
	// 密码
	Password string `json:"password" binding:"required,min=6"`
	// 用户类型
	Type int8 `json:"type" binding:"required,oneof=1 2"`
}

// UserUpdateRequest 用户更新请求
type UserUpdateRequest struct {
	// 昵称
	Nickname *string `json:"nickname" binding:"omitempty,max=50"`
	// 手机号
	Telephone *string `json:"telephone" binding:"omitempty,max=20"`
	// 邮箱
	Email *string `json:"email" binding:"omitempty,max=100"`
	// 头像
	Avatar *string `json:"avatar"`
	// 性别
	Gender *int8 `json:"gender" binding:"omitempty,oneof=0 1 2"`
	// 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=10 -1"`
	// 用户类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2"`
	// 详情
	Description *string `json:"description" binding:"omitempty,max=255"`
}

// UserBatchDeleteRequest 批量删除用户请求
type UserBatchDeleteRequest struct {
	// IDs 用户ID列表
	IDs []uint `json:"ids" binding:"required"`
}
