package admin

import (
	"gin/internal/dto/common"
)

// SettingListReq 应用设置列表查询
type SettingListReq struct {
	//分组
	GroupID *uint `json:"group_id" form:"group_id" views:"label:分组;type:select"`
	//名称
	Name *string `json:"name" form:"name" views:"label:名称"`

	// 分页参数
	common.Pagination
}

// SettingUpdateReq 应用设置更新参数
type SettingUpdateReq struct {
	// 名称
	Name *string `json:"name" form:"name" views:"label:名称"`
}

// SettingDataUpdateReq 应用设置配置参数
type SettingDataUpdateReq struct {
	Value interface{} `json:"value" form:"value"`
}
