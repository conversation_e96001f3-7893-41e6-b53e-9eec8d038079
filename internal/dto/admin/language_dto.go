package admin

import "gin/internal/dto/common"

// LanguageCreateRequest 创建语言请求
type LanguageCreateRequest struct {
	// Code 语言代码
	Code string `json:"code" binding:"required,max=10"`
	// Name 中文名称
	Name string `json:"name" binding:"required,max=50"`
	// NameNative 本地语言名称
	NameNative string `json:"nameNative" binding:"required,max=100"`
	// Icon 语言图标
	Icon string `json:"icon" binding:"omitempty,max=255"`
	// Sort 排序
	Sort int `json:"sort" binding:"omitempty,min=0"`
}

// LanguageUpdateRequest 更新语言请求
type LanguageUpdateRequest struct {
	// Name 中文名称
	Name *string `json:"name" binding:"omitempty,max=50"`
	// NameNative 本地语言名称
	NameNative *string `json:"nameNative" binding:"omitempty,max=100"`
	// Icon 语言图标
	Icon *string `json:"icon" binding:"omitempty,max=255"`
	// Sort 排序
	Sort *int `json:"sort" binding:"omitempty,min=0"`
}

// LanguageListRequest 获取语言列表请求
type LanguageListRequest struct {
	// Code 语言代码
	Code *string `json:"code" form:"code"`
	// Name 中文名称
	Name *string `json:"name" form:"name"`
	// NameNative 本地语言名称
	NameNative *string `json:"nameNative" form:"nameNative"`
	// Status 状态
	Status *int8 `json:"status" form:"status"`
	// 分页参数
	common.Pagination
}

// LanguageBatchDeleteRequest 批量删除语言请求
type LanguageBatchDeleteRequest struct {
	// IDs 语言ID列表
	IDs []uint `json:"ids" binding:"required"`
}
