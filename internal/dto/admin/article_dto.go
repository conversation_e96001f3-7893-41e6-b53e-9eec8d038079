package admin

import "gin/internal/dto/common"

// ArticleCreateRequest 创建文章请求
type ArticleCreateRequest struct {
	// Icon 图标
	Icon string `json:"icon" binding:"omitempty,max=255" views:"label:图标;type:image"`
	// Title 标题
	Title string `json:"title" binding:"required,max=255" views:"label:标题;type:input"`
	// Content 内容
	Content string `json:"content" binding:"required" views:"label:内容;type:textarea"`
	// Route 路由
	Route string `json:"route" binding:"omitempty,max=255" views:"label:路由;type:input"`
	// Sort 排序
	Sort int `json:"sort" binding:"omitempty,min=0" views:"label:排序;type:input"`
	// Views 浏览量
	Views int `json:"views" binding:"omitempty,min=0" views:"label:浏览量;type:input"`
	// Type 类型
	Type int8 `json:"type" binding:"required,oneof=1 2 3" views:"label:类型;type:select;options:1=文章,2=分类,3=页面"`
	// IsTop 是否置顶
	IsTop bool `json:"isTop" binding:"omitempty" views:"label:是否置顶;type:switch"`
	// IsHot 是否热门
	IsHot bool `json:"isHot" binding:"omitempty" views:"label:是否热门;type:switch"`
	// Status 状态
	Status int8 `json:"status" binding:"required,oneof=1 0" views:"label:状态;type:select;options:1=正常,0=禁用"`
}

// ArticleUpdateRequest 更新文章请求
type ArticleUpdateRequest struct {
	// Icon 图标
	Icon *string `json:"icon" binding:"omitempty,max=255"`
	// Title 标题
	Title *string `json:"title" binding:"omitempty,max=255"`
	// Content 内容
	Content *string `json:"content" binding:"omitempty"`
	// Route 路由
	Route *string `json:"route" binding:"omitempty,max=255"`
	// Sort 排序
	Sort *int `json:"sort" binding:"omitempty,min=0"`
	// Views 浏览量
	Views *int `json:"views" binding:"omitempty,min=0"`
	// Type 类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2 3"`
	// IsTop 是否置顶
	IsTop *bool `json:"isTop" binding:"omitempty"`
	// IsHot 是否热门
	IsHot *bool `json:"isHot" binding:"omitempty"`
	// Status 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
}

// ArticleListRequest 获取文章列表请求
type ArticleListRequest struct {
	// Title 标题
	Title *string `json:"title" form:"title" binding:"omitempty,max=255" views:"label:标题;type:input"`
	// Route 路由
	Route *string `json:"route" form:"route" binding:"omitempty,max=255" views:"label:路由;type:input"`
	// Type 类型
	Type *int8 `json:"type" form:"type" binding:"omitempty,oneof=1 2 3" views:"label:类型;type:select;options:1=全部,2=文章,3=分类"`
	// IsTop 是否置顶
	IsTop *bool `json:"isTop" form:"isTop" binding:"omitempty" views:"label:是否置顶;type:switch"`
	// IsHot 是否热门
	IsHot *bool `json:"isHot" form:"isHot" binding:"omitempty" views:"label:是否热门;type:switch"`
	// Status 状态
	Status *int8 `json:"status" form:"status" binding:"omitempty,oneof=1 0" views:"label:状态;type:select;options:1=全部,0=草稿,1=已发布"`
	// 分页参数
	common.Pagination
}

// ArticleBatchDeleteRequest 批量删除文章请求
type ArticleBatchDeleteRequest struct {
	// IDs 文章ID列表
	IDs []uint `json:"ids" binding:"required"`
}
