package admin

import "gin/internal/dto/common"

// UserNoticeListRequest 用户通知列表请求
type UserNoticeListRequest struct {
	// SenderID 发送者ID
	SenderID *uint `form:"senderId"`
	// ReceiverID 接收者ID
	ReceiverID *uint `form:"receiverId"`
	// Title 通知标题
	Title *string `form:"title"`
	// Summary 通知摘要
	Summary *string `form:"summary"`
	// Content 通知内容
	Content *string `form:"content"`
	// Priority 通知优先级
	Priority *int8 `form:"priority"`
	// Status 通知状态
	Status *int8 `form:"status"`
	// Type 通知类型
	Type *int8 `form:"type"`
	// Route 通知路由
	Route *string `form:"route"`
	// IsRead 是否已读
	IsRead *bool `form:"isRead"`
	// StartTime 开始时间
	StartTime *string `form:"startTime"`
	// EndTime 结束时间
	EndTime *string `form:"endTime"`
	// 分页参数
	common.Pagination
}

// UserNoticeCreateRequest 用户通知创建请求
type UserNoticeCreateRequest struct {
	// SenderID 发送者ID
	SenderID uint `json:"senderId" binding:"required"`
	// ReceiverID 接收者ID
	ReceiverID uint `json:"receiverId" binding:"required"`
	// Title 通知标题
	Title string `json:"title" binding:"required,max=255"`
	// Summary 通知摘要
	Summary string `json:"summary" binding:"omitempty,max=500"`
	// Content 通知内容
	Content string `json:"content" binding:"required"`
	// Priority 通知优先级
	Priority int8 `json:"priority" binding:"required,oneof=1 2 3 4"`
	// Type 通知类型
	Type int8 `json:"type" binding:"required,oneof=1 2 3 4 5 6"`
	// Route 通知路由
	Route string `json:"route" binding:"required,max=255"`
}

// UserNoticeUpdateRequest 用户通知更新请求
type UserNoticeUpdateRequest struct {
	// Title 通知标题
	Title *string `json:"title" binding:"omitempty,max=255"`
	// Summary 通知摘要
	Summary *string `json:"summary" binding:"omitempty,max=500"`
	// Content 通知内容
	Content *string `json:"content" binding:"omitempty"`
	// Priority 通知优先级
	Priority *int8 `json:"priority" binding:"omitempty,oneof=1 2 3 4"`
	// Type 通知类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2 3 4 5 6"`
	// Route 通知路由
	Route *string `json:"route" binding:"omitempty,max=255"`
	// IsRead 是否已读
	IsRead *bool `json:"isRead" binding:"omitempty"`
}

// UserNoticeBatchDeleteRequest 用户通知批量删除请求
type UserNoticeBatchDeleteRequest struct {
	// IDs 用户通知ID列表
	IDs []uint `json:"ids" binding:"required"`
}
