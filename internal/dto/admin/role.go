package admin

import "time"

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	Page     int    `form:"page" binding:"required,min=1"`
	PageSize int    `form:"pageSize" binding:"required,min=1,max=100"`
	Keyword  string `form:"keyword"`
	Status   *int8  `form:"status"`
}

// RoleCreateRequest 创建角色请求
type RoleCreateRequest struct {
	Name        string   `json:"name" binding:"required,max=50"`
	Code        string   `json:"code" binding:"required,max=50"`
	Status      int8     `json:"status" binding:"oneof=10 -1"`
	Sort        int      `json:"sort" binding:"min=0"`
	DataScope   int8     `json:"dataScope" binding:"min=0,max=5"`
	Description string   `json:"description" binding:"max=200"`
	MenuIDs     []uint64 `json:"menuIds"`
}

// RoleUpdateRequest 更新角色请求
type RoleUpdateRequest struct {
	Name        string   `json:"name" binding:"required,max=50"`
	Code        string   `json:"code" binding:"required,max=50"`
	Status      int8     `json:"status" binding:"oneof=10 -1"`
	Sort        int      `json:"sort" binding:"min=0"`
	DataScope   int8     `json:"dataScope" binding:"min=0,max=5"`
	Description string   `json:"description" binding:"max=200"`
	MenuIDs     []uint64 `json:"menuIds"`
}

// RoleStatusRequest 更新角色状态请求
type RoleStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=10 -1"`
}

// RolePermissionRequest 更新角色权限请求
type RolePermissionRequest struct {
	MenuIDs []uint64 `json:"menuIds" binding:"required"`
}

// RoleDataScopeRequest 更新角色数据权限请求
type RoleDataScopeRequest struct {
	DataScope int8     `json:"dataScope" binding:"required,min=0,max=5"`
	DeptIDs   []uint64 `json:"deptIds"`
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID          uint64    `json:"id"`
	Name        string    `json:"name"`
	Code        string    `json:"code"`
	Status      int8      `json:"status"`
	Sort        int       `json:"sort"`
	DataScope   int8      `json:"dataScope"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// RoleDetailResponse 角色详情响应
type RoleDetailResponse struct {
	RoleResponse
	MenuIDs []uint64 `json:"menuIds"`
	DeptIDs []uint64 `json:"deptIds"`
}

// RoleListResponse 角色列表响应
type RoleListResponse struct {
	Total int64          `json:"total"`
	Items []RoleResponse `json:"items"`
}

// DataScopeDesc 数据权限描述
var DataScopeDesc = map[int8]string{
	0: "全部数据权限",
	1: "自定义数据权限",
	2: "本部门数据权限",
	3: "本部门及以下数据权限",
	4: "仅本人数据权限",
	5: "无数据权限",
}