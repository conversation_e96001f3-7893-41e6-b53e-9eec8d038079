package admin

import "gin/internal/dto/common"

// UserTransferListReq 用户转账列表查询参数
type UserTransferListReq struct {
	// 管理ID
	ManagerID *uint `json:"manager_id" form:"manager_id" views:"label:管理;searchable"`
	// 发送用户ID
	SendUserID *uint `json:"send_user_id" form:"send_user_id" views:"label:发送用户;searchable"`
	// 接收用户ID
	ReceiveUserID *uint `json:"receive_user_id" form:"receive_user_id" views:"label:接收用户;searchable"`
	// 资产ID
	AssetID *uint `json:"asset_id" form:"asset_id" views:"label:资产;type:select"`
	// 类型
	Type *int8 `json:"type" form:"type" views:"label:类型;type:select"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// StartTime 开始时间
	StartTime *string `json:"start_time" form:"start_time" views:"label:开始时间;type:date"`
	// EndTime 结束时间
	EndTime *string `json:"end_time" form:"end_time" views:"label:结束时间;type:date"`

	// 分页请求
	common.Pagination
}

// UserTransferCreateReq 用户转账创建请求
type UserTransferCreateReq struct {
	// 发送用户ID
	SendUserID uint `json:"send_user_id" form:"send_user_id" binding:"required" views:"label:发送用户;searchable"`
	// 接收用户ID
	ReceiveUserID uint `json:"receive_user_id" form:"receive_user_id" binding:"required" views:"label:接收用户;searchable"`
	// 资产ID
	AssetID uint `json:"asset_id" form:"asset_id" binding:"required" views:"label:资产;type:select"`
	// 转账金额
	Amount float64 `json:"amount" form:"amount" binding:"required,gt=0" views:"label:转账金额;type:number"`
}

// UserTransferUpdateReq 用户转账更新请求
type UserTransferUpdateReq struct {
	// 转账金额
	Amount *float64 `json:"amount" form:"amount" views:"label:转账金额;type:number;row:6"`
	// 实际到账金额
	ActualAmount *float64 `json:"actual_amount" form:"actual_amount" views:"label:实际到账金额;type:number;row:6"`
	// 固定手续费
	FixedFee *float64 `json:"fixed_fee" form:"fixed_fee" views:"label:固定手续费;type:number;row:6"`
	// 手续费(%)
	RateFee *float64 `json:"rate_fee" form:"rate_fee" views:"label:手续费(%);type:number;row:6"`
	// 订单时间
	CreatedAt *string `json:"created_at" form:"created_at" views:"label:提交时间;type:datetime"`
	// 原因
	Reason *string `json:"reason" form:"reason" views:"label:原因;type:textarea;"`
}
