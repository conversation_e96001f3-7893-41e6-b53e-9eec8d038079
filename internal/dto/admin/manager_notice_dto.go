package admin

import "gin/internal/dto/common"

// ManagerNoticeListRequest 管理员通知列表请求
type ManagerNoticeListRequest struct {
	// Title 通知标题
	Title *string `form:"title"`
	// Type 通知类型
	Type *int8 `form:"type"`
	// IsRead 是否已读
	IsRead *bool `form:"isRead"`
	// 分页参数
	common.Pagination
}

// ManagerNoticeCreateRequest 创建管理员通知请求
type ManagerNoticeCreateRequest struct {
	// ReceiverID 接收者ID
	ReceiverID uint `json:"receiverId"`
	// Title 通知标题
	Title string `json:"title" binding:"required,max=255"`
	// Content 通知内容
	Content string `json:"content" binding:"required"`
	// Type 通知类型
	Type int8 `json:"type" binding:"required,oneof=1 2 3"`
	// Route 通知路由
	Route string `json:"route" binding:"required,max=255"`
}

// ManagerNoticeUpdateRequest 更新管理员通知请求
type ManagerNoticeUpdateRequest struct {
	// Title 通知标题
	Title *string `json:"title" binding:"omitempty,max=255"`
	// Content 通知内容
	Content *string `json:"content" binding:"omitempty"`
	// Type 通知类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2 3"`
	// Route 通知路由
	Route *string `json:"route" binding:"omitempty,max=255"`
}

// ManagerNoticeBatchDeleteRequest 批量删除管理员通知请求
type ManagerNoticeBatchDeleteRequest struct {
	// IDs 管理员通知ID列表
	IDs []uint `json:"ids" binding:"required"`
}
