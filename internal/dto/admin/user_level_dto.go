package admin

import "gin/internal/dto/common"

// UserLevelListRequest 用户等级列表请求
type UserLevelListRequest struct {
	// UserID 用户ID
	UserID *uint `form:"userId"`
	// LevelID 等级ID
	LevelID *uint `form:"levelId"`
	// Type 类型
	Type *int8 `form:"type"`
	// Status 状态
	Status *int8 `form:"status"`
	// StartTime 开始时间
	StartTime *string `form:"startTime"`
	// EndTime 结束时间
	EndTime *string `form:"endTime"`
	// 分页参数
	common.Pagination
}

// UserLevelCreateRequest 创建用户等级请求
type UserLevelCreateRequest struct {
	// UserID 用户ID
	UserID uint `json:"userId" binding:"required"`
	// LevelID 等级ID
	LevelID uint `json:"levelId" binding:"required"`
	// Type 类型
	Type int8 `json:"type" binding:"required,oneof=1 2 3"`
	// Status 状态
	Status int8 `json:"status" binding:"required,oneof=1 0"`
	// ExpiredAt 过期时间
	ExpiredAt string `json:"expiredAt" binding:"omitempty"`
}

// UserLevelUpdateRequest 更新用户等级请求
type UserLevelUpdateRequest struct {
	// UserID 用户ID
	UserID *uint `json:"userId" binding:"omitempty"`
	// LevelID 等级ID
	LevelID *uint `json:"levelId" binding:"omitempty"`
	// Type 类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2 3"`
	// Status 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
	// ExpiredAt 过期时间
	ExpiredAt *string `json:"expiredAt" binding:"omitempty"`
}

// UserLevelBatchDeleteRequest 批量删除用户等级请求
type UserLevelBatchDeleteRequest struct {
	// IDs 用户等级ID列表
	IDs []uint `json:"ids" binding:"required"`
}
