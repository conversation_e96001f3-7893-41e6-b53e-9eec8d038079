package admin

import "gin/internal/dto/common"

// PermissionListRequest 权限列表请求
type PermissionListRequest struct {
	// Name 权限名称
	Name *string `form:"name"`
	// Code 权限编码
	Code *string `form:"code"`
	// Resource 资源标识
	Resource *string `form:"resource"`
	// Action 操作类型
	Action *string `form:"action"`
	// Method HTTP方法
	Method *string `form:"method"`
	// API API路径
	API *string `form:"api"`
	// IsSystem 是否系统内置
	IsSystem *bool `form:"isSystem"`
	// Status 状态
	Status *int8 `form:"status"`
	// 分页参数
	common.Pagination
}

// PermissionCreateRequest 创建权限请求
type PermissionCreateRequest struct {
	// Name 权限名称
	Name string `json:"name" binding:"required,max=255"`
	// Code 权限编码
	Code string `json:"code" binding:"required,max=100"`
	// Resource 资源标识
	Resource string `json:"resource" binding:"omitempty,max=255"`
	// Action 操作类型(create,read,update,delete,view等)
	Action string `json:"action" binding:"omitempty,max=50"`
	// Method HTTP方法
	Method string `json:"method" binding:"omitempty,max=10"`
	// API API路径
	API string `json:"api" binding:"omitempty,max=255"`
	// Status 状态(1:启用,0:禁用)
	Status int8 `json:"status" binding:"omitempty,oneof=1 0"`
	// Remark 备注
	Remark string `json:"remark" binding:"omitempty,max=500"`
}

// PermissionUpdateRequest 更新权限请求
type PermissionUpdateRequest struct {
	// Name 权限名称
	Name *string `json:"name" binding:"omitempty,max=255"`
	// Code 权限编码
	Code *string `json:"code" binding:"omitempty,max=100"`
	// Resource 资源标识
	Resource *string `json:"resource" binding:"omitempty,max=255"`
	// Action 操作类型(create,read,update,delete,view等)
	Action *string `json:"action" binding:"omitempty,max=50"`
	// Method HTTP方法
	Method *string `json:"method" binding:"omitempty,max=10"`
	// API API路径
	API *string `json:"api" binding:"omitempty,max=255"`
	// Status 状态(1:启用,0:禁用)
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
	// Remark 备注
	Remark *string `json:"remark" binding:"omitempty,max=500"`
}
