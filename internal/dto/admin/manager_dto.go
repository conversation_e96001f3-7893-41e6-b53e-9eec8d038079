package admin

import (
	"gin/internal/dto/common"
	"time"
)

type ManageListRequest struct {
	// 用户名
	Username *string `json:"username" form:"username" binding:"omitempty,max=60" views:"label:用户名"`
	// 昵称
	Nickname *string `json:"nickname" form:"nickname" binding:"omitempty,max=50"`
	// 手机号
	Mobile *string `form:"mobile" json:"mobile" binding:"omitempty,max=20"`
	// 邮箱
	Email *string `form:"email" json:"email" binding:"omitempty,max=100"`
	// 性别
	Gender *int8 `form:"gender" json:"gender" binding:"omitempty,oneof=0 1 2"`
	// 状态
	Status *int8 `form:"status" json:"status" binding:"omitempty,oneof=1 0"`
	// 开始时间
	StartTime *string `form:"startTime" json:"startTime" binding:"omitempty"`
	// 结束时间
	EndTime *string `form:"endTime" json:"endTime" binding:"omitempty"`
	// 分页参数
	common.Pagination
}

// LoginRequest 登录请求参数
type LoginRequest struct {
	// 用户名
	Username string `json:"username" form:"username" binding:"required,max=60"`
	// 密码
	Password string `json:"password" form:"password" binding:"required"`
}

// ChangePasswordRequest 修改密码请求参数
type ChangePasswordRequest struct {
	// 旧密码
	OldPassword string `json:"oldPassword" binding:"required"`
	// 新密码
	NewPassword string `json:"newPassword" binding:"required,min=6"`
}

// SettingsRequest 个人设置请求参数
type SettingsRequest struct {
	// 头像
	Avatar string `json:"avatar" binding:"omitempty,max=255"`
	// 昵称
	Nickname string `json:"nickname" binding:"omitempty,max=50"`
	// 性别
	Gender int8 `json:"gender" binding:"omitempty,oneof=0 1 2"`
	// 邮箱
	Email string `json:"email" binding:"omitempty,max=100"`
	// 手机号
	Mobile string `json:"mobile" binding:"omitempty,max=20"`
	// 登录模式
	LoginMode int8 `json:"loginMode" binding:"omitempty,oneof=1 2 3"`
	// 最大同时登录设备数
	MaxDevices int8 `json:"maxDevices" binding:"omitempty,min=1,max=10"`
}

// ResetPasswordRequest 重置密码请求参数
type ResetPasswordRequest struct {
	// 新密码
	NewPassword string `json:"newPassword" binding:"required,min=6"`
}

// LoginResponse 登录响应参数
type LoginResponse struct {
	//ID           uint     `json:"id"`
	//Username     string   `json:"username"`
	//RealName     string   `json:"realName"`
	//Roles        []string `json:"roles"`
	//AccessToken  string   `json:"accessToken"`
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
}

// ManageCreateRequest 管理员用户创建请求参数
type ManageCreateRequest struct {
	// 用户名
	Username string `json:"username" binding:"required,max=60"`
	// 昵称
	Nickname string `json:"nickname" binding:"required,max=50"`
	// 密码
	Password string `json:"password" binding:"required,min=6"`
	// 手机号
	Mobile string `json:"mobile" binding:"omitempty,max=20"`
	// 邮箱
	Email string `json:"email" binding:"omitempty,max=100"`
	// 头像
	Avatar string `json:"avatar"`
	// 性别
	Gender int8 `json:"gender" binding:"omitempty,oneof=0 1 2"`
	// 状态
	Status int8 `json:"status" binding:"required,oneof=10 -1"`
	// 过期时间
	ExpiredAt time.Time `json:"expiredAt" binding:"omitempty"`
}

// ManageUpdateRequest 管理员用户更新请求参数
type ManageUpdateRequest struct {
	// 昵称
	Nickname *string `json:"nickname" binding:"omitempty,max=50"`
	// 手机号
	Mobile *string `json:"mobile" binding:"omitempty,max=20"`
	// 邮箱
	Email *string `json:"email" binding:"omitempty,max=100"`
	// 头像
	Avatar *string `json:"avatar"`
	// 性别
	Gender *int8 `json:"gender" binding:"omitempty,oneof=0 1 2"`
	// 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=10 -1"`
	// 过期时间
	ExpiredAt *time.Time `json:"expiredAt" binding:"omitempty"`
	// 登录模式
	LoginMode *int8 `json:"loginMode" binding:"omitempty,oneof=1 2 3"`
	// 最大同时登录设备数
	MaxDevices *int8 `json:"maxDevices" binding:"omitempty,min=1,max=10"`
	// 密码
	Password *string `json:"password" binding:"omitempty,min=6"`
}

// ManageResponse 管理员用户响应参数
type ManageResponse struct {
	ID                   uint      `json:"id"`
	Username             string    `json:"username"`
	Nickname             string    `json:"nickname"`
	Gender               int8      `json:"gender"`
	Avatar               string    `json:"avatar"`
	Email                string    `json:"email"`
	Mobile               string    `json:"mobile"`
	Status               int8      `json:"status"`
	CreatedAt            time.Time `json:"createdAt"`
	LoginMode            int8      `json:"loginMode"`
	MaxDevices           int8      `json:"maxDevices"`
	LastLoginIP          string    `json:"lastLoginIp"`
	LastLoginTime        time.Time `json:"lastLoginTime"`
	LastPasswordChangeAt time.Time `json:"lastPasswordChangeAt"`
	LoginFailCount       int       `json:"loginFailCount"`
	ExpiredAt            time.Time `json:"expiredAt"`
	Buttons              []string  `json:"buttons"`
	Roles                []string  `json:"roles"`
}

// UserRoleRequest 用户角色管理请求参数
type UserRoleRequest struct {
	RoleCodes []string `json:"roleCodes" binding:"required" example:"['admin','user']"`
}
