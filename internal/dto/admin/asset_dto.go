package admin

import "gin/internal/dto/common"

// AssetCreateReq 创建资产请求
type AssetCreateReq struct {
	// Name 资产名称
	Name string `json:"name" binding:"required,max=60"`
	// Subtitle 副标题
	Subtitle string `json:"subtitle" binding:"required,max=255"`
	// NameNative 货币名称
	NameNative string `json:"nameNative" binding:"required,max=100"`
	// Symbol 标识符号
	Symbol string `json:"symbol" binding:"required,max=10"`
	// Icon 图标
	Icon string `json:"icon" binding:"required,max=255"`
	// Type 类型
	Type int8 `json:"type" binding:"required,oneof=1 2"`
	// Rate 汇率
	Rate float64 `json:"rate" binding:"required,min=0"`
	// Sort 排序
	Sort int8 `json:"sort" binding:"required,min=0"`
	// Decimals 小数位数
	Decimals int8 `json:"decimals" binding:"required,min=0,max=8"`
	// Description 描述
	Description string `json:"description" binding:"omitempty,max=255"`
	// Data 数据
	Data AssetData `json:"data" binding:"required"`
}

// AssetData 资产数据
type AssetData struct {
	// IsDeposit 是否支持充值
	IsDeposit bool `json:"is_deposit" form:"is_deposit"`
	// IsWithdraw 是否支持提现
	IsWithdraw bool `json:"is_withdraw" form:"is_withdraw"`
	// IsTransfer 是否支持转账
	IsTransfer bool `json:"is_transfer" form:"is_transfer"`
	// IsSwap 是否支持兑换
	IsSwap bool `json:"is_swap" form:"is_swap"`
}

// AssetUpdateReq 更新资产请求
type AssetUpdateReq struct {
	// Name 资产名称
	Name *string `json:"name" binding:"omitempty,max=60"`
	// Subtitle 副标题
	Subtitle *string `json:"subtitle" binding:"omitempty,max=255"`
	// NameNative 货币名称
	NameNative *string `json:"nameNative" binding:"omitempty,max=100"`
	// Symbol 标识符号
	Symbol *string `json:"symbol" binding:"omitempty,max=10"`
	// Icon 图标
	Icon *string `json:"icon" binding:"omitempty,max=255"`
	// Type 类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2"`
	// Rate 汇率
	Rate *float64 `json:"rate" binding:"omitempty,min=0"`
	// Sort 排序
	Sort *int8 `json:"sort" binding:"omitempty,min=0"`
	// Status 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
	// Decimals 小数位数
	Decimals *int8 `json:"decimals" binding:"omitempty,min=0,max=8"`
	// Description 描述
	Description *string `json:"description" binding:"omitempty,max=255"`
	// Data 数据
	Data *AssetData `json:"data" binding:"omitempty"`
}

// AssetListReq 获取资产列表请求
type AssetListReq struct {
	// Name 资产名称
	Name *string `json:"name" form:"name"`
	// Symbol 标识符号
	Symbol *string `json:"symbol" form:"symbol"`
	// Type 类型
	Type *int8 `json:"type" form:"type"`
	// Status 状态
	Status *int8 `json:"status" form:"status"`
	// 分页参数
	common.Pagination
}
