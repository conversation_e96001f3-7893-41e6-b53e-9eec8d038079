package admin

import "gin/internal/dto/common"

// PaymentListReq 支付查询请求
type PaymentListReq struct {
	// 模式
	Mode *int8 `json:"mode" form:"mode" views:"label:模式;type:select;"`
	// 资产
	AssetID *uint `json:"asset_id" form:"asset_id" views:"label:资产;type:select;"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select;"`
	// 类型
	Type *int8 `json:"type" form:"type" views:"label:类型;type:select;"`
	// 开始时间
	StartTime *string `json:"start_time" form:"start_time" views:"label:开始时间;type:date"`
	// 结束时间
	EndTime *string `json:"end_time" form:"end_time" views:"label:结束时间;type:date"`

	// 分页参数
	common.Pagination
}

// PaymentCreateReq 创建支付请求
type PaymentCreateReq struct {
	//图标
	Icon string `json:"icon" form:"icon" binding:"required" views:"label:图标;type:image"`
	//名称
	Name string `json:"name" form:"name" binding:"required" views:"label:名称"`
	// 资产
	AssetID uint `json:"asset_id" form:"asset_id" binding:"required" views:"label:资产;type:select"`
	//模式
	Mode int8 `json:"mode" form:"mode" binding:"required" views:"label:模式;type:select"`
}

// PaymentUpdateReq 更新支付请求
type PaymentUpdateReq struct {
	//图标
	Icon *string `json:"icon" form:"icon" views:"label:图标;type:image"`
	// 名称
	Name *string `json:"name" form:"name" views:"label:名称"`
	// 副标题
	SubtitleField *string `json:"subtitle_field" form:"subtitle_field" views:"label:副标题;type:translate"`
	//描述
	DescriptionField *string `json:"description_field" form:"description_field" views:"label:描述;type:translate"`
	//最大金额
	MaxAmount *float64 `json:"max_amount" form:"max_amount" views:"label:最大金额;type:number;row:6"`
	// 最小金额
	MinAmount *float64 `json:"min_amount" form:"min_amount" views:"label:最小金额;type:number;row:6"`
	//开始时间
	StartTime *string `json:"start_time" form:"start_time" views:"label:开始时间;type:date;row:6"`
	//结束时间
	EndTime *string `json:"end_time" form:"end_time" views:"label:结束时间;type:date;row:6"`
	//固定手续费
	FixedFee *float64 `json:"fixed_fee" form:"fixed_fee" views:"label:固定手续费;type:number;row:6"`
	//手续费
	RateFee *float64 `json:"rate_fee" form:"rate_fee" views:"label:手续费;type:number;row:6"`
	// 排序
	Sort *int8 `json:"sort" form:"sort" binding:"omitempty,gt=0" views:"label:排序;type:number;row:6"`
	//是否跳转客服
	IsRedirectCS *bool `json:"is_redirect_cs" form:"is_redirect_cs" views:"label:跳转客服;type:toggle;row:6"`
	//是否开启凭证
	IsProof *bool `json:"is_proof" form:"is_proof" views:"label:开启凭证;type:toggle;row:6"`
	//状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// 数据
	Data interface{} `json:"data" form:"data"`
}
