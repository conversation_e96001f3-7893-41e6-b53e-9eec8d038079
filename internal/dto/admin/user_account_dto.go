package admin

import "gin/internal/dto/common"

// UserAccountListReq 用户账户列表查询参数请求
type UserAccountListReq struct {
	// 用户ID
	UserID *uint `json:"user_id" form:"user_id" views:"label:用户;searchable"`
	// 支付ID
	PaymentID *uint `json:"payment_id" form:"payment_id" views:"label:支付;type:select"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`

	// 分页请求
	common.Pagination
}

// UserAccountCreateReq 用户账户创建请求
type UserAccountCreateReq struct {
	// 用户ID
	UserID uint `json:"user_id" form:"user_id" binding:"required" views:"label:用户;searchable"`
	// 支付ID
	PaymentID uint `json:"payment_id" form:"payment_id" binding:"required" views:"label:支付;type:select"`
	// 卡号｜地址
	CardNumber string `json:"card_number" form:"card_number" binding:"required" views:"label:卡号｜地址"`
}

// UserAccountUpdateReq 用户账户更新请求
type UserAccountUpdateReq struct {
	// 备注
	Name *string `json:"name" form:"name" views:"label:备注"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// 数据
	Data interface{} `json:"data"`
}
