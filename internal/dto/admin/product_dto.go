package admin

import "gin/internal/dto/common"

// ProductCreateReq 创建产品请求
type ProductCreateReq struct {
	Name        string  `json:"name" binding:"required,max=100"`
	Description string  `json:"description" binding:"max=255"`
	Price       float64 `json:"price" binding:"required,min=0"`
	Stock       int     `json:"stock" binding:"required,min=0"`
	CategoryID  uint    `json:"category_id" binding:"required"`
}

// ProductUpdateReq 更新产品请求
type ProductUpdateReq struct {
	Name        *string  `json:"name" binding:"omitempty,max=100"`
	Description *string  `json:"description" binding:"omitempty,max=255"`
	Price       *float64 `json:"price" binding:"omitempty,min=0"`
	Stock       *int     `json:"stock" binding:"omitempty,min=0"`
	CategoryID  *uint    `json:"category_id" binding:"omitempty"`
}

// ProductListReq 获取产品列表请求
type ProductListReq struct {
	Name       *string `json:"name" form:"name"`
	CategoryID *uint   `json:"category_id" form:"category_id"`
	common.Pagination
}
