package admin

import "gin/internal/dto/common"

// UserSwapListReq 用户闪兑列表查询参数
type UserSwapListReq struct {
	// 管理ID
	ManagerID *uint `json:"manager_id" form:"manager_id" views:"label:管理;searchable"`
	// 发送用户ID
	SendUserID *uint `json:"send_user_id" form:"send_user_id" views:"label:发送用户;searchable"`
	// 发送资产ID
	SendAssetID *uint `json:"send_asset_id" form:"send_asset_id" views:"label:发送资产;type:select"`
	// 接收资产ID
	ReceiveAssetID *uint `json:"receive_asset_id" form:"receive_asset_id" views:"label:接收资产;type:select"`
	// 类型
	Type *int8 `json:"type" form:"type" views:"label:类型;type:select"`
	// 状态
	Status *int8 `json:"status" form:"status" views:"label:状态;type:select"`
	// StartTime 开始时间
	StartTime *string `json:"start_time" form:"start_time" views:"label:开始时间;type:date"`
	// EndTime 结束时间
	EndTime *string `json:"end_time" form:"end_time" views:"label:结束时间;type:date"`

	// 分页请求
	common.Pagination
}

// UserSwapCreateReq 用户闪兑创建请求
type UserSwapCreateReq struct {
	// 发送用户ID
	SendUserID uint `json:"send_user_id" form:"send_user_id" binding:"required" views:"label:发送用户;searchable"`
	// 发送资产ID
	SendAssetID uint `json:"send_asset_id" form:"send_asset_id" binding:"required" views:"label:发送资产;type:select"`
	// 接收资产ID
	ReceiveAssetID uint `json:"receive_asset_id" form:"receive_asset_id" binding:"required" views:"label:接收资产;type:select"`
	// 发送金额
	SendAmount float64 `json:"send_amount" form:"send_amount" binding:"required,gt=0" views:"label:发送金额;type:number"`
}

// UserSwapUpdateReq 用户闪兑更新请求
type UserSwapUpdateReq struct {
	// 发送金额
	SendAmount *float64 `json:"send_amount" form:"send_amount" views:"label:发送金额;type:number;row:6"`
	// 接收金额
	ReceiveAmount *float64 `json:"receive_amount" form:"receive_amount" views:"label:接收金额;type:number;row:6"`
	// 汇率
	Rate *float64 `json:"rate" form:"rate" views:"label:汇率;type:number;row:6"`
	// 订单时间
	CreatedAt *string `json:"created_at" form:"created_at" views:"label:订单时间;type:datetime;row:6"`
	// 固定手续费
	FixedFee *float64 `json:"fixed_fee" form:"fixed_fee" views:"label:固定手续费;type:number;row:6"`
	// 手续费(%)
	RateFee *float64 `json:"rate_fee" form:"rate_fee" views:"label:手续费(%);type:number;row:6"`
	// 原因
	Reason *string `json:"reason" form:"reason" views:"label:原因;type:textarea"`
}
