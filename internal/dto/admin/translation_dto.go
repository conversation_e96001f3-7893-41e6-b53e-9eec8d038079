package admin

import "gin/internal/dto/common"

// TranslationCreateRequest 创建翻译请求
type TranslationCreateRequest struct {
	// Key 翻译键
	Key string `json:"key" binding:"required,max=100"`
	// Value 翻译值
	Value string `json:"value" binding:"required"`
	// Lang 语言代码
	Lang string `json:"lang" binding:"required,max=10"`
	// Module 所属模块
	Module string `json:"module" binding:"required,max=50"`
	// Status 状态
	Status int8 `json:"status" binding:"required,oneof=1 0"`
}

// TranslationUpdateRequest 更新翻译请求
type TranslationUpdateRequest struct {
	// Value 翻译值
	Value *string `json:"value" binding:"omitempty"`
	// Lang 语言代码
	Lang *string `json:"lang" binding:"omitempty,max=10"`
	// Module 所属模块
	Module *string `json:"module" binding:"omitempty,max=50"`
	// Status 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
}

// TranslationListRequest 获取翻译列表请求
type TranslationListRequest struct {
	// Key 翻译键
	Key *string `json:"key" form:"key"`
	// Lang 语言代码
	Lang *string `json:"lang" form:"lang"`
	// Module 所属模块
	Module *string `json:"module" form:"module"`
	// Status 状态
	Status *int8 `json:"status" form:"status" binding:"omitempty,oneof=1 0"`
	// 分页参数
	common.Pagination
}

// TranslationBatchDeleteRequest 批量删除翻译请求
type TranslationBatchDeleteRequest struct {
	// IDs 翻译ID列表
	IDs []uint `json:"ids" binding:"required"`
}
