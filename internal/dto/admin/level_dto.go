package admin

import "gin/internal/dto/common"

// LevelCreateRequest 创建等级请求
type LevelCreateRequest struct {
	// Name 等级名称
	Name string `json:"name" binding:"required,max=255"`
	// Icon 等级图标
	Icon string `json:"icon" binding:"omitempty,max=255"`
	// Symbol 等级标识
	Symbol string `json:"symbol" binding:"required,max=20"`
	// Type 等级类型
	Type int8 `json:"type" binding:"required,oneof=1"`
	// Sort 排序
	Sort int `json:"sort" binding:"required,min=0"`
	// Amount 金额
	Amount float64 `json:"amount" binding:"required,min=0"`
	// Discount 折扣率
	Discount float64 `json:"discount" binding:"required,min=0,max=1"`
	// Days 天数
	Days int `json:"days" binding:"required,min=0"`
	// Status 状态
	Status int8 `json:"status" binding:"required,oneof=1 0"`
	// Description 描述
	Description string `json:"description" binding:"omitempty,max=255"`
}

// LevelUpdateRequest 更新等级请求
type LevelUpdateRequest struct {
	// Name 等级名称
	Name *string `json:"name" binding:"omitempty,max=255"`
	// Icon 等级图标
	Icon *string `json:"icon" binding:"omitempty,max=255"`
	// Symbol 等级标识
	Symbol *string `json:"symbol" binding:"omitempty,max=20"`
	// Type 等级类型
	Type *int8 `json:"type" binding:"omitempty,oneof=1"`
	// Sort 排序
	Sort *int `json:"sort" binding:"omitempty,min=0"`
	// Amount 金额
	Amount *float64 `json:"amount" binding:"omitempty,min=0"`
	// Discount 折扣率
	Discount *float64 `json:"discount" binding:"omitempty,min=0,max=1"`
	// Days 天数
	Days *int `json:"days" binding:"omitempty,min=0"`
	// Status 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=1 0"`
	// Description 描述
	Description *string `json:"description" binding:"omitempty,max=255"`
}

// LevelListRequest 获取等级列表请求
type LevelListRequest struct {
	// Name 等级名称
	Name *string `json:"name" form:"name"`
	// Symbol 等级标识
	Symbol *string `json:"symbol" form:"symbol"`
	// Type 等级类型
	Type *int8 `json:"type" form:"type"`
	// Status 状态
	Status *int8 `json:"status" form:"status"`
	// 分页参数
	common.Pagination
}

// LevelBatchDeleteRequest 批量删除等级请求
type LevelBatchDeleteRequest struct {
	// IDs 等级ID列表
	IDs []uint `json:"ids" binding:"required"`
}
