package admin

import "gin/internal/models"

// MenuListRequest 菜单列表请求
type MenuListRequest struct {
	// Name 菜单名称
	Name *string `form:"name"`
	// Code 菜单编码
	Code *string `form:"code"`
	// Type 菜单类型(1:目录,2:菜单)
	Type *int8 `form:"type"`
	// Status 状态
	Status *int8 `form:"status"`
	// ParentID 父级ID
	ParentID *uint `form:"parentId"`
	// Path 路由路径
	Path *string `form:"path"`
}

// MenuCreateRequest 创建菜单请求
type MenuCreateRequest struct {
	// Name 菜单名称
	Name string `json:"name" binding:"required,max=255"`
	// Code 菜单编码
	Code string `json:"code" binding:"required,max=100"`
	// Type 菜单类型(1:目录,2:菜单)
	Type int8 `json:"type" binding:"required,oneof=1 2"`
	// Status 状态(10:启用,-1:禁用)
	Status int8 `json:"status" binding:"omitempty,oneof=10 -1"`
	// ParentID 父级ID
	ParentID uint `json:"parentId" binding:"omitempty"`
	// Sort 排序
	Sort int `json:"sort" binding:"omitempty,min=0"`
	// Path 路由路径
	Path string `json:"path" binding:"omitempty,max=255"`
	// Component 组件路径
	Component string `json:"component" binding:"omitempty,max=255"`
	// Redirect 重定向路径
	Redirect string `json:"redirect" binding:"omitempty,max=255"`
	// Remark 备注
	Remark string `json:"remark" binding:"omitempty,max=500"`
	// Meta 菜单元数据
	Meta *MenuMetaRequest `json:"meta"`
	// PermissionIDs 关联的权限ID列表
	PermissionIDs []uint `json:"permissionIds" binding:"omitempty"`
}

// MenuUpdateRequest 更新菜单请求
type MenuUpdateRequest struct {
	// Name 菜单名称
	Name *string `json:"name" binding:"omitempty,max=255"`
	// Code 菜单编码
	Code *string `json:"code" binding:"omitempty,max=100"`
	// Type 菜单类型(1:目录,2:菜单)
	Type *int8 `json:"type" binding:"omitempty,oneof=1 2"`
	// Status 状态(10:启用,-1:禁用)
	Status *int8 `json:"status" binding:"omitempty,oneof=10 -1"`
	// ParentID 父级ID
	ParentID *uint `json:"parentId" binding:"omitempty"`
	// Sort 排序
	Sort *int `json:"sort" binding:"omitempty,min=0"`
	// Path 路由路径
	Path *string `json:"path" binding:"omitempty,max=255"`
	// Component 组件路径
	Component *string `json:"component" binding:"omitempty,max=255"`
	// Redirect 重定向路径
	Redirect *string `json:"redirect" binding:"omitempty,max=255"`
	// Remark 备注
	Remark *string `json:"remark" binding:"omitempty,max=500"`
	// Meta 菜单元数据
	Meta *MenuMetaRequest `json:"meta"`
	// PermissionIDs 关联的权限ID列表
	PermissionIDs []uint `json:"permissionIds" binding:"omitempty"`
}

// MenuMetaRequest 菜单元数据请求
type MenuMetaRequest struct {
	Title                    *string  `json:"title"`                    // 标题名称
	Icon                     *string  `json:"icon"`                     // 图标
	ActiveIcon               *string  `json:"activeIcon"`               // 激活图标
	ActivePath               *string  `json:"activePath"`               // 当前激活的菜单路径
	Authority                []string `json:"authority"`                // 需要特定的角色标识
	HideInMenu               *bool    `json:"hideInMenu"`               // 菜单中不展现
	HideChildrenInMenu       *bool    `json:"hideChildrenInMenu"`       // 子级在菜单中不展现
	HideInBreadcrumb         *bool    `json:"hideInBreadcrumb"`         // 面包屑中不展现
	HideInTab                *bool    `json:"hideInTab"`                // 标签页不展现
	KeepAlive                *bool    `json:"keepAlive"`                // 开启缓存
	AffixTab                 *bool    `json:"affixTab"`                 // 固定标签页
	AffixTabOrder            *int     `json:"affixTabOrder"`            // 固定标签页顺序
	Badge                    *string  `json:"badge"`                    // 徽标
	BadgeType                *string  `json:"badgeType"`                // 徽标类型
	BadgeVariants            *string  `json:"badgeVariants"`            // 徽标颜色
	IframeSrc                *string  `json:"iframeSrc"`                // iframe 地址
	Link                     *string  `json:"link"`                     // 外链地址
	IgnoreAccess             *bool    `json:"ignoreAccess"`             // 忽略权限检查
	MenuVisibleWithForbidden *bool    `json:"menuVisibleWithForbidden"` // 可见但禁止访问
	MaxNumOfOpenTab          *int     `json:"maxNumOfOpenTab"`          // 最大打开标签数
	Order                    *int     `json:"order"`                    // 排序
	Loaded                   *bool    `json:"loaded"`                   // 是否已加载
}

// MenuResponse 菜单响应
type MenuResponse struct {
	*models.Menu
	Permissions []*models.Permission `json:"permissions,omitempty"` // 关联的权限
}

// MenuTreeResponse 菜单树响应
type MenuTreeResponse struct {
	*models.Menu
	Children []*MenuTreeResponse `json:"children,omitempty"`
}

// MenuPermissionResponse 菜单权限关联响应
type MenuPermissionResponse struct {
	MenuID      uint                 `json:"menuId"`
	MenuName    string               `json:"menuName"`
	Permissions []*models.Permission `json:"permissions"`
}

// ConvertToMenuMeta 将请求转换为菜单元数据
func (req *MenuMetaRequest) ConvertToMenuMeta() models.MenuMeta {
	meta := models.MenuMeta{}

	if req.Title != nil {
		meta.Title = *req.Title
	}
	if req.Icon != nil {
		meta.Icon = *req.Icon
	}
	if req.ActiveIcon != nil {
		meta.ActiveIcon = *req.ActiveIcon
	}
	if req.ActivePath != nil {
		meta.ActivePath = *req.ActivePath
	}

	return meta
}
