package admin

import "gin/internal/dto/common"

// UserBillListReq 用户账单列表查询参数
type UserBillListReq struct {
	// 管理ID
	ManagerID *uint `json:"manager_id" form:"manager_id" views:"label:管理;searchable"`
	// 用户ID
	UserID *uint `json:"user_id" form:"user_id" views:"label:用户;searchable"`
	// 资产ID
	AssetID *uint `json:"asset_id" form:"asset_id" views:"label:资产;type:select"`
	// 类型
	Type *int8 `json:"type" form:"type" views:"label:类型;type:select"`

	// 分页参数
	common.Pagination
}

// UserBillUpdateReq 用户账单更新请求
type UserBillUpdateReq struct {
	// 金额
	Amount *float64 `json:"amount" form:"amount" views:"label:金额;type:number"`
	// 手续费
	Fee *float64 `json:"fee" form:"fee" views:"label:手续费;type:number"`
	// 余额
	Balance *float64 `json:"balance" form:"balance" views:"label:余额;type:number"`
	// 时间
	CreatedAt *string `json:"created_at" form:"created_at" views:"label:时间;type:datetime"`
}
