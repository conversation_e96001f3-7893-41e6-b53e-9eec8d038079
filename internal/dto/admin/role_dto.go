package admin

import "gin/internal/dto/common"

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	// Name 角色名称
	Name *string `json:"name" form:"name" binding:"omitempty,max=50"`
	// Code 角色编码
	Code *string `json:"code" form:"code" binding:"omitempty,max=50"`
	// Status 状态
	Status *int8 `json:"status" form:"status" binding:"omitempty,oneof=10 -1"`
	// StartTime 开始时间
	StartTime *string `json:"startTime" form:"start_time" binding:"omitempty"`
	// EndTime 结束时间
	EndTime *string `json:"endTime" form:"end_time" binding:"omitempty"`
	// 分页参数
	common.Pagination
}

// RoleCreateRequest 创建角色请求
type RoleCreateRequest struct {
	// Name 角色名称
	Name string `json:"name" binding:"required,max=50"`
	// Code 角色编码
	Code string `json:"code" binding:"required,max=50"`
	// Status 状态
	Status int8 `json:"status" binding:"required,oneof=10 -1"`
	// Sort 排序
	Sort int `json:"sort" binding:"omitempty,min=0"`
	// Remark 描述
	Remark string `json:"remark" binding:"omitempty,max=255"`
	// Permission 权限
	Permission *[]uint64 `json:"permission" binding:"omitempty"`
}

// RoleUpdateRequest 更新角色请求
type RoleUpdateRequest struct {
	// Name 角色名称
	Name *string `json:"name" binding:"omitempty,max=50"`
	// Code 角色编码
	Code *string `json:"code" binding:"omitempty,max=50"`
	// Status 状态
	Status *int8 `json:"status" binding:"omitempty,oneof=10 -1"`
	// Sort 排序
	Sort *int `json:"sort" binding:"omitempty,min=0"`
	// DataScope 数据范围
	DataScope *int8 `json:"dataScope" binding:"omitempty,min=0,max=5"`
	// Description 描述
	Remark *string `json:"remark" binding:"omitempty,max=255"`
	// Permissions 权限
	Permissions []uint `json:"permissions" binding:"omitempty"`
}

// RoleBatchDeleteRequest 批量删除角色请求
type RoleBatchDeleteRequest struct {
	// IDs 角色ID列表
	IDs []uint `json:"ids" binding:"required"`
}

// RolePermissionRequest 更新角色权限请求
type RolePermissionRequest struct {
	MenuIDs []uint `json:"menuIds" binding:"required"`
}
