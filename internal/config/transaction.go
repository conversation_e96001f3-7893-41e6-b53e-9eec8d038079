package config

import (
	"gin/internal/repository"
	"gin/internal/service"
	"gin/internal/transaction"
	"gorm.io/gorm"
)

// TransactionConfig 事务配置
type TransactionConfig struct {
	// EnableNestedTransaction 是否启用嵌套事务
	EnableNestedTransaction bool `yaml:"enable_nested_transaction" json:"enable_nested_transaction"`
	// DefaultIsolationLevel 默认隔离级别
	DefaultIsolationLevel string `yaml:"default_isolation_level" json:"default_isolation_level"`
	// EnableTransactionLogging 是否启用事务日志
	EnableTransactionLogging bool `yaml:"enable_transaction_logging" json:"enable_transaction_logging"`
}

// ServiceManager 服务管理器
type ServiceManager struct {
	db              *gorm.DB
	config          *TransactionConfig
	txManager       transaction.TransactionManager
	repoManager     *repository.RepositoryManager
	txDecorator     *transaction.ServiceTransactionDecorator
	
	// 服务实例
	userService     service.IUserService
}

// NewServiceManager 创建服务管理器
func NewServiceManager(db *gorm.DB, config *TransactionConfig) *ServiceManager {
	sm := &ServiceManager{
		db:     db,
		config: config,
	}
	
	sm.initializeTransactionManager()
	sm.initializeRepositories()
	sm.initializeServices()
	
	return sm
}

// initializeTransactionManager 初始化事务管理器
func (sm *ServiceManager) initializeTransactionManager() {
	if sm.config.EnableNestedTransaction {
		sm.txManager = transaction.NewAdvancedTransactionManager(sm.db)
	} else {
		sm.txManager = transaction.NewTransactionManager(sm.db)
	}
	
	// 创建事务装饰器
	if sm.config.EnableTransactionLogging {
		// 这里需要实现一个Logger接口的实现
		// logger := NewTransactionLogger()
		// sm.txDecorator = transaction.NewLoggingTransactionDecorator(sm.txManager, logger)
		sm.txDecorator = transaction.NewServiceTransactionDecorator(sm.txManager)
	} else {
		sm.txDecorator = transaction.NewServiceTransactionDecorator(sm.txManager)
	}
}

// initializeRepositories 初始化仓库
func (sm *ServiceManager) initializeRepositories() {
	sm.repoManager = repository.NewRepositoryManager(sm.db, sm.txManager)
}

// initializeServices 初始化服务
func (sm *ServiceManager) initializeServices() {
	sm.userService = service.NewUserService(sm.repoManager, sm.txDecorator)
}

// UserService 获取用户服务
func (sm *ServiceManager) UserService() service.IUserService {
	return sm.userService
}

// TransactionManager 获取事务管理器
func (sm *ServiceManager) TransactionManager() transaction.TransactionManager {
	return sm.txManager
}

// TransactionDecorator 获取事务装饰器
func (sm *ServiceManager) TransactionDecorator() *transaction.ServiceTransactionDecorator {
	return sm.txDecorator
}

// RepositoryManager 获取仓库管理器
func (sm *ServiceManager) RepositoryManager() *repository.RepositoryManager {
	return sm.repoManager
}

// DefaultTransactionConfig 默认事务配置
var DefaultTransactionConfig = &TransactionConfig{
	EnableNestedTransaction:  true,
	DefaultIsolationLevel:    "READ_COMMITTED",
	EnableTransactionLogging: false,
}

// TransactionLogger 事务日志记录器实现示例
type TransactionLogger struct{}

// Info 记录信息日志
func (l *TransactionLogger) Info(msg string, fields ...interface{}) {
	// 实现日志记录逻辑
	// 可以使用 zap, logrus 等日志库
}

// Error 记录错误日志
func (l *TransactionLogger) Error(msg string, fields ...interface{}) {
	// 实现错误日志记录逻辑
}

// Debug 记录调试日志
func (l *TransactionLogger) Debug(msg string, fields ...interface{}) {
	// 实现调试日志记录逻辑
}

// NewTransactionLogger 创建事务日志记录器
func NewTransactionLogger() *TransactionLogger {
	return &TransactionLogger{}
}
