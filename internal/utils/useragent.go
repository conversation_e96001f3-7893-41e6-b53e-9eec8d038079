package utils

import (
	"strings"
)

// UserAgentInfo 存储用户代理解析结果
type UserAgentInfo struct {
	Browser      string // 浏览器名称
	BrowserVer   string // 浏览器版本
	OS           string // 操作系统
	OSVer        string // 操作系统版本
	Device       string // 设备类型
	IsMobile     bool   // 是否移动设备
	IsTablet     bool   // 是否平板设备
	IsBot        bool   // 是否爬虫
	RawUserAgent string // 原始用户代理字符串
}

// ParseUserAgent 解析用户代理字符串，返回浏览器和操作系统信息
func ParseUserAgent(userAgent string) *UserAgentInfo {
	info := &UserAgentInfo{
		Browser:      "Unknown",
		OS:           "Unknown",
		Device:       "Desktop",
		IsMobile:     false,
		IsTablet:     false,
		IsBot:        false,
		RawUserAgent: userAgent,
	}

	// 检测操作系统
	switch {
	case strings.Contains(userAgent, "Windows NT 10.0"):
		info.OS = "Windows"
		info.OSVer = "10"
	case strings.Contains(userAgent, "Windows NT 6.3"):
		info.OS = "Windows"
		info.OSVer = "8.1"
	case strings.Contains(userAgent, "Windows NT 6.2"):
		info.OS = "Windows"
		info.OSVer = "8"
	case strings.Contains(userAgent, "Windows NT 6.1"):
		info.OS = "Windows"
		info.OSVer = "7"
	case strings.Contains(userAgent, "Windows NT 6.0"):
		info.OS = "Windows"
		info.OSVer = "Vista"
	case strings.Contains(userAgent, "Windows NT 5.1"):
		info.OS = "Windows"
		info.OSVer = "XP"
	case strings.Contains(userAgent, "Windows NT"):
		info.OS = "Windows"
	case strings.Contains(userAgent, "Mac OS X"):
		info.OS = "MacOS"
		// 尝试提取版本号
		if idx := strings.Index(userAgent, "Mac OS X "); idx > -1 {
			ver := userAgent[idx+9:]
			if endIdx := strings.Index(ver, ";"); endIdx > -1 {
				ver = ver[:endIdx]
			} else if endIdx := strings.Index(ver, ")"); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.OSVer = strings.Replace(ver, "_", ".", -1)
		}
	case strings.Contains(userAgent, "Linux"):
		info.OS = "Linux"
	case strings.Contains(userAgent, "Android"):
		info.OS = "Android"
		info.IsMobile = true
		// 尝试提取版本号
		if idx := strings.Index(userAgent, "Android "); idx > -1 {
			ver := userAgent[idx+8:]
			if endIdx := strings.Index(ver, ";"); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.OSVer = ver
		}
		// 检测是否平板
		if strings.Contains(userAgent, "Tablet") || strings.Contains(userAgent, "iPad") {
			info.IsTablet = true
			info.Device = "Tablet"
		} else {
			info.Device = "Mobile"
		}
	case strings.Contains(userAgent, "iPhone"):
		info.OS = "iOS"
		info.IsMobile = true
		info.Device = "Mobile"
		// 尝试提取版本号
		if idx := strings.Index(userAgent, "OS "); idx > -1 {
			ver := userAgent[idx+3:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.OSVer = strings.Replace(ver, "_", ".", -1)
		}
	case strings.Contains(userAgent, "iPad"):
		info.OS = "iOS"
		info.IsMobile = true
		info.IsTablet = true
		info.Device = "Tablet"
		// 尝试提取版本号
		if idx := strings.Index(userAgent, "OS "); idx > -1 {
			ver := userAgent[idx+3:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.OSVer = strings.Replace(ver, "_", ".", -1)
		}
	}

	// 检测浏览器
	switch {
	case strings.Contains(userAgent, "MSIE") || strings.Contains(userAgent, "Trident"):
		info.Browser = "IE"
		if strings.Contains(userAgent, "MSIE") {
			if idx := strings.Index(userAgent, "MSIE "); idx > -1 {
				ver := userAgent[idx+5:]
				if endIdx := strings.Index(ver, ";"); endIdx > -1 {
					ver = ver[:endIdx]
				}
				info.BrowserVer = strings.TrimSpace(ver)
			}
		} else if strings.Contains(userAgent, "Trident") {
			if strings.Contains(userAgent, "rv:11") {
				info.BrowserVer = "11.0"
			}
		}
	case strings.Contains(userAgent, "Edge/"):
		info.Browser = "Edge"
		if idx := strings.Index(userAgent, "Edge/"); idx > -1 {
			ver := userAgent[idx+5:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.BrowserVer = ver
		}
	case strings.Contains(userAgent, "Edg/"):
		info.Browser = "Edge"
		if idx := strings.Index(userAgent, "Edg/"); idx > -1 {
			ver := userAgent[idx+4:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.BrowserVer = ver
		}
	case strings.Contains(userAgent, "Chrome/"):
		info.Browser = "Chrome"
		if idx := strings.Index(userAgent, "Chrome/"); idx > -1 {
			ver := userAgent[idx+7:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.BrowserVer = ver
		}
	case strings.Contains(userAgent, "Firefox/"):
		info.Browser = "Firefox"
		if idx := strings.Index(userAgent, "Firefox/"); idx > -1 {
			ver := userAgent[idx+8:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.BrowserVer = ver
		}
	case strings.Contains(userAgent, "Safari/") && !strings.Contains(userAgent, "Chrome/"):
		info.Browser = "Safari"
		if idx := strings.Index(userAgent, "Version/"); idx > -1 {
			ver := userAgent[idx+8:]
			if endIdx := strings.Index(ver, " "); endIdx > -1 {
				ver = ver[:endIdx]
			}
			info.BrowserVer = ver
		}
	}

	// 检测是否为爬虫
	if strings.Contains(strings.ToLower(userAgent), "bot") ||
		strings.Contains(strings.ToLower(userAgent), "spider") ||
		strings.Contains(strings.ToLower(userAgent), "crawler") {
		info.IsBot = true
	}

	return info
}

// GetBrowser 获取浏览器名称
func GetBrowser(userAgent string) string {
	return ParseUserAgent(userAgent).Browser
}

// GetOS 获取操作系统名称
func GetOS(userAgent string) string {
	return ParseUserAgent(userAgent).OS
}

// IsMobile 判断是否为移动设备
func IsMobile(userAgent string) bool {
	return ParseUserAgent(userAgent).IsMobile
}
