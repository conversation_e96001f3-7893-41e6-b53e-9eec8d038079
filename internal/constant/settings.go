package constant

const (
	// 提示音选项
	SettingAudioCheckboxDeposit       = "audio_deposit"       // 充值
	SettingAudioCheckboxWithdraw      = "audio_withdraw"      // 提现
	SettingAudioCheckboxRegister      = "audio_register"      // 注册
	SettingAudioCheckboxLogin         = "audio_login"         // 登录
	SettingAudioCheckboxBuyVip        = "audio_buy_vip"       // 购买VIP
	SettingAudioCheckboxCertification = "audio_certification" // 实名认证
	SettingAudioCheckboxTransfer      = "audio_transfer"      // 转账
	SettingAudioCheckboxSwap          = "audio_swap"          // 闪兑
	SettingAudioCheckboxOrder         = "audio_order"         // 下单

	// 用户冻结状态选项
	SettingStatusUserFreezeCheckboxLogin    = "freeze_login"    // 禁止登陆
	SettingStatusUserFreezeCheckboxWithdraw = "freeze_withdraw" // 禁止提现
	SettingStatusUserFreezeCheckboxDeposit  = "freeze_deposit"  // 禁止充值
	SettingStatusUserFreezeCheckboxTransfer = "freeze_transfer" // 禁止转账
	SettingStatusUserFreezeCheckboxSwap     = "freeze_swap"     // 禁止闪兑
	SettingStatusUserFreezeCheckboxOrder    = "freeze_order"    // 禁止下单

	// 基础模版选项
	SettingBasicTemplateCheckboxShowLevelTag            = "basic_show_level"                       // 显示等级标签
	SettingBasicTemplateCheckboxShowCertificationTag    = "basic_show_certification"               // 显示认证标签
	SettingBasicTemplateCheckboxShowScoreTag            = "basic_show_score"                       // 显示信用标签
	SettingBasicTemplateCheckboxShowChangePassword      = "basic_show_change_password"             // 显示修改密码
	SettingBasicTemplateCheckboxShowChangeSecurityKey   = "basic_show_change_security_key"         // 显示修改支付密码
	SettingBasicTemplateCheckboxWithdrawAccountDelete   = "basic_show_withdraw_account_delete"     // 显示提现账户删除
	SettingBasicTemplateCheckboxWithdrawAccountUpdate   = "basic_show_withdraw_account_update"     // 显示提现账户更新
	SettingBasicTemplateCheckboxWithdrawAccountNumber   = "basic_show_withdraw_account_number"     // 显示提现账户号码
	SettingBasicTemplateCheckboxWithdrawAccountPassword = "basic_enable_withdraw_account_password" // 开启提现账户密码
	SettingBasicTemplateCheckboxDepositPassword         = "basic_enable_deposit_password"          // 开启充值密码
	SettingBasicTemplateCheckboxWithdrawPassword        = "basic_enable_withdraw_password"         // 开启提现密码
	SettingBasicTemplateCheckboxTransferPassword        = "basic_enable_transfer_password"         // 开启转账密码
	SettingBasicTemplateCheckboxSwapPassword            = "basic_enable_swap_password"             // 开启闪兑密码
	SettingBasicTemplateCheckboxBuyLevelPassword        = "basic_enable_buy_level_password"        // 开启购买等级密码
	SettingBasicTemplateCheckboxBuyProductPassword      = "basic_enable_buy_product_password"      // 开启购买产品密码

	// 登录模版选项
	SettingLoginTemplateCheckboxShowSiteName       = "show_site_name"       // 显示项目名称
	SettingLoginTemplateCheckboxShowSiteLogo       = "show_site_logo"       // 显示项目logo
	SettingLoginTemplateCheckboxShowSwitchLanguage = "show_switch_language" // 显示切换语言
	SettingLoginTemplateCheckboxShowRegister       = "show_register"        // 显示注册

	// 注册模版选项
	SettingRegisterTemplateCheckboxShowSiteName       = "show_site_name"       // 显示项目名称
	SettingRegisterTemplateCheckboxShowSiteLogo       = "show_site_logo"       // 显示项目logo
	SettingRegisterTemplateCheckboxShowSwitchLanguage = "show_switch_language" // 显示切换语言
	SettingRegisterTemplateCheckboxCmfPassword        = "cfm_password"         // 确认密码
	SettingRegisterTemplateCheckboxSecurityKey        = "security_key"         // 支付密码
	SettingRegisterTemplateCheckboxCfmSecurityKey     = "cfm_security_key"     // 确认支付密码
	SettingRegisterTemplateCheckboxInviteCode         = "invite_code"          // 邀请码
	SettingRegisterTemplateCheckboxNickname           = "nickname"             // 昵称
	SettingRegisterTemplateCheckboxSex                = "sex"                  // 性别
	SettingRegisterTemplateCheckboxBirthday           = "birthday"             // 生日
)

// SettingBanner 轮播图
type SettingBanner struct {
	Banner        []string `json:"banner" form:"banner" views:"label:轮播图;type:image;multiple"`
	DesktopBanner []string `json:"desktop_banner" form:"desktop_banner" views:"label:桌面端轮播图;type:image;multiple"`
}

// SettingSiteReward 站点奖励配置
type SettingSiteReward struct {
	Invite   float64 `json:"invite" form:"invite" views:"label:邀请奖励;type:number"`
	Register float64 `json:"register" form:"register" views:"label:注册奖励;type:number"`
}

// SettingSiteFee 站点手续费
type SettingSiteFee struct {
	Transfer float64 `json:"transfer" form:"transfer" views:"label:转账手续费;type:number"`
	Swap     float64 `json:"swap" form:"swap" views:"label:闪兑手续费;type:number"`
}

// SettingSiteSocial 站点社交
type SettingSiteSocial struct {
	Icon string `json:"icon" form:"icon" views:"label:图标;type:image"`
	Link string `json:"link" form:"link" views:"label:链接;"`
	Name string `json:"name" form:"name" views:"label:名称;"`
}

// SettingDistribution 分销配置
type SettingDistribution struct {
	Product  float64 `json:"product" form:"product" views:"label:购买产品;type:number;row:6"`
	Earnings float64 `json:"earnings" form:"earnings" views:"label:产品收益;type:number;row:6"`
}

// SettingAppDownload 下载APP
type SettingAppDownload struct {
	Android string `json:"android" form:"android" views:"label:安卓下载;type:file"`
	IOS     string `json:"ios" form:"ios" views:"label:IOS下载;type:file"`
}

// SettingWithdraw 提现配置
type SettingWithdraw struct {
	Account   int8                `json:"account" form:"account" views:"label:提现数量;type:number;row:6"`
	AuditNums int8                `json:"audit_nums" form:"audit_nums" views:"label:审核数量;type:number;row:6"`
	Nums      SettingWithdrawNums `json:"nums" form:"nums"`
}

// SettingWithdrawNums 提现数量
type SettingWithdrawNums struct {
	Day int8 `json:"day" form:"day" views:"label:天数;type:number;row:6"`
	Num int8 `json:"num" form:"num" views:"label:次数;type:number;row:6"`
}

// SettingNoticeTips 公告提示词
type SettingNoticeTips struct {
	UserFreeze string `json:"user_freeze" form:"user_freeze" views:"label:用户冻结提示词;type:translate"`
	UserScore  string `json:"user_score" form:"user_score" views:"label:用户信用分提示词;type:translate"`
	Notice     string `json:"notice" form:"notice" views:"label:公告提示词;type:translate"`
}

// SettingAudio 提示音
type SettingAudio struct {
	Audio []string `json:"audio" form:"audio" views:"label:提示音;type:checkbox"`
}

// SettingStatusUserFreeze 用户冻结状态
type SettingStatusUserFreeze struct {
	Status []string `json:"status" form:"status" views:"label:用户冻结状态;type:checkbox;"`
}

// SettingBasicTemplate 基础模版
type SettingBasicTemplate struct {
	Checkbox []string `json:"checkbox" form:"checkbox" views:"label:表单配置;type:checkbox;"` // 表单配置
}

// SettingRegisterTemplate 注册模版
type SettingRegisterTemplate struct {
	Background        string   `json:"background" form:"background" views:"label:背景图片;type:image"`                    // 背景图片
	DesktopBackground string   `json:"desktop_background" form:"desktop_background" views:"label:桌面端背景图片;type:image"` // 桌面端背景图片
	Tips              string   `json:"tips" form:"tips" views:"label:提示词;type:translate"`                             // 提示词
	AgreenmentURL     string   `json:"agreenment_url" form:"agreenment_url" views:"label:注册协议链接;"`                    // 注册协议链接
	PolicyURL         string   `json:"policy_url" form:"policy_url" views:"label:隐私政策链接;"`                            // 隐私政策链接
	Checkbox          []string `json:"checkbox" form:"checkbox" views:"label:表单配置;type:checkbox;"`                    // 表单配置
}

// SettingLoginTemplate 登录模版
type SettingLoginTemplate struct {
	Background        string   `json:"background" form:"background" views:"label:背景图片;type:image"`                    // 背景图片
	DesktopBackground string   `json:"desktop_background" form:"desktop_background" views:"label:桌面端背景图片;type:image"` // 桌面端背景图片
	Tips              string   `json:"tips" form:"tips" views:"label:提示词;type:translate"`                             // 提示词
	ForgetURL         string   `json:"forget_url" form:"forget_url" views:"label:忘记密码链接;"`                            // 忘记密码链接
	Checkbox          []string `json:"checkbox" form:"checkbox" views:"label:表单配置;type:checkbox;"`                    // 表单配置
}
