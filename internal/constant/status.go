package constant

// 通用状态常量
const (
	// StatusEnabled 基础状态
	StatusEnabled  int8 = 10 // 启用
	StatusDisabled int8 = -1 // 禁用

	// DataScopeAll 数据范围
	DataScopeAll          int8 = 1 // 全部数据
	DataScopeCustom       int8 = 2 // 自定义数据
	DataScopeDept         int8 = 3 // 本部门数据
	DataScopeDeptAndChild int8 = 4 // 本部门及以下数据
	DataScopeSelf         int8 = 5 // 仅本人数据

	// OperationTypeCreate 业务操作类型
	OperationTypeCreate int8 = 1 // 创建
	OperationTypeUpdate int8 = 2 // 更新
	OperationTypeDelete int8 = 3 // 删除
	OperationTypeQuery  int8 = 4 // 查询
	OperationTypeExport int8 = 5 // 导出
	OperationTypeImport int8 = 6 // 导入

	// MenuTypeDirectory 菜单类型
	MenuTypeDirectory int8 = 1 // 目录
	MenuTypeMenu      int8 = 2 // 菜单
	MenuTypeButton    int8 = 3 // 按钮
)

// GetStatusText 获取通用状态文本
func GetStatusText(status int8) string {
	switch status {
	case StatusEnabled:
		return "启用"
	case StatusDisabled:
		return "禁用"
	default:
		return "未知状态"
	}
}

// GetDataScopeText 获取数据范围文本
func GetDataScopeText(dataScope int8) string {
	switch dataScope {
	case DataScopeAll:
		return "全部数据"
	case DataScopeCustom:
		return "自定义数据"
	case DataScopeDept:
		return "本部门数据"
	case DataScopeDeptAndChild:
		return "本部门及以下数据"
	case DataScopeSelf:
		return "仅本人数据"
	default:
		return "未知范围"
	}
}

// GetMenuTypeText 获取菜单类型文本
func GetMenuTypeText(menuType int8) string {
	switch menuType {
	case MenuTypeDirectory:
		return "目录"
	case MenuTypeMenu:
		return "菜单"
	case MenuTypeButton:
		return "按钮"
	default:
		return "未知类型"
	}
}
