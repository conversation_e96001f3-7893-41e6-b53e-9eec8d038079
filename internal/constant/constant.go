package constant

// Option 选项结构，用于前端下拉框等
type Option struct {
	Label string      `json:"label"` // 显示文本
	Value interface{} `json:"value"` // 实际值
	Code  string      `json:"code"`  // 编码（可选）
}

// RolePermissionConfig 角色权限配置
type RolePermissionConfig struct {
	Permissions       []string // 接受权限，* 表示接受所有
	FilterPermissions []string // 过滤权限，如果有设置，那么不会执行接受权限
}

// ManagerRoleMap 定义了管理和角色的映射关系
var ManagerRoleMap = map[string][]string{
	InitialSuperManagerUsername: {InitialSuperManageRoleCode},
}

// RolePermissionsMap 定义角色权限配置
var RolePermissionsMap = map[string]RolePermissionConfig{
	InitialSuperManageRoleCode: {
		Permissions: []string{"*"}, // 超级管理员拥有所有权限
	},
}

// 系统默认管理和角色
const (
	InitialSuperManagerUsername = "superadmin"
	InitialSuperManagerPassword = "Aa123098.."

	InitialSuperManageRoleCode = "super_admin"
)
