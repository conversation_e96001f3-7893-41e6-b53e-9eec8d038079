package constant

import "errors"

var (
	// ErrSystemError 系统错误
	ErrSystemError = errors.New("system_error")
	// ErrBalanceNotEnough 余额不足
	ErrBalanceNotEnough = errors.New("balance_not_enough")
	// ErrCaptchaIncorrect 验证码不正确
	ErrCaptchaIncorrect = errors.New("captcha_incorrect")
	// ErrInviteCodeExpired 邀请码已过期
	ErrInviteCodeExpired = errors.New("invite_code_expired")
	// ErrInviteCodeNotExists 邀请码不存在
	ErrInviteCodeNotExists = errors.New("invite_code_not_exists")
	// ErrLevelNotExists 等级不存在
	ErrLevelNotExists = errors.New("level_not_exists")
	// ErrUserExists 用户已存在
	ErrUserExists = errors.New("user_exists")
	// ErrUserNotExists 用户不存在
	ErrUserNotExists = errors.New("user_not_exists")
	// ErrUserNotActive 用户未激活
	ErrUserNotActive = errors.New("user_not_active")
	// ErrUserDisabled 用户已禁用
	ErrUserDisabled = errors.New("user_disabled")
	// ErrDepositAmountRange 充值金额范围错误
	ErrDepositAmountRange = errors.New("deposit_amount_range")
	// ErrDepositTimeRange 充值时间范围错误
	ErrDepositTimeRange = errors.New("deposit_time_range")
	// ErrWithdrawAmountRange 提现金额范围错误
	ErrWithdrawAmountRange = errors.New("withdraw_amount_range")
	// ErrWithdrawTimeRange 提现时间范围错误
	ErrWithdrawTimeRange = errors.New("withdraw_time_range")
	// ErrOldPasswordIncorrect 旧密码不正确
	ErrOldPasswordIncorrect = errors.New("old_password_incorrect")
	// ErrSecurityKeyIncorrect 支付密码错误
	ErrSecurityKeyIncorrect = errors.New("security_key_incorrect")
	// ErrAccountOrPasswordIncorrect 账户或密码不正确
	ErrAccountOrPasswordIncorrect = errors.New("account_or_password_incorrect")
	// ErrBindAccountOverLimit 绑定账户超过限制
	ErrBindAccountOverLimit = errors.New("bind_account_over_limit")
	// ErrFrontMenuNotExists 前台菜单不存在
	ErrFrontMenuNotExists = errors.New("front_menu_not_exists")
	// ErrAssetNotExists 资产不存在
	ErrAssetNotExists = errors.New("asset_not_exists")
)
