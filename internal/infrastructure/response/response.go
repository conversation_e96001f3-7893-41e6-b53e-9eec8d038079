package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构 (恢复为原始字段)
type Response struct {
	Code    int         `json:"code"`           // 业务状态码
	Message string      `json:"message"`        // 提示信息
	Data    interface{} `json:"data,omitempty"` // 数据或错误详情
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.SecureJSON(http.StatusOK, Response{
		Code:    0,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, httpStatus int, bizCode int, message string, err error) {
	resp := Response{
		Code:    bizCode,
		Message: message,
	}
	if err != nil {
		resp.Data = err.Error() // 将错误信息放入 Data
	}
	c.SecureJSON(httpStatus, resp)
}

// AbortWithError 中止请求并发送包含原始错误信息的错误响应
func AbortWithError(c *gin.Context, httpStatus int, bizCode int, message string, err error) {
	resp := Response{
		Code:    bizCode,
		Message: message,
	}
	if err != nil {
		resp.Data = err.Error() // 将错误信息放入 Data
	}
	c.AbortWithStatusJSON(httpStatus, resp)
}

// AbortBadRequest 中止请求并发送 Bad Request (400) 响应
// details 通常是错误字符串或 map[string]string
func AbortBadRequest(c *gin.Context, message string, details interface{}) {
	c.AbortWithStatusJSON(http.StatusBadRequest, Response{
		Code:    http.StatusBadRequest, // 使用 HTTP 状态码作为业务码，或定义自己的
		Message: message,
		Data:    details, // 将错误详情放入 Data
	})
}

// AbortValidationFailure 中止请求并发送参数验证失败 (400) 响应
// errors 通常是 map[string]string
func AbortValidationFailure(c *gin.Context, message string, errors interface{}) {
	c.AbortWithStatusJSON(http.StatusBadRequest, Response{
		Code:    http.StatusBadRequest, // 或自定义业务码
		Message: message,
		Data:    errors, // 将验证错误详情放入 Data
	})
}

// AbortUnauthorized 中止请求并发送未授权 (401) 响应
func AbortUnauthorized(c *gin.Context, message string) {
	c.AbortWithStatusJSON(http.StatusUnauthorized, Response{
		Code:    http.StatusUnauthorized, // 或自定义业务码
		Message: message,
		// Data 字段为空
	})
}

// AbortForbidden 中止请求并发送禁止访问 (403) 响应
func AbortForbidden(c *gin.Context, message string) {
	c.AbortWithStatusJSON(http.StatusForbidden, Response{
		Code:    http.StatusForbidden, // 或自定义业务码
		Message: message,
		// Data 字段为空
	})
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Items interface{} `json:"items"` // 数据列表
	Total int64       `json:"total"` // 总记录数
}

// SuccessWithPagination 分页成功响应
func SuccessWithPagination(c *gin.Context, list interface{}, total int64) {
	Success(c, PaginationResponse{ // 调用 Success 函数，将分页结构放入 Data
		Items: list,
		Total: total,
	})
}

// AbortInternalServerError 中止请求并发送服务器内部错误 (500) 响应
func AbortInternalServerError(c *gin.Context, message string) {
	c.AbortWithStatusJSON(http.StatusInternalServerError, Response{
		Code:    http.StatusInternalServerError, // 或自定义业务码
		Message: message,
		// Data 字段为空
	})
}

// AbortNotFound 中止请求并发送资源不存在 (404) 响应
func AbortNotFound(c *gin.Context, message string) {
	c.AbortWithStatusJSON(http.StatusNotFound, Response{
		Code:    http.StatusNotFound, // 或自定义业务码
		Message: message,
		// Data 字段为空
	})
}
