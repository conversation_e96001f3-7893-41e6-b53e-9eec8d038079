package data

import (
	"gin/internal/models"

	"gorm.io/gorm"
)

// InitLang 初始化语言数据
func InitLang(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Lang{}); err != nil {
		return err
	}
	// 检查是否已存在管理员
	var count int64
	if err := db.Model(&models.Lang{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	langList := []*models.Lang{
		{
			Code:   "zh-CN",
			Name:   "简体中文",
			Icon:   "/static/lang/zh-CN.png",
			Sort:   1,
			Status: 10,
		},
		{
			Code:   "zh-TW",
			Name:   "繁體中文",
			Icon:   "/static/lang/zh-TW.png",
			Sort:   2,
			Status: 10,
		},
		{
			Code:   "en-US",
			Name:   "English(US)",
			Icon:   "/static/lang/en-US.png",
			Sort:   3,
			Status: 10,
		},
		{
			Code:   "en-GB",
			Name:   "English(UK)",
			Icon:   "/static/lang/en-GB.png",
			Sort:   4,
			Status: 10,
		},
		{
			Code:   "ja-JP",
			Name:   "日本語",
			Icon:   "/static/lang/ja-JP.png",
			Sort:   5,
			Status: 10,
		},
	}

	if err := db.Create(&langList).Error; err != nil {
		return err
	}
	return nil
}

// InitTranslation 初始化翻译数据
func InitTranslation(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Translation{}); err != nil {
		return err
	}

	// 检查是否已存在数据
	var count int64
	if err := db.Model(&models.Translation{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil
	}

	translations := []*models.Translation{
		{Key: "common.success", Module: "common", Lang: "zh-CN", Value: "操作成功", Status: 10},
		{Key: "common.failed", Module: "common", Lang: "zh-CN", Value: "操作失败", Status: 10},
		{Key: "common.save_success", Module: "common", Lang: "zh-CN", Value: "保存成功", Status: 10},
		{Key: "common.delete_success", Module: "common", Lang: "zh-CN", Value: "删除成功", Status: 10},
		{Key: "common.update_success", Module: "common", Lang: "zh-CN", Value: "更新成功", Status: 10},
		{Key: "validation.required", Module: "validation", Lang: "zh-CN", Value: "{{field}}不能为空", Status: 10},
		{Key: "validation.min", Module: "validation", Lang: "zh-CN", Value: "{{field}}长度不能小于{{min}}", Status: 10},
		{Key: "validation.max", Module: "validation", Lang: "zh-CN", Value: "{{field}}长度不能大于{{max}}", Status: 10},
		{Key: "validation.email", Module: "validation", Lang: "zh-CN", Value: "{{field}}必须是有效的邮箱地址", Status: 10},
		{Key: "admin.login.success", Module: "admin", Lang: "zh-CN", Value: "登录成功", Status: 10},
		{Key: "admin.login.failed", Module: "admin", Lang: "zh-CN", Value: "登录失败", Status: 10},
		{Key: "admin.logout.success", Module: "admin", Lang: "zh-CN", Value: "退出登录成功", Status: 10},
		{Key: "admin.password.change_success", Module: "admin", Lang: "zh-CN", Value: "密码修改成功", Status: 10},
		{Key: "admin.account.disabled", Module: "admin", Lang: "zh-CN", Value: "账号已被禁用", Status: 10},
		{Key: "admin.account.expired", Module: "admin", Lang: "zh-CN", Value: "账号已过期", Status: 10},
	}

	if err := db.Create(&translations).Error; err != nil {
		return err
	}
	return nil
}

// InitLoginLog 初始化
func InitLoginLog(db *gorm.DB) error {
	// 创建表结构
	if err := db.AutoMigrate(&models.LoginLog{}); err != nil {
		return err
	}

	return nil
}

// InitOperLog 初始化
func InitOperLog(db *gorm.DB) error {
	// 创建表结构
	if err := db.AutoMigrate(&models.OperateLog{}); err != nil {
		return err
	}

	return nil
}
