package data

import (
	"gin/internal/models"
	"time"

	"gorm.io/gorm"
)

// InitPosition 初始化岗位数据
func InitPosition(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Position{}); err != nil {
		return err
	}

	// 检查是否已存在岗位数据
	var count int64
	if err := db.Model(&models.Position{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	now := time.Now()
	// 初始化岗位数据
	positions := []*models.Position{
		{ID: 1, TenantID: 0, Name: "总经理", Code: "GM", DeptID: 1, Type: 1, Level: 4, Status: 10, Sort: 1, Remark: "公司最高管理者", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 2, TenantID: 0, Name: "研发总监", Code: "CTO", DeptID: 2, Type: 1, Level: 3, Status: 10, Sort: 2, Remark: "负责研发部门管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 3, TenantID: 0, Name: "市场总监", Code: "CMO", DeptID: 3, Type: 1, Level: 3, Status: 10, Sort: 3, Remark: "负责市场部门管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 4, TenantID: 0, Name: "财务总监", Code: "CFO", DeptID: 4, Type: 1, Level: 3, Status: 10, Sort: 4, Remark: "负责财务部门管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 5, TenantID: 0, Name: "人事总监", Code: "HRD", DeptID: 5, Type: 1, Level: 3, Status: 10, Sort: 5, Remark: "负责人事部门管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 6, TenantID: 0, Name: "前端负责人", Code: "FE-LEAD", DeptID: 6, Type: 3, Level: 3, Status: 10, Sort: 6, Remark: "负责前端团队管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 7, TenantID: 0, Name: "后端负责人", Code: "BE-LEAD", DeptID: 7, Type: 3, Level: 3, Status: 10, Sort: 7, Remark: "负责后端团队管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 8, TenantID: 0, Name: "测试负责人", Code: "QA-LEAD", DeptID: 8, Type: 3, Level: 3, Status: 10, Sort: 8, Remark: "负责测试团队管理", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 9, TenantID: 0, Name: "高级前端工程师", Code: "SR-FE", DeptID: 6, Type: 3, Level: 3, Status: 10, Sort: 9, Remark: "负责前端核心开发", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 10, TenantID: 0, Name: "高级后端工程师", Code: "SR-BE", DeptID: 7, Type: 3, Level: 3, Status: 10, Sort: 10, Remark: "负责后端核心开发", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 11, TenantID: 0, Name: "高级测试工程师", Code: "SR-QA", DeptID: 8, Type: 3, Level: 3, Status: 10, Sort: 11, Remark: "负责测试规划与执行", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 12, TenantID: 0, Name: "中级前端工程师", Code: "MID-FE", DeptID: 6, Type: 3, Level: 2, Status: 10, Sort: 12, Remark: "负责前端功能开发", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 13, TenantID: 0, Name: "中级后端工程师", Code: "MID-BE", DeptID: 7, Type: 3, Level: 2, Status: 10, Sort: 13, Remark: "负责后端功能开发", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 14, TenantID: 0, Name: "中级测试工程师", Code: "MID-QA", DeptID: 8, Type: 3, Level: 2, Status: 10, Sort: 14, Remark: "负责功能测试", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 15, TenantID: 0, Name: "初级前端工程师", Code: "JR-FE", DeptID: 6, Type: 3, Level: 1, Status: 10, Sort: 15, Remark: "负责前端基础开发", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 16, TenantID: 0, Name: "初级后端工程师", Code: "JR-BE", DeptID: 7, Type: 3, Level: 1, Status: 10, Sort: 16, Remark: "负责后端基础开发", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 17, TenantID: 0, Name: "初级测试工程师", Code: "JR-QA", DeptID: 8, Type: 3, Level: 1, Status: 10, Sort: 17, Remark: "负责基础测试", IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
	}

	if err := db.Create(&positions).Error; err != nil {
		return err
	}
	return nil
}
