package data

import (
	"gin/internal/models"
	"strconv"
	"strings"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/gorm"
)

// InitCasbinRules 初始化Casbin规则
func InitCasbinRules(db *gorm.DB, enforcer *casbin.SyncedCachedEnforcer) error {
	// 检查是否已存在Casbin规则
	var count int64
	if err := db.Model(&gormadapter.CasbinRule{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil
	}

	permissions := initPermissions()
	roles := initRoles()
	users := initManagers()
	tenants := initTenants()

	// 创建角色映射方便查找
	roleMap := make(map[string]*models.Role)
	for _, role := range roles {
		roleMap[role.Code] = role
	}

	// 创建管理员映射方便查找
	userMap := make(map[string]uint64)
	for _, user := range users {
		userMap[user.UserName] = user.ID
	}

	// 创建租户映射方便查找
	tenantMap := make(map[uint64]string)
	for _, tenant := range tenants {
		tenantMap[tenant.ID] = tenant.Code
	}

	// 角色 → 权限
	for _, role := range roles {
		roleConfig, exists := RolePermissionsMap[role.Code]
		if !exists {
			continue
		}

		// 处理角色-权限
		for _, p := range permissions {
			if shouldAssignPermission(p.Code, roleConfig) {
				tenantCode, ok := tenantMap[p.TenantID]
				if !ok {
					tenantCode = "*"
				}

				if p.Type == 1 || p.Type == 2 {
					enforcer.AddPolicy(role.Code, tenantCode, p.Code, "read")
				} else if p.Type == 3 {
					enforcer.AddPolicy(role.Code, tenantCode, p.Code, "write")
				}

				if p.API != "" && p.Method != "" {
					action := "read"
					if p.Method == "POST" || p.Method == "PUT" || p.Method == "DELETE" {
						action = "write"
					}
					enforcer.AddPolicy(role.Code, tenantCode, p.API, action)
				}
			}
		}
	}

	// 用户 → 角色
	for username, roleNames := range UserRoleMap {
		userID, ok := userMap[username]
		if !ok {
			continue
		}
		user := findUserByID(users, userID)
		if user == nil {
			continue
		}

		for _, roleName := range roleNames {
			role, ok := roleMap[roleName]
			if !ok {
				continue
			}

			global := user.TenantID == 0 || role.TenantID == 0

			if err := addUserRole(enforcer, userID, roleName, global, user.TenantID, tenantMap); err != nil {
				return err
			}
		}
	}

	return nil
}

// addUserRole 给用户添加角色绑定
func addUserRole(enforcer *casbin.SyncedCachedEnforcer, userID uint64, roleName string, global bool, tenantId uint64, tenantMap map[uint64]string) error {
	userIDStr := strconv.FormatUint(userID, 10)

	if global {
		// 全局用户，只添加 *
		_, err := enforcer.AddGroupingPolicy(userIDStr, roleName, "*")
		if err != nil {
			return err
		}
	} else {
		tenantCode, ok := tenantMap[tenantId]
		if !ok {
			tenantCode = "*"
		}
		// 普通用户，添加 default 租户
		_, err := enforcer.AddGroupingPolicy(userIDStr, roleName, tenantCode)
		if err != nil {
			return err
		}
	}
	return nil
}

// shouldAssignPermission 判断是否应该给角色分配这个权限
func shouldAssignPermission(permissionCode string, config RolePermissionConfig) bool {
	for _, filter := range config.FilterPermissions {
		if matchPermission(permissionCode, filter) {
			return false
		}
	}
	for _, accept := range config.Permissions {
		if accept == "*" || matchPermission(permissionCode, accept) {
			return true
		}
	}
	return false
}

// matchPermission 匹配权限支持通配符
func matchPermission(permission, pattern string) bool {
	if pattern == "*" {
		return true
	}
	if strings.HasSuffix(pattern, ":*") {
		prefix := strings.TrimSuffix(pattern, ":*")
		return strings.HasPrefix(permission, prefix+":")
	}
	return permission == pattern
}

// findUserByID 从列表中查用户
func findUserByID(users []*models.Manager, id uint64) *models.Manager {
	for _, u := range users {
		if u.ID == id {
			return u
		}
	}
	return nil
}
