package data

import (
	"gin/internal/models"
	"time"

	"gorm.io/gorm"
)

// InitPermission 初始化权限数据
func InitPermission(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Permission{}); err != nil {
		return err
	}

	// 检查是否已存在权限数据
	var count int64
	if err := db.Model(&models.Permission{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	// 初始化权限数据
	permissions := initPermissions()

	if err := db.Create(&permissions).Error; err != nil {
		return err
	}
	return nil
}

func initPermissions() []*models.Permission {
	now := time.Now()
	return []*models.Permission{
		// 仪表盘
		{ID: 1, TenantID: 0, Name: "仪表盘", Code: "dashboard", Status: 10, Path: "/", ParentID: 0, Sort: 1, Type: 1, Component: "BasicLayout", Redirect: "/analytics", ToAuth: 1, IsSystem: true, API: "", Method: "", Meta: models.RouteMeta{Title: "仪表盘", Icon: "mdi:home"}, CreatedAt: now, UpdatedAt: now},
		{ID: 2, TenantID: 0, Name: "分析页", Code: "dashboard:analytics", Status: 10, Path: "/analytics", ParentID: 1, Sort: 1, Type: 2, Component: "/dashboard/analytics/index", ToAuth: 1, IsSystem: true, API: "/api/dashboard/analytics", Method: "GET", Meta: models.RouteMeta{Title: "分析页", Icon: "carbon:analytics"}, CreatedAt: now, UpdatedAt: now},
		{ID: 3, TenantID: 0, Name: "工作台", Code: "dashboard:workspace", Status: 10, Path: "/workspace", ParentID: 1, Sort: 2, Type: 2, Component: "/dashboard/workspace/index", ToAuth: 1, IsSystem: true, API: "/api/dashboard/workspace", Method: "GET", Meta: models.RouteMeta{Title: "工作台", Icon: "carbon:workspace"}, CreatedAt: now, UpdatedAt: now},

		// 系统管理
		{ID: 10, TenantID: 0, Name: "系统管理", Code: "system", Status: 10, Path: "/system", ParentID: 0, Sort: 10, Type: 1, Component: "BasicLayout", Redirect: "/system/admin", ToAuth: 1, IsSystem: true, API: "", Method: "", Meta: models.RouteMeta{Title: "系统管理", Icon: "mdi:cog-outline"}, CreatedAt: now, UpdatedAt: now},
		// 管理员管理
		{ID: 11, TenantID: 0, Name: "管理员管理", Code: "system:admin", Status: 10, Path: "/system/admin", ParentID: 10, Sort: 1, Type: 2, Component: "/system/admin/index", ToAuth: 1, IsSystem: true, API: "/api/admin", Method: "GET", Meta: models.RouteMeta{Title: "管理员管理", Icon: "mdi:account-cog"}, CreatedAt: now, UpdatedAt: now},
		{ID: 12, TenantID: 0, Name: "创建管理员", Code: "system:admin:create", Status: 10, Path: "", ParentID: 11, Sort: 1, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/admin", Method: "POST", Meta: models.RouteMeta{Title: "创建", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 13, TenantID: 0, Name: "编辑管理员", Code: "system:admin:update", Status: 10, Path: "", ParentID: 11, Sort: 2, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/admin/:id", Method: "PUT", Meta: models.RouteMeta{Title: "编辑", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 14, TenantID: 0, Name: "删除管理员", Code: "system:admin:delete", Status: 10, Path: "", ParentID: 11, Sort: 3, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/admin/:id", Method: "DELETE", Meta: models.RouteMeta{Title: "删除", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 15, TenantID: 0, Name: "查看管理员", Code: "system:admin:read", Status: 10, Path: "", ParentID: 11, Sort: 4, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/admin/:id", Method: "GET", Meta: models.RouteMeta{Title: "查看", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 16, TenantID: 0, Name: "修改密码", Code: "system:admin:password", Status: 10, Path: "", ParentID: 11, Sort: 5, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/admin/:id/password", Method: "PUT", Meta: models.RouteMeta{Title: "修改密码", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 17, TenantID: 0, Name: "修改状态", Code: "system:admin:status", Status: 10, Path: "", ParentID: 11, Sort: 6, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/admin/:id/status", Method: "PUT", Meta: models.RouteMeta{Title: "修改状态", Icon: ""}, CreatedAt: now, UpdatedAt: now},

		// 角色管理
		{ID: 20, TenantID: 0, Name: "角色管理", Code: "system:role", Status: 10, Path: "/system/role", ParentID: 10, Sort: 2, Type: 2, Component: "/system/role/index", ToAuth: 1, IsSystem: true, API: "/api/role", Method: "GET", Meta: models.RouteMeta{Title: "角色管理", Icon: "mdi:account-group"}, CreatedAt: now, UpdatedAt: now},
		{ID: 21, TenantID: 0, Name: "创建角色", Code: "system:role:create", Status: 10, Path: "", ParentID: 20, Sort: 1, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/role", Method: "POST", Meta: models.RouteMeta{Title: "创建", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 22, TenantID: 0, Name: "编辑角色", Code: "system:role:update", Status: 10, Path: "", ParentID: 20, Sort: 2, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/role/:id", Method: "PUT", Meta: models.RouteMeta{Title: "编辑", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 23, TenantID: 0, Name: "删除角色", Code: "system:role:delete", Status: 10, Path: "", ParentID: 20, Sort: 3, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/role/:id", Method: "DELETE", Meta: models.RouteMeta{Title: "删除", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 24, TenantID: 0, Name: "查看角色", Code: "system:role:read", Status: 10, Path: "", ParentID: 20, Sort: 4, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/role/:id", Method: "GET", Meta: models.RouteMeta{Title: "查看", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 25, TenantID: 0, Name: "分配权限", Code: "system:role:permission", Status: 10, Path: "", ParentID: 20, Sort: 5, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/role/:id/permissions", Method: "PUT", Meta: models.RouteMeta{Title: "分配权限", Icon: ""}, CreatedAt: now, UpdatedAt: now},

		// 菜单管理
		{ID: 30, TenantID: 0, Name: "菜单管理", Code: "system:menu", Status: 10, Path: "/system/menu", ParentID: 10, Sort: 3, Type: 2, Component: "/system/menu/index", ToAuth: 1, IsSystem: true, API: "/api/menu", Method: "GET", Meta: models.RouteMeta{Title: "菜单管理", Icon: "mdi:menu"}, CreatedAt: now, UpdatedAt: now},
		{ID: 31, TenantID: 0, Name: "创建菜单", Code: "system:menu:create", Status: 10, Path: "", ParentID: 30, Sort: 1, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/menu", Method: "POST", Meta: models.RouteMeta{Title: "创建", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 32, TenantID: 0, Name: "编辑菜单", Code: "system:menu:update", Status: 10, Path: "", ParentID: 30, Sort: 2, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/menu/:id", Method: "PUT", Meta: models.RouteMeta{Title: "编辑", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 33, TenantID: 0, Name: "删除菜单", Code: "system:menu:delete", Status: 10, Path: "", ParentID: 30, Sort: 3, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/menu/:id", Method: "DELETE", Meta: models.RouteMeta{Title: "删除", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 34, TenantID: 0, Name: "查看菜单", Code: "system:menu:read", Status: 10, Path: "", ParentID: 30, Sort: 4, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/menu/:id", Method: "GET", Meta: models.RouteMeta{Title: "查看", Icon: ""}, CreatedAt: now, UpdatedAt: now},

		// 租户管理
		{ID: 40, TenantID: 0, Name: "租户管理", Code: "system:tenant", Status: 10, Path: "/system/tenant", ParentID: 10, Sort: 4, Type: 2, Component: "/system/tenant/index", ToAuth: 1, IsSystem: true, API: "/api/tenant", Method: "GET", Meta: models.RouteMeta{Title: "租户管理", Icon: "mdi:office-building"}, CreatedAt: now, UpdatedAt: now},
		{ID: 41, TenantID: 0, Name: "创建租户", Code: "system:tenant:create", Status: 10, Path: "", ParentID: 40, Sort: 1, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/tenant", Method: "POST", Meta: models.RouteMeta{Title: "创建", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 42, TenantID: 0, Name: "编辑租户", Code: "system:tenant:update", Status: 10, Path: "", ParentID: 40, Sort: 2, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/tenant/:id", Method: "PUT", Meta: models.RouteMeta{Title: "编辑", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 43, TenantID: 0, Name: "删除租户", Code: "system:tenant:delete", Status: 10, Path: "", ParentID: 40, Sort: 3, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/tenant/:id", Method: "DELETE", Meta: models.RouteMeta{Title: "删除", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 44, TenantID: 0, Name: "查看租户", Code: "system:tenant:read", Status: 10, Path: "", ParentID: 40, Sort: 4, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/tenant/:id", Method: "GET", Meta: models.RouteMeta{Title: "查看", Icon: ""}, CreatedAt: now, UpdatedAt: now},
		{ID: 45, TenantID: 0, Name: "修改状态", Code: "system:tenant:status", Status: 10, Path: "", ParentID: 40, Sort: 5, Type: 3, Component: "", ToAuth: 1, IsSystem: true, API: "/api/tenant/:id/status", Method: "PUT", Meta: models.RouteMeta{Title: "修改状态", Icon: ""}, CreatedAt: now, UpdatedAt: now},

		// 个人中心
		{ID: 50, TenantID: 0, Name: "个人中心", Code: "account", Status: 10, Path: "/account", ParentID: 0, Sort: 20, Type: 1, Component: "BasicLayout", Redirect: "/account/info", ToAuth: 1, IsSystem: true, API: "", Method: "", Meta: models.RouteMeta{Title: "个人中心", Icon: "mdi:account-circle"}, CreatedAt: now, UpdatedAt: now},
		{ID: 51, TenantID: 0, Name: "个人信息", Code: "account:info", Status: 10, Path: "/account/info", ParentID: 50, Sort: 1, Type: 2, Component: "/account/info/index", ToAuth: 1, IsSystem: true, API: "/api/admin/info", Method: "GET", Meta: models.RouteMeta{Title: "个人信息", Icon: "mdi:account-details"}, CreatedAt: now, UpdatedAt: now},
		{ID: 52, TenantID: 0, Name: "个人设置", Code: "account:settings", Status: 10, Path: "/account/settings", ParentID: 50, Sort: 2, Type: 2, Component: "/account/settings/index", ToAuth: 1, IsSystem: true, API: "/api/admin/info", Method: "GET", Meta: models.RouteMeta{Title: "个人设置", Icon: "mdi:account-cog"}, CreatedAt: now, UpdatedAt: now},
		{ID: 53, TenantID: 0, Name: "消息中心", Code: "account:message", Status: 10, Path: "/account/message", ParentID: 50, Sort: 3, Type: 2, Component: "/account/message/index", ToAuth: 1, IsSystem: true, API: "/api/messages", Method: "GET", Meta: models.RouteMeta{Title: "消息中心", Icon: "mdi:message-text"}, CreatedAt: now, UpdatedAt: now},
	}
}
