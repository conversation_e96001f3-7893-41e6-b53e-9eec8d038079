package data

import (
	"gin/internal/models"
	"gin/internal/utils"
	"time"

	"gorm.io/gorm"
)

// UserRoleMap 定义了用户和角色的映射关系
var UserRoleMap = map[string][]string{
	"admin":      {"super_admin"},
	"test_admin": {"admin"},
}

// InitManagers 初始化管理员数据
func InitManagers(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Manager{}); err != nil {
		return err
	}
	// 检查是否已存在管理员
	var count int64
	if err := db.Model(&models.Manager{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}
	// 初始化用户数据
	users := initManagers()

	// 批量创建用户
	if err := db.Create(&users).Error; err != nil {
		return err
	}

	return nil
}

func initManagers() []*models.Manager {
	salt := utils.RandomString(6)
	password := utils.EncryptPassword("123qwe", salt)
	users := []*models.Manager{
		{
			ID:                   1,
			TenantID:             0,
			UserName:             "admin",
			Mobile:               "1234567890",
			NickName:             "超级管理员",
			Password:             password,
			Salt:                 salt,
			Avatar:               "/static/avatar/default_avatar.png",
			DeptID:               1,
			IsAdmin:              true,
			LastLoginIP:          "127.0.0.1",
			LastLoginTime:        time.Now(),
			LastPasswordChangeAt: time.Now(),
			ExpiredAt:            time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC),
		},
		{
			ID:                   2,
			TenantID:             0,
			UserName:             "test_admin",
			Mobile:               "1122334455",
			NickName:             "测试管理员",
			Password:             password,
			Salt:                 salt,
			Avatar:               "/static/avatar/default_avatar.png",
			DeptID:               1,
			LastLoginIP:          "127.0.0.1",
			LastLoginTime:        time.Now(),
			LastPasswordChangeAt: time.Now(),
			ExpiredAt:            time.Date(2099, 12, 31, 23, 59, 59, 0, time.UTC),
		},
	}
	return users
}
