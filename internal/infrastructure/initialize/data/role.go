package data

import (
	"gin/internal/models"

	"gorm.io/gorm"
)

// RolePermissionConfig 角色权限配置
type RolePermissionConfig struct {
	Permissions       []string // 接受权限，* 表示接受所有
	FilterPermissions []string // 过滤权限，如果有设置，那么不会执行接受权限
}

// RolePermissionsMap 定义角色权限配置
var RolePermissionsMap = map[string]RolePermissionConfig{
	"super_admin": {
		Permissions: []string{"*"}, // 超级管理员拥有所有权限
	},
	"admin": {
		Permissions: []string{"dashboard:*", "system:*"}, // 系统管理员拥有仪表盘和系统管理权限
		FilterPermissions: []string{
			"system:tenant:*", // 过滤租户管理相关权限
		},
	},
	"tenant_admin": {
		Permissions: []string{"dashboard:*", "system:*"}, // 租户管理员拥有仪表盘和系统管理权限
		FilterPermissions: []string{
			"system:tenant:create", "system:tenant:delete", // 过滤创建和删除租户的权限
		},
	},
	"guest": {
		Permissions: []string{"dashboard:analytics"}, // 访客只有分析页权限
		FilterPermissions: []string{
			"*:create", "*:update", "*:delete", // 过滤所有创建、更新、删除操作
		},
	},
}

// InitRole 初始化角色数据
func InitRole(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Role{}); err != nil {
		return err
	}

	// 检查是否已存在角色数据
	var count int64
	if err := db.Model(&models.Role{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	// 初始化角色数据
	roles := initRoles()

	if err := db.Create(&roles).Error; err != nil {
		return err
	}
	return nil
}

func initRoles() []*models.Role {
	return []*models.Role{
		{ID: 1, TenantID: 0, Name: "超级管理员", Code: "super_admin", Status: 10, Sort: 1, Remark: "系统超级管理员，拥有所有权限", DataScope: 1, IsSystem: true, CreatedBy: 1, UpdatedBy: 1},
		{ID: 2, TenantID: 0, Name: "系统管理员", Code: "admin", Status: 10, Sort: 2, Remark: "系统管理员，拥有大部分系统权限", DataScope: 1, IsSystem: true, CreatedBy: 1, UpdatedBy: 1},
		{ID: 3, TenantID: 0, Name: "租户管理员", Code: "tenant_admin", Status: 10, Sort: 3, Remark: "租户管理员，拥有租户内所有权限", DataScope: 1, IsSystem: true, CreatedBy: 1, UpdatedBy: 1},
		{ID: 4, TenantID: 0, Name: "访客", Code: "guest", Status: 10, Sort: 6, Remark: "访客，仅拥有查看权限", DataScope: 5, IsSystem: true, CreatedBy: 1, UpdatedBy: 1},
	}
}
