package data

import (
	"gin/internal/models"
	"time"

	"gorm.io/gorm"
)

// InitTenant 初始化默认租户
func InitTenant(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Tenant{}); err != nil {
		return err
	}

	// 检查默认租户是否已存在
	var count int64
	if err := db.Model(&models.Tenant{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	tenants := initTenants()

	return db.Create(&tenants).Error
}

func initTenants() []*models.Tenant {
	// 创建默认租户数据
	now := time.Now()
	expiredAt := now.AddDate(1, 0, 0)
	return []*models.Tenant{
		{ID: 1, Name: "测试租户", Domain: "test.example.com", Code: "test", Description: "用于测试环境的租户", Logo: "https://example.com/logo/test.png", ContactName: "测试管理员", ContactEmail: "<EMAIL>", ContactPhone: "13900139000", Address: "上海市浦东新区", MaxUsers: 50, IsActive: true, ExpiredAt: expiredAt, CreatedAt: now, UpdatedAt: now},
		{ID: 2, Name: "开发租户", Domain: "dev.example.com", Code: "dev", Description: "用于开发环境的租户", Logo: "https://example.com/logo/dev.png", ContactName: "开发管理员", ContactEmail: "<EMAIL>", ContactPhone: "13700137000", Address: "广州市天河区", MaxUsers: 20, IsActive: true, ExpiredAt: expiredAt, CreatedAt: now, UpdatedAt: now},
		{ID: 3, Name: "演示租户", Domain: "demo.example.com", Code: "demo", Description: "用于演示的租户", Logo: "https://example.com/logo/demo.png", ContactName: "演示管理员", ContactEmail: "<EMAIL>", ContactPhone: "13600136000", Address: "深圳市南山区", MaxUsers: 200, IsActive: true, ExpiredAt: expiredAt, CreatedAt: now, UpdatedAt: now},
	}
}
