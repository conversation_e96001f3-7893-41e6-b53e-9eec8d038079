package data

import (
	"gin/internal/constant"
	"gin/internal/models"
	"time"

	"gorm.io/gorm"
)

// InitDict 初始化数据字典
func InitDict(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Type{}, &models.Data{}); err != nil {
		return err
	}

	// 检查是否已存在数据
	var count int64
	if err := db.Model(&models.Type{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	now := time.Now()

	// 初始化字典类型
	dictTypes := []*models.Type{
		{ID: 1, Name: "用户状态", Code: "sys_user_status", Status: constant.StatusEnabled, Remark: "用户状态列表", CreatedAt: now, UpdatedAt: now},
		{ID: 2, Name: "审核状态", Code: "sys_audit_status", Status: constant.StatusEnabled, Remark: "审核状态列表", CreatedAt: now, UpdatedAt: now},
		{ID: 3, Name: "处理状态", Code: "sys_process_status", Status: constant.StatusEnabled, Remark: "处理状态列表", CreatedAt: now, UpdatedAt: now},
		{ID: 4, Name: "岗位类型", Code: "sys_position_type", Status: constant.StatusEnabled, Remark: "岗位类型列表", CreatedAt: now, UpdatedAt: now},
		{ID: 5, Name: "岗位级别", Code: "sys_position_level", Status: constant.StatusEnabled, Remark: "岗位级别列表", CreatedAt: now, UpdatedAt: now},
	}

	if err := db.Create(&dictTypes).Error; err != nil {
		return err
	}

	// 初始化字典数据
	var dictDataList []*models.Data

	// 用户状态
	dictDataList = append(dictDataList, []*models.Data{
		{TypeID: 1, Label: "正常", Value: "10", Sort: 1, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 1, Label: "禁用", Value: "-1", Sort: 2, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 1, Label: "锁定", Value: "-2", Sort: 3, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 1, Label: "过期", Value: "-3", Sort: 4, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
	}...)

	// 审核状态
	dictDataList = append(dictDataList, []*models.Data{
		{TypeID: 2, Label: "待审核", Value: "0", Sort: 1, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 2, Label: "已通过", Value: "10", Sort: 2, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 2, Label: "已拒绝", Value: "-1", Sort: 3, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
	}...)

	// 处理状态
	dictDataList = append(dictDataList, []*models.Data{
		{TypeID: 3, Label: "待处理", Value: "0", Sort: 1, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 3, Label: "处理中", Value: "1", Sort: 2, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 3, Label: "已完成", Value: "10", Sort: 3, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 3, Label: "处理失败", Value: "-1", Sort: 4, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 3, Label: "已取消", Value: "-2", Sort: 5, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
	}...)

	// 岗位类型
	dictDataList = append(dictDataList, []*models.Data{
		{TypeID: 4, Label: "管理岗", Value: "1", Sort: 1, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 4, Label: "业务岗", Value: "2", Sort: 2, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 4, Label: "技术岗", Value: "3", Sort: 3, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
	}...)

	// 岗位级别
	dictDataList = append(dictDataList, []*models.Data{
		{TypeID: 5, Label: "初级", Value: "1", Sort: 1, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 5, Label: "中级", Value: "2", Sort: 2, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 5, Label: "高级", Value: "3", Sort: 3, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
		{TypeID: 5, Label: "专家", Value: "4", Sort: 4, Status: constant.StatusEnabled, CreatedAt: now, UpdatedAt: now},
	}...)

	if err := db.Create(&dictDataList).Error; err != nil {
		return err
	}

	return nil
}
