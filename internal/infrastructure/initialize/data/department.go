package data

import (
	"gin/internal/models"
	"time"

	"gorm.io/gorm"
)

// InitDepartment 初始化部门数据
func InitDepartment(db *gorm.DB) error {
	// 先创建表结构
	if err := db.AutoMigrate(&models.Department{}); err != nil {
		return err
	}

	// 检查是否已存在部门数据
	var count int64
	if err := db.Model(&models.Department{}).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在数据，跳过初始化
	}

	now := time.Now()
	// 初始化部门数据
	departments := []*models.Department{
		{ID: 1, TenantID: 0, Name: "总公司", Code: "HQ", ParentID: 0, Ancestors: "0", Leader: "张三", Phone: "13800000001", Email: "<EMAIL>", Status: 10, Sort: 1, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 2, TenantID: 0, Name: "研发部", Code: "RD", ParentID: 1, Ancestors: "0,1", Leader: "李四", Phone: "13800000002", Email: "<EMAIL>", Status: 10, Sort: 1, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 3, TenantID: 0, Name: "市场部", Code: "MKT", ParentID: 1, Ancestors: "0,1", Leader: "王五", Phone: "13800000003", Email: "<EMAIL>", Status: 10, Sort: 2, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 4, TenantID: 0, Name: "财务部", Code: "FIN", ParentID: 1, Ancestors: "0,1", Leader: "赵六", Phone: "13800000004", Email: "<EMAIL>", Status: 10, Sort: 3, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 5, TenantID: 0, Name: "人事部", Code: "HR", ParentID: 1, Ancestors: "0,1", Leader: "钱七", Phone: "13800000005", Email: "<EMAIL>", Status: 10, Sort: 4, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 6, TenantID: 0, Name: "前端组", Code: "RD-FE", ParentID: 2, Ancestors: "0,1,2", Leader: "孙八", Phone: "13800000006", Email: "<EMAIL>", Status: 10, Sort: 1, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 7, TenantID: 0, Name: "后端组", Code: "RD-BE", ParentID: 2, Ancestors: "0,1,2", Leader: "周九", Phone: "13800000007", Email: "<EMAIL>", Status: 10, Sort: 2, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
		{ID: 8, TenantID: 0, Name: "测试组", Code: "RD-QA", ParentID: 2, Ancestors: "0,1,2", Leader: "吴十", Phone: "13800000008", Email: "<EMAIL>", Status: 10, Sort: 3, IsSystem: true, CreatedBy: 1, UpdatedBy: 1, CreatedAt: now, UpdatedAt: now},
	}

	if err := db.Create(&departments).Error; err != nil {
		return err
	}
	return nil
}
