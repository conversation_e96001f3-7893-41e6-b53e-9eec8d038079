package initialize

import (
	"gin/internal/infrastructure/initialize/data"
	"github.com/casbin/casbin/v2"
	"gorm.io/gorm"
)

type DataInitializer struct {
	uow      *gorm.DB
	enforcer *casbin.SyncedCachedEnforcer
}

func NewDataInitializer(uow *gorm.DB, enforcer *casbin.SyncedCachedEnforcer) *DataInitializer {
	return &DataInitializer{
		uow:      uow,
		enforcer: enforcer,
	}
}

// InitAll 初始化所有测试数据
func (i *DataInitializer) InitAll() error {
	if err := data.InitManagers(i.uow); err != nil {
		return err
	}
	if err := data.InitLang(i.uow); err != nil {
		return err
	}
	if err := data.InitTranslation(i.uow); err != nil {
		return err
	}
	if err := data.InitTenant(i.uow); err != nil {
		return err
	}
	if err := data.InitLoginLog(i.uow); err != nil {
		return err
	}
	if err := data.InitOperLog(i.uow); err != nil {
		return err
	}
	if err := data.InitRole(i.uow); err != nil {
		return err
	}
	if err := data.InitPermission(i.uow); err != nil {
		return err
	}
	if err := data.InitDepartment(i.uow); err != nil {
		return err
	}
	if err := data.InitPosition(i.uow); err != nil {
		return err
	}
	if err := data.InitDict(i.uow); err != nil {
		return err
	}
	if err := data.InitCasbinRules(i.uow, i.enforcer); err != nil {
		return err
	}
	return nil
}
