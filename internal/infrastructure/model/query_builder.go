package model

import (
	"fmt"
	"reflect"
	"strings"
	"time"

	"gorm.io/gorm"
)

// QueryOperator 定义查询操作符
type QueryOperator string

// 支持的查询操作符
const (
	OpEq      QueryOperator = "eq"       // 等于
	OpNeq     QueryOperator = "neq"      // 不等于
	OpLike    QueryOperator = "like"     // 模糊查询
	OpGt      QueryOperator = "gt"       // 大于
	OpGte     QueryOperator = "gte"      // 大于等于
	OpLt      QueryOperator = "lt"       // 小于
	OpLte     QueryOperator = "lte"      // 小于等于
	OpIn      QueryOperator = "in"       // 包含
	OpNotIn   QueryOperator = "not_in"   // 不包含
	OpBetween QueryOperator = "between"  // 范围
	OpIsNull  QueryOperator = "is_null"  // 为空
	OpNotNull QueryOperator = "not_null" // 不为空
)

// QueryValueType 定义查询值类型
type QueryValueType string

// 支持的查询值类型
const (
	TypeString  QueryValueType = "string"  // 字符串
	TypeInt     QueryValueType = "int"     // 整数
	TypeFloat   QueryValueType = "float"   // 浮点数
	TypeBool    QueryValueType = "bool"    // 布尔值
	TypeTime    QueryValueType = "time"    // 时间
	TypeDate    QueryValueType = "date"    // 日期
	TypeArray   QueryValueType = "array"   // 数组
	TypeComplex QueryValueType = "complex" // 复杂类型
)

// QueryField 查询字段定义
type QueryField struct {
	Column    string         // 数据库列名
	Operation QueryOperator  // 操作符
	ValueType QueryValueType // 值类型
	Options   []string       // 可选值列表，用于验证
}

// QueryBuilder 查询构建器，用于构建动态查询条件
type QueryBuilder struct {
	Fields map[string]QueryField // 字段映射
}

// NewQueryBuilder 创建查询构建器
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		Fields: make(map[string]QueryField),
	}
}

// RegisterField 注册查询字段
func (qb *QueryBuilder) RegisterField(paramName string, field QueryField) *QueryBuilder {
	qb.Fields[paramName] = field
	return qb
}

// RegisterFields 批量注册查询字段
func (qb *QueryBuilder) RegisterFields(fields map[string]QueryField) *QueryBuilder {
	for k, v := range fields {
		qb.Fields[k] = v
	}
	return qb
}

// BuildQuery 根据查询参数构建查询条件
func (qb *QueryBuilder) BuildQuery(db *gorm.DB, params map[string]interface{}) *gorm.DB {
	for key, value := range params {
		if value == nil {
			continue
		}

		// 处理空字符串
		if strVal, ok := value.(string); ok && strVal == "" {
			continue
		}

		if field, exists := qb.Fields[key]; exists {
			db = qb.applyCondition(db, field, value)
		}
	}
	return db
}

// applyCondition 应用查询条件
func (qb *QueryBuilder) applyCondition(db *gorm.DB, field QueryField, value interface{}) *gorm.DB {
	// 根据值类型进行转换
	convertedValue, err := qb.convertValue(value, field.ValueType)
	if err != nil {
		// 转换失败，跳过该条件
		return db
	}

	// 根据操作符构建查询条件
	switch field.Operation {
	case OpEq:
		return db.Where(field.Column+" = ?", convertedValue)
	case OpNeq:
		return db.Where(field.Column+" != ?", convertedValue)
	case OpLike:
		if strVal, ok := convertedValue.(string); ok {
			return db.Where(field.Column+" LIKE ?", "%"+strVal+"%")
		}
	case OpGt:
		return db.Where(field.Column+" > ?", convertedValue)
	case OpGte:
		return db.Where(field.Column+" >= ?", convertedValue)
	case OpLt:
		return db.Where(field.Column+" < ?", convertedValue)
	case OpLte:
		return db.Where(field.Column+" <= ?", convertedValue)
	case OpIn:
		if reflect.TypeOf(convertedValue).Kind() == reflect.Slice {
			return db.Where(field.Column+" IN ?", convertedValue)
		}
	case OpNotIn:
		if reflect.TypeOf(convertedValue).Kind() == reflect.Slice {
			return db.Where(field.Column+" NOT IN ?", convertedValue)
		}
	case OpBetween:
		if arr, ok := convertedValue.([]interface{}); ok && len(arr) == 2 {
			return db.Where(field.Column+" BETWEEN ? AND ?", arr[0], arr[1])
		}
	case OpIsNull:
		return db.Where(field.Column + " IS NULL")
	case OpNotNull:
		return db.Where(field.Column + " IS NOT NULL")
	}

	return db
}

// convertValue 转换值类型
func (qb *QueryBuilder) convertValue(value interface{}, valueType QueryValueType) (interface{}, error) {
	switch valueType {
	case TypeString:
		return fmt.Sprintf("%v", value), nil
	case TypeInt:
		switch v := value.(type) {
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			return v, nil
		case string:
			if v == "" {
				return 0, nil
			}
			var intVal int
			_, err := fmt.Sscanf(v, "%d", &intVal)
			return intVal, err
		default:
			return 0, fmt.Errorf("cannot convert %v to int", value)
		}
	case TypeFloat:
		switch v := value.(type) {
		case float32, float64:
			return v, nil
		case string:
			if v == "" {
				return 0.0, nil
			}
			var floatVal float64
			_, err := fmt.Sscanf(v, "%f", &floatVal)
			return floatVal, err
		default:
			return 0.0, fmt.Errorf("cannot convert %v to float", value)
		}
	case TypeBool:
		switch v := value.(type) {
		case bool:
			return v, nil
		case string:
			return strings.ToLower(v) == "true" || v == "1", nil
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			return v != 0, nil
		default:
			return false, fmt.Errorf("cannot convert %v to bool", value)
		}
	case TypeTime, TypeDate:
		switch v := value.(type) {
		case time.Time:
			return v, nil
		case string:
			if v == "" {
				return time.Time{}, fmt.Errorf("empty time string")
			}
			// 尝试多种时间格式
			formats := []string{
				"2006-01-02 15:04:05",
				"2006-01-02",
				"2006/01/02 15:04:05",
				"2006/01/02",
				time.RFC3339,
			}

			for _, format := range formats {
				t, err := time.Parse(format, v)
				if err == nil {
					return t, nil
				}
			}
			return time.Time{}, fmt.Errorf("cannot parse time: %s", v)
		default:
			return time.Time{}, fmt.Errorf("cannot convert %v to time", value)
		}
	case TypeArray:
		if reflect.TypeOf(value).Kind() == reflect.Slice {
			return value, nil
		}
		if strVal, ok := value.(string); ok && strVal != "" {
			// 尝试将字符串解析为数组，例如 "1,2,3"
			return strings.Split(strVal, ","), nil
		}
		return nil, fmt.Errorf("cannot convert %v to array", value)
	default:
		// 对于复杂类型或未知类型，直接返回原值
		return value, nil
	}
}

// ParseTimeRange 解析时间范围
func (qb *QueryBuilder) ParseTimeRange(params map[string]interface{}, startKey, endKey string) error {
	// 处理开始时间
	if startTimeStr, ok := params[startKey].(string); ok && startTimeStr != "" {
		startTime, err := time.Parse("2006-01-02", startTimeStr)
		if err != nil {
			return err
		}
		params[startKey] = startTime
	}

	// 处理结束时间
	if endTimeStr, ok := params[endKey].(string); ok && endTimeStr != "" {
		endTime, err := time.Parse("2006-01-02", endTimeStr)
		if err != nil {
			return err
		}
		// 设置为当天结束时间
		endTime = endTime.Add(24*time.Hour - time.Second)
		params[endKey] = endTime
	}

	return nil
}
