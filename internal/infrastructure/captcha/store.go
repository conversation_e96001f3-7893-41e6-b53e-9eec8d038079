package captcha

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/redis/rueidis"
)

// MemoryStore 内存验证码存储
type MemoryStore struct {
	store  map[string]*StoreItem
	mutex  sync.RWMutex
	maxLen int
	ttl    time.Duration
}

// StoreItem 存储项
type StoreItem struct {
	value     string
	createdAt time.Time
}

// NewMemoryStore 创建内存存储
func NewMemoryStore(maxLen int, ttl time.Duration) *MemoryStore {
	store := &MemoryStore{
		store:  make(map[string]*StoreItem),
		maxLen: maxLen,
		ttl:    ttl,
	}

	// 启动清理协程
	go store.cleanup()

	return store
}

// Set 设置验证码
func (m *MemoryStore) Set(id string, value string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 如果存储达到最大长度，清理过期项
	if len(m.store) >= m.maxLen {
		m.cleanupExpired()
	}

	// 如果仍然达到最大长度，删除最旧的项
	if len(m.store) >= m.maxLen {
		m.removeOldest()
	}

	m.store[id] = &StoreItem{
		value:     value,
		createdAt: time.Now(),
	}

	return nil
}

// Get 获取验证码
func (m *MemoryStore) Get(id string, clear bool) string {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	item, exists := m.store[id]
	if !exists {
		return ""
	}

	// 检查是否过期
	if time.Since(item.createdAt) > m.ttl {
		delete(m.store, id)
		return ""
	}

	value := item.value

	if clear {
		delete(m.store, id)
	}

	return value
}

// Verify 验证验证码
func (m *MemoryStore) Verify(id, answer string, clear bool) bool {
	stored := m.Get(id, clear)
	return stored != "" && strings.EqualFold(stored, answer)
}

// cleanup 定期清理过期项
func (m *MemoryStore) cleanup() {
	ticker := time.NewTicker(time.Minute) // 每分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		m.mutex.Lock()
		m.cleanupExpired()
		m.mutex.Unlock()
	}
}

// cleanupExpired 清理过期项（需要在锁内调用）
func (m *MemoryStore) cleanupExpired() {
	now := time.Now()
	for id, item := range m.store {
		if now.Sub(item.createdAt) > m.ttl {
			delete(m.store, id)
		}
	}
}

// removeOldest 删除最旧的项（需要在锁内调用）
func (m *MemoryStore) removeOldest() {
	var oldestID string
	var oldestTime time.Time

	for id, item := range m.store {
		if oldestID == "" || item.createdAt.Before(oldestTime) {
			oldestID = id
			oldestTime = item.createdAt
		}
	}

	if oldestID != "" {
		delete(m.store, oldestID)
	}
}

// RedisCaptchaStore Redis验证码存储
type RedisCaptchaStore struct {
	client     rueidis.Client
	expiration time.Duration
}

// NewRedisCaptchaStore 创建Redis存储
func NewRedisCaptchaStore(client rueidis.Client, expiration time.Duration) *RedisCaptchaStore {
	return &RedisCaptchaStore{
		client:     client,
		expiration: expiration,
	}
}

// Set 设置验证码
func (r *RedisCaptchaStore) Set(id string, value string) error {
	key := fmt.Sprintf("captcha:%s", id)
	ctx := context.Background()
	return r.client.Do(ctx, r.client.B().Set().Key(key).Value(value).Ex(r.expiration).Build()).Error()
}

// Get 获取验证码
func (r *RedisCaptchaStore) Get(id string, clear bool) string {
	key := fmt.Sprintf("captcha:%s", id)
	ctx := context.Background()

	val, err := r.client.Do(ctx, r.client.B().Get().Key(key).Build()).ToString()
	if err != nil {
		return ""
	}

	if clear {
		_ = r.client.Do(ctx, r.client.B().Del().Key(key).Build()).Error()
	}

	return val
}

// Verify 验证验证码
func (r *RedisCaptchaStore) Verify(id, answer string, clear bool) bool {
	stored := r.Get(id, clear)
	return stored != "" && strings.EqualFold(stored, answer)
}
