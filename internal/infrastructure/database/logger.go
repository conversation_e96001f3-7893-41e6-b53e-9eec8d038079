package database

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm/logger"
)

type SQLLogger struct {
	logger.Interface
	SlowThreshold time.Duration
}

func (l *SQLLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()

	// 记录 SQL 执行信息
	logStr := fmt.Sprintf("[SQL] %s | 影响行数: %d | 耗时: %s", sql, rows, elapsed)
	if err != nil {
		logStr += fmt.Sprintf(" | 错误: %v", err)
	}

	if elapsed > l.SlowThreshold {
		logStr = fmt.Sprintf("[慢查询] %s", logStr)
	}

	fmt.Println(logStr)
}
