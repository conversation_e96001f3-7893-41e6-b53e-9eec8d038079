package cache

import (
	"context"
	"errors"
	"fmt"
	"gin/internal/infrastructure/config"
	"time"

	"github.com/redis/rueidis"
)

// RedisClient 封装Redis客户端
type RedisClient struct {
	client rueidis.Client
}

// NewRedisClient 创建新的Redis客户端实例
func NewRedisClient(conf *config.RedisConfig) (*RedisClient, error) {
	client, err := rueidis.NewClient(rueidis.ClientOption{
		InitAddress:      []string{fmt.Sprintf("%s:%d", conf.Host, conf.Port)},
		Password:         conf.Password,
		SelectDB:         conf.DB,
		ConnWriteTimeout: time.Second * 5,
		BlockingPoolSize: conf.PoolSize,
		DisableCache:     true, // 禁用客户端缓存
	})
	if err != nil {
		return nil, fmt.Errorf("redis初始化失败: %w", err)
	}

	return &RedisClient{
		client: client,
	}, nil
}

// Client 返回底层的 rueidis.Client
func (r *RedisClient) Client() rueidis.Client {
	if r == nil {
		return nil
	}
	return r.client
}

// CheckHealth 检查Redis客户端的健康状态
func (r *RedisClient) CheckHealth(ctx context.Context) bool {
	if r == nil || r.client == nil {
		return false
	}
	return r.client.Do(ctx, r.client.B().Ping().Build()).Error() == nil
}

// Close 关闭Redis客户端
func (r *RedisClient) Close() {
	if r != nil && r.client != nil {
		r.client.Close()
	}
}

// Set 执行SET操作
// 统一错误处理，确保在客户端未初始化时返回有意义的错误。
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	if r == nil || r.client == nil {
		return errors.New("redis客户端未初始化")
	}

	cmd := r.client.B().Set().Key(key).Value(fmt.Sprint(value))
	if ttl > 0 {
		cmd.Ex(ttl)
	}
	return r.client.Do(ctx, cmd.Build()).Error()
}

// Get 执行GET操作
func (r *RedisClient) Get(ctx context.Context, key string) (string, error) {
	if r.client == nil {
		return "", errors.New("redis客户端尚未初始化")
	}
	result := r.client.Do(ctx, r.client.B().Get().Key(key).Build())
	if result.Error() != nil {
		if errors.Is(result.Error(), rueidis.Nil) {
			return "", nil // Key不存在，返回空字符串和nil错误
		}
		return "", result.Error() // 其他Redis错误
	}
	return result.ToString() // 成功获取，返回字符串
}

// Del 执行DEL操作
func (r *RedisClient) Del(ctx context.Context, key string) error {
	if r.client == nil {
		return errors.New("redis客户端尚未初始化")
	}
	return r.client.Do(ctx, r.client.B().Del().Key(key).Build()).Error()
}

// IncrBy 执行INCURABLY操作
func (r *RedisClient) IncrBy(ctx context.Context, key string, increment int64) (int64, error) {
	if r == nil || r.client == nil {
		return 0, errors.New("redis客户端未初始化")
	}
	result := r.client.Do(ctx, r.client.B().Incrby().Key(key).Increment(increment).Build())
	if err := result.Error(); err != nil {
		return 0, fmt.Errorf("redis键%s递增失败: %w", key, err)
	}
	return result.AsInt64()
}
