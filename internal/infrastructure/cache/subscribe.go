package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"github.com/redis/rueidis"
)

// SubscribeService Redis 订阅服务接口
type SubscribeService interface {
	// Subscribe 订阅频道并注册回调函数
	Subscribe(channel string, callback func(data []byte)) error
	// UnSubscribe 取消订阅频道
	UnSubscribe(channel string) error
	// Publish 发布消息到频道
	Publish(channel string, message interface{}) error
	// GetSubscribedChannels 获取当前订阅的频道列表
	GetSubscribedChannels() []string
	// Close 关闭订阅连接
	Close() error
}

// Config 配置选项
type Config struct {
	MaxReconnectAttempts int           // 最大重连尝试次数，0表示无限重试
	ReconnectInterval    time.Duration // 重连间隔
	MessageBufferSize    int           // 消息缓冲区大小
	WorkerPoolSize       int           // 工作池大小
}

// messageTask 消息任务
type messageTask struct {
	channel  string
	data     []byte
	callback func(data []byte)
}

// RedisSubscribeService Redis 订阅服务实现
type RedisSubscribeService struct {
	sync.RWMutex
	client       rueidis.Client               // Rueidis客户端
	config       Config                       // 配置
	clientMaps   map[string]func(data []byte) // 订阅者回调函数映射
	ctx          context.Context              // 上下文
	cancel       context.CancelFunc           // 取消函数
	closed       int32                        // 是否已关闭 (原子操作)
	isConsuming  int32                        // 是否正在消费 (原子操作)
	consumeWg    sync.WaitGroup               // 消费者等待组
	workerWg     sync.WaitGroup               // 工作者等待组
	messageQueue chan messageTask             // 消息队列
	workerPool   chan struct{}                // 工作池信号量
}

// NewRedisSubscribeService 创建Redis订阅服务实例
func NewRedisSubscribeService(client rueidis.Client, configs ...Config) SubscribeService {
	var config Config
	if len(configs) > 0 {
		config = configs[0]
	} else {
		config = Config{
			MaxReconnectAttempts: 0, // 无限重试
			ReconnectInterval:    time.Second * 3,
			MessageBufferSize:    1000,
			WorkerPoolSize:       10,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	service := &RedisSubscribeService{
		client:       client,
		config:       config,
		clientMaps:   make(map[string]func(data []byte)),
		ctx:          ctx,
		cancel:       cancel,
		messageQueue: make(chan messageTask, config.MessageBufferSize),
		workerPool:   make(chan struct{}, config.WorkerPoolSize),
	}

	// 初始化工作池
	for i := 0; i < config.WorkerPoolSize; i++ {
		service.workerPool <- struct{}{}
	}

	// 启动工作者
	service.startWorkers()

	return service
}

// Subscribe 订阅频道并注册回调函数
func (r *RedisSubscribeService) Subscribe(channel string, callback func(data []byte)) error {
	if atomic.LoadInt32(&r.closed) == 1 {
		return fmt.Errorf("subscribe service is closed")
	}

	r.Lock()
	defer r.Unlock()

	// 注册回调函数
	r.clientMaps[channel] = callback

	// 如果还没有启动消费者，启动它
	if atomic.CompareAndSwapInt32(&r.isConsuming, 0, 1) {
		r.consumeWg.Add(1)
		go r.consume()
	}

	return nil
}

// UnSubscribe 取消订阅频道
func (r *RedisSubscribeService) UnSubscribe(channel string) error {
	if atomic.LoadInt32(&r.closed) == 1 {
		return fmt.Errorf("subscribe service is closed")
	}

	r.Lock()
	defer r.Unlock()

	// 删除回调函数
	delete(r.clientMaps, channel)

	return nil
}

// Publish 发布消息到频道 (支持批量发布优化)
func (r *RedisSubscribeService) Publish(channel string, message interface{}) error {
	if atomic.LoadInt32(&r.closed) == 1 {
		return fmt.Errorf("subscribe service is closed")
	}

	var data string
	switch v := message.(type) {
	case string:
		data = v
	case []byte:
		data = string(v)
	default:
		jsonData, err := json.Marshal(message)
		if err != nil {
			return fmt.Errorf("failed to marshal message: %w", err)
		}
		data = string(jsonData)
	}

	// 使用 rueidis 的管道优化
	cmd := r.client.B().Publish().Channel(channel).Message(data).Build()
	result := r.client.Do(r.ctx, cmd)

	if result.Error() != nil {
		return fmt.Errorf("failed to publish message to channel %s: %w", channel, result.Error())
	}

	return nil
}

// GetSubscribedChannels 获取当前订阅的频道列表
func (r *RedisSubscribeService) GetSubscribedChannels() []string {
	r.RLock()
	defer r.RUnlock()

	channels := make([]string, 0, len(r.clientMaps))
	for channel := range r.clientMaps {
		channels = append(channels, channel)
	}

	return channels
}

// Close 关闭订阅连接
func (r *RedisSubscribeService) Close() error {
	if !atomic.CompareAndSwapInt32(&r.closed, 0, 1) {
		return nil // 已经关闭
	}

	// 取消上下文
	r.cancel()

	// 等待所有协程结束
	r.consumeWg.Wait()
	r.workerWg.Wait()

	// 关闭消息队列
	close(r.messageQueue)
	close(r.workerPool)

	// 清空回调映射
	r.Lock()
	r.clientMaps = make(map[string]func(data []byte))
	r.Unlock()

	return nil
}

// startWorkers 启动工作者协程池
func (r *RedisSubscribeService) startWorkers() {
	for i := 0; i < r.config.WorkerPoolSize; i++ {
		r.workerWg.Add(1)
		go r.worker()
	}
}

// worker 工作者协程
func (r *RedisSubscribeService) worker() {
	defer r.workerWg.Done()

	for {
		select {
		case <-r.ctx.Done():
			return
		case task, ok := <-r.messageQueue:
			if !ok {
				return
			}

			// 执行回调函数
			func() {
				defer func() {
					if err := recover(); err != nil {
						log.Printf("Panic in message callback for channel %s: %v", task.channel, err)
					}
				}()

				task.callback(task.data)
			}()
		}
	}
}

// getCurrentChannels 获取当前频道列表
func (r *RedisSubscribeService) getCurrentChannels() []string {
	r.RLock()
	defer r.RUnlock()

	channels := make([]string, 0, len(r.clientMaps))
	for channel := range r.clientMaps {
		channels = append(channels, channel)
	}
	return channels
}

// channelsEqual 比较两个频道列表是否相等
func (r *RedisSubscribeService) channelsEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	// 创建映射来比较
	mapA := make(map[string]bool)
	for _, ch := range a {
		mapA[ch] = true
	}

	for _, ch := range b {
		if !mapA[ch] {
			return false
		}
	}

	return true
}

// consume 消费订阅消息
func (r *RedisSubscribeService) consume() {
	defer r.consumeWg.Done()
	defer atomic.StoreInt32(&r.isConsuming, 0)

	var currentReceiveCancel context.CancelFunc
	var currentChannels []string
	var reconnectAttempts int64

	for {
		select {
		case <-r.ctx.Done():
			if currentReceiveCancel != nil {
				currentReceiveCancel()
			}
			return
		default:
			// 获取当前需要订阅的频道
			channels := r.getCurrentChannels()

			if len(channels) == 0 {
				// 没有订阅的频道，取消当前订阅并等待
				if currentReceiveCancel != nil {
					currentReceiveCancel()
					currentReceiveCancel = nil
				}
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// 检查频道是否发生变化
			if !r.channelsEqual(currentChannels, channels) {
				// 取消当前订阅
				if currentReceiveCancel != nil {
					currentReceiveCancel()
					time.Sleep(10 * time.Millisecond) // 短暂等待以确保旧订阅被清理
				}

				currentChannels = channels

				// 创建新的订阅
				cmd, valid := r.buildSubscribeCommand(channels)
				if !valid {
					time.Sleep(100 * time.Millisecond)
					continue
				}

				// 启动新的订阅goroutine
				receiveCtx, cancel := context.WithCancel(r.ctx)
				currentReceiveCancel = cancel

				go func() {
					err := r.client.Receive(receiveCtx, cmd, func(msg rueidis.PubSubMessage) {
						r.handleMessage(msg)
					})

					if err != nil && receiveCtx.Err() == nil && r.ctx.Err() == nil {
						atomic.AddInt64(&reconnectAttempts, 1)
						attempts := atomic.LoadInt64(&reconnectAttempts)

						// 检查是否超过最大重连次数
						if r.config.MaxReconnectAttempts > 0 && attempts >= int64(r.config.MaxReconnectAttempts) {
							log.Printf("Max reconnect attempts reached: %d", attempts)
							return
						}

						log.Printf("Subscription lost, will retry: %v", err)
					} else if err == nil {
						// 重连成功，重置计数器
						if atomic.LoadInt64(&reconnectAttempts) > 0 {
							log.Printf("Subscription restored after %d attempts", atomic.SwapInt64(&reconnectAttempts, 0))
						}
					}
				}()
			}

			// 等待一段时间再检查频道变化
			time.Sleep(100 * time.Millisecond)
		}
	}
}

// buildSubscribeCommand 构建订阅命令
func (r *RedisSubscribeService) buildSubscribeCommand(channels []string) (rueidis.Completed, bool) {
	if len(channels) == 0 {
		return rueidis.Completed{}, false
	}

	if len(channels) == 1 {
		return r.client.B().Subscribe().Channel(channels[0]).Build(), true
	}

	// 多频道订阅
	builder := r.client.B().Subscribe().Channel(channels[0])
	for _, channel := range channels[1:] {
		builder = builder.Channel(channel)
	}
	return builder.Build(), true
}

// handleMessage 处理接收到的消息
func (r *RedisSubscribeService) handleMessage(msg rueidis.PubSubMessage) {
	channel := msg.Channel
	payload := []byte(msg.Message)

	r.RLock()
	callback, ok := r.clientMaps[channel]
	r.RUnlock()

	if !ok {
		return
	}

	// 将消息任务放入队列
	select {
	case r.messageQueue <- messageTask{
		channel:  channel,
		data:     payload,
		callback: callback,
	}:
	case <-r.ctx.Done():
		return
	default:
		// 队列满了，使用日志记录
		log.Printf("Message queue full, dropping message for channel: %s", channel)
	}
}
