package config

// UploadConfig 文件上传配置
type UploadConfig struct {
	MaxFileSize  int64    `yaml:"max_file_size" default:"52428800"`        // 最大文件大小（字节，默认50MB）
	StoragePath  string   `yaml:"storage_path" default:"./static/uploads"` // 存储路径
	BaseURL      string   `yaml:"base_url" default:"/uploads"`             // 基础访问URL
	AllowedTypes []string `yaml:"allowed_types"`                           // 允许的文件扩展名
}

// GetDefaultUploadConfig 获取默认上传配置
func GetDefaultUploadConfig() *UploadConfig {
	return &UploadConfig{
		MaxFileSize: 50 << 20, // 50MB
		StoragePath: "./static/uploads",
		BaseURL:     "/uploads",
		AllowedTypes: []string{
			".jpg", ".jpeg", ".png", ".gif", ".webp", // 图片
			".pdf", ".doc", ".docx", ".txt", // 文档
			".zip", ".rar", // 压缩包
		},
	}
}
