package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	JWT      JWTConfig      `yaml:"jwt"`
	Redis    RedisConfig    `yaml:"redis"`
	Logger   LoggerConfig   `yaml:"logger"`
	Limiter  LimiterConfig  `yaml:"limiter"` // 直接添加限流配置
	Captcha  CaptchaConfig  `yaml:"captcha"` // 验证码配置
}

type LoggerConfig struct {
	Level       string     `yaml:"level"`       // 日志级别
	Format      string     `yaml:"format"`      // 日志格式 (json/console)
	Output      string     `yaml:"output"`      // 输出目标 (stdout/file/both)
	AddSource   bool       `yaml:"add_source"`  // 是否添加源码信息
	TimeFormat  string     `yaml:"time_format"` // 时间格式
	CallerSkip  int        `yaml:"caller_skip"` // 调用者跳过的帧数
	Development bool       `yaml:"development"` // 是否为开发模式
	MaxBuffer   int        `yaml:"max_buffer"`  // 最大缓冲区大小
	File        FileConfig `yaml:"file"`        // 文件配置
}

type FileConfig struct {
	Filename   string `yaml:"filename"`    // 日志文件路径
	MaxSize    int    `yaml:"max_size"`    // 每个日志文件的最大大小(MB)
	MaxAge     int    `yaml:"max_age"`     // 日志文件保留天数
	MaxBackups int    `yaml:"max_backups"` // 保留的旧日志文件最大数量
	Compress   bool   `yaml:"compress"`    // 是否压缩旧日志文件
	ShowLine   bool   `yaml:"show_line"`   // 是否显示调用行号
	LocalTime  bool   `yaml:"local_time"`  // 是否使用本地时间
}

type ServerConfig struct {
	Port     string `yaml:"port"`
	Mode     string `yaml:"mode"`
	LogLevel string `yaml:"log_level"`
}

type DatabaseConfig struct {
	Host            string `yaml:"host"`
	Port            string `yaml:"port"`
	User            string `yaml:"user"`
	Password        string `yaml:"password"`
	DBName          string `yaml:"dbname"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime"`
	ConnMaxIdleTime int    `yaml:"conn_max_idle_time"`
	LogLevel        string `yaml:"log_level"`
	SlowThreshold   int    `yaml:"slow_threshold"`
}

type JWTConfig struct {
	Secret     string `yaml:"secret"`
	ExpireTime int    `yaml:"expire_time"`
}

type RedisConfig struct {
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	Password     string `yaml:"password"`
	DB           int    `yaml:"db"`
	PoolSize     int    `yaml:"pool_size"`
	MinIdleConns int    `yaml:"min_idle_conns"`
}

// LimiterConfig 添加限流配置结构体
type LimiterConfig struct {
	Max        int64 `yaml:"max"`        // 速率限制最大数
	Expiration int   `yaml:"expiration"` // 速率限制过期时间（秒）
}

// CaptchaConfig 验证码配置结构体
type CaptchaConfig struct {
	Type       string `yaml:"type"`        // 验证码类型：digit, string, chinese, math
	Height     int    `yaml:"height"`      // 图片高度
	Width      int    `yaml:"width"`       // 图片宽度
	Length     int    `yaml:"length"`      // 验证码长度
	NoiseCount int    `yaml:"noise_count"` // 干扰线数量 (字符串、中文、数学验证码)
	Source     string `yaml:"source"`      // 字符源 (字符串验证码)
	BgColor    string `yaml:"bg_color"`    // 背景颜色 (RGB格式: "255,255,255")
	StoreType  string `yaml:"store_type"`  // 存储类型：memory, redis
	Expiration int    `yaml:"expiration"`  // 验证码过期时间(秒)
}

func LoadConfig(path string) (*Config, error) {
	config := &Config{}

	file, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	err = yaml.Unmarshal(file, config)
	if err != nil {
		return nil, err
	}

	return config, nil
}
