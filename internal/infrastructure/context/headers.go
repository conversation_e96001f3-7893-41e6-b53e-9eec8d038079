package context

import (
	"gin/pkg/jwt"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// GetLang 从请求中检索语言。
// 首先检查 Accept-Language 请求头，然后回退到 'lang' 查询参数。
func GetLang(ctx *gin.Context) string {
	if lang := ctx.GetHeader("Accept-Language"); lang != "" {
		// 请求头可能包含多个语言项，可能还带权重，例如: zh-CN;q=0.9,en;q=0.8
		if parts := strings.Split(lang, ","); len(parts) > 0 {
			primary := strings.Split(parts[0], ";")[0]
			return primary
		}
	}

	if queryLang := ctx.Query("lang"); queryLang != "" {
		return queryLang
	}

	return "zh-CN"
}

// GetTimeZone 从请求中检索时区。
// 首先检查 Time-Zone 请求头，然后回退到 'timezone' 查询参数。
func GetTimeZone(ctx *gin.Context) string {
	// 使用 Gin 的 GetHeader 方法检查 Time-Zone 请求头
	timeZone := ctx.GetHeader("Time-Zone")
	if timeZone != "" {
		return timeZone
	}

	// 如果请求头中没有找到，检查查询参数
	timeZone = ctx.Query("timezone")
	if timeZone != "" {
		return timeZone
	}

	// 默认返回亚洲/上海时区
	return "Asia/Shanghai"
}

// GetTimeZoneLocation 获取时区对应的 time.Location
func GetTimeZoneLocation(ctx *gin.Context) (*time.Location, error) {
	tz := GetTimeZone(ctx)
	return time.LoadLocation(tz)
}

// GetOriginHost 从请求中检索来源Host。
// 首先检查 X-Forwarded-Host 请求头，然后回退到 Referer 请求头。
// 如果两者都不存在，则返回空字符串。
func GetOriginHost(ctx *gin.Context) string {
	// 首先检查 X-Forwarded-Host 请求头
	origin := ctx.GetHeader("X-Forwarded-Host")
	if origin != "" {
		return origin
	}

	// 检查 Origin 请求头
	origin = ctx.GetHeader("Origin")
	if origin != "" {
		if parsedURL, err := url.Parse(origin); err == nil {
			return parsedURL.Host
		}
	}

	// 如果 X-Forwarded-Host 和 Origin 不存在，检查 Referer 请求头
	referer := ctx.GetHeader("Referer")
	if referer != "" {
		// 解析 Referer URL 以提取 host
		if parsedURL, err := url.Parse(referer); err == nil {
			return parsedURL.Host
		}
	}

	// 如果都没有找到，返回请求的 Host
	return ctx.Request.Host
}

// GetClientIP 获取客户端真实IP地址
func GetClientIP(ctx *gin.Context) string {
	// 尝试从各种代理头中获取
	for _, header := range []string{
		"X-Real-IP",
		"X-Forwarded-For",
		"CF-Connecting-IP", // Cloudflare
		"True-Client-IP",   // Akamai and Cloudflare
	} {
		ip := ctx.GetHeader(header)
		if ip != "" {
			// X-Forwarded-For 可能包含多个IP，取第一个
			if header == "X-Forwarded-For" {
				ips := strings.Split(ip, ",")
				if len(ips) > 0 {
					return strings.TrimSpace(ips[0])
				}
			}
			return ip
		}
	}

	// 如果没有代理头，使用 RemoteAddr
	return ctx.ClientIP()
}

// GetUserIdFromCtx 从上下文中获取用户ID
func GetUserIdFromCtx(ctx *gin.Context) uint {
	v, exists := ctx.Get(ClaimsContextKey)
	if !exists {
		return 0
	}
	return v.(*jwt.TokenClaims).UserId
}

// GetManagerIdFromCtx 从上下文中获取管理员ID
func GetManagerIdFromCtx(ctx *gin.Context) uint {
	v, exists := ctx.Get(ClaimsContextKey)
	if !exists {
		return 0
	}
	return v.(*jwt.TokenClaims).ManagerId
}
