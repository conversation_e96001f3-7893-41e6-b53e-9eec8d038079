package casbin

import (
	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/persist"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/gorm"
)

// IEnforcer 定义Casbin Enforcer的接口
type IEnforcer interface {
	// 策略管理
	ReloadPolicy() error

	// 权限检查
	Enforce(rvals ...interface{}) (bool, error)

	// 角色管理
	AddRoleForUser(user string, role string, domain ...string) (bool, error)
	DeleteRoleForUser(user string, role string, domain ...string) (bool, error)
	GetRolesForUser(name string, domain ...string) ([]string, error)
	GetUsersForRole(name string, domain ...string) ([]string, error)
	HasRoleForUser(name string, role string, domain ...string) (bool, error)

	// 策略管理
	AddPolicy(params ...interface{}) (bool, error)
	RemovePolicy(params ...interface{}) (bool, error)
	AddNamedPolicy(ptype string, params ...interface{}) (bool, error)
	RemoveNamedPolicy(ptype string, params ...interface{}) (bool, error)

	// 获取原始enforcer
	GetEnforcer() *casbin.SyncedCachedEnforcer
}

// Casbin 结构体封装 Casbin 相关组件
type Casbin struct {
	Enforcer *casbin.SyncedCachedEnforcer
	Adapter  persist.Adapter
}

// NewCasbin 创建新的 Casbin 实例
func NewCasbin(db *gorm.DB) (IEnforcer, error) {
	// 初始化 Adapter
	adapter, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, err
	}

	// 创建 Enforcer
	enforcer, err := casbin.NewSyncedCachedEnforcer("config/rbac_model.conf", adapter)
	if err != nil {
		return nil, err
	}

	// 加载策略
	if err = enforcer.LoadPolicy(); err != nil {
		return nil, err
	}

	// 启用自动保存
	enforcer.EnableAutoSave(true)

	return &Casbin{
		Enforcer: enforcer,
		Adapter:  adapter,
	}, nil
}

// ReloadPolicy 重新加载策略
func (c *Casbin) ReloadPolicy() error {
	if c != nil && c.Enforcer != nil {
		return c.Enforcer.LoadPolicy()
	}
	return nil
}

// Enforce 检查权限
func (c *Casbin) Enforce(rvals ...interface{}) (bool, error) {
	return c.Enforcer.Enforce(rvals...)
}

// AddRoleForUser 为用户添加角色
func (c *Casbin) AddRoleForUser(user string, role string, domain ...string) (bool, error) {
	return c.Enforcer.AddRoleForUser(user, role, domain...)
}

// DeleteRoleForUser 删除用户的角色
func (c *Casbin) DeleteRoleForUser(user string, role string, domain ...string) (bool, error) {
	return c.Enforcer.DeleteRoleForUser(user, role, domain...)
}

// GetRolesForUser 获取用户的所有角色
func (c *Casbin) GetRolesForUser(name string, domain ...string) ([]string, error) {
	return c.Enforcer.GetRolesForUser(name, domain...)
}

// GetUsersForRole 获取拥有指定角色的所有用户
func (c *Casbin) GetUsersForRole(name string, domain ...string) ([]string, error) {
	return c.Enforcer.GetUsersForRole(name, domain...)
}

// HasRoleForUser 检查用户是否拥有指定角色
func (c *Casbin) HasRoleForUser(name string, role string, domain ...string) (bool, error) {
	return c.Enforcer.HasRoleForUser(name, role, domain...)
}

// AddPolicy 添加策略
func (c *Casbin) AddPolicy(params ...interface{}) (bool, error) {
	return c.Enforcer.AddPolicy(params...)
}

// RemovePolicy 删除策略
func (c *Casbin) RemovePolicy(params ...interface{}) (bool, error) {
	return c.Enforcer.RemovePolicy(params...)
}

// AddNamedPolicy 添加命名策略
func (c *Casbin) AddNamedPolicy(ptype string, params ...interface{}) (bool, error) {
	return c.Enforcer.AddNamedPolicy(ptype, params...)
}

// RemoveNamedPolicy 删除命名策略
func (c *Casbin) RemoveNamedPolicy(ptype string, params ...interface{}) (bool, error) {
	return c.Enforcer.RemoveNamedPolicy(ptype, params...)
}

// GetEnforcer 获取原始enforcer
func (c *Casbin) GetEnforcer() *casbin.SyncedCachedEnforcer {
	return c.Enforcer
}
