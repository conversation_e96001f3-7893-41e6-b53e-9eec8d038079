package logger

import (
	"fmt"
	"gin/internal/infrastructure/config"
	"os"
	"path/filepath"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var Log *zap.Logger

type Logger struct {
	*zap.Logger
}

func InitLogger(cfg *config.LoggerConfig) error {
	// 创建日志目录
	if err := os.MkdirAll(filepath.Dir(cfg.Filename), os.ModePerm); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 获取日志级别
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建编码器
	encoder := createEncoder(cfg.Format)

	// 定义输出目的地
	var writeSyncer zapcore.WriteSyncer
	if cfg.Console {
		// 输出到控制台
		writeSyncer = zapcore.AddSync(os.Stdout)
	} else {
		// 输出到文件
		writeSyncer = zapcore.AddSync(&lumberjack.Logger{
			Filename:   cfg.Filename,
			MaxSize:    cfg.MaxSize,
			MaxAge:     cfg.MaxAge,
			MaxBackups: cfg.MaxBackups,
			Compress:   cfg.Compress,
		})
	}

	// 创建核心
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// 创建 Logger
	options := []zap.Option{zap.AddCaller()}
	if cfg.ShowLine {
		options = append(options, zap.AddCallerSkip(1))
	}
	Log = zap.New(core, options...)

	// 替换全局 logger
	zap.ReplaceGlobals(Log)

	return nil
}

// createEncoder 创建编码器
func createEncoder(format string) zapcore.Encoder {
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.FullCallerEncoder,
	}

	if format == "json" {
		return zapcore.NewJSONEncoder(encoderConfig)
	}
	return zapcore.NewConsoleEncoder(encoderConfig)
}

// Debug 提供便捷的日志方法
func Debug(msg string, fields ...zap.Field) {
	Log.Debug(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	Log.Info(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	Log.Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	Log.Error(msg, fields...)
}

// Sync 确保所有日志都已写入
func Sync() error {
	return Log.Sync()
}
