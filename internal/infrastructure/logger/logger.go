package logger

import (
	"fmt"
	"gin/internal/infrastructure/config"
	"os"
	"path/filepath"
	"time"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Sync() error
}

// LoggerImpl 日志实现
type LoggerImpl struct {
	*zap.Logger
}

// NewLogger 创建新的日志实例
func NewLogger(cfg *config.LoggerConfig) (Logger, error) {
	// 创建日志目录
	if err := os.MkdirAll(filepath.Dir(cfg.File.Filename), os.ModePerm); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 获取日志级别
	var level zapcore.Level
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建编码器
	encoder := createEncoder(cfg)

	// 定义输出目的地
	writeSyncer := createWriteSyncer(cfg)

	// 创建核心
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// 创建 Logger
	options := []zap.Option{zap.AddCaller()}
	if cfg.AddSource {
		options = append(options, zap.AddCallerSkip(cfg.CallerSkip))
	}

	logger := zap.New(core, options...)

	return &LoggerImpl{Logger: logger}, nil
}

// createEncoder 创建编码器
func createEncoder(cfg *config.LoggerConfig) zapcore.Encoder {
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.FullCallerEncoder,
	}

	if cfg.Format == "json" {
		return zapcore.NewJSONEncoder(encoderConfig)
	}

	// 为控制台输出配置彩色编码器
	encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	encoderConfig.EncodeTime = func(t time.Time, encoder zapcore.PrimitiveArrayEncoder) {
		encoder.AppendString(t.Format(cfg.TimeFormat))
	}

	return zapcore.NewConsoleEncoder(encoderConfig)
}

// createWriteSyncer 创建输出同步器
func createWriteSyncer(cfg *config.LoggerConfig) zapcore.WriteSyncer {
	var writers []zapcore.WriteSyncer

	// 根据输出配置添加不同的输出目标
	switch cfg.Output {
	case "stdout":
		writers = append(writers, zapcore.AddSync(os.Stdout))
	case "file":
		writers = append(writers, zapcore.AddSync(&lumberjack.Logger{
			Filename:   cfg.File.Filename,
			MaxSize:    cfg.File.MaxSize,
			MaxAge:     cfg.File.MaxAge,
			MaxBackups: cfg.File.MaxBackups,
			Compress:   cfg.File.Compress,
			LocalTime:  cfg.File.LocalTime,
		}))
	case "both":
		writers = append(writers, zapcore.AddSync(os.Stdout))
		writers = append(writers, zapcore.AddSync(&lumberjack.Logger{
			Filename:   cfg.File.Filename,
			MaxSize:    cfg.File.MaxSize,
			MaxAge:     cfg.File.MaxAge,
			MaxBackups: cfg.File.MaxBackups,
			Compress:   cfg.File.Compress,
			LocalTime:  cfg.File.LocalTime,
		}))
	default:
		writers = append(writers, zapcore.AddSync(os.Stdout))
	}

	return zapcore.NewMultiWriteSyncer(writers...)
}

// Debug 实现 Logger 接口
func (l *LoggerImpl) Debug(msg string, fields ...zap.Field) {
	l.Logger.Debug(msg, fields...)
}

// Info 实现 Logger 接口
func (l *LoggerImpl) Info(msg string, fields ...zap.Field) {
	l.Logger.Info(msg, fields...)
}

// Warn 实现 Logger 接口
func (l *LoggerImpl) Warn(msg string, fields ...zap.Field) {
	l.Logger.Warn(msg, fields...)
}

// Error 实现 Logger 接口
func (l *LoggerImpl) Error(msg string, fields ...zap.Field) {
	l.Logger.Error(msg, fields...)
}

// Sync 实现 Logger 接口
func (l *LoggerImpl) Sync() error {
	return l.Logger.Sync()
}
