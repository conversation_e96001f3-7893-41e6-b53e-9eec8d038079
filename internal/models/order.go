package models

import (
	"time"
)

const (
	OrderStatusPending   int8 = 1 // 待支付
	OrderStatusCompleted int8 = 2 // 已完成
	OrderStatusCanceled  int8 = 3 // 已取消
	OrderStatusExpired   int8 = 4 // 已过期
	OrderStatusRefunded  int8 = 5 // 已退款
	OrderStatusClosed    int8 = 6 // 已关闭
)

// Order 订单
type Order struct {
	BaseModel

	// 用户ID
	UserID uint `gorm:"not null;index;comment:用户ID" json:"user_id"`
	// 用户
	User *User `gorm:"foreignKey:UserID;references:ID" json:"user"`
	// 分类ID
	CategoryID uint `gorm:"not null;index;comment:分类ID" json:"category_id"`
	// 分类
	Category *Category `gorm:"foreignKey:CategoryID;references:ID" json:"category"`
	// 产品ID
	ProductID uint `gorm:"not null;index;comment:产品ID" json:"product_id"`
	// 产品
	Product *Product `gorm:"foreignKey:ProductID;references:ID" json:"product"`
	// 订单编号
	OrderSn string `gorm:"type:varchar(64);not null;uniqueIndex;comment:订单编号" json:"order_sn"`
	// 金额
	Amount float64 `gorm:"type:decimal(18,4) not null;comment:金额" json:"amount"`
	// 实际金额
	RealAmount float64 `gorm:"type:decimal(18,4) not null;comment:实际金额" json:"real_amount"`
	// 数量
	Nums int `gorm:"not null;default:0;comment:数量" json:"nums"`
	// 类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:商品订单)" json:"type"`
	// 状态
	Status int8 `gorm:"not null;default:1;index;comment:订单状态(1:待支付,2:已完成,3:已取消,4:已过期,5:已退款,6:已关闭)" json:"status"`
	// 数据
	Data string `gorm:"type:text;comment:数据" json:"data"`
	// 过期时间
	ExpiredAt time.Time `gorm:"index;comment:过期时间" json:"expired_at"`
}
