package models

import "time"

// Type 字典类型表，用于管理系统中的字典分类信息
type Type struct {
	ID        uint64     `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	Name      string     `gorm:"column:name;type:varchar(100);not null;comment:字典名称" json:"name"`
	Code      string     `gorm:"column:code;type:varchar(100);not null;uniqueIndex;comment:字典编码" json:"code"`
	Status    int8       `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1-启用 0-禁用" json:"status"`
	Remark    string     `gorm:"column:remark;type:varchar(500);comment:备注说明" json:"remark"`
	CreatedAt time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

func (Type) TableName() string {
	return "sys_dict_types"
}
