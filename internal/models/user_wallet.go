package models

const (
	UserWalletTypeDeposit    int8 = 1 // 充值
	UserWalletTypeWithdrawal int8 = 2 // 提现

	UserWalletStatusPending int8 = 1 // 待处理
	UserWalletStatusSuccess int8 = 2 // 已完成
	UserWalletStatusCancel  int8 = 3 // 已取消
	UserWalletStatusFailed  int8 = 4 // 已失败
)

// UserWallet 用户钱包
type UserWallet struct {
	BaseModel

	// 用户ID
	UserID uint `gorm:"not null;index;comment:用户ID" json:"user_id"`
	// 用户信息
	User *User `gorm:"foreignKey:UserID;references:ID" json:"user"`
	// 资产ID
	AssetID uint `gorm:"not null;index;comment:资产ID" json:"asset_id"`
	// 资产信息
	Asset *Asset `gorm:"foreignKey:AssetID;references:ID" json:"asset"`
	// 来源ID
	SourceID uint `gorm:"not null;index;comment:来源ID" json:"source_id"`
	// 类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:充值,2:提现)" json:"type"`
	// 订单编号
	OrderSN string `gorm:"type:varchar(255) not null;uniqueIndex;comment:订单编号" json:"order_sn"`
	// 金额
	Amount float64 `gorm:"type:decimal(18,4) not null;comment:金额" json:"amount"`
	// 到账金额
	ArrivalAmount float64 `gorm:"type:decimal(18,4) not null;default:0;comment:到账金额" json:"arrival_amount"`
	// 固定手续费
	FixedFee float64 `gorm:"type:decimal(18,4) not null;default:0;comment:固定手续费" json:"fixed_fee"`
	// 手续费(%)
	RateFee float64 `gorm:"type:decimal(8,3) not null;default:0;comment:手续费(%)" json:"rate_fee"`
	// 状态
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:待处理,2:已完成,3:已取消,4:已失败)" json:"status"`
	// 凭证
	Proof string `gorm:"type:varchar(255);comment:凭证" json:"proof"`
	// 原因
	Reason string `gorm:"type:varchar(1024);comment:原因" json:"reason"`
	// 数据
	Data string `gorm:"type:text;comment:数据" json:"data"`
}
