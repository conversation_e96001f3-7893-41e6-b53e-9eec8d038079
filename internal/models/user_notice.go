package models

import (
	"gin/internal/constant"
	"time"
)

// Redis缓存key前缀
const (
	UserNoticeTypeSystem   int8 = 1 // 系统通知
	UserNoticeTypeOrder    int8 = 2 // 订单通知
	UserNoticeTypeAccount  int8 = 3 // 账户通知
	UserNoticeTypeActivity int8 = 4 // 活动通知
	UserNoticeTypeMessage  int8 = 5 // 消息通知
	UserNoticeTypeSecurity int8 = 6 // 安全通知

	UserNoticeStatusNormal  int8 = 1 // 正常
	UserNoticeStatusPinned  int8 = 2 // 置顶
	UserNoticeStatusHidden  int8 = 3 // 隐藏
	UserNoticeStatusExpired int8 = 4 // 失效

	UserNoticePriorityLow    int8 = 1 // 低优先级
	UserNoticePriorityNormal int8 = 2 // 普通优先级
	UserNoticePriorityHigh   int8 = 3 // 高优先级
	UserNoticePriorityUrgent int8 = 4 // 紧急优先级

	UserNotificationUnreadCountPrefix = "user:notification:unread_count:%d" // 用户未读消息数量
	UserNotificationListPrefix        = "user:notification:list:%d"         // 用户通知列表缓存
)

// UserNotice 用户通知
type UserNotice struct {
	BaseModel
	SenderID   uint       `gorm:"index;not null;comment:发送者ID(0:系统)" json:"sender_id"`
	ReceiverID uint       `gorm:"index;not null;comment:接收者ID" json:"receiver_id"`
	Receiver   *User      `gorm:"foreignKey:ReceiverID" json:"receiver,omitempty"`
	Title      string     `gorm:"type:varchar(255) not null;comment:通知标题" json:"title"`
	Summary    string     `gorm:"type:varchar(500);comment:通知摘要" json:"summary"`
	Content    string     `gorm:"type:text;not null;comment:内容" json:"content"`
	Priority   int8       `gorm:"not null;default:2;comment:优先级(1:低,2:普通,3:高,4:紧急)" json:"priority"`
	Status     int8       `gorm:"not null;default:1;index:idx_status;comment:状态(1:正常,2:置顶,3:隐藏,4:失效)" json:"status"`
	ReadAt     *time.Time `gorm:"comment:阅读时间" json:"read_at,omitempty"`
	Type       int8       `gorm:"not null;default:1;index:idx_type;comment:类型(1:系统,2:订单,3:账户,4:活动,5:消息,6:安全)" json:"type"`
	Route      string     `gorm:"type:varchar(255);comment:路由" json:"route"`
	IsRead     bool       `gorm:"default:false;index:idx_is_read;comment:是否已读" json:"is_read"`
}

// TypeOptions 实现 OptionsProvider 接口
func (u *UserNotice) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "系统通知", Value: UserNoticeTypeSystem, Code: "SYSTEM"},
		{Label: "订单通知", Value: UserNoticeTypeOrder, Code: "ORDER"},
		{Label: "账户通知", Value: UserNoticeTypeAccount, Code: "ACCOUNT"},
		{Label: "活动通知", Value: UserNoticeTypeActivity, Code: "ACTIVITY"},
		{Label: "消息通知", Value: UserNoticeTypeMessage, Code: "MESSAGE"},
		{Label: "安全通知", Value: UserNoticeTypeSecurity, Code: "SECURITY"},
	}
}

// StatusOptions 实现 OptionsProvider 接口
func (u *UserNotice) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "正常", Value: UserNoticeStatusNormal, Code: "NORMAL"},
		{Label: "置顶", Value: UserNoticeStatusPinned, Code: "PINNED"},
		{Label: "隐藏", Value: UserNoticeStatusHidden, Code: "HIDDEN"},
		{Label: "失效", Value: UserNoticeStatusExpired, Code: "EXPIRED"},
	}
}

// Options 实现 OptionsProvider 接口
func (u *UserNotice) Options() []constant.Option {
	return []constant.Option{
		{Label: "低优先级", Value: UserNoticePriorityLow, Code: "LOW"},
		{Label: "普通优先级", Value: UserNoticePriorityNormal, Code: "NORMAL"},
		{Label: "高优先级", Value: UserNoticePriorityHigh, Code: "HIGH"},
		{Label: "紧急优先级", Value: UserNoticePriorityUrgent, Code: "URGENT"},
	}
}
