package models

import (
	"fmt"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	FrontMenuTypeTabbar     int8 = 1 // 导航菜单
	FrontMenuTypeUsers      int8 = 2 // 用户菜单
	FrontMenuTypeUsersQuick int8 = 3 // 用户快捷菜单
	FrontMenuTypeHomeQuick  int8 = 4 // 首页快捷菜单

	FrontMenuModeMobile  int8 = 1 // 移动端
	FrontMenuModeDesktop int8 = 2 // 电脑端

	FrontMenuStatusEnabled  int8 = 1 // 启用
	FrontMenuStatusDisabled int8 = 0 // 禁用

	FrontMenuNameTranslateRedisKeyPrefix     = "front:menu:name:translate:%s"     // 菜单名称翻译前缀
	FrontMenuSubtitleTranslateRedisKeyPrefix = "front:menu:subtitle:translate:%s" // 菜单副标题翻译前缀
)

// FrontMenu 前台菜单
type FrontMenu struct {
	BaseModel

	// 父级ID
	ParentID uint `gorm:"index;comment:父级ID;default:NULL" json:"parent_id"`
	// 名称
	NameField string `gorm:"type:varchar(255) not null;comment:名称" json:"name_field"`
	// 副标题
	SubtitleField string `gorm:"type:varchar(255) not null;comment:副标题" json:"subtitle_field"`
	// 图标
	Icon string `gorm:"type:varchar(255) not null;comment:图标" json:"icon"`
	// 激活图标
	ActiveIcon string `gorm:"type:varchar(255) not null;comment:激活图标" json:"active_icon"`
	// 路由
	Route string `gorm:"type:varchar(255) not null;comment:路由" json:"route"`
	// 排序
	Sort int8 `gorm:"not null;default:99;index;comment:排序" json:"sort"`
	// 模式
	Mode int8 `gorm:"not null;default:1;index;comment:模式(1:移动端,2:电脑端)" json:"mode"`
	// 类型(1:导航)
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:导航菜单,2:用户菜单,3:用户快捷菜单,4:首页快捷菜单)" json:"type"`
	// 状态(1:启用,0:禁用)
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`
	// 子菜单
	Children []*FrontMenu `gorm:"foreignKey:ParentID" json:"children"`

	// 非数据库字段
	Name     string `gorm:"-" json:"name"`     // 名称（当前语言）
	Subtitle string `gorm:"-" json:"subtitle"` // 副标题（当前语言）
}

// BeforeCreate 创建前钩子
func (m *FrontMenu) BeforeCreate(tx *gorm.DB) error {
	uid := uuid.New().String()
	m.NameField = fmt.Sprintf(FrontMenuNameTranslateRedisKeyPrefix, uid)
	m.SubtitleField = fmt.Sprintf(FrontMenuSubtitleTranslateRedisKeyPrefix, uid)
	return nil
}

// AfterCreate 创建后处理
func (m *FrontMenu) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:   "菜单名称翻译",
			Lang:   LanguageDefault,
			Key:    m.NameField,
			Value:  m.Name,
			Type:   TranslationTypeSystem,
			Status: TranslationStatusEnabled,
		},
		{
			Name:   "菜单副标题翻译",
			Lang:   LanguageDefault,
			Key:    m.SubtitleField,
			Value:  m.Subtitle,
			Type:   TranslationTypeSystem,
			Status: TranslationStatusEnabled,
		},
	}
	if err := tx.Create(&translates).Error; err != nil {
		return err
	}
	return nil
}

// Translate 查询后处理
func (m *FrontMenu) Translate(tx *gorm.DB, locale string) error {
	// 加载名称翻译
	if m.NameField != "" {
		var nameTranslation Translation
		if err := tx.Where("`key` = ? AND `lang` = ?", locale, m.NameField).Find(&nameTranslation).Error; err == nil {
			m.Name = nameTranslation.Value
		}
	}

	// 加载副标题翻译
	if m.SubtitleField != "" {
		var subtitleTranslation Translation
		if err := tx.Where("`key` = ? AND `lang` = ?", locale, m.SubtitleField).Find(&subtitleTranslation).Error; err == nil {
			m.Subtitle = subtitleTranslation.Value
		}
	}

	// 加载子菜单
	for _, v := range m.Children {
		if err := v.Translate(tx, locale); err != nil {
			return err
		}
	}

	return nil
}
