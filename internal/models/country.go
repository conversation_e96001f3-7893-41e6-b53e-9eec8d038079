package models

import "gin/internal/constant"

const (
	CountryStatusEnabled  int8 = 1 // 启用
	CountryStatusDisabled int8 = 0 // 禁用
)

// Country 国家表
type Country struct {
	BaseModel
	NameZh     string `gorm:"type:varchar(100) not null;comment:中文名称" json:"name_zh"`
	NameNative string `gorm:"type:varchar(100) not null;comment:本地语言名称" json:"name_native"`
	Icon       string `gorm:"type:varchar(255);not null;comment:国旗图标URL" json:"icon"`
	ISO2       string `gorm:"type:char(2);not null;uniqueIndex;comment:ISO 3166-1 alpha-2代码" json:"iso2"`
	Code       string `gorm:"type:varchar(10);not null;comment:国家区号" json:"code"`
	Sort       int    `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	Status     int8   `gorm:"type:tinyint;not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`
}

// StatusOptions 实现 OptionsProvider 接口
func (c *Country) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: CountryStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: CountryStatusDisabled, Code: "DISABLED"},
	}
}
