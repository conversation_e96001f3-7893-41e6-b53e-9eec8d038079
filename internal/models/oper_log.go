package models

import (
	"gin/internal/utils"
	"time"
)

// OperateLog 系统操作日志
type OperateLog struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID    uint64    `gorm:"index;comment:租户ID" json:"tenantId"`
	UserID      uint64    `gorm:"index;comment:用户ID" json:"userId"`
	Module      string    `gorm:"type:varchar(50);index;comment:操作模块" json:"module"`
	Type        int8      `gorm:"type:tinyint;comment:操作类型(1:新增,2:删除,3:修改,4:查询)" json:"type"`
	Description string    `gorm:"type:varchar(255);comment:操作描述" json:"description"`
	ReqPath     string    `gorm:"type:varchar(255);comment:请求路径" json:"reqPath"`
	ReqParams   string    `gorm:"type:text;comment:请求参数" json:"reqParams"`
	ResResult   string    `gorm:"type:text;comment:返回结果" json:"resResult"`
	Status      int8      `gorm:"type:tinyint;default:10;comment:操作状态(-1:失败,10:成功)" json:"status"`
	ErrorMsg    string    `gorm:"type:varchar(500);comment:错误信息" json:"errorMsg"`
	IP          string    `gorm:"type:varchar(45);comment:操作IP" json:"ip"`
	Location    string    `gorm:"type:varchar(255);comment:操作地点" json:"location"`
	UserAgent   string    `gorm:"type:varchar(500);comment:用户代理" json:"userAgent"`
	CreatedAt   time.Time `gorm:"autoCreateTime(3);comment:创建时间" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime(3);comment:更新时间" json:"updatedAt"`
}

// NewOperateLogLog 创建操作日志记录
func NewOperateLogLog(userID uint64, tenantID uint64, module string, operateType int8, description, reqPath, ip, userAgent string, status int8, errorMsg string) *OperateLog {

	return &OperateLog{
		UserID:      userID,
		TenantID:    tenantID,
		Module:      module,
		Type:        operateType,
		Description: description,
		ReqPath:     reqPath,
		IP:          ip,
		Status:      status,
		ErrorMsg:    errorMsg,
		Location:    utils.ResolveIPRegion(ip),
		UserAgent:   userAgent,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}
