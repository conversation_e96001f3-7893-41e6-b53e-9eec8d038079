package models

const (
	UserCertificationModeIdentity int8 = 1 // 身份认证

	UserCertificationTypeIdentity int8 = 1 // 身份证

	UserCertificationStatusPending   int8 = 1 // 待审核
	UserCertificationStatusCompleted int8 = 2 // 已通过
	UserCertificationStatusRejected  int8 = 3 // 已拒绝
)

// UserCertification 用户认证
type UserCertification struct {
	BaseModel

	// 用户ID
	UserID uint `gorm:"not null;index;comment:用户ID" json:"user_id"`
	// 用户信息
	User *User `gorm:"foreignKey:UserID;references:ID" json:"user"`
	// 模式
	Mode int8 `gorm:"not null;default:1;comment:模式(1:身份认证)" json:"mode"`
	// 证件姓名
	RealName string `gorm:"type:varchar(50) not null;comment:证件姓名" json:"real_name"`
	// 证件号码
	IDNumber string `gorm:"type:varchar(50) not null;uniqueIndex;comment:证件号码" json:"id_number"`
	// 证件照正面
	Photo1 string `gorm:"type:varchar(255) not null;comment:证件照正面" json:"photo1"`
	// 证件照反面
	Photo2 string `gorm:"type:varchar(255) not null;comment:证件照反面" json:"photo2"`
	// 手持证件照
	Photo3 string `gorm:"type:varchar(255);comment:手持证件照" json:"photo3"`
	// 详细地址
	Address string `gorm:"type:varchar(255);comment:详细地址" json:"address"`
	// 类型
	Type int8 `gorm:"not null;default:1;comment:类型(1:身份证)" json:"type"`
	// 状态
	Status int8 `gorm:"not null;default:1;comment:状态(1:待审核 2:已通过 3:已拒绝)" json:"status"`
	// 拒绝原因
	Reason string `gorm:"type:varchar(1024);comment:拒绝原因" json:"reason"`
}
