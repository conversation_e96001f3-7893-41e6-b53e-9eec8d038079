package models

import (
	"gin/internal/constant"
	"gin/pkg/net"
)

const (
	AccessTypeFrontend int8 = 1 // 前台访问
	AccessTypeBackend  int8 = 2 // 后台访问

	LoginStatusFailed  int8 = 0 // 登录失败
	LoginStatusSuccess int8 = 1 // 登录成功
)

// LoginLog 系统登录记录
type LoginLog struct {
	BaseModel
	UserID        uint   `gorm:"type:int unsigned;index:idx_user_login;comment:用户ID" json:"userId"`
	ManagerID     uint   `gorm:"type:int unsigned;index:idx_manager_login;comment:管理员ID" json:"managerId"`
	Username      string `gorm:"type:varchar(50);index:idx_username;comment:用户名" json:"username"`
	IP            string `gorm:"type:varchar(45);index:idx_ip;comment:登录IP" json:"ip"`
	Status        int8   `gorm:"type:tinyint;default:0;index:idx_status;comment:登录状态(1:成功,0:失败)" json:"status"`
	Message       string `gorm:"type:varchar(255);comment:登录信息" json:"message"`
	Type          int8   `gorm:"type:tinyint;default:1;comment:访问类型(1:前台,2:后台)" json:"type"`
	OS            string `gorm:"type:varchar(50);comment:操作系统" json:"os"`
	OSVer         string `gorm:"type:varchar(20);comment:操作系统版本" json:"osVer"`
	Device        string `gorm:"type:varchar(20);comment:设备类型(例如：Desktop,Mobile,Tablet)" json:"device"`
	LoginLocation string `gorm:"type:varchar(255);comment:登录地点" json:"loginLocation"`
	Browser       string `gorm:"type:varchar(50);comment:浏览器类型" json:"browser"`
	BrowserVer    string `gorm:"type:varchar(20);comment:浏览器版本" json:"browserVer"`
	UserAgent     string `gorm:"type:varchar(500);comment:原始User-Agent" json:"userAgent"`
	IsBot         bool   `gorm:"type:tinyint(1);default:0;comment:是否爬虫" json:"isBot"`
}

// NewUserLoginLog 创建普通用户登录日志
func NewUserLoginLog(userId uint, username, ip, userAgent string, status int8, message string) *LoginLog {
	// 解析UserAgent
	uaInfo := net.ParseUserAgent(userAgent)

	return &LoginLog{
		UserID:        userId,
		ManagerID:     0, // 普通用户登录时ManagerID为0
		Username:      username,
		IP:            ip,
		Status:        status,
		Message:       message,
		Type:          AccessTypeFrontend,
		OS:            uaInfo.OS,
		OSVer:         uaInfo.OSVer,
		Device:        uaInfo.Device,
		LoginLocation: net.ResolveIPRegion(ip),
		Browser:       uaInfo.Browser,
		BrowserVer:    uaInfo.BrowserVer,
		UserAgent:     userAgent,
		IsBot:         uaInfo.IsBot,
	}
}

// NewManagerLoginLog 创建管理员登录日志
func NewManagerLoginLog(managerId uint, username, ip, userAgent string, status int8, message string) *LoginLog {
	// 解析UserAgent
	uaInfo := net.ParseUserAgent(userAgent)

	return &LoginLog{
		UserID:        0, // 管理员登录时UserID为0
		ManagerID:     managerId,
		Username:      username,
		IP:            ip,
		Status:        status,
		Message:       message,
		Type:          AccessTypeBackend,
		OS:            uaInfo.OS,
		OSVer:         uaInfo.OSVer,
		Device:        uaInfo.Device,
		LoginLocation: net.ResolveIPRegion(ip),
		Browser:       uaInfo.Browser,
		BrowserVer:    uaInfo.BrowserVer,
		UserAgent:     userAgent,
		IsBot:         uaInfo.IsBot,
	}
}

// TypeOptions 实现 OptionsProvider 接口
func (l *LoginLog) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "前台访问", Value: AccessTypeFrontend, Code: "FRONTEND"},
		{Label: "后台访问", Value: AccessTypeBackend, Code: "BACKEND"},
	}
}

// StatusOptions 实现 OptionsProvider 接口
func (l *LoginLog) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "登录成功", Value: LoginStatusSuccess, Code: "SUCCESS"},
		{Label: "登录失败", Value: LoginStatusFailed, Code: "FAILED"},
	}
}
