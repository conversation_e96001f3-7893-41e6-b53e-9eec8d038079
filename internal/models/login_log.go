package models

import (
	"gin/internal/utils"
	"time"
)

// LoginLog 系统登录记录
type LoginLog struct {
	ID            uint64    `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID      uint64    `gorm:"index;comment:租户ID" json:"tenantId"`
	UserID        uint64    `gorm:"type:int unsigned;comment:用户ID" json:"userId"`
	Username      string    `gorm:"type:varchar(50);comment:用户名" json:"username"`
	IP            string    `gorm:"type:varchar(45);comment:登录IP" json:"ip"`
	LoginTime     time.Time `gorm:"type:datetime;comment:登录时间" json:"loginTime"`
	Status        int8      `gorm:"type:tinyint;default:10;comment:登录状态(-1:失败,10:成功)" json:"status"`
	Message       string    `gorm:"type:varchar(255);comment:登录信息" json:"message"`
	AccessType    int8      `gorm:"type:tinyint;default:1;comment:访问类型(1:前台,2:后台)" json:"accessType"`
	OS            string    `gorm:"type:varchar(50);comment:操作系统" json:"os"`
	OSVer         string    `gorm:"type:varchar(20);comment:操作系统版本" json:"osVer"`
	Device        string    `gorm:"type:varchar(20);comment:设备类型(Desktop,Mobile,Tablet)" json:"device"`
	LoginLocation string    `gorm:"type:varchar(255);comment:登录地点" json:"loginLocation"`
	Browser       string    `gorm:"type:varchar(50);comment:浏览器类型" json:"browser"`
	BrowserVer    string    `gorm:"type:varchar(20);comment:浏览器版本" json:"browserVer"`
	UserAgent     string    `gorm:"type:varchar(500);comment:原始User-Agent" json:"userAgent"`
	IsBot         bool      `gorm:"type:tinyint(1);default:0;comment:是否爬虫" json:"isBot"`
	CreatedAt     time.Time `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt     time.Time `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP" json:"updatedAt"`
}

// NewLoginLog 创建登录日志记录
func NewLoginLog(userID uint64, tenantID uint64, username, ip, userAgent string, status int8, message string, accessType int8) *LoginLog {
	// 解析UserAgent
	uaInfo := utils.ParseUserAgent(userAgent)

	return &LoginLog{
		UserID:        userID,
		TenantID:      tenantID,
		Username:      username,
		IP:            ip,
		LoginTime:     time.Now(),
		Status:        status,
		Message:       message,
		AccessType:    accessType,
		OS:            uaInfo.OS,
		OSVer:         uaInfo.OSVer,
		Device:        uaInfo.Device,
		LoginLocation: utils.ResolveIPRegion(ip),
		Browser:       uaInfo.Browser,
		BrowserVer:    uaInfo.BrowserVer,
		UserAgent:     userAgent,
		IsBot:         uaInfo.IsBot,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}
