package models

const (
	UserAccountStatusEnabled  int8 = 1 // 启用
	UserAccountStatusDisabled int8 = 0 // 禁用
)

// UserAccount 用户账户
type UserAccount struct {
	BaseModel
	// 管理ID
	ManagerID uint `gorm:"index;not null;comment:'管理ID'" json:"manager_id"`
	// 管理信息
	Manager *Manager `gorm:"foreignKey:ManagerID;references:ID" json:"manager"`
	// 用户ID
	UserID uint `gorm:"not null;comment:用户ID" json:"user_id"`
	// 用户信息
	User *User `gorm:"foreignKey:UserID;references:ID" json:"user"`
	// 资产ID
	AssetID uint `gorm:"not null;comment:资产ID" json:"asset_id"`
	// 资产信息
	Asset *Asset `gorm:"foreignKey:AssetID;references:ID" json:"asset"`
	// 支付ID
	PaymentID uint `gorm:"not null;comment:支付ID" json:"payment_id"`
	// 支付信息
	Payment *Payment `gorm:"foreignKey:PaymentID;references:ID" json:"payment"`
	// 名称
	Name string `gorm:"type:varchar(255) not null;comment:名称" json:"name"`
	// 类型(1:银行卡,2:加密货币,3:三方支付) - 同步 payment中的类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:银行卡,2:加密货币)" json:"type"`
	// 状态(1:启用,0:禁用)
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`
	// 数据
	Data string `gorm:"type:text;comment:数据" json:"data"`
}
