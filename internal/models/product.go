package models

import (
	"database/sql/driver"
	"fmt"
	"github.com/goccy/go-json"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// StringArray 自定义字符串数组类型
type StringArray []string

// Value 实现 driver.Valuer 接口
func (sa StringArray) Value() (driver.Value, error) {
	if sa == nil {
		return "[]", nil
	}
	return json.Marshal(sa)
}

// Scan 实现 sql.Scanner 接口
func (sa *StringArray) Scan(value interface{}) error {
	return json.Unmarshal(value.([]byte), sa)
}

const (
	ProductStatusEnabled  int8 = 1 // 启用
	ProductStatusDisabled int8 = 0 // 禁用

	ProductNameTranslateRedisKeyPrefix        = "product:name:translate:%s"        // 产品名称翻译前缀
	ProductSubtitleTranslateRedisKeyPrefix    = "product:subtitle:translate:%s"    // 产品副标题翻译前缀
	ProductDescriptionTranslateRedisKeyPrefix = "product:description:translate:%s" // 产品描述翻译前缀
)

// Product 产品
type Product struct {
	BaseModel

	// 类目ID
	CategoryID uint `gorm:"not null;index;comment:类目ID" json:"category_id"`
	// 类目
	Category *Category `gorm:"foreignKey:CategoryID;references:ID;comment:类目" json:"category"`
	// 名称
	NameField string `gorm:"type:varchar(255) not null;index;comment:名称" json:"name_field"`
	// 副标题
	SubtitleField string `gorm:"type:varchar(255);comment:副标题" json:"subtitle_field"`
	// 图标
	Icon string `gorm:"type:varchar(255);comment:图标" json:"icon"`
	// 图片组
	Images StringArray `gorm:"type:json;comment:图片组" json:"images"`
	// 价格
	Amount float64 `gorm:"decimal(18,4) not null;default:0;comment:价格" json:"amount"`
	// 折扣
	Discount float64 `gorm:"decimal(8,3) not null;default:0;comment:折扣" json:"discount"`
	// 类型
	Type int8 `gorm:"type:tinyint not null;default:1;index;comment:类型(1:商品类型)" json:"type"`
	// 排序
	Sort int8 `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	// 状态
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:启用,2:禁用)" json:"status"`
	// 翻译描述字段
	DescriptionField string `gorm:"type:varchar(255);comment:翻译描述字段" json:"description_field"`

	// 非数据库字段
	Name        string `gorm:"-" json:"name"`        // 名称（当前语言）
	Subtitle    string `gorm:"-" json:"subtitle"`    // 副标题（当前语言）
	Description string `gorm:"-" json:"description"` // 描述（当前语言）
}

// Validate 验证
func (p *Product) Validate() error {
	return nil
}

// BeforeCreate 创建前钩子
func (p *Product) BeforeCreate(tx *gorm.DB) error {
	uid := uuid.New().String()
	p.NameField = fmt.Sprintf(ProductNameTranslateRedisKeyPrefix, uid)
	p.SubtitleField = fmt.Sprintf(ProductSubtitleTranslateRedisKeyPrefix, uid)
	p.DescriptionField = fmt.Sprintf(ProductDescriptionTranslateRedisKeyPrefix, uid)
	return p.Validate()
}

// AfterCreate 创建后处理
func (p *Product) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:  "产品名称翻译",
			Lang:  LanguageDefault,
			Key:   p.NameField,
			Value: p.Name,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "产品副标题翻译",
			Lang:  LanguageDefault,
			Key:   p.SubtitleField,
			Value: p.Subtitle,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "产品描述翻译",
			Lang:  LanguageDefault,
			Key:   p.DescriptionField,
			Value: p.Description,
			Type:  TranslationTypeSystem,
		},
	}
	if err := tx.Create(&translates).Error; err != nil {
		return err
	}
	return nil
}

// BeforeUpdate 更新前钩子
func (p *Product) BeforeUpdate(tx *gorm.DB) error {
	return p.Validate()
}

// Translate 翻译
func (p *Product) Translate(tx *gorm.DB, locale string) error {
	// 加载名称翻译
	if p.NameField != "" {
		var nameTranslate Translation
		if err := tx.Where("lang = ? AND key = ?", locale, p.NameField).Find(&nameTranslate).Error; err == nil {
			p.Name = nameTranslate.Value
		}
	}

	// 加载副标题翻译
	if p.SubtitleField != "" {
		var subtitleTranslate Translation
		if err := tx.Where("lang = ? AND key = ?", locale, p.SubtitleField).Find(&subtitleTranslate).Error; err == nil {
			p.Subtitle = subtitleTranslate.Value
		}
	}

	// 加载描述翻译
	if p.DescriptionField != "" {
		var descriptionTranslate Translation
		if err := tx.Where("lang = ? AND key = ?", locale, p.DescriptionField).Find(&descriptionTranslate).Error; err == nil {
			p.Description = descriptionTranslate.Value
		}
	}
	return nil
}
