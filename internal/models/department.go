package models

import "time"

// Department 部门模型
type Department struct {
	ID        uint64     `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID  uint64     `gorm:"column:tenant_id;not null;default:0;index;comment:租户ID" json:"tenantId"`
	Name      string     `gorm:"column:name;type:varchar(100);not null;comment:部门名称" json:"name"`
	Code      string     `gorm:"column:code;type:varchar(50);comment:部门编码" json:"code"`
	ParentID  uint64     `gorm:"column:parent_id;default:0;comment:父级ID" json:"parentId"`
	Ancestors string     `gorm:"column:ancestors;type:varchar(255);comment:祖级列表" json:"ancestors"`
	Leader    string     `gorm:"column:leader;type:varchar(50);comment:负责人" json:"leader"`
	Phone     string     `gorm:"column:phone;type:varchar(20);comment:联系电话" json:"phone"`
	Email     string     `gorm:"column:email;type:varchar(100);comment:邮箱" json:"email"`
	Status    int8       `gorm:"column:status;type:tinyint;default:10;comment:状态(10:正常,-1:禁用)" json:"status"`
	Sort      int        `gorm:"column:sort;type:int;default:0;comment:排序" json:"sort"`
	IsSystem  bool       `gorm:"column:is_system;not null;default:false;comment:是否系统内置" json:"isSystem"`
	CreatedBy uint64     `gorm:"column:created_by;comment:创建人" json:"createdBy"`
	UpdatedBy uint64     `gorm:"column:updated_by;comment:更新人" json:"updatedBy"`
	CreatedAt time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

// TableName 指定表名
func (Department) TableName() string {
	return "sys_departments"
}

// UserDepartment 用户-部门关联表
type UserDepartment struct {
	ID           uint64    `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID     uint64    `gorm:"column:tenant_id;not null;default:0;index;comment:租户ID" json:"tenantId"`
	UserID       uint64    `gorm:"column:user_id;not null;index:idx_user_dept;comment:用户ID" json:"userId"`
	DepartmentID uint64    `gorm:"column:department_id;not null;index:idx_user_dept;comment:部门ID" json:"departmentId"`
	IsLeader     bool      `gorm:"column:is_leader;not null;default:false;comment:是否部门负责人" json:"isLeader"`
	IsPrimary    bool      `gorm:"column:is_primary;not null;default:false;comment:是否主部门" json:"isPrimary"`
	CreatedAt    time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"createdAt"`
}

// TableName 指定表名
func (UserDepartment) TableName() string {
	return "sys_user_departments"
}
