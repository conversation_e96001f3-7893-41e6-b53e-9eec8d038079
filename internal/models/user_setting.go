package models

const (
	UserSettingTypeForm = 1 // 表单
)

// UserSetting 用户设置
type UserSetting struct {
	BaseModel

	UserID uint   `gorm:"not null;index;comment:用户ID" json:"user_id"`
	Name   string `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	Type   int8   `gorm:"not null;default:1;comment:类型(1:表单)" json:"type"`
	Field  string `gorm:"type:varchar(60) not null;uniqueIndex;comment:键名" json:"field"`
	Value  string `gorm:"type:text;comment:数据" json:"value"`
	Data   string `gorm:"type:json;comment:配置" json:"data"`
}
