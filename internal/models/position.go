package models

import "time"

// Position 岗位模型
type Position struct {
	ID        uint64     `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID  uint64     `gorm:"column:tenant_id;not null;default:0;index;comment:租户ID" json:"tenantId"`
	Name      string     `gorm:"column:name;type:varchar(100);not null;comment:岗位名称" json:"name"`
	Code      string     `gorm:"column:code;type:varchar(50);not null;uniqueIndex:idx_tenant_code;comment:岗位编码" json:"code"`
	DeptID    uint64     `gorm:"column:dept_id;not null;index;comment:所属部门ID" json:"deptId"`
	Type      int8       `gorm:"column:type;type:tinyint;default:1;comment:岗位类型(1:管理岗,2:业务岗,3:技术岗)" json:"type"`
	Level     int8       `gorm:"column:level;type:tinyint;default:1;comment:岗位级别(1:初级,2:中级,3:高级,4:专家)" json:"level"`
	Status    int8       `gorm:"column:status;type:tinyint;default:10;comment:状态(10:正常,-1:禁用)" json:"status"`
	Sort      int        `gorm:"column:sort;type:int;default:0;comment:排序" json:"sort"`
	Remark    string     `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	IsSystem  bool       `gorm:"column:is_system;not null;default:false;comment:是否系统内置" json:"isSystem"`
	CreatedBy uint64     `gorm:"column:created_by;comment:创建人" json:"createdBy"`
	UpdatedBy uint64     `gorm:"column:updated_by;comment:更新人" json:"updatedBy"`
	CreatedAt time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

// TableName 指定表名
func (Position) TableName() string {
	return "sys_positions"
}

// UserPosition 用户-岗位关联表
type UserPosition struct {
	ID         uint64    `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID   uint64    `gorm:"column:tenant_id;not null;default:0;index;comment:租户ID" json:"tenantId"`
	UserID     uint64    `gorm:"column:user_id;not null;index:idx_user_position;comment:用户ID" json:"userId"`
	PositionID uint64    `gorm:"column:position_id;not null;index:idx_user_position;comment:岗位ID" json:"positionId"`
	IsPrimary  bool      `gorm:"column:is_primary;not null;default:false;comment:是否主岗位" json:"isPrimary"`
	StartDate  time.Time `gorm:"column:start_date;comment:任职开始日期" json:"startDate"`
	EndDate    time.Time `gorm:"column:end_date;comment:任职结束日期" json:"endDate"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt  time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updatedAt"`
}

// TableName 指定表名
func (UserPosition) TableName() string {
	return "sys_user_positions"
}
