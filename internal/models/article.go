package models

import (
	"fmt"
	"gin/internal/constant"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	ArticleTypeNews   int8 = 1 // 新闻资讯
	ArticleTypeHelp   int8 = 2 // 帮助中心
	ArticleTypeNotice int8 = 3 // 公告中心

	ArticleStatusEnabled  int8 = 1 // 启用
	ArticleStatusDisabled int8 = 0 // 禁用

	ArticleTitleTranslateRedisKeyPrefix   = "article:title:translate:%s"   // 文章标题翻译缓存前缀
	ArticleContentTranslateRedisKeyPrefix = "article:content:translate:%s" // 文章内容翻译缓存前缀
)

// Article 文章管理
type Article struct {
	BaseModel
	Icon         string `gorm:"type:varchar(255);comment:图标" json:"icon"`
	TitleField   string `gorm:"type:varchar(255) not null;comment:标题" json:"title_field"`
	ContentField string `gorm:"type:varchar(255);comment:内容" json:"content_field"`
	Route        string `gorm:"type:varchar(255);comment:路由" json:"route"`
	Sort         int    `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	Views        int    `gorm:"not null;default:1000;comment:浏览量" json:"views"`
	Type         int8   `gorm:"not null;default:1;index;comment:类型(1:新闻资讯,2:帮助中心,3:公告中心)" json:"type"`
	IsTop        bool   `gorm:"not null;default:false;comment:是否置顶" json:"is_top"`
	IsHot        bool   `gorm:"not null;default:false;comment:是否热门" json:"is_hot"`
	Status       int8   `gorm:"type:tinyint;not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`

	// 非数据库字段
	Title   string `gorm:"-" json:"title"`   // 标题（当前语言）
	Content string `gorm:"-" json:"content"` // 内容（当前语言）
}

// BeforeCreate 创建前验证
func (a *Article) BeforeCreate(_ *gorm.DB) error {
	// 生成UUID作为标识符
	uid := uuid.New().String()

	// 生成标题和内容的翻译字段名
	a.TitleField = fmt.Sprintf(ArticleTitleTranslateRedisKeyPrefix, uid)
	a.ContentField = fmt.Sprintf(ArticleContentTranslateRedisKeyPrefix, uid)

	return nil
}

// AfterCreate 创建后处理
func (a *Article) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:  "文章标题翻译",
			Lang:  LanguageDefault,
			Key:   a.TitleField,
			Value: a.Title,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "文章内容翻译",
			Lang:  LanguageDefault,
			Key:   a.ContentField,
			Value: a.Content,
			Type:  TranslationTypeSystem,
		},
	}

	return tx.Create(&translates).Error
}

// AfterFind 查询后处理
func (a *Article) AfterFind(tx *gorm.DB) (err error) {
	return a.Translate(tx)
}

// Translate 翻译
func (a *Article) Translate(tx *gorm.DB) error {
	// 加载标题翻译
	if a.TitleField != "" {
		var titleTranslation Translation
		if err := tx.Where("lang = ? AND `key` = ?", LanguageDefault, a.TitleField).Find(&titleTranslation).Error; err == nil {
			a.Title = titleTranslation.Value
		}
	}

	// 加载内容翻译
	if a.ContentField != "" {
		var contentTranslation Translation
		if err := tx.Where("lang = ? AND `key` = ?", LanguageDefault, a.ContentField).Find(&contentTranslation).Error; err == nil {
			a.Content = contentTranslation.Value
		}
	}

	return nil
}

// TypeOptions 实现 OptionsProvider 接口
func (a *Article) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "新闻资讯", Value: ArticleTypeNews, Code: "NEWS"},
		{Label: "帮助中心", Value: ArticleTypeHelp, Code: "HELP"},
		{Label: "公告中心", Value: ArticleTypeNotice, Code: "NOTICE"},
	}
}

// StatusOptions 实现 OptionsProvider 接口
func (a *Article) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: ArticleStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: ArticleStatusDisabled, Code: "DISABLED"},
	}
}
