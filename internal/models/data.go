package models

import (
	"time"

	"gorm.io/gorm"
)

// Data 字典数据表，用于存储具体的字典项数据
type Data struct {
	ID        uint64     `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TypeID    uint64     `gorm:"column:type_id;not null;index;comment:字典类型ID" json:"typeId"`
	Label     string     `gorm:"column:label;type:varchar(100);not null;comment:字典标签" json:"label"`
	Value     string     `gorm:"column:value;type:varchar(100);not null;comment:字典值" json:"value"`
	Code      string     `gorm:"column:code;type:varchar(100);comment:字典编码" json:"code"`
	Sort      int        `gorm:"column:sort;not null;default:0;comment:显示顺序" json:"sort"`
	Status    int8       `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1-启用 0-禁用" json:"status"`
	Remark    string     `gorm:"column:remark;type:varchar(500);comment:备注说明" json:"remark"`
	CreatedAt time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

// TableName 指定表名
func (*Data) TableName() string {
	return "sys_dict_data"
}

// BeforeCreate 添加一个新的唯一索引约束
func (d *Data) BeforeCreate(_ *gorm.DB) error {
	// 如果code为空，则使用value作为code
	if d.Code == "" {
		d.Code = d.Value
	}
	return nil
}
