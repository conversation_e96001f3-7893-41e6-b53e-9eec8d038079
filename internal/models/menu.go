package models

import (
	"time"
)

// ClientType 表示菜单适用的客户端类型
type ClientType int8

const (
	ClientTypeAll     ClientType = 0 // 所有端
	ClientTypeWeb     ClientType = 1 // Web端
	ClientTypeMobile  ClientType = 2 // 移动端
	ClientTypeAdmin   ClientType = 3 // 管理后台
	ClientTypeDesktop ClientType = 4 // 桌面端
	ClientTypeWechat  ClientType = 5 // 微信小程序
)

// FrontendMenu 前台菜单模型
type FrontendMenu struct {
	ID         uint64          `gorm:"column:id;primaryKey;autoIncrement;comment:菜单ID" json:"id"`
	ParentID   uint64          `gorm:"column:parent_id;index;default:0;comment:父菜单ID" json:"parentId"`
	Name       string          `gorm:"column:name;type:varchar(100);not null;comment:菜单名称" json:"name"`
	Path       string          `gorm:"column:path;type:varchar(200);comment:路由路径" json:"path"`
	Component  string          `gorm:"column:component;type:varchar(200);comment:组件路径" json:"component"`
	Redirect   string          `gorm:"column:redirect;type:varchar(200);comment:重定向路径" json:"redirect"`
	Title      string          `gorm:"column:title;type:varchar(100);not null;comment:显示标题" json:"title"`
	Icon       string          `gorm:"column:icon;type:varchar(100);comment:默认图标" json:"icon"`
	ActiveIcon string          `gorm:"column:active_icon;type:varchar(100);comment:激活状态图标" json:"activeIcon"`
	Sort       int             `gorm:"column:sort;type:int;default:0;comment:排序权重" json:"sort"`
	Hidden     bool            `gorm:"column:hidden;type:tinyint(1);default:0;comment:是否隐藏" json:"hidden"`
	KeepAlive  bool            `gorm:"column:keep_alive;type:tinyint(1);default:1;comment:是否缓存" json:"keepAlive"`
	Target     string          `gorm:"column:target;type:varchar(20);default:'_self';comment:打开方式" json:"target"`
	ClientType ClientType      `gorm:"column:client_type;type:tinyint;default:0;comment:客户端类型" json:"clientType"`
	PermCode   string          `gorm:"column:perm_code;type:varchar(100);index;comment:权限标识" json:"permCode"`
	Status     int8            `gorm:"column:status;type:tinyint;default:10;comment:状态 0-禁用 10-启用" json:"status"`
	CreatedAt  time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt  time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt  *time.Time      `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
	Children   []*FrontendMenu `gorm:"-" json:"children"` // 子菜单，不存储到数据库
}

// TableName 表名
func (FrontendMenu) TableName() string {
	return "sys_frontend_menus"
}

// UpdateBasicInfo 更新菜单基础信息
func (m *FrontendMenu) UpdateBasicInfo(name, path, title string) {
	m.Name = name
	m.Path = path
	m.Title = title
	m.UpdatedAt = time.Now()
}

// UpdateVisibility 更新菜单可见性配置
func (m *FrontendMenu) UpdateVisibility(hidden bool, clientType ClientType) {
	m.Hidden = hidden
	m.ClientType = clientType
	m.UpdatedAt = time.Now()
}

// UpdatePermissions 更新菜单权限配置
func (m *FrontendMenu) UpdatePermissions(permCode string, keepAlive bool) {
	m.PermCode = permCode
	m.KeepAlive = keepAlive
	m.UpdatedAt = time.Now()
}

// IsVisible 检查菜单是否在指定客户端可见
func (m *FrontendMenu) IsVisible(clientType ClientType) bool {
	return m.ClientType == ClientTypeAll || m.ClientType == clientType
}
