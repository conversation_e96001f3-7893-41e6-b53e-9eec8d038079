package models

import (
	"database/sql/driver"
	"errors"
	"gin/internal/constant"
	"github.com/goccy/go-json"
)

const (
	MenuStatusDisabled int8 = 0 // 禁用
	MenuStatusEnabled  int8 = 1 // 启用

	MenuTypeDirectory int8 = 1 // 目录
	MenuTypeMenu      int8 = 2 // 菜单
)

// Menu 后台菜单模型
type Menu struct {
	BaseModel
	Name      string   `gorm:"type:varchar(255);not null;comment:菜单名称" json:"name"`
	Status    int8     `gorm:"type:tinyint;default:1;comment:状态(1:启用,0:禁用)" json:"status"`
	Path      string   `gorm:"type:varchar(255);comment:路由路径" json:"path"`
	ParentID  uint     `gorm:"default:null;comment:'父级ID'" json:"parent_id"`
	Sort      int      `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	Type      int8     `gorm:"type:tinyint;comment:类型(1:目录,2:菜单)" json:"type"`
	Component string   `gorm:"type:varchar(255);comment:组件路径" json:"component"`
	Redirect  string   `gorm:"type:varchar(255);comment:重定向路径" json:"redirect"`
	Code      string   `gorm:"type:varchar(100);not null;uniqueIndex;comment:菜单编码" json:"code"`
	Meta      MenuMeta `gorm:"type:json;comment:菜单元数据" json:"meta"`
	IsSystem  bool     `gorm:"not null;default:false;comment:是否系统内置" json:"isSystem"`
	Remark    string   `gorm:"type:varchar(500);comment:备注" json:"remark"`
	Children  []*Menu  `gorm:"foreignKey:ParentID" json:"children"`
}

// AuthItem 权限项
type AuthItem struct {
	Title    string `json:"title"`     // 权限名称
	AuthMark string `json:"auth_mark"` // 权限标识
}

// MenuMeta 菜单元数据 - 基于前端 MenuListType.meta 设计
type MenuMeta struct {
	Title         string     `json:"title"`                   // 路由标题
	Icon          string     `json:"icon,omitempty"`          // 路由图标
	ShowBadge     bool       `json:"showBadge,omitempty"`     // 是否显示徽章
	ShowTextBadge string     `json:"showTextBadge,omitempty"` // 文本徽章
	IsHide        bool       `json:"isHide,omitempty"`        // 是否在菜单中隐藏
	IsHideTab     bool       `json:"isHideTab,omitempty"`     // 是否在标签页中隐藏
	Link          string     `json:"link,omitempty"`          // 外部链接
	IsIframe      bool       `json:"isIframe,omitempty"`      // 是否为iframe
	KeepAlive     bool       `json:"keepAlive,omitempty"`     // 是否缓存
	FixedTab      bool       `json:"fixedTab,omitempty"`      // 是否固定标签页
	AuthList      []AuthItem `json:"authList,omitempty"`      // 操作权限列表
	Roles         []string   `json:"roles,omitempty"`         // 角色权限
	IsFirstLevel  bool       `json:"isFirstLevel,omitempty"`  // 是否为一级菜单（自动识别）

	// 兼容原有字段（保持向下兼容）
	ActiveIcon       string `json:"activeIcon,omitempty"`       // 激活图标（菜单）
	ActivePath       string `json:"activePath,omitempty"`       // 当前激活的菜单路径
	HideInBreadcrumb bool   `json:"hideInBreadcrumb,omitempty"` // 面包屑中不展现
	AffixTabOrder    int    `json:"affixTabOrder,omitempty"`    // 固定标签页顺序
	BadgeType        string `json:"badgeType,omitempty"`        // 徽标类型
	BadgeVariants    string `json:"badgeVariants,omitempty"`    // 徽标颜色
	IgnoreAccess     bool   `json:"ignoreAccess,omitempty"`     // 忽略权限检查
	MaxNumOfOpenTab  int    `json:"maxNumOfOpenTab,omitempty"`  // 最大打开标签数
	Loaded           bool   `json:"loaded,omitempty"`           // 是否已加载
}

// Value 实现 driver.Valuer 接口，将 MenuMeta 转换为数据库可存储的值
func (mm MenuMeta) Value() (driver.Value, error) {
	bytes, err := json.Marshal(mm)
	return string(bytes), err
}

// Scan 实现 sql.Scanner 接口，将数据库中的值转换为 MenuMeta
func (mm *MenuMeta) Scan(value interface{}) error {
	if value == nil {
		*mm = MenuMeta{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("类型断言失败")
	}

	return json.Unmarshal(bytes, mm)
}

// StatusOptions 实现 OptionsProvider 接口
func (m *Menu) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: MenuStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: MenuStatusDisabled, Code: "DISABLED"},
	}
}

// TypeOptions 实现 OptionsProvider 接口
func (m *Menu) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "目录", Value: MenuTypeDirectory, Code: "DIRECTORY"},
		{Label: "菜单", Value: MenuTypeMenu, Code: "MENU"},
	}
}
