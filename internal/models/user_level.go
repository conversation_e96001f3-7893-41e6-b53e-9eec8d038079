package models

import (
	"database/sql/driver"
	"errors"
	"gin/internal/constant"
	"github.com/goccy/go-json"
	"time"
)

const (
	UserLevelTypeBuy     int8 = 1 // 购买
	UserLevelTypeDeposit int8 = 2 // 充值
	UserLevelTypeGift    int8 = 3 // 赠送

	UserLevelStatusActive   int8 = 1 // 激活
	UserLevelStatusDisabled int8 = 0 // 禁用
)

// UserLevel 会员表
type UserLevel struct {
	BaseModel

	UserID    uint          `gorm:"not null;index;comment:用户ID" json:"user_id"`
	LevelID   uint          `gorm:"not null;index;comment:等级ID" json:"level_id"`
	Type      int8          `gorm:"type:tinyint;not null;default:1;index;comment:类型(1:购买,2:充值,3:赠送)" json:"type"`
	Status    int8          `gorm:"not null;default:1;index;comment:状态(1:激活,0:禁用)" json:"status"`
	ExpiredAt time.Time     `gorm:"index;comment:过期时间" json:"expired_at"`
	Data      UserLevelData `gorm:"type:json;comment:等级信息" json:"data"`
}

// UserLevelData 用户等级数据
type UserLevelData struct {
	Level
}

// Value 值
func (u UserLevelData) Value() (driver.Value, error) {
	return json.Marshal(u)
}

// Scan 扫描
func (u *UserLevelData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, u)
}

// TypeOptions 实现 OptionsProvider 接口
func (u *UserLevel) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "购买", Value: UserLevelTypeBuy, Code: "BUY"},
		{Label: "充值", Value: UserLevelTypeDeposit, Code: "DEPOSIT"},
		{Label: "赠送", Value: UserLevelTypeGift, Code: "GIFT"},
	}
}

// StatusOptions 实现 OptionsProvider 接口
func (u *UserLevel) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "激活", Value: UserLevelStatusActive, Code: "ACTIVE"},
		{Label: "禁用", Value: UserLevelStatusDisabled, Code: "DISABLED"},
	}
}
