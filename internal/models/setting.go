package models

// SettingType 配置类型
type SettingType string

// SettingField 配置字段
type SettingField string

const (
	SettingTypeString SettingType = "string" // 字符串类型
	SettingTypeNumber SettingType = "number" // 数字类型
	SettingTypeBool   SettingType = "bool"   // 布尔类型
	SettingTypeJSON   SettingType = "json"   // JSON类型
	SettingTypeImage  SettingType = "image"  // 图片类型
	SettingTypeColor  SettingType = "color"  // 颜色类型
	SettingTypeArray  SettingType = "array"  // 数组类型
	SettingTypeObject SettingType = "object" // 对象类型

	SettingGroupIDSite     = 1 // 站点配置
	SettingGroupIDTemplate = 2 // 模版配置

	SettingTipsUserFreeze    = "tips_user_freeze"    // 用户冻结提示词
	SettingTipsUserScore     = "tips_user_score"     // 用户信用分提示词
	SettingTipsNotice        = "tips_notice"         // 公告提示词
	SettingTipsRegister      = "tips_register"       // 注册提示词
	SettingTipsLogin         = "tips_login"          // 登录提示词
	SettingTipsSiteDesc      = "tips_site_desc"      // 站点描述
	SettingTipsSiteCopyright = "tips_site_copyright" // 站点版权
	SettingTipsSiteIntroduce = "tips_site_introduce" // 站点介绍

	SettingSiteLogo         SettingField = "site_logo"          // 站点LOGO
	SettingSiteFavicon      SettingField = "site_favicon"       // 站点favicon
	SettingSiteName         SettingField = "site_name"          // 站点名称
	SettingSiteTimezone     SettingField = "site_timezone"      // 项目时区
	SettingSiteBanner       SettingField = "site_banner"        // 轮播图
	SettingSiteDesc         SettingField = "site_desc"          // 站点描述
	SettingSiteCopyright    SettingField = "site_copyright"     // 站点版权
	SettingSiteIntroduce    SettingField = "site_introduce"     // 站点介绍
	SettingSiteAppDownload  SettingField = "site_app_download"  // 下载APP
	SettingAudio            SettingField = "audio"              // 提示音
	SettingStatusUserFreeze SettingField = "status_user_freeze" // 用户冻结状态
	SettingSiteFee          SettingField = "site_fee"           // 站点手续费
	SettingSiteWithdraw     SettingField = "site_withdraw"      // 提现配置
	SettingSiteSocial       SettingField = "site_social"        // 站点社交
	SettingSiteReward       SettingField = "site_reward"        // 站点奖励配置
	SettingDistribution     SettingField = "distribution"       // 分销配置
	SettingTemplateBasic    SettingField = "template_basic"     // 基础模版
	SettingTemplateLogin    SettingField = "template_login"     // 登录模版
	SettingTemplateRegister SettingField = "template_register"  // 注册模版
)

// Setting 系统配置
type Setting struct {
	BaseModel
	GroupID     uint        `gorm:"not null;index;comment:分组(1:站点配置,2:模版配置)" json:"group_id"`
	Name        string      `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	Field       string      `gorm:"type:varchar(60) not null;uniqueIndex;comment:键名" json:"field"`
	Value       string      `gorm:"column:value;type:text;comment:配置值" json:"value"`
	Type        SettingType `gorm:"column:type;type:varchar(20);default:'string';comment:值类型" json:"type"`
	Description string      `gorm:"column:description;type:varchar(255);comment:配置描述" json:"description"`
	Sort        int32       `gorm:"column:sort;type:int;default:0;comment:排序权重" json:"sort"`
	Status      int8        `gorm:"type:tinyint;not null;default:1;comment:状态(1:启用,0:禁用)" json:"status"`
}
