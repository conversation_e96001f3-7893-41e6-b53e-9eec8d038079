package models

const (
	UserBillNameTranslateKeyPrefix = "user:bill:name:translate:%v" // 用户账单名称翻译缓存前缀

	UserBillTypeDeposit            int8 = 1  // 充值
	UserBillTypeSystemReward       int8 = 2  // 系统奖励
	UserBillTypeRegisterReward     int8 = 3  // 注册奖励
	UserBillTypeInviteReward       int8 = 4  // 邀请奖励
	UserBillTypeDistributionReward int8 = 5  // 分销奖励
	UserBillTypeBalanceUnfreeze    int8 = 6  // 余额解冻
	UserBillTypeSystemAddition     int8 = 7  // 系统加款
	UserBillTypeWithdrawalReject   int8 = 8  // 提现拒绝
	UserBillTypeProductRefund      int8 = 9  // 产品退款
	UserBillTypeProductEarnings    int8 = 10 // 产品收益
	UserBillTypeTransferReceive    int8 = 11 // 转账接收
	UserBillTypeSwapsReceive       int8 = 12 // 闪兑接收

	UserBillTypeWithdrawal         int8 = -1 // 提现
	UserBillTypeSystemDeduction    int8 = -2 // 系统扣款
	UserBillTypeProductPurchase    int8 = -3 // 购买产品
	UserBillTypeMembershipPurchase int8 = -4 // 购买会员
	UserBillTypeBalanceFreeze      int8 = -5 // 余额冻结
	UserBillTypeTransferSend       int8 = -6 // 转账发送
	UserBillTypeSwapsSend          int8 = -7 // 闪兑发送
)

// UserBill 用户账单
type UserBill struct {
	BaseModel

	// 用户ID
	UserID uint `gorm:"not null;index;comment:用户ID" json:"user_id"`
	// 用户信息
	User *User `gorm:"foreignKey:UserID;references:ID" json:"user"`
	// 资产ID
	AssetID uint `gorm:"not null;index;comment:资产ID" json:"asset_id"`
	// 资产信息
	Asset *Asset `gorm:"foreignKey:AssetID;references:ID" json:"asset"`
	// 来源ID
	SourceID uint `gorm:"not null;index;comment:来源ID" json:"source_id"`
	// 类型
	Type int8 `gorm:"not null;index;comment:类型" json:"type"`
	// 名称
	Name string `gorm:"not null;index;comment:名称" json:"name"`
	// 金额
	Amount float64 `gorm:"type:decimal(18,4);not null;comment:金额" json:"amount"`
	// 手续费
	Fee float64 `gorm:"type:decimal(18,4);not null;comment:手续费" json:"fee"`
	// 余额
	Balance float64 `gorm:"type:decimal(18,4);not null;comment:余额" json:"balance"`
}
