package models

import (
	"fmt"
	"gin/internal/constant"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

const (
	UserTypeVirtual int8 = 1 // 虚拟用户
	UserTypeNormal  int8 = 2 // 普通用户

	UserStatusActive   int8 = 1 // 激活
	UserStatusDisabled int8 = 2 // 禁用
	UserStatusFrozen   int8 = 3 // 冻结
	UserStatusLocked   int8 = 4 // 锁定

	UserGenderUnknown int8 = 0 // 未知
	UserGenderMale    int8 = 1 // 男
	UserGenderFemale  int8 = 2 // 女
)

// User 用户表
type User struct {
	BaseModel

	// LevelID 等级ID，关联等级表 - 记录主等级的ID
	LevelID uint `gorm:"index;default:null;comment:'等级ID'" json:"level_id"`
	// UserLevel 用户等级 - 必须添加一个条件是主等级
	UserLevel UserLevel `gorm:"foreignKey:UserID;references:ID;conditions:level_id=LevelID" json:"user_level"`
	// ParentID 父级ID，用于多级分销关系
	ParentID uint `gorm:"index;default:null;comment:父级ID" json:"parent_id"`
	// Parent 父级用户
	Parent *User `gorm:"foreignKey:ParentID;references:ID" json:"parent"`
	// Username 用户名，用于登录
	Username string `gorm:"type:varchar(255);default:null;uniqueIndex;comment:用户名" json:"username"`
	// Nickname 用户昵称，显示名称
	Nickname string `gorm:"type:varchar(100) not null;index;comment:昵称" json:"nickname"`
	// Email 用户邮箱，可选
	Email string `gorm:"type:varchar(255);default:null;uniqueIndex;comment:邮箱" json:"email"`
	// Telephone 用户手机号，可选
	Telephone string `gorm:"type:varchar(255);default:null;uniqueIndex;comment:手机号码" json:"telephone"`
	// Avatar 用户头像URL
	Avatar string `gorm:"type:varchar(255);comment:头像" json:"avatar"`
	// Score 用户信用分，默认100分
	Score int32 `gorm:"not null;default:100;comment:信用分" json:"score"`
	// MinScore 最低信用分
	MinScore int32 `gorm:"not null;default:60;comment:最低信用分" json:"min_score"`
	// MaxScore 最高信用分
	MaxScore int32 `gorm:"not null;default:200;comment:最高信用分" json:"max_score"`
	// Integral 用户积分
	Integral int32 `gorm:"not null;default:0;comment:积分" json:"integral"`
	// Gender 用户性别
	Gender int8 `gorm:"type:tinyint;default:0;comment:性别(0:保密,1:男,2:女)" json:"gender"`
	// Birthday 用户生日
	Birthday *time.Time `gorm:"default:null;comment:生日" json:"birthday"`
	// Password 用户密码，加密存储
	PasswordHash string `gorm:"type:varchar(255);comment:密码" json:"password_hash"`
	// SecurityKey 支付密码
	SecurityKey string `gorm:"type:varchar(255);comment:密钥" json:"security_key"`
	// FrozenAmount 冻结金额，不可用余额
	FrozenAmount float64 `gorm:"type:decimal(18,2);not null;default:0;comment:冻结金额" json:"frozen_amount"`
	// AvailableAmount 可用金额，可提现余额
	AvailableAmount float64 `gorm:"type:decimal(18,2);not null;default:0;comment:可用金额" json:"available_amount"`
	// InviteCode 邀请码，用于推广
	InviteCode string `gorm:"type:char(6);default:null;uniqueIndex;comment:邀请码" json:"invite_code"`
	// Type 用户类型：1虚拟用户，2普通用户
	Type int8 `gorm:"not null;default:2;index;comment:类型(1:虚拟用户,2:普通用户)" json:"type"`
	// Status 用户状态
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:激活,2:禁用,3:冻结,4:锁定)" json:"status"`
	// 账户锁定截止时间
	LockedUntil *time.Time `gorm:"default:null;comment:'锁定截止时间'" json:"locked_until,omitempty"`
	// 登录失败次数
	FailedLoginAttempts int8 `gorm:"not null;default:0;comment:'登录失败次数'" json:"failed_login_attempts"`
	// 最后登录时间
	LastLoginAt *time.Time `gorm:"default:null;comment:'最后登录时间'" json:"last_login_at,omitempty"`
	// 最后登录IP地址
	LastLoginIP string `gorm:"type:varchar(255);comment:'最后登录IP'" json:"last_login_ip,omitempty"`
	// 最后密码修改时间
	PasswordChangedAt *time.Time `gorm:"default:null;comment:'最后密码修改时间'" json:"password_changed_at,omitempty"`
	// 最后密钥修改时间
	SecurityChangedAt *time.Time `gorm:"default:null;comment:'最后密钥修改时间'" json:"security_changed_at,omitempty"`
	// CreatedIP 注册IP地址
	CreatedIP string `gorm:"type:varchar(255);comment:注册IP" json:"created_ip"`
	// Description 用户详细描述
	Description string `gorm:"type:text;comment:详情" json:"description"`
}

// BeforeCreate GORM 钩子：创建前验证
func (u *User) BeforeCreate(tx *gorm.DB) error {
	u.InviteCode = uuid.New().String()[:6]

	// 生成密码哈希值
	if err := u.GeneratePasswordHash(u.PasswordHash); err != nil {
		return err
	}

	return nil
}

// BeforeUpdate GORM 钩子：更新前验证
func (u *User) BeforeUpdate(tx *gorm.DB) error {
	return nil
}

// CompareHashAndSecurityKey 比较安全密钥
func (u *User) CompareHashAndSecurityKey(securityKey string) bool {
	return bcrypt.CompareHashAndPassword([]byte(u.SecurityKey), []byte(securityKey)) == nil
}

// CompareHashAndPassword 比较密码哈希值
func (u *User) CompareHashAndPassword(password string) bool {
	return bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password)) == nil
}

// GeneratePasswordHash 生成密码哈希值
func (u *User) GeneratePasswordHash(password string) error {
	hashedPassword, hashErr := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if hashErr != nil {
		return fmt.Errorf("user password hashing failed: %v", hashErr)
	}
	u.PasswordHash = string(hashedPassword)
	return nil
}

// TypeOptions 实现 OptionsProvider 接口
func (u *User) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "虚拟用户", Value: UserTypeVirtual, Code: "VIRTUAL"},
		{Label: "普通用户", Value: UserTypeNormal, Code: "NORMAL"},
	}
}

// Options 实现 OptionsProvider 接口
func (u *User) Options() []constant.Option {
	return []constant.Option{
		{Label: "激活", Value: UserStatusActive, Code: "ACTIVE"},
		{Label: "禁用", Value: UserStatusDisabled, Code: "DISABLED"},
		{Label: "冻结", Value: UserStatusFrozen, Code: "FROZEN"},
		{Label: "锁定", Value: UserStatusLocked, Code: "LOCKED"},
	}
}

// GenderOptions 实现 OptionsProvider 接口
func (u *User) GenderOptions() []constant.Option {
	return []constant.Option{
		{Label: "未知", Value: UserGenderUnknown, Code: "UNKNOWN"},
		{Label: "男", Value: UserGenderMale, Code: "MALE"},
		{Label: "女", Value: UserGenderFemale, Code: "FEMALE"},
	}
}
