package models

// MenuPermission 菜单权限关联模型 - 管理菜单和权限的多对多关系
type MenuPermission struct {
	BaseModel
	MenuID       uint   `gorm:"not null;comment:菜单ID;index:idx_menu_permission" json:"menuId"`
	PermissionID uint   `gorm:"not null;comment:权限ID;index:idx_menu_permission" json:"permissionId"`
	IsRequired   bool   `gorm:"not null;default:true;comment:是否必需权限" json:"isRequired"`
	Sort         int    `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	Remark       string `gorm:"type:varchar(500);comment:备注" json:"remark"`

	// 关联关系
	Menu       *Menu       `gorm:"foreignKey:MenuID" json:"menu,omitempty"`
	Permission *Permission `gorm:"foreignKey:PermissionID" json:"permission,omitempty"`
}
