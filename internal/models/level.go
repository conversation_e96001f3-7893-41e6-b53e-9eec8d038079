package models

import (
	"fmt"
	"gin/internal/constant"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	LevelTypeMember int8 = 1 // 会员等级

	LevelStatusActive   int8 = 1 // 激活
	LevelStatusDisabled int8 = 2 // 禁用

	LevelNameTranslateRedisKeyPrefix        = "level:name:translate:%s"        // 等级名称翻译前缀
	LevelDescriptionTranslateRedisKeyPrefix = "level:description:translate:%s" // 等级描述翻译前缀
)

// Level 等级表
type Level struct {
	BaseModel

	NameField        string  `gorm:"type:varchar(255) not null;index;comment:名称" json:"name_field"`
	Icon             string  `gorm:"type:varchar(255);comment:图标" json:"icon"`
	Symbol           string  `gorm:"type:varchar(20) not null;uniqueIndex;comment:等级标识" json:"symbol"`
	Type             int8    `gorm:"not null;default:1;comment:类型(1:会员等级)" json:"type"`
	Sort             int     `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	Amount           float64 `gorm:"type:decimal(18,2) not null;default:0;comment:金额" json:"amount"`
	Discount         float64 `gorm:"type:decimal(18,2) not null;default:1;comment:折扣率" json:"discount"`
	Days             int     `gorm:"not null;default:0;comment:天数" json:"days"`
	Status           int8    `gorm:"type:tinyint;not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`
	DescriptionField string  `gorm:"type:varchar(255);comment:翻译描述字段" json:"description_field"`

	// 非数据库字段
	Name        string `gorm:"-" json:"name"`        // 名称（当前语言）
	Description string `gorm:"-" json:"description"` // 描述（当前语言）
}

// BeforeCreate 创建前验证
func (d *Level) BeforeCreate(tx *gorm.DB) error {
	uid := uuid.New().String()
	d.NameField = fmt.Sprintf(LevelNameTranslateRedisKeyPrefix, uid)
	d.DescriptionField = fmt.Sprintf(LevelDescriptionTranslateRedisKeyPrefix, uid)
	return nil
}

// AfterCreate 创建后处理
func (d *Level) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:   "等级名称翻译",
			Lang:   LanguageDefault,
			Key:    d.NameField,
			Value:  d.Name,
			Type:   TranslationTypeSystem,
			Module: "level",
		},
		{
			Name:   "等级描述翻译",
			Lang:   LanguageDefault,
			Key:    d.DescriptionField,
			Type:   TranslationTypeSystem,
			Module: "level",
			Value:  d.Description,
		},
	}
	if err := tx.Create(&translates).Error; err != nil {
		return err
	}
	return nil
}

// AfterFind 查询后处理
func (d *Level) AfterFind(tx *gorm.DB) (err error) {
	return d.Translate(tx)
}

// Translate 翻译
func (d *Level) Translate(tx *gorm.DB) error {
	// 加载名称翻译
	if d.NameField != "" {
		var nameTranslate Translation
		if err := tx.Where("`lang` = ? AND `key` = ?", LanguageDefault, d.NameField).Find(&nameTranslate).Error; err == nil {
			d.Name = nameTranslate.Value
		}
	}

	// 加载描述翻译
	if d.DescriptionField != "" {
		var descriptionTranslate Translation
		if err := tx.Where("`lang` = ? AND `key` = ?", LanguageDefault, d.DescriptionField).Find(&descriptionTranslate).Error; err == nil {
			d.Description = descriptionTranslate.Value
		}
	}
	return nil
}

// TypeOptions 实现 OptionsProvider 接口
func (d *Level) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "会员等级", Value: LevelTypeMember, Code: "MEMBER"},
	}
}

func (d *Level) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "激活", Value: LevelStatusActive, Code: "ACTIVE"},
		{Label: "禁用", Value: LevelStatusDisabled, Code: "DISABLED"},
	}
}
