package models

import (
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	CategoryTypeCommodity int8 = 1 // 商品类型

	CategoryStatusEnabled  int8 = 1 // 启用
	CategoryStatusDisabled int8 = 0 // 禁用

	CategoryNameTranslateRedisKeyPrefix        = "category:name:translate:%s"        // 分类名称翻译前缀
	CategorySubtitleTranslateRedisKeyPrefix    = "category:subtitle:translate:%s"    // 分类副标题翻译前缀
	CategoryDescriptionTranslateRedisKeyPrefix = "category:description:translate:%s" // 分类描述翻译前缀
)

// Category 分类
type Category struct {
	BaseModel

	// 父级ID
	ParentID uint `gorm:"default:null;comment:父级ID" json:"parent_id"`
	// 类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:商品类型)" json:"type"`
	// 名称
	NameField string `gorm:"type:varchar(255) not null;index;comment:名称" json:"name_field"`
	// 副标题
	SubtitleField string `gorm:"type:varchar(255);comment:副标题" json:"subtitle_field"`
	// 图标
	Icon string `gorm:"type:varchar(255);comment:图标" json:"icon"`
	// 排序
	Sort int8 `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	// 状态
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:启用,2:禁用)" json:"status"`
	// 翻译描述字段
	DescriptionField string `gorm:"type:varchar(255);comment:翻译描述字段" json:"description_field"`

	// 子分类
	Children []*Category `gorm:"foreignKey:ParentID" json:"children"`

	// 非数据库字段
	Name        string `gorm:"-" json:"name"`        // 名称（当前语言）
	Subtitle    string `gorm:"-" json:"subtitle"`    // 副标题（当前语言）
	Description string `gorm:"-" json:"description"` // 描述（当前语言）
}

// BeforeCreate 创建前钩子
func (c *Category) BeforeCreate(tx *gorm.DB) error {
	uid := uuid.New().String()
	c.NameField = fmt.Sprintf(CategoryNameTranslateRedisKeyPrefix, uid)
	c.SubtitleField = fmt.Sprintf(CategorySubtitleTranslateRedisKeyPrefix, uid)
	c.DescriptionField = fmt.Sprintf(CategoryDescriptionTranslateRedisKeyPrefix, uid)
	return nil
}

// AfterCreate 创建后处理
func (c *Category) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:  "分类名称翻译",
			Lang:  LanguageDefault,
			Key:   c.NameField,
			Value: c.Name,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "分类副标题翻译",
			Lang:  LanguageDefault,
			Key:   c.SubtitleField,
			Value: c.Subtitle,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "分类描述翻译",
			Lang:  LanguageDefault,
			Key:   c.DescriptionField,
			Value: c.Description,
			Type:  TranslationTypeSystem,
		},
	}
	if err := tx.Create(&translates).Error; err != nil {
		return err
	}
	return nil
}

// Translate 查询后处理
func (c *Category) Translate(tx *gorm.DB, locale string) error {
	// 加载名称翻译
	if c.NameField != "" {
		var nameTranslate Translation
		if err := tx.Where("lang = ? AND key = ?", locale, c.NameField).Find(&nameTranslate).Error; err == nil {
			c.Name = nameTranslate.Value
		}
	}

	// 加载副标题翻译
	if c.SubtitleField != "" {
		var subtitleTranslate Translation
		if err := tx.Where("lang = ? AND key = ?", locale, c.SubtitleField).Find(&subtitleTranslate).Error; err == nil {
			c.Subtitle = subtitleTranslate.Value
		}
	}

	// 加载描述翻译
	if c.DescriptionField != "" {
		var descriptionTranslate Translation
		if err := tx.Where("lang = ? AND key = ?", locale, c.DescriptionField).Find(&descriptionTranslate).Error; err == nil {
			c.Description = descriptionTranslate.Value
		}
	}

	// 加载子分类
	for _, v := range c.Children {
		if err := v.Translate(tx, locale); err != nil {
			return err
		}
	}
	return nil
}
