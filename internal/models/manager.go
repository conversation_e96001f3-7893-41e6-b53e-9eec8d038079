package models

import (
	"time"

	"gorm.io/gorm"
)

// 状态常量
const (
	StatusEnabled  int8 = 10
	StatusDisabled int8 = -1
)

// 性别常量
const (
	GenderSecret int8 = 0
	GenderMale   int8 = 1
	GenderFemale int8 = 2
)

// 登录类型常量
const (
	LoginTypeSingle int8 = 1 // 单点登录
	LoginTypeMulti  int8 = 2 // 多点登录
)

// Manager 管理员
type Manager struct {
	ID                   uint64     `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID             uint64     `gorm:"index;comment:租户ID" json:"tenantId"`
	Status               int8       `gorm:"type:tinyint;default:10;comment:状态(10:启用,-1:禁用)" json:"status"`
	UserName             string     `gorm:"type:varchar(60);comment:用户名" json:"userName"`
	Mobile               string     `gorm:"type:varchar(20);comment:中国手机不带国家代码，国际手机号格式为：国家代码-手机号" json:"mobile"`
	NickName             string     `gorm:"type:varchar(50);comment:用户昵称" json:"nickName"`
	Password             string     `gorm:"type:varchar(255);comment:登录密码" json:"-"`
	Salt                 string     `gorm:"type:char(10);comment:加密盐" json:"-"`
	Email                string     `gorm:"type:varchar(100);comment:用户登录邮箱" json:"email"`
	Gender               int8       `gorm:"column:sex;type:tinyint;default:0;comment:性别(0:保密,1:男,2:女)" json:"gender"`
	Avatar               string     `gorm:"type:varchar(255);comment:用户头像" json:"avatar"`
	DeptID               uint       `gorm:"type:int unsigned;default:0;comment:部门id" json:"deptId"`
	RoleIDs              string     `gorm:"type:varchar(255);comment:角色ID列表,逗号分隔" json:"roleIds"`
	LoginType            int8       `gorm:"column:is_only;type:tinyint;default:1;comment:登陆类型(1:单点登陆,2多点登陆)" json:"loginType"`
	IsAdmin              bool       `gorm:"type:bool;default:false;comment:超级管理员" json:"isAdmin"`
	IsTenantAdmin        bool       `gorm:"type:bool;default:false;comment:租户管理员" json:"isTenantAdmin"`
	LastLoginIP          string     `gorm:"type:varchar(45);comment:最后登录ip" json:"lastLoginIp"`
	LastLoginTime        time.Time  `gorm:"type:datetime;comment:最后登录时间" json:"lastLoginTime"`
	LastPasswordChangeAt time.Time  `gorm:"type:datetime;comment:最后修改密码时间" json:"lastPasswordChangeAt"`
	LoginFailCount       int        `gorm:"type:int;default:0;comment:登录失败次数" json:"loginFailCount"`
	ExpiredAt            time.Time  `gorm:"type:datetime(3);comment:过期时间" json:"expiredAt"`
	CreatedAt            time.Time  `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt            time.Time  `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt            *time.Time `gorm:"index;comment:删除时间" json:"deletedAt"`
}

// IsSuperAdmin 判断是否为系统超级管理员
func (m *Manager) IsSuperAdmin() bool {
	return m.IsAdmin
}

// IsTenantAdministrator 判断是否为租户管理员
func (m *Manager) IsTenantAdministrator() bool {
	return m.IsTenantAdmin
}

// BelongsToTenant 判断管理员是否属于指定租户
func (m *Manager) BelongsToTenant(tenantID uint64) bool {
	// 超级管理员可以访问所有租户
	if m.IsSuperAdmin() {
		return true
	}
	return m.TenantID == tenantID
}

// IsEnabled 判断管理员账号是否启用
func (m *Manager) IsEnabled() bool {
	return m.Status == StatusEnabled
}

// IsDisabled 判断管理员账号是否禁用
func (m *Manager) IsDisabled() bool {
	return m.Status == StatusDisabled
}

// IsSingleLogin 判断是否为单点登录
func (m *Manager) IsSingleLogin() bool {
	return m.LoginType == LoginTypeSingle
}

// IsMultiLogin 判断是否为多点登录
func (m *Manager) IsMultiLogin() bool {
	return m.LoginType == LoginTypeMulti
}

// BeforeCreate GORM 创建前钩子
func (m *Manager) BeforeCreate(tx *gorm.DB) error {
	// 这里可以添加创建前的逻辑，例如密码加密等
	return nil
}

// BeforeUpdate GORM 更新前钩子
func (m *Manager) BeforeUpdate(tx *gorm.DB) error {
	// 这里可以添加更新前的逻辑
	return nil
}
