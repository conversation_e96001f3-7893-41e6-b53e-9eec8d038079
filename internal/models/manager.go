package models

import (
	"fmt"
	"gin/internal/constant"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"time"
)

const (
	ManagerStatusEnabled  int8 = 10 // 正常
	ManagerStatusDisabled int8 = 20 // 禁用
	ManagerStatusLocked   int8 = 30 // 锁定
	ManagerStatusExpired  int8 = 40 // 过期

	GenderSecret int8 = 0 // 保密
	GenderMale   int8 = 1 // 男
	GenderFemale int8 = 2 // 女

	LoginModeSingleDevice int8 = 1 // 单设备登录
	LoginModeMultiDevice  int8 = 2 // 多设备登录
	LoginModeLimited      int8 = 3 // 限制设备数登录
)

// Manager 管理员
type Manager struct {
	BaseModel
	Username             string    `gorm:"type:varchar(60);comment:用户名" json:"username"`
	Nickname             string    `gorm:"type:varchar(50);comment:用户昵称" json:"nickname"`
	Mobile               string    `gorm:"type:varchar(20);comment:国际手机号格式为：国家代码-手机号" json:"mobile"`
	PasswordHash         string    `gorm:"type:varchar(255);comment:'密码哈希'" json:"-"`
	Email                string    `gorm:"type:varchar(100);comment:邮箱" json:"email"`
	Gender               int8      `gorm:"type:tinyint;default:0;comment:性别(0:保密,1:男,2:女)" json:"gender"`
	Avatar               string    `gorm:"type:varchar(255);comment:头像" json:"avatar"`
	DeptID               uint      `gorm:"type:int unsigned;default:0;comment:部门id(暂未使用,为后续扩展预留)" json:"deptId,omitempty"`
	LoginMode            int8      `gorm:"type:tinyint;default:1;comment:登录模式(1:单设备登录,2:多设备登录,3:限制设备数)" json:"loginMode"`
	MaxDevices           int8      `gorm:"type:tinyint;default:1;comment:最大同时登录设备数(当LoginMode=3时生效)" json:"maxDevices"`
	LastLoginIP          string    `gorm:"type:varchar(45);comment:最后登录ip" json:"lastLoginIp"`
	LastLoginTime        time.Time `gorm:"type:datetime;comment:最后登录时间" json:"lastLoginTime"`
	LastPasswordChangeAt time.Time `gorm:"type:datetime;comment:最后修改密码时间" json:"lastPasswordChangeAt"`
	LoginFailCount       int       `gorm:"type:int;default:0;comment:登录失败次数" json:"loginFailCount"`
	ExpiredAt            time.Time `gorm:"type:datetime(3);comment:过期时间" json:"expiredAt"`
	Status               int8      `gorm:"type:tinyint;default:10;comment:状态(10:正常,20:禁用,30:锁定,40:过期)" json:"status"`
	CustomerServiceURL   string    `gorm:"type:varchar(255);comment:'客服链接'" json:"customer_service_url"`
	//Roles       []Role       `gorm:"many2many:manager_roles;" json:"roles,omitempty"`
	//Permissions []Permission `gorm:"many2many:manager_permissions;" json:"permissions,omitempty"`
}

// BeforeCreate GORM 钩子：创建前验证
func (m *Manager) BeforeCreate(tx *gorm.DB) error {
	// 哈希初始化密码
	if err := m.GeneratePasswordHash(m.PasswordHash); err != nil {
		return err
	}

	return nil
}

// GeneratePasswordHash 生成密码哈希值
func (m *Manager) GeneratePasswordHash(password string) error {
	hashedPassword, hashErr := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if hashErr != nil {
		return fmt.Errorf("manager password hashing failed: %v", hashErr)
	}
	m.PasswordHash = string(hashedPassword)
	return nil
}

// CompareHashAndPassword 比较密码哈希值
func (m *Manager) CompareHashAndPassword(password string) bool {
	return bcrypt.CompareHashAndPassword([]byte(m.PasswordHash), []byte(password)) == nil
}

// GetLoginModeEnum 获取登录模式枚举
func (m *Manager) GetLoginModeEnum() int8 {
	return m.LoginMode
}

// SetMaxDevices 设置最大设备数
func (m *Manager) SetMaxDevices(maxDevices int8) {
	if maxDevices > 0 {
		m.MaxDevices = maxDevices
	}
}

// GetEffectiveMaxDevices 获取有效的最大设备数限制
func (m *Manager) GetEffectiveMaxDevices() int8 {
	switch m.GetLoginModeEnum() {
	case LoginModeSingleDevice:
		return 1
	case LoginModeLimited:
		return m.MaxDevices
	default: // LoginModeMultiDevice
		return -1 // 无限制
	}
}

// StatusOptions 实现 OptionsProvider 接口
func (m *Manager) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "正常", Value: ManagerStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: ManagerStatusDisabled, Code: "DISABLED"},
		{Label: "锁定", Value: ManagerStatusLocked, Code: "LOCKED"},
		{Label: "过期", Value: ManagerStatusExpired, Code: "EXPIRED"},
	}
}

// GenderOptions 实现 OptionsProvider 接口
func (m *Manager) GenderOptions() []constant.Option {
	return []constant.Option{
		{Label: "保密", Value: GenderSecret, Code: "SECRET"},
		{Label: "男", Value: GenderMale, Code: "MALE"},
		{Label: "女", Value: GenderFemale, Code: "FEMALE"},
	}
}

// LoginModeOptions 实现 OptionsProvider 接口
func (m *Manager) LoginModeOptions() []constant.Option {
	return []constant.Option{
		{Label: "单设备登录", Value: LoginModeSingleDevice, Code: "SINGLE_DEVICE"},
		{Label: "多设备登录", Value: LoginModeMultiDevice, Code: "MULTI_DEVICE"},
		{Label: "限制设备数", Value: LoginModeLimited, Code: "LIMITED"},
	}
}
