package models

import "time"

// Lang 语言表
type Lang struct {
	ID        uint64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TenantID  uint64     `gorm:"column:tenant_id;index;default:0;comment:租户ID(0表示全局)" json:"tenantId"`
	Code      string     `gorm:"column:code;type:varchar(10);uniqueIndex:idx_code_tenant;comment:语言代码" json:"code"`
	Name      string     `gorm:"column:name;type:varchar(50);comment:语言名称" json:"name"`
	Icon      string     `gorm:"column:icon;type:varchar(255);comment:语言图标" json:"icon"`
	Sort      int        `gorm:"column:sort;type:int;default:0;comment:排序" json:"sort"`
	Status    int8       `gorm:"column:status;type:tinyint;default:10;comment:状态" json:"status"`
	CreatedAt time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"column:updated_at" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"column:deleted_at;index" json:"deletedAt"`
}

// TableName 表名
func (Lang) TableName() string {
	return "sys_lang"
}
