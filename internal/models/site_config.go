package models

import (
	"encoding/json"
	"time"
)

// ConfigType 配置类型
type ConfigType string

const (
	ConfigTypeString ConfigType = "string" // 字符串类型
	ConfigTypeNumber ConfigType = "number" // 数字类型
	ConfigTypeBool   ConfigType = "bool"   // 布尔类型
	ConfigTypeJSON   ConfigType = "json"   // JSON类型
	ConfigTypeImage  ConfigType = "image"  // 图片类型
	ConfigTypeColor  ConfigType = "color"  // 颜色类型
	ConfigTypeArray  ConfigType = "array"  // 数组类型
	ConfigTypeObject ConfigType = "object" // 对象类型
)

// Config 站点配置模型
type Config struct {
	ID          uint64     `gorm:"column:id;primaryKey;autoIncrement;comment:配置ID" json:"id"`
	TenantID    uint64     `gorm:"column:tenant_id;index;default:0;comment:租户ID(0表示全局)" json:"tenantId"`
	Group       string     `gorm:"column:group;type:varchar(50);not null;comment:配置分组" json:"group"`
	Key         string     `gorm:"column:key;type:varchar(100);not null;comment:配置键名" json:"key"`
	Value       string     `gorm:"column:value;type:text;comment:配置值" json:"value"`
	Type        ConfigType `gorm:"column:type;type:varchar(20);default:'string';comment:值类型" json:"type"`
	Title       string     `gorm:"column:title;type:varchar(100);comment:配置标题" json:"title"`
	Description string     `gorm:"column:description;type:varchar(255);comment:配置描述" json:"description"`
	Sort        int        `gorm:"column:sort;type:int;default:0;comment:排序权重" json:"sort"`
	Status      int8       `gorm:"column:status;type:tinyint;default:10;comment:状态 0-禁用 10-启用" json:"status"`
	CreatedAt   time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt   time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt   *time.Time `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

// TableName 表名
func (*Config) TableName() string {
	return "sys_config"
}

// GetStringValue 获取字符串值
func (c *Config) GetStringValue() string {
	return c.Value
}

// GetNumberValue 获取数字值
func (c *Config) GetNumberValue() (float64, error) {
	var value float64
	err := json.Unmarshal([]byte(c.Value), &value)
	return value, err
}

// GetBoolValue 获取布尔值
func (c *Config) GetBoolValue() (bool, error) {
	var value bool
	err := json.Unmarshal([]byte(c.Value), &value)
	return value, err
}

// GetJSONValue 获取JSON值
func (c *Config) GetJSONValue() (map[string]interface{}, error) {
	var value map[string]interface{}
	err := json.Unmarshal([]byte(c.Value), &value)
	return value, err
}

// GetArrayValue 获取数组值
func (c *Config) GetArrayValue() ([]interface{}, error) {
	var value []interface{}
	err := json.Unmarshal([]byte(c.Value), &value)
	return value, err
}

// SetValue 设置配置值
func (c *Config) SetValue(value interface{}) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	c.Value = string(data)
	c.UpdatedAt = time.Now()
	return nil
}

// UpdateMeta 更新配置元信息
func (c *Config) UpdateMeta(title, description string, sort int) {
	c.Title = title
	c.Description = description
	c.Sort = sort
	c.UpdatedAt = time.Now()
}
