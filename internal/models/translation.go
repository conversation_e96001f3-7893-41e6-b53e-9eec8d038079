package models

import "time"

type Translation struct {
	ID        uint64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TenantID  uint64     `gorm:"column:tenant_id;index;default:0;comment:租户ID(0表示全局)" json:"tenantId"`
	Key       string     `gorm:"column:key;type:varchar(100);uniqueIndex:idx_key_lang_tenant;comment:翻译键" json:"key"`
	Value     string     `gorm:"column:value;type:text;comment:翻译值" json:"value"`
	Lang      string     `gorm:"column:lang;type:varchar(10);uniqueIndex:idx_key_lang_tenant;comment:语言代码" json:"lang"`
	Module    string     `gorm:"column:module;type:varchar(50);index;comment:所属模块" json:"module"`
	Status    int8       `gorm:"column:status;type:tinyint;default:10;comment:状态" json:"status"`
	CreatedAt time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"column:updated_at" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"column:deleted_at;index" json:"deletedAt"`
}

// TableName 表名
func (Translation) TableName() string {
	return "sys_translation"
}
