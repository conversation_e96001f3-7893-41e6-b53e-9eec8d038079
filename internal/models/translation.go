package models

import "gin/internal/constant"

const (
	TranslationTypeSystem   int8 = 1 // 系统翻译
	TranslationTypeLanguage int8 = 2 // 语言包

	TranslationStatusEnabled  int8 = 1 // 启用
	TranslationStatusDisabled int8 = 0 // 禁用
)

// Translation 翻译模型
type Translation struct {
	BaseModel
	Name   string `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	Key    string `gorm:"type:varchar(100);uniqueIndex:idx_key_lang_tenant;comment:翻译键" json:"key"`
	Value  string `gorm:"type:text;comment:翻译值" json:"value"`
	Lang   string `gorm:"type:varchar(10);uniqueIndex:idx_key_lang_tenant;comment:语言代码" json:"lang"`
	Module string `gorm:"type:varchar(50);index;comment:所属模块" json:"module"`
	Type   int8   `gorm:"type:tinyint;not null;default:1;comment:类型(1:系统,2:语言包)" json:"type"`
	Status int8   `gorm:"type:tinyint;not null;default:1;comment:状态(1:启用,0:禁用)" json:"status"`
}

// TypeOptions 实现 OptionsProvider 接口
func (t *Translation) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "系统翻译", Value: TranslationTypeSystem, Code: "SYSTEM"},
		{Label: "语言包", Value: TranslationTypeLanguage, Code: "CUSTOM"},
	}
}

// StatusOptions 实现 OptionsProvider 接口
func (t *Translation) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: TranslationStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: TranslationStatusDisabled, Code: "DISABLED"},
	}
}
