package models

// UserAsset 用户资产
type UserAsset struct {
	BaseModel

	// 用户ID
	UserID uint `gorm:"not null;uniqueIndex:idx_user_asset;comment:用户ID" json:"user_id"`
	// 用户信息
	User *User `gorm:"foreignKey:UserID;references:ID" json:"user"`
	// 资产ID
	AssetID uint `gorm:"not null;uniqueIndex:idx_user_asset;comment:资产ID" json:"asset_id"`
	// 资产信息
	Asset *Asset `gorm:"foreignKey:AssetID;references:ID" json:"asset"`
	// 冻结金额
	FrozenAmount float64 `gorm:"type:decimal(18,4) not null;default:0;comment:冻结金额" json:"frozen_amount"`
	// 可用金额
	AvailableAmount float64 `gorm:"type:decimal(18,4) not null;default:0;comment:可用金额" json:"available_amount"`
	// 收益金额
	ProfitAmount float64 `gorm:"type:decimal(18,4) not null;default:0;comment:收益金额" json:"profit_amount"`
	// 充值总金额
	SumDepositAmount float64 `gorm:"type:decimal(18,4) not null;default:0;comment:充值总金额" json:"sum_deposit_amount"`
	// 提现总金额
	SumWithdrawAmount float64 `gorm:"type:decimal(18,4) not null;default:0;comment:提现总金额" json:"sum_withdraw_amount"`
	// 是否主资产
	IsPrimary bool `gorm:"not null;default:false;comment:是否主资产" json:"is_primary"`
}
