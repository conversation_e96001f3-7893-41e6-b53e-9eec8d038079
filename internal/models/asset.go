package models

import (
	"database/sql/driver"
	"fmt"
	"github.com/goccy/go-json"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	AssetTypePlatform int8 = 1 // 平台币
	AssetTypeCrypto   int8 = 2 // 加密货币

	AssetStatusEnabled  int8 = 1 // 启用
	AssetStatusDisabled int8 = 0 // 禁用

	AssetSubtitleTranslateRedisKeyPrefix    = "asset:subtitle:translate:%s"    // 副标题翻译前缀
	AssetDescriptionTranslateRedisKeyPrefix = "asset:description:translate:%s" // 描述翻译前缀
)

// Asset 资产表
type Asset struct {
	BaseModel

	// 名称
	Name string `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	// 副标题
	SubtitleField string `gorm:"type:varchar(255) not null;comment:副标题" json:"subtitle_field"`
	// 货币名称
	NameNative string `gorm:"type:varchar(100) not null;comment:货币名称" json:"name_native"`
	// 标识符号
	Symbol string `gorm:"type:varchar(10) not null;uniqueIndex;comment:标识符号" json:"symbol"`
	// 图标
	Icon string `gorm:"type:varchar(255) not null;comment:图标" json:"icon"`
	// 类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:平台币,2:加密货币)" json:"type"`
	// 汇率
	Rate float64 `gorm:"not null;default:1;comment:汇率(基准值1)" json:"rate"`
	// 排序
	Sort int8 `gorm:"not null;default:99;index;comment:排序" json:"sort"`
	// 状态(1:启用,0:禁用)
	Status int8 `gorm:"not null;default:1;comment:状态(1:启用,0:禁用)" json:"status"`
	// 小数位数
	Decimals int8 `gorm:"not null;default:2;comment:小数位数" json:"decimals"`
	// 语言描述
	DescriptionField string `gorm:"type:varchar(255);comment:语言描述" json:"description_field"`
	// 数据
	Data AssetData `gorm:"type:json;comment:数据" json:"data"`

	// 非数据库字段
	Subtitle    string `gorm:"-" json:"subtitle"`    // 副标题（当前语言）
	Description string `gorm:"-" json:"description"` // 描述（当前语言）
}

// AssetData 资产数据
type AssetData struct {
	IsDeposit  bool `json:"is_deposit" form:"is_deposit"`   // 是否支持充值
	IsWithdraw bool `json:"is_withdraw" form:"is_withdraw"` // 是否支持提现
	IsTransfer bool `json:"is_transfer" form:"is_transfer"` // 是否支持转账
	IsSwap     bool `json:"is_swap" form:"is_swap"`         // 是否支持兑换
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (ad AssetData) Value() (driver.Value, error) {
	return json.Marshal(ad)
}

// Scan 扫描资产数据
func (ad *AssetData) Scan(value interface{}) error {
	return json.Unmarshal(value.([]byte), ad)
}

// BeforeCreate 创建前钩子
func (a *Asset) BeforeCreate(tx *gorm.DB) error {
	uid := uuid.New().String()
	a.SubtitleField = fmt.Sprintf(AssetSubtitleTranslateRedisKeyPrefix, uid)
	a.DescriptionField = fmt.Sprintf(AssetDescriptionTranslateRedisKeyPrefix, uid)
	return nil
}

// AfterCreate 创建后处理
func (a *Asset) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:  "资产副标题翻译",
			Lang:  LanguageDefault,
			Key:   a.SubtitleField,
			Value: a.Subtitle,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "资产描述翻译",
			Lang:  LanguageDefault,
			Key:   a.DescriptionField,
			Value: a.Description,
			Type:  TranslationTypeSystem,
		},
	}
	if err := tx.Create(&translates).Error; err != nil {
		return err
	}
	return nil
}

// AfterFind 查询后处理
func (a *Asset) AfterFind(tx *gorm.DB) (err error) {
	return a.Translate(tx, LanguageDefault)
}

// Translate 翻译
func (a *Asset) Translate(tx *gorm.DB, locale string) error {
	// 加载副标题翻译
	if a.SubtitleField != "" {
		var subtitleTranslate Translation
		if err := tx.Where("`lang` = ? AND `key` = ?", locale, a.SubtitleField).Find(&subtitleTranslate).Error; err == nil {
			a.Subtitle = subtitleTranslate.Value
		}
	}

	// 加载描述翻译
	if a.DescriptionField != "" {
		var descriptionTranslate Translation
		if err := tx.Where("`lang` = ? AND `key` = ?", locale, a.DescriptionField).Find(&descriptionTranslate).Error; err == nil {
			a.Description = descriptionTranslate.Value
		}
	}

	return nil
}
