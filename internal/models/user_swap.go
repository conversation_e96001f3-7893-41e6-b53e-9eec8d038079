package models

const (
	UserSwapTypeFlash = 1 // 闪兑

	UserSwapStatusPending   = 1 // 待处理
	UserSwapStatusCompleted = 2 // 已完成
	UserSwapStatusCanceled  = 3 // 已取消
	UserSwapStatusFailed    = 4 // 已失败
)

// UserSwap 用户闪兑
type UserSwap struct {
	BaseModel

	// 发送用户ID
	SendUserID uint `gorm:"index;comment:发送用户ID" json:"send_user_id"`
	// 发送用户信息
	SendUser *User `gorm:"foreignKey:SendUserID;references:ID" json:"send_user"`
	// 接收用户ID
	ReceiveUserID uint `gorm:"index;comment:接收用户ID" json:"receive_user_id"`
	// 接收用户信息
	ReceiveUser *User `gorm:"foreignKey:ReceiveUserID;references:ID" json:"receive_user"`
	// 发送资产ID
	SendAssetID uint `gorm:"index;comment:发送资产ID" json:"send_asset_id"`
	// 发送资产信息
	SendAsset *Asset `gorm:"foreignKey:SendAssetID;references:ID" json:"send_asset"`
	// 接收资产ID
	ReceiveAssetID uint `gorm:"index;comment:接收资产ID" json:"receive_asset_id"`
	// 接收资产信息
	ReceiveAsset *Asset `gorm:"foreignKey:ReceiveAssetID;references:ID" json:"receive_asset"`
	// 发送金额
	SendAmount float64 `gorm:"type:decimal(18,4);comment:发送金额" json:"send_amount"`
	// 接收金额
	ReceiveAmount float64 `gorm:"type:decimal(18,4);comment:接收金额" json:"receive_amount"`
	// 汇率
	Rate float64 `gorm:"type:decimal(18,4);comment:汇率" json:"rate"`
	// 固定手续费
	FixedFee float64 `gorm:"type:decimal(18,4) not null;default:0;comment:固定手续费" json:"fixed_fee"`
	// 手续费
	RateFee float64 `gorm:"type:decimal(8,3) not null;default:0;comment:手续费(%)" json:"rate_fee"`
	// 类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:闪兑)" json:"type"`
	// 状态
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:待处理,2:已完成,3:已取消,4:已失败)" json:"status"`
	// 原因
	Reason string `gorm:"type:varchar(1024);comment:原因" json:"reason"`
}
