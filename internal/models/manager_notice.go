package models

import "gin/internal/constant"

const (
	ManagerNoticeTypeSystem   int8 = 1 // 系统通知
	ManagerNoticeTypePersonal int8 = 2 // 个人消息
	ManagerNoticeTypeWarning  int8 = 3 // 警告通知
)

// ManagerNotice 管理系统通知表
type ManagerNotice struct {
	BaseModel

	SenderID   uint   `gorm:"index;not null;comment:发送者ID(0:系统)" json:"sender_id"`
	ReceiverID uint   `gorm:"index;not null;comment:接收者ID(0:所有管理员)" json:"receiver_id"`
	Title      string `gorm:"type:varchar(255);not null;comment:标题" json:"title"`
	Content    string `gorm:"type:text;not null;comment:内容" json:"content"`
	Type       int8   `gorm:"type:tinyint;not null;default:1;comment:类型(1:系统通知,2:个人消息,3:警告通知)" json:"type"`
	Route      string `gorm:"type:varchar(255);not null;comment:路由" json:"route"`
	IsRead     bool   `gorm:"default:false;comment:是否已读" json:"is_read"`
}

// TypeOptions 实现 OptionsProvider 接口
func (m *ManagerNotice) TypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "系统通知", Value: ManagerNoticeTypeSystem, Code: "SYSTEM"},
		{Label: "个人消息", Value: ManagerNoticeTypePersonal, Code: "PERSONAL"},
		{Label: "警告通知", Value: ManagerNoticeTypeWarning, Code: "WARNING"},
	}
}
