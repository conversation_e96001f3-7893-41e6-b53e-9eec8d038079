package models

import (
	"fmt"
	"github.com/goccy/go-json"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	PaymentTypeBankCard   int8 = 1 // 银行卡
	PaymentTypeCrypto     int8 = 2 // 加密货币
	PaymentTypeThirdParty int8 = 3 // 三方支付

	PaymentModeDeposit  int8 = 1 // 充值
	PaymentModeWithdraw int8 = 2 // 提现

	PaymentStatusEnabled  int8 = 1 // 启用
	PaymentStatusDisabled int8 = 0 // 禁用

	PaymentSubtitleTranslateRedisKeyPrefix    = "payment:subtitle:translate:%s"    // 支付副标题翻译前缀
	PaymentDescriptionTranslateRedisKeyPrefix = "payment:description:translate:%s" // 支付描述翻译前缀
)

// Payment 支付表
type Payment struct {
	BaseModel

	// 资产ID
	AssetID uint `gorm:"index;comment:资产ID" json:"asset_id"`
	// 资产
	Asset *Asset `gorm:"foreignKey:AssetID" json:"asset"`
	// 名称
	Name string `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	// 副标题
	SubtitleField string `gorm:"type:varchar(255) not null;default:'';comment:副标题" json:"subtitle_field"`
	// 图标
	Icon string `gorm:"type:varchar(255) not null;comment:图标" json:"icon"`
	// 类型(1:银行卡,2:加密货币,3:三方支付)
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:银行卡,2:加密货币,3:三方支付)" json:"type"`
	// 模式(1:充值,2:提现)
	Mode int8 `gorm:"not null;default:1;index;comment:模式(1:充值,2:提现)" json:"mode"`
	// 最小金额
	MinAmount float64 `gorm:"type:decimal(18,4) not null;default:1;comment:最小金额" json:"min_amount"`
	// 最大金额
	MaxAmount float64 `gorm:"type:decimal(18,4) not null;default:100000;comment:最大金额" json:"max_amount"`
	// 开始时间
	StartAt string `gorm:"type:char(8) not null;default:'00:00:00';comment:开始时间" json:"start_at"`
	// 结束时间
	EndAt string `gorm:"type:char(8) not null;default:'23:59:59';comment:结束时间" json:"end_at"`
	// 固定手续费
	FixedFee float64 `gorm:"type:decimal(18,4) not null;default:0;comment:固定手续费" json:"fixed_fee"`
	// 手续费(%)
	RateFee float64 `gorm:"type:decimal(8,3) not null;default:0;comment:手续费(%)" json:"rate_fee"`
	// 等级
	Level int8 `gorm:"not null;default:0;comment:等级" json:"level"`
	// 状态(1:启用,0:禁用)
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`
	// 排序
	Sort int8 `gorm:"not null;default:99;comment:排序" json:"sort"`
	// 是否需要凭证
	IsProof bool `gorm:"not null;default:false;comment:是否需要凭证" json:"is_proof"`
	// 是否跳转客服
	IsRedirectCS bool `gorm:"not null;default:false;comment:是否跳转客服" json:"is_redirect_cs"`
	// 翻译描述字段
	DescriptionField string `gorm:"type:varchar(255);comment:翻译描述字段" json:"description_field"`
	// 数据
	Data string `gorm:"type:text;comment:数据" json:"data"`

	// 非数据库字段
	Subtitle    string `gorm:"-" json:"subtitle"`    // 副标题（当前语言）
	Description string `gorm:"-" json:"description"` // 描述（当前语言）
}

// PaymentDataBankCard 银行卡支付额外数据
type PaymentDataBankCard struct {
	Name     string `json:"name" form:"name" views:"label:银行名称"`           // 银行名称
	Address  string `json:"address" form:"address" views:"label:银行地址"`     // 银行地址
	RealName string `json:"real_name" form:"real_name" views:"label:真实姓名"` // 真实姓名
	CardNo   string `json:"card_no" form:"card_no" views:"label:银行卡号"`     // 银行卡号
	AreaCode string `json:"area_code" form:"area_code" views:"label:地区代码"` // 地区代码
}

// PaymentDataCrypto 加密货币支付额外数据
type PaymentDataCrypto struct {
	Name    string `json:"name" form:"name" views:"label:网络名称"`       // 网络名称
	Address string `json:"address" form:"address" views:"label:钱包地址"` // 钱包地址
}

// Validate 验证支付数据
func (p *Payment) Validate() error {
	return nil
}

// BeforeCreate 创建前钩子
func (p *Payment) BeforeCreate(tx *gorm.DB) error {
	uid := uuid.New().String()
	p.SubtitleField = fmt.Sprintf(PaymentSubtitleTranslateRedisKeyPrefix, uid)
	p.DescriptionField = fmt.Sprintf(PaymentDescriptionTranslateRedisKeyPrefix, uid)
	return p.Validate()
}

// AfterCreate 创建后处理
func (p *Payment) AfterCreate(tx *gorm.DB) error {
	// 创建默认语言的翻译记录
	translates := []Translation{
		{
			Name:  "支付副标题翻译",
			Lang:  LanguageDefault,
			Key:   p.SubtitleField,
			Value: p.Subtitle,
			Type:  TranslationTypeSystem,
		},
		{
			Name:  "支付描述翻译",
			Lang:  LanguageDefault,
			Key:   p.DescriptionField,
			Value: p.Description,
			Type:  TranslationTypeSystem,
		},
	}
	if err := tx.Create(&translates).Error; err != nil {
		return err
	}
	return nil
}

// BeforeUpdate 更新前钩子
func (p *Payment) BeforeUpdate(tx *gorm.DB) error {
	return p.Validate()
}

// Translate 查询后处理
func (p *Payment) Translate(tx *gorm.DB, locale string) error {
	// 加载副标题翻译
	if p.SubtitleField != "" {
		var subtitleTranslate Translation
		if err := tx.Where("`lang` = ? AND `key` = ?", locale, p.SubtitleField).Find(&subtitleTranslate).Error; err == nil {
			p.Subtitle = subtitleTranslate.Value
		}
	}

	// 加载描述翻译
	if p.DescriptionField != "" {
		var descriptionTranslate Translation
		if err := tx.Where("`lang` = ? AND `key` = ?", locale, p.DescriptionField).Find(&descriptionTranslate).Error; err == nil {
			p.Description = descriptionTranslate.Value
		}
	}
	return nil
}

// GetWithdrawData 获取提现数据
func (p *Payment) GetWithdrawCryptoData() *PaymentDataCrypto {
	data := p.GetWithdrawData()
	if data == nil {
		return &PaymentDataCrypto{}
	}
	return data.(*PaymentDataCrypto)
}

// GetWithdrawBankCardData 获取提现银行卡数据
func (p *Payment) GetWithdrawBankCardData() *PaymentDataBankCard {
	data := p.GetWithdrawData()
	if data == nil {
		return &PaymentDataBankCard{}
	}
	return data.(*PaymentDataBankCard)
}

// GetWithdrawData 获取提现数据
func (p *Payment) GetWithdrawData() interface{} {
	if p.Mode != PaymentModeWithdraw {
		return nil
	}

	// 获取提现设置的方式
	options := p.GetWithdrawSelectData()
	accountName := ""
	if len(options) > 0 {
		accountName = options[0].Value.(string)
	}

	switch p.Type {
	case PaymentTypeCrypto:
		return &PaymentDataCrypto{
			Name: accountName,
		}
	case PaymentTypeBankCard:
		return &PaymentDataBankCard{
			Name: accountName,
		}
	}
	return nil
}

// GetWithdrawSelectData 获取提现支付方式选择数据
func (p *Payment) GetWithdrawSelectData() []*InputSelectOption {
	options := make([]*InputSelectOption, 0)
	if p.Mode != PaymentModeWithdraw {
		return options
	}

	if p.Data != "" {
		_ = json.Unmarshal([]byte(p.Data), &options)
	}
	return options
}

// InputSelectOption 下拉框Options
type InputSelectOption struct {
	Icon  string      `json:"icon" form:"icon" views:"label:图标;type:image;row:12"` // 选项图标
	Label string      `json:"label" form:"label" views:"label:标签;row:6"`           // 选项标签
	Value interface{} `json:"value" form:"value" views:"label:值;row:6"`            // 选项值
}
