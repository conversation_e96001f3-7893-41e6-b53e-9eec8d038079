package models

const (
	UserTransferTypeInternal = 1 // 内部转账

	UserTransferStatusPending   = 1 // 待处理
	UserTransferStatusCompleted = 2 // 已完成
	UserTransferStatusCanceled  = 3 // 已取消
	UserTransferStatusFailed    = 4 // 已失败
)

// UserTransfer 用户转账
type UserTransfer struct {
	BaseModel

	// 发送用户ID
	SendUserID uint `gorm:"index;comment:发送用户ID" json:"send_user_id"`
	// 发送用户信息
	SendUser *User `gorm:"foreignKey:SendUserID;references:ID" json:"send_user"`
	// 接收用户ID
	ReceiveUserID uint `gorm:"index;comment:接收用户ID" json:"receive_user_id"`
	// 接收用户信息
	ReceiveUser *User `gorm:"foreignKey:ReceiveUserID;references:ID" json:"receive_user"`
	// 资产ID
	AssetID uint `gorm:"index;comment:资产ID" json:"asset_id"`
	// 资产信息
	Asset *Asset `gorm:"foreignKey:AssetID;references:ID" json:"asset"`
	// 转账金额
	Amount float64 `gorm:"type:decimal(18,4) not null;comment:转账金额" json:"amount"`
	// 固定手续费
	FixedFee float64 `gorm:"type:decimal(18,4) not null;default:0;comment:固定手续费" json:"fixed_fee"`
	// 手续费
	RateFee float64 `gorm:"type:decimal(8,3) not null;default:0;comment:手续费(%)" json:"rate_fee"`
	// 实际到账金额
	ActualAmount float64 `gorm:"type:decimal(18,4) not null;comment:实际到账金额" json:"actual_amount"`
	// 类型
	Type int8 `gorm:"not null;default:1;index;comment:类型(1:内部转账)" json:"type"`
	// 状态
	Status int8 `gorm:"not null;default:1;index;comment:状态(1:待处理,2:已完成,3:已取消,4:已失败)" json:"status"`
	// 原因
	Reason string `gorm:"type:varchar(1024);comment:原因" json:"reason"`
}
