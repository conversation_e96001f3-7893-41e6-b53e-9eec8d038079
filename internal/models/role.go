package models

import "gin/internal/constant"

const (
	RoleStatusEnabled  int8 = 1 // 启用
	RoleStatusDisabled int8 = 0 // 禁用

	DataScopeAll     int8 = 1 // 所有数据
	DataScopeCustom  int8 = 2 // 自定义数据
	DataScopeDept    int8 = 3 // 部门数据
	DataScopeDeptAnd int8 = 4 // 部门及以下数据
	DataScopeSelf    int8 = 5 // 仅本人数据
)

// Role 角色模型
type Role struct {
	BaseModel
	Name        string `gorm:"type:varchar(50);not null;comment:角色名称" json:"name"`
	Code        string `gorm:"type:varchar(50);not null;uniqueIndex;comment:角色编码" json:"code"`
	Status      int8   `gorm:"type:tinyint;not null;default:1;comment:状态(1:启用,0:禁用)" json:"status"`
	Sort        int    `gorm:"not null;default:0;comment:排序" json:"sort"`
	Remark      string `gorm:"type:varchar(255);comment:备注" json:"remark"`
	DataScope   int8   `gorm:"type:tinyint;not null;default:5;comment:数据范围(1:全部,2:自定义,3:本部门,4:本部门及以下,5:仅本人)" json:"dataScope"`
	IsSystem    bool   `gorm:"not null;default:false;comment:是否系统内置" json:"isSystem"`
	Permissions []uint `gorm:"-" json:"permissions,omitempty"`
}

// StatusOptions 实现 OptionsProvider 接口
func (r *Role) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: RoleStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: RoleStatusDisabled, Code: "DISABLED"},
	}
}

// DataScopeOptions 实现 OptionsProvider 接口
func (r *Role) DataScopeOptions() []constant.Option {
	return []constant.Option{
		{Label: "所有数据", Value: DataScopeAll, Code: "ALL"},
		{Label: "自定义数据", Value: DataScopeCustom, Code: "CUSTOM"},
		{Label: "部门数据", Value: DataScopeDept, Code: "DEPT"},
		{Label: "部门及以下数据", Value: DataScopeDeptAnd, Code: "DEPT_AND"},
		{Label: "仅本人数据", Value: DataScopeSelf, Code: "SELF"},
	}
}
