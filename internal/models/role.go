package models

import "time"

// 角色状态常量
const (
	RoleStatusEnabled  int8 = 10 // 启用
	RoleStatusDisabled int8 = -1 // 禁用
)

// 数据范围常量
const (
	DataScopeAll          int8 = 1 // 全部数据
	DataScopeCustom       int8 = 2 // 自定义数据
	DataScopeDept         int8 = 3 // 本部门数据
	DataScopeDeptAndChild int8 = 4 // 本部门及以下数据
	DataScopeSelf         int8 = 5 // 仅本人数据
)

// Role 角色模型
type Role struct {
	ID        uint64     `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID  uint64     `gorm:"not null;default:0;index;comment:租户ID" json:"tenantId"`
	Name      string     `gorm:"type:varchar(50);not null;comment:角色名称" json:"name"`
	Code      string     `gorm:"type:varchar(50);not null;uniqueIndex:idx_tenant_code;comment:角色编码" json:"code"`
	Status    int8       `gorm:"type:tinyint;not null;default:10;comment:状态(10:启用,-1:禁用)" json:"status"`
	Sort      int        `gorm:"not null;default:0;comment:排序" json:"sort"`
	Remark    string     `gorm:"type:varchar(255);comment:备注" json:"remark"`
	DataScope int8       `gorm:"type:tinyint;not null;default:1;comment:数据范围(1:全部,2:自定义,3:本部门,4:本部门及以下,5:仅本人)" json:"dataScope"`
	IsSystem  bool       `gorm:"not null;default:false;comment:是否系统内置" json:"isSystem"`
	CreatedBy uint64     `gorm:"comment:创建人" json:"createdBy"`
	UpdatedBy uint64     `gorm:"comment:更新人" json:"updatedBy"`
	CreatedAt time.Time  `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt time.Time  `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt *time.Time `gorm:"index;comment:删除时间" json:"deletedAt"`
}

// 根据角色的数据范围构建查询条件
// func buildDataScopeCondition(db *gorm.DB, userID uint64, roleDataScope int8) *gorm.DB {
//     switch roleDataScope {
//     case 1: // 全部数据
//         return db
//     case 2: // 自定义数据
//         // 获取角色关联的部门ID列表
//         deptIDs := getDeptIDsByRoleID(roleID)
//         return db.Where("dept_id IN ?", deptIDs)
//     case 3: // 本部门数据
//         userDeptID := getUserPrimaryDeptID(userID)
//         return db.Where("dept_id = ?", userDeptID)
//     case 4: // 本部门及以下数据
//         userDeptID := getUserPrimaryDeptID(userID)
//         deptIDs := getChildDeptIDs(userDeptID)
//         deptIDs = append(deptIDs, userDeptID)
//         return db.Where("dept_id IN ?", deptIDs)
//     case 5: // 仅本人数据
//         return db.Where("created_by = ?", userID)
//     default:
//         return db.Where("created_by = ?", userID) // 默认仅本人数据
//     }
// }

// GetDataScopeName 获取数据范围名称
func (r *Role) GetDataScopeName() string {
	switch r.DataScope {
	case DataScopeAll:
		return "全部数据"
	case DataScopeCustom:
		return "自定义数据"
	case DataScopeDept:
		return "本部门数据"
	case DataScopeDeptAndChild:
		return "本部门及以下数据"
	case DataScopeSelf:
		return "仅本人数据"
	default:
		return "未知范围"
	}
}

// IsEnabled 判断角色是否启用
func (r *Role) IsEnabled() bool {
	return r.Status == RoleStatusEnabled
}
