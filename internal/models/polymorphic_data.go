package models

// PolymorphicData 通用多态数据表
type PolymorphicData struct {
	BaseModel

	// 多态关联字段
	EntityType    string `gorm:"type:varchar(50) not null;index:idx_entity_attr;comment:实体类型" json:"entity_type"`      // payment, order, product
	EntityID      uint   `gorm:"not null;index:idx_entity_attr;comment:实体ID" json:"entity_id"`                         // 关联的主表ID
	EntitySubType string `gorm:"type:varchar(50) not null;index:idx_entity_attr;comment:数据子类型" json:"entity_sub_type"` // bank_card, crypto, third_party
	Value         string `gorm:"type:json;comment:数据内容" json:"value"`                                                  // 数据字段（JSON格式）
}
