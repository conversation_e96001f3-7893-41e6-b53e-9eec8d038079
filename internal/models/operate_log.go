package models

import "gin/internal/constant"

const (
	OperateModeFrontend int8 = 1 // 前台操作
	OperateModeBackend  int8 = 2 // 后台操作

	OperateTypeCreate int8 = 1 // 新增
	OperateTypeDelete int8 = 2 // 删除
	OperateTypeUpdate int8 = 3 // 更新
	OperateTypeQuery  int8 = 4 // 查询
	OperateTypeOther  int8 = 5 // 其他

	OperateStatusSuccess int8 = 1 // 成功
	OperateStatusFailed  int8 = 0 // 失败
)

// OperateLog 系统操作日志
type OperateLog struct {
	BaseModel
	UserID      uint   `gorm:"type:int unsigned;index:idx_user_operate;comment:用户ID" json:"userId"`
	ManagerID   uint   `gorm:"type:int unsigned;index:idx_manager_operate;comment:管理员ID" json:"managerId"`
	Username    string `gorm:"type:varchar(50);index:idx_username;comment:用户名" json:"username"`
	Module      string `gorm:"type:varchar(50);index;comment:操作模块" json:"module"`
	Type        int8   `gorm:"type:tinyint;comment:操作类型(1:新增,2:删除,3:更新,4:查询,5:其他)" json:"type"`
	Mode        int8   `gorm:"type:tinyint;comment:操作模式(1:前台,2:后台)" json:"mode"`
	Description string `gorm:"type:varchar(255);comment:操作描述" json:"description"`
	ReqPath     string `gorm:"type:varchar(255);comment:请求路径" json:"reqPath"`
	ReqParams   string `gorm:"type:text;comment:请求参数" json:"reqParams"`
	ResResult   string `gorm:"type:text;comment:返回结果" json:"resResult"`
	Status      int8   `gorm:"type:tinyint;default:1;comment:操作状态(1:成功,0:失败)" json:"status"`
	ErrorMsg    string `gorm:"type:varchar(500);comment:错误信息" json:"errorMsg"`
	IP          string `gorm:"type:varchar(45);comment:操作IP" json:"ip"`
	Location    string `gorm:"type:varchar(255);comment:操作地点" json:"location"`
	UserAgent   string `gorm:"type:varchar(500);comment:用户代理" json:"userAgent"`
}

// NewUserOperateLog 创建普通用户操作日志
func NewUserOperateLog(userId uint, username, module, description, reqPath, reqParams, resResult string, status int8, errorMsg string) *OperateLog {
	return &OperateLog{
		UserID:      userId,
		ManagerID:   0,
		Username:    username,
		Module:      module,
		Type:        OperateTypeOther,
		Mode:        OperateModeFrontend,
		Description: description,
		ReqPath:     reqPath,
		ReqParams:   reqParams,
		ResResult:   resResult,
		Status:      status,
		ErrorMsg:    errorMsg,
	}
}

// NewManagerOperateLog 创建管理员操作日志
func NewManagerOperateLog(managerId uint, username, module, description, reqPath, reqParams, resResult string, status int8, errorMsg string) *OperateLog {
	return &OperateLog{
		UserID:      0,
		ManagerID:   managerId,
		Username:    username,
		Module:      module,
		Type:        OperateTypeOther,
		Mode:        OperateModeBackend,
		Description: description,
		ReqPath:     reqPath,
		ReqParams:   reqParams,
		ResResult:   resResult,
		Status:      status,
		ErrorMsg:    errorMsg,
	}
}

// InferOperateType 根据 HTTP Method 推断操作类型
func InferOperateType(method string) int8 {
	switch method {
	case "GET":
		return OperateTypeQuery
	case "POST":
		return OperateTypeCreate
	case "PUT", "PATCH":
		return OperateTypeUpdate
	case "DELETE":
		return OperateTypeDelete
	default:
		return OperateTypeOther
	}
}

// InferOperateStatus 根据 HTTP 状态码推断操作类型
func InferOperateStatus(statusCode int) int8 {
	if statusCode >= 200 && statusCode < 300 {
		return OperateStatusFailed
	}
	return OperateStatusSuccess
}

// OperateModeOptions 实现 OptionsProvider 接口
func (o *OperateLog) OperateModeOptions() []constant.Option {
	return []constant.Option{
		{Label: "前台操作", Value: OperateModeFrontend, Code: "FRONTEND"},
		{Label: "后台操作", Value: OperateModeBackend, Code: "BACKEND"},
	}
}

// OperateTypeOptions 实现 OptionsProvider 接口
func (o *OperateLog) OperateTypeOptions() []constant.Option {
	return []constant.Option{
		{Label: "新增", Value: OperateTypeCreate, Code: "CREATE"},
		{Label: "删除", Value: OperateTypeDelete, Code: "DELETE"},
		{Label: "修改", Value: OperateTypeUpdate, Code: "UPDATE"},
		{Label: "查询", Value: OperateTypeQuery, Code: "QUERY"},
		{Label: "其他", Value: OperateTypeOther, Code: "OTHER"},
	}
}

// OperateStatusOptions 实现 OptionsProvider 接口
func (o *OperateLog) OperateStatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "成功", Value: OperateStatusSuccess, Code: "SUCCESS"},
		{Label: "失败", Value: OperateStatusFailed, Code: "FAILED"},
	}
}
