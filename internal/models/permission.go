package models

import "gin/internal/constant"

const (
	PermissionStatusDisabled int8 = 0 // 禁用
	PermissionStatusEnabled  int8 = 1 // 启用
)

// Permission 权限模型 - 专注于权限控制和API访问
type Permission struct {
	BaseModel
	Name     string `gorm:"column:name;type:varchar(255);not null;comment:权限名称" json:"name"`
	Code     string `gorm:"column:code;type:varchar(100);not null;uniqueIndex;comment:权限编码" json:"code"`
	Resource string `gorm:"column:resource;type:varchar(255);comment:资源标识" json:"resource"`
	Action   string `gorm:"column:action;type:varchar(50);comment:操作类型(create,read,update,delete,view等)" json:"action"`
	Method   string `gorm:"column:method;type:varchar(10);comment:HTTP方法" json:"method"`
	API      string `gorm:"column:api;type:varchar(255);comment:API路径" json:"api"`
	IsSystem bool   `gorm:"column:is_system;not null;default:false;comment:是否系统内置" json:"isSystem"`
	Status   int8   `gorm:"column:status;type:tinyint;default:1;comment:状态(1:启用,0:禁用)" json:"status"`
	Remark   string `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`
}

// StatusOptions 实现 OptionsProvider 接口
func (p *Permission) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: PermissionStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: PermissionStatusDisabled, Code: "DISABLED"},
	}
}
