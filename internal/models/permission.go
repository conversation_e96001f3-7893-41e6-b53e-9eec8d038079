package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// Permission 权限模型
type Permission struct {
	ID        uint64        `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	TenantID  uint64        `gorm:"column:tenant_id;not null;default:0;index;comment:租户ID" json:"tenantId"`
	Name      string        `gorm:"column:name;type:varchar(255);not null;comment:名称" json:"name"`
	Status    int8          `gorm:"column:status;type:tinyint;default:10;comment:状态(10:启用,-1:禁用)" json:"status"`
	Path      string        `gorm:"column:path;type:varchar(255);comment:路由路径" json:"path"`
	ParentID  uint64        `gorm:"column:parent_id;default:0;comment:父级ID" json:"parentId"`
	Sort      int           `gorm:"column:sort;type:int;default:0;comment:排序" json:"sort"`
	Type      int8          `gorm:"column:type;type:tinyint;comment:类型(1:目录,2:菜单,3:按钮)" json:"type"`
	ToAuth    int8          `gorm:"column:to_auth;type:tinyint;default:1;comment:权限标识(1:需要授权,2:不需要授权)" json:"toAuth"`
	Component string        `gorm:"column:component;type:varchar(255);comment:组件" json:"component"`
	Redirect  string        `gorm:"column:redirect;type:varchar(255);comment:重定向" json:"redirect"`
	Code      string        `gorm:"column:code;type:varchar(100);not null;uniqueIndex:idx_tenant_code;comment:权限编码" json:"code"`
	Method    string        `gorm:"column:method;type:varchar(10);comment:HTTP方法" json:"method"`
	API       string        `gorm:"column:api;type:varchar(255);comment:API路径" json:"api"`
	Meta      RouteMeta     `gorm:"column:meta;type:json;comment:菜单元数据" json:"meta"`
	IsSystem  bool          `gorm:"column:is_system;not null;default:false;comment:是否系统内置" json:"isSystem"`
	Remark    string        `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`
	Children  []*Permission `gorm:"-" json:"children,omitempty"`
	CreatedAt time.Time     `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt time.Time     `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt *time.Time    `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

// TableName 指定表名
func (*Permission) TableName() string {
	return "sys_permissions"
}

// IsMenu 判断是否为菜单
func (p *Permission) IsMenu() bool {
	return p.Type == 2
}

// IsDirectory 判断是否为目录
func (p *Permission) IsDirectory() bool {
	return p.Type == 1
}

// IsButton 判断是否为按钮
func (p *Permission) IsButton() bool {
	return p.Type == 3
}

// IsEnabled 判断是否启用
func (p *Permission) IsEnabled() bool {
	return p.Status == 10
}

// NeedAuth 判断是否需要授权
func (p *Permission) NeedAuth() bool {
	return p.ToAuth == 1
}

// RouteMeta 路由元数据
type RouteMeta struct {
	Title                    string   `json:"title"`                              // 标题名称
	Icon                     string   `json:"icon,omitempty"`                     // 图标（菜单/tab）
	ActiveIcon               string   `json:"activeIcon,omitempty"`               // 激活图标（菜单）
	ActivePath               string   `json:"activePath,omitempty"`               // 当前激活的菜单路径
	Authority                []string `json:"authority,omitempty"`                // 需要特定的角色标识
	HideInMenu               bool     `json:"hideInMenu,omitempty"`               // 菜单中不展现
	HideChildrenInMenu       bool     `json:"hideChildrenInMenu,omitempty"`       // 子级在菜单中不展现
	HideInBreadcrumb         bool     `json:"hideInBreadcrumb,omitempty"`         // 面包屑中不展现
	HideInTab                bool     `json:"hideInTab,omitempty"`                // 标签页不展现
	KeepAlive                bool     `json:"keepAlive,omitempty"`                // 开启缓存
	AffixTab                 bool     `json:"affixTab,omitempty"`                 // 固定标签页
	AffixTabOrder            int      `json:"affixTabOrder,omitempty"`            // 固定标签页顺序
	Badge                    string   `json:"badge,omitempty"`                    // 徽标
	BadgeType                string   `json:"badgeType,omitempty"`                // 徽标类型
	BadgeVariants            string   `json:"badgeVariants,omitempty"`            // 徽标颜色
	IframeSrc                string   `json:"iframeSrc,omitempty"`                // iframe 地址
	Link                     string   `json:"link,omitempty"`                     // 外链地址
	IgnoreAccess             bool     `json:"ignoreAccess,omitempty"`             // 忽略权限检查
	MenuVisibleWithForbidden bool     `json:"menuVisibleWithForbidden,omitempty"` // 可见但禁止访问
	MaxNumOfOpenTab          int      `json:"maxNumOfOpenTab,omitempty"`          // 最大打开标签数
	Order                    int      `json:"order,omitempty"`                    // 排序
	Loaded                   bool     `json:"loaded,omitempty"`                   // 是否已加载
}

// Value 实现 driver.Valuer 接口，将 RouteMeta 转换为数据库可存储的值
func (m RouteMeta) Value() (driver.Value, error) {
	bytes, err := json.Marshal(m)
	return string(bytes), err
}

// Scan 实现 sql.Scanner 接口，将数据库中的值转换为 RouteMeta
func (m *RouteMeta) Scan(value interface{}) error {
	if value == nil {
		*m = RouteMeta{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("类型断言失败")
	}

	return json.Unmarshal(bytes, m)
}

// BuildPermissionTree 构建权限树
func BuildPermissionTree(permissions []*Permission) []*Permission {
	// 创建一个映射，用于快速查找权限
	permMap := make(map[uint64]*Permission)
	for _, perm := range permissions {
		permMap[perm.ID] = perm
	}

	// 构建树结构
	var rootNodes []*Permission
	for _, perm := range permissions {
		if perm.ParentID == 0 {
			// 根节点
			rootNodes = append(rootNodes, perm)
		} else {
			// 子节点，添加到父节点的children中
			if parent, exists := permMap[perm.ParentID]; exists {
				parent.Children = append(parent.Children, perm)
			}
		}
	}

	return rootNodes
}
