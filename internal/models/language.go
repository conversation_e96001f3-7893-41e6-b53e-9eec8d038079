package models

import "gin/internal/constant"

const (
	LanguageStatusEnabled  int8 = 1 // 启用
	LanguageStatusDisabled int8 = 0 // 禁用

	LanguageDefault = "zh-CN" // 默认语言
)

// Language 语言表
type Language struct {
	BaseModel
	Code       string `gorm:"type:varchar(10);uniqueIndex;not null;comment:语言代码(如:zh-CN,en-US)" json:"code"`
	Name       string `gorm:"type:varchar(50);not null;comment:中文名称" json:"name"`
	NameNative string `gorm:"type:varchar(100);not null;comment:本地语言名称" json:"name_native"`
	Icon       string `gorm:"type:varchar(255);comment:语言图标" json:"icon"`
	Sort       int    `gorm:"not null;default:1;index;comment:排序" json:"sort"`
	Status     int8   `gorm:"type:tinyint;not null;default:1;index;comment:状态(1:启用,0:禁用)" json:"status"`
}

// StatusOptions 实现 OptionsProvider 接口
func (l *Language) StatusOptions() []constant.Option {
	return []constant.Option{
		{Label: "启用", Value: LanguageStatusEnabled, Code: "ENABLED"},
		{Label: "禁用", Value: LanguageStatusDisabled, Code: "DISABLED"},
	}
}
