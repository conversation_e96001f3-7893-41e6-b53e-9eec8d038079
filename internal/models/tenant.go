package models

import (
	"encoding/json"
	"errors"
	"time"
)

// TenantStatus 租户状态常量
const (
	TenantStatusActive   = true  // 激活状态
	TenantStatusInactive = false // 未激活状态
)

// Tenant 表示系统中的一个租户
type Tenant struct {
	ID           uint64     `gorm:"column:id;primaryKey;comment:租户唯一ID" json:"id"`
	Name         string     `gorm:"column:name;type:varchar(100);not null;comment:租户名称" json:"name"`
	Domain       string     `gorm:"column:domain;type:varchar(100);unique;not null;comment:租户域名" json:"domain"`
	Code         string     `gorm:"column:code;type:varchar(50);unique;comment:租户编码" json:"code"`
	Description  string     `gorm:"column:description;type:varchar(255);comment:租户描述" json:"description"`
	Logo         string     `gorm:"column:logo;type:varchar(255);comment:租户Logo" json:"logo"`
	ContactName  string     `gorm:"column:contact_name;type:varchar(50);comment:联系人姓名" json:"contactName"`
	ContactEmail string     `gorm:"column:contact_email;type:varchar(100);comment:联系人邮箱" json:"contactEmail"`
	ContactPhone string     `gorm:"column:contact_phone;type:varchar(20);comment:联系人电话" json:"contactPhone"`
	Address      string     `gorm:"column:address;type:varchar(255);comment:联系地址" json:"address"`
	MaxUsers     int        `gorm:"column:max_users;type:int;default:10;comment:最大用户数" json:"maxUsers"`
	IsActive     bool       `gorm:"column:is_active;type:bool;default:true;comment:是否激活" json:"isActive"`
	ExpiredAt    time.Time  `gorm:"column:expired_at;type:datetime;comment:过期时间" json:"expiredAt"`
	Data         string     `gorm:"column:data;type:text;comment:额外数据JSON" json:"data"`
	CreatedAt    time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt    time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	DeletedAt    *time.Time `gorm:"column:deleted_at;index;comment:删除时间" json:"deletedAt"`
}

// TableName 指定表名
func (Tenant) TableName() string {
	return "sys_tenants"
}

// NewTenant 创建一个新的租户实例
func NewTenant(name, domain, code string) *Tenant {
	now := time.Now()
	return &Tenant{
		Name:      name,
		Domain:    domain,
		Code:      code,
		IsActive:  TenantStatusActive,
		MaxUsers:  10,                   // 默认最大用户数
		ExpiredAt: now.AddDate(1, 0, 0), // 默认过期时间为一年后
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// UpdateInfo 更新租户基本信息
func (t *Tenant) UpdateInfo(name, domain, code, description string) {
	t.Name = name
	t.Domain = domain
	t.Code = code
	t.Description = description
	t.UpdatedAt = time.Now()
}

// UpdateContact 更新租户联系信息
func (t *Tenant) UpdateContact(contactName, contactEmail, contactPhone, address string) {
	t.ContactName = contactName
	t.ContactEmail = contactEmail
	t.ContactPhone = contactPhone
	t.Address = address
	t.UpdatedAt = time.Now()
}

// SetExpiration 设置租户过期时间
func (t *Tenant) SetExpiration(expiredAt time.Time) {
	t.ExpiredAt = expiredAt
	t.UpdatedAt = time.Now()
}

// SetMaxUsers 设置租户最大用户数
func (t *Tenant) SetMaxUsers(maxUsers int) {
	t.MaxUsers = maxUsers
	t.UpdatedAt = time.Now()
}

// SetLogo 设置租户Logo
func (t *Tenant) SetLogo(logo string) {
	t.Logo = logo
	t.UpdatedAt = time.Now()
}

// Activate 激活租户
func (t *Tenant) Activate() {
	t.IsActive = TenantStatusActive
	t.UpdatedAt = time.Now()
}

// Deactivate 停用租户
func (t *Tenant) Deactivate() {
	t.IsActive = TenantStatusInactive
	t.UpdatedAt = time.Now()
}

// IsExpired 检查租户是否已过期
func (t *Tenant) IsExpired() bool {
	return time.Now().After(t.ExpiredAt)
}

// ExtendExpiration 延长租户过期时间
func (t *Tenant) ExtendExpiration(months int) {
	t.ExpiredAt = t.ExpiredAt.AddDate(0, months, 0)
	t.UpdatedAt = time.Now()
}

// GetDataMap 获取额外数据的Map形式
func (t *Tenant) GetDataMap() (map[string]interface{}, error) {
	if t.Data == "" {
		return make(map[string]interface{}), nil
	}

	var dataMap map[string]interface{}
	err := json.Unmarshal([]byte(t.Data), &dataMap)
	return dataMap, err
}

// SetDataMap 设置额外数据
func (t *Tenant) SetDataMap(dataMap map[string]interface{}) error {
	data, err := json.Marshal(dataMap)
	if err != nil {
		return err
	}

	t.Data = string(data)
	t.UpdatedAt = time.Now()
	return nil
}

// SetDataValue 设置单个额外数据值
func (t *Tenant) SetDataValue(key string, value interface{}) error {
	dataMap, err := t.GetDataMap()
	if err != nil {
		return err
	}

	dataMap[key] = value
	return t.SetDataMap(dataMap)
}

// GetDataValue 获取单个额外数据值
func (t *Tenant) GetDataValue(key string) (interface{}, error) {
	dataMap, err := t.GetDataMap()
	if err != nil {
		return nil, err
	}

	value, exists := dataMap[key]
	if !exists {
		return nil, errors.New("key not found in tenant data")
	}

	return value, nil
}

// Validate 验证租户数据是否有效
func (t *Tenant) Validate() error {
	if t.Name == "" {
		return errors.New("租户名称不能为空")
	}
	if t.Domain == "" {
		return errors.New("租户域名不能为空")
	}
	if t.Code == "" {
		return errors.New("租户编码不能为空")
	}
	if t.MaxUsers <= 0 {
		return errors.New("最大用户数必须大于0")
	}
	return nil
}
