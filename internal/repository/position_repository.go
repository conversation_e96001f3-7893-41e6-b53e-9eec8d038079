package repository

import (
	"context"
	"errors"
	"gin/internal/infrastructure/model"
	"gin/internal/models"
	"reflect"
	"time"

	"gorm.io/gorm"
)

// PositionRepo 岗位仓库接口
type PositionRepo interface {
	BaseRepository
	// 高级查询
	FindByDeptID(ctx context.Context, deptID uint64) ([]*models.Position, error)
	FindByStatus(ctx context.Context, status int8) ([]*models.Position, error)
	FindByType(ctx context.Context, posType int8) ([]*models.Position, error)
	FindByLevel(ctx context.Context, level int8) ([]*models.Position, error)
	FindByCode(ctx context.Context, code string) (*models.Position, error)

	// 批量操作
	BatchCreate(ctx context.Context, positions []*models.Position) error
	BatchDelete(ctx context.Context, ids []uint64) error

	// 统计
	CountByDeptID(ctx context.Context, deptID uint64) (int64, error)
	CountByStatus(ctx context.Context, status int8) (int64, error)

	// 验证
	ValidatePosition(position *models.Position) error
}

// positionRepoImpl 岗位仓库实现
type positionRepoImpl struct {
	*baseRepositoryImpl
}

// NewPositionRepository 创建岗位仓库
func NewPositionRepository(db *gorm.DB) PositionRepo {
	queryBuilder := createPositionQueryBuilder()
	baseRepo := NewBaseRepository(db, reflect.TypeOf(models.Position{}), queryBuilder, "sys_positions", "id")
	return &positionRepoImpl{
		baseRepositoryImpl: baseRepo.(*baseRepositoryImpl),
	}
}

// createPositionQueryBuilder 创建岗位查询构建器
func createPositionQueryBuilder() *model.QueryBuilder {
	qb := model.NewQueryBuilder()
	return qb.RegisterFields(map[string]model.QueryField{
		"id":        {Column: "id", Operation: model.OpEq, ValueType: model.TypeInt},
		"dept_id":   {Column: "dept_id", Operation: model.OpEq, ValueType: model.TypeInt},
		"tenant_id": {Column: "tenant_id", Operation: model.OpEq, ValueType: model.TypeInt},
		"name":      {Column: "name", Operation: model.OpLike, ValueType: model.TypeString},
		"code":      {Column: "code", Operation: model.OpEq, ValueType: model.TypeString},
		"type":      {Column: "type", Operation: model.OpEq, ValueType: model.TypeInt},
		"level":     {Column: "level", Operation: model.OpEq, ValueType: model.TypeInt},
		"status":    {Column: "status", Operation: model.OpEq, ValueType: model.TypeInt},
	})
}

// Create 创建岗位
func (r *positionRepoImpl) Create(entity interface{}) error {
	position, ok := entity.(*models.Position)
	if !ok {
		return errors.New("实体类型错误")
	}

	// 验证岗位数据
	if err := r.ValidatePosition(position); err != nil {
		return err
	}

	// 检查编码是否已存在
	var count int64
	err := r.DB().Model(&models.Position{}).Where("code = ? AND tenant_id = ?", position.Code, position.TenantID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("岗位编码已存在")
	}

	// 创建岗位
	return r.baseRepositoryImpl.Create(position)
}

// Update 更新岗位
func (r *positionRepoImpl) Update(entity interface{}) error {
	position, ok := entity.(*models.Position)
	if !ok {
		return errors.New("实体类型错误")
	}

	// 验证岗位数据
	if err := r.ValidatePosition(position); err != nil {
		return err
	}

	// 检查岗位是否存在
	var existingPosition models.Position
	err := r.DB().First(&existingPosition, position.ID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("岗位不存在")
		}
		return err
	}

	// 检查编码是否已被其他岗位使用
	var count int64
	err = r.DB().Model(&models.Position{}).Where("code = ? AND tenant_id = ? AND id != ?", position.Code, position.TenantID, position.ID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("岗位编码已被其他岗位使用")
	}

	// 更新岗位
	position.UpdatedAt = time.Now()
	return r.baseRepositoryImpl.Update(position)
}

// FindByDeptID 根据部门ID查询岗位
func (r *positionRepoImpl) FindByDeptID(ctx context.Context, deptID uint64) ([]*models.Position, error) {
	var positions []*models.Position
	err := r.WithContext(ctx).DB().Where("dept_id = ?", deptID).Find(&positions).Error
	return positions, err
}

// FindByStatus 根据状态查询岗位
func (r *positionRepoImpl) FindByStatus(ctx context.Context, status int8) ([]*models.Position, error) {
	var positions []*models.Position
	err := r.WithContext(ctx).DB().Where("status = ?", status).Find(&positions).Error
	return positions, err
}

// FindByType 根据类型查询岗位
func (r *positionRepoImpl) FindByType(ctx context.Context, posType int8) ([]*models.Position, error) {
	var positions []*models.Position
	err := r.WithContext(ctx).DB().Where("type = ?", posType).Find(&positions).Error
	return positions, err
}

// FindByLevel 根据级别查询岗位
func (r *positionRepoImpl) FindByLevel(ctx context.Context, level int8) ([]*models.Position, error) {
	var positions []*models.Position
	err := r.WithContext(ctx).DB().Where("level = ?", level).Find(&positions).Error
	return positions, err
}

// FindByCode 根据编码查询岗位
func (r *positionRepoImpl) FindByCode(ctx context.Context, code string) (*models.Position, error) {
	var position models.Position
	err := r.WithContext(ctx).DB().Where("code = ?", code).First(&position).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &position, nil
}

// BatchCreate 批量创建岗位
func (r *positionRepoImpl) BatchCreate(ctx context.Context, positions []*models.Position) error {
	// 验证岗位数据
	for _, position := range positions {
		if err := r.ValidatePosition(position); err != nil {
			return err
		}
	}

	// 批量创建岗位
	return r.WithContext(ctx).DB().Create(positions).Error
}

// BatchDelete 批量删除岗位
func (r *positionRepoImpl) BatchDelete(ctx context.Context, ids []uint64) error {
	return r.WithContext(ctx).DB().Delete(&models.Position{}, "id IN ?", ids).Error
}

// CountByDeptID 统计部门岗位数
func (r *positionRepoImpl) CountByDeptID(ctx context.Context, deptID uint64) (int64, error) {
	var count int64
	err := r.WithContext(ctx).DB().Model(&models.Position{}).Where("dept_id = ?", deptID).Count(&count).Error
	return count, err
}

// CountByStatus 统计状态岗位数
func (r *positionRepoImpl) CountByStatus(ctx context.Context, status int8) (int64, error) {
	var count int64
	err := r.WithContext(ctx).DB().Model(&models.Position{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

// ValidatePosition 验证岗位数据
func (r *positionRepoImpl) ValidatePosition(position *models.Position) error {
	if position.Name == "" {
		return errors.New("岗位名称不能为空")
	}
	if position.Code == "" {
		return errors.New("岗位编码不能为空")
	}
	if position.DeptID == 0 {
		return errors.New("所属部门不能为空")
	}
	return nil
}
