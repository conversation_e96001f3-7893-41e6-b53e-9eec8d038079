package repository

import (
	"context"
	"errors"
	"gin/internal/infrastructure/model"
	"gin/internal/models"
	"reflect"

	"gorm.io/gorm"
)

// RoleRepo 角色仓库接口
type RoleRepo interface {
	// BaseRepository 继承基础仓库接口
	BaseRepository

	FindByCode(ctx context.Context, code string, tenantID uint64) (*models.Role, error)
	FindByName(ctx context.Context, name string, tenantID uint64) (*models.Role, error)
	FindEnabledRoles(ctx context.Context, tenantID uint64) ([]*models.Role, error)
	FindSystemRoles(ctx context.Context) ([]*models.Role, error)
}

// roleRepoImpl 角色仓库实现
type roleRepoImpl struct {
	*baseRepositoryImpl
}

// NewRoleRepository 创建角色仓库
func NewRoleRepository(db *gorm.DB) RoleRepo {
	// 创建查询构建器
	queryBuilder := createRoleQueryBuilder()

	// 创建基础仓库
	baseRepo := NewBaseRepository(db, reflect.TypeOf(models.Role{}), queryBuilder, "role", "id")

	return &roleRepoImpl{
		baseRepositoryImpl: baseRepo.(*baseRepositoryImpl),
	}
}

func createRoleQueryBuilder() *model.QueryBuilder {
	qb := model.NewQueryBuilder()
	return qb.RegisterFields(map[string]model.QueryField{
		"name":       {Column: "name", Operation: model.OpLike, ValueType: model.TypeString},
		"code":       {Column: "code", Operation: model.OpLike, ValueType: model.TypeString},
		"status":     {Column: "status", Operation: model.OpEq, ValueType: model.TypeInt},
		"tenant_id":  {Column: "tenant_id", Operation: model.OpEq, ValueType: model.TypeInt},
		"data_scope": {Column: "data_scope", Operation: model.OpEq, ValueType: model.TypeInt},
		"is_system":  {Column: "is_system", Operation: model.OpEq, ValueType: model.TypeBool},
		"startTime":  {Column: "created_at", Operation: model.OpGte, ValueType: model.TypeTime},
		"endTime":    {Column: "created_at", Operation: model.OpLte, ValueType: model.TypeTime},
	})
}

// FindByCode 根据角色编码查找角色
func (r *roleRepoImpl) FindByCode(ctx context.Context, code string, tenantID uint64) (*models.Role, error) {
	var role models.Role
	err := r.db.Where("code = ? AND tenant_id = ?", code, tenantID).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &role, nil
}

// FindByName 根据角色名称查找角色
func (r *roleRepoImpl) FindByName(ctx context.Context, name string, tenantID uint64) (*models.Role, error) {
	var role models.Role
	err := r.db.Where("name = ? AND tenant_id = ?", name, tenantID).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &role, nil
}

// FindEnabledRoles 查找指定租户下所有启用的角色
func (r *roleRepoImpl) FindEnabledRoles(ctx context.Context, tenantID uint64) ([]*models.Role, error) {
	var roles []*models.Role
	err := r.db.Where("status = ? AND tenant_id = ?", models.RoleStatusEnabled, tenantID).Order("sort ASC").Find(&roles).Error
	if err != nil {
		return nil, err
	}
	return roles, nil
}

// FindSystemRoles 查找所有系统内置角色
func (r *roleRepoImpl) FindSystemRoles(ctx context.Context) ([]*models.Role, error) {
	var roles []*models.Role
	err := r.db.Where("is_system = ?", true).Find(&roles).Error
	if err != nil {
		return nil, err
	}
	return roles, nil
}
