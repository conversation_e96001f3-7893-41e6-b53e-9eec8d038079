package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// CategoryRepository 分类仓储接口
type CategoryRepository interface {
	base.Repository[models.Category]
}

// NewCategoryRepository 创建分类仓储
func NewCategoryRepository(db *gorm.DB) CategoryRepository {
	baseRepo := base.NewBaseRepository[models.Category](db)

	return &categoryRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// categoryRepositoryImpl 分类仓储实现
type categoryRepositoryImpl struct {
	base.Repository[models.Category]
	db *gorm.DB
}
