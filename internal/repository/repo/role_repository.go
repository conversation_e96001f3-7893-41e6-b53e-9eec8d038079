package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// RoleRepository 角色仓储接口
type RoleRepository interface {
	base.Repository[models.Role]

	// FindByCode 根据角色编码查找角色
	FindByCode(ctx context.Context, code string) (*models.Role, error)
	// FindByName 根据角色名称查找角色
	FindByName(ctx context.Context, name string) (*models.Role, error)
	// FindEnabledRoles 查找所有启用的角色
	FindEnabledRoles(ctx context.Context) ([]*models.Role, error)
	// FindSystemRoles 查找所有系统内置角色
	FindSystemRoles(ctx context.Context) ([]*models.Role, error)
}

// roleRepositoryImpl 角色仓储实现
type roleRepositoryImpl struct {
	base.Repository[models.Role]
	db *gorm.DB
}

// NewRoleRepository 创建角色仓储
func NewRoleRepository(db *gorm.DB) RoleRepository {
	baseRepo := base.NewBaseRepository[models.Role](db)

	return &roleRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByCode 根据角色编码查找角色
func (r *roleRepositoryImpl) FindByCode(ctx context.Context, code string) (*models.Role, error) {
	role := &models.Role{}
	err := r.db.WithContext(ctx).Where("code = ?", code).First(role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return role, nil
}

// FindByName 根据角色名称查找角色
func (r *roleRepositoryImpl) FindByName(ctx context.Context, name string) (*models.Role, error) {
	role := &models.Role{}
	err := r.db.WithContext(ctx).Where("name = ?", name).First(role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return role, nil
}

// FindEnabledRoles 查找所有启用的角色
func (r *roleRepositoryImpl) FindEnabledRoles(ctx context.Context) ([]*models.Role, error) {
	roles := make([]*models.Role, 0)
	err := r.db.WithContext(ctx).Where("status = ?", models.RoleStatusEnabled).Find(&roles).Error
	if err != nil {
		return nil, err
	}
	return roles, nil
}

// FindSystemRoles 查找所有系统内置角色
func (r *roleRepositoryImpl) FindSystemRoles(ctx context.Context) ([]*models.Role, error) {
	roles := make([]*models.Role, 0)
	err := r.db.WithContext(ctx).Where("is_system = ?", true).Find(&roles).Error
	if err != nil {
		return nil, err
	}
	return roles, nil
}
