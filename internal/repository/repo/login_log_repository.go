package repo

import (
	"context"
	"time"

	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// LoginLogRepository 登录日志仓储接口
type LoginLogRepository interface {
	base.Repository[models.LoginLog]
	// DeleteOldLogs 删除指定时间之前的日志
	DeleteOldLogs(ctx context.Context, beforeTime time.Time) error
}

// loginLogRepositoryImpl 登录日志仓储实现
type loginLogRepositoryImpl struct {
	base.Repository[models.LoginLog]
	db *gorm.DB
}

// NewLoginLogRepository 创建登录日志仓储
func NewLoginLogRepository(db *gorm.DB) LoginLogRepository {
	baseRepo := base.NewBaseRepository[models.LoginLog](db)

	return &loginLogRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// DeleteOldLogs 删除指定时间之前的日志
func (r *loginLogRepositoryImpl) DeleteOldLogs(ctx context.Context, beforeTime time.Time) error {
	return r.db.WithContext(ctx).Where("login_time < ?", beforeTime).Delete(&models.LoginLog{}).Error
}
