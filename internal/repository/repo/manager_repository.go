package repo

import (
	"context"
	"errors"

	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// ManagerRepository 管理员用户仓储接口
type ManagerRepository interface {
	base.Repository[models.Manager]

	// FindByUsername 根据用户名查询管理员
	FindByUsername(ctx context.Context, username string) (*models.Manager, error)
	// FindByEmail 根据邮箱查询管理员
	FindByEmail(ctx context.Context, email string) (*models.Manager, error)
}

// managerRepositoryImpl 管理员用户仓储实现
type managerRepositoryImpl struct {
	base.Repository[models.Manager]
	db *gorm.DB
}

// NewManagerRepository 创建管理员用户仓储
func NewManagerRepository(db *gorm.DB) ManagerRepository {
	baseRepo := base.NewBaseRepository[models.Manager](db)

	return &managerRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByUsername 根据用户名查询管理员
func (r *managerRepositoryImpl) FindByUsername(ctx context.Context, username string) (*models.Manager, error) {
	manager := &models.Manager{}
	err := r.db.WithContext(ctx).Where("username = ?", username).First(manager).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return manager, nil
}

// FindByEmail 根据邮箱查询管理员
func (r *managerRepositoryImpl) FindByEmail(ctx context.Context, email string) (*models.Manager, error) {
	manager := &models.Manager{}
	err := r.db.WithContext(ctx).Where("email = ?", email).First(manager).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return manager, nil
}
