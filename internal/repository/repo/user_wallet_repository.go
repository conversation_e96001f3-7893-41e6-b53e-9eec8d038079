package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// UserWalletRepository 用户钱包仓储接口
type UserWalletRepository interface {
	base.Repository[models.UserWallet]
	// FindByIDAndType 根据ID和类型查找用户钱包
	FindByIDAndType(ctx context.Context, id uint, walletType int8) (*models.UserWallet, error)
}

// userWalletRepositoryImpl 用户钱包仓储实现
type userWalletRepositoryImpl struct {
	base.Repository[models.UserWallet]
	db *gorm.DB
}

// NewUserWalletRepository 创建用户钱包仓储
func NewUserWalletRepository(db *gorm.DB) UserWalletRepository {
	baseRepo := base.NewBaseRepository[models.UserWallet](db)

	return &userWalletRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByIDAndType 根据ID和类型查找用户钱包
func (r *userWalletRepositoryImpl) FindByIDAndType(ctx context.Context, id uint, walletType int8) (*models.UserWallet, error) {
	var userWallet models.UserWallet
	err := r.db.WithContext(ctx).Where("id = ? AND type = ?", id, walletType).First(&userWallet).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &userWallet, nil
}
