package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// UserCertificationRepository 用户认证仓储接口
type UserCertificationRepository interface {
	base.Repository[models.UserCertification]
	// FindByUserID 根据用户ID查找认证
	FindByUserID(ctx context.Context, userID uint) (*models.UserCertification, error)
}

// userCertificationRepositoryImpl 用户认证仓储实现
type userCertificationRepositoryImpl struct {
	base.Repository[models.UserCertification]
	db *gorm.DB
}

// NewUserCertificationRepository 创建认证资产仓储
func NewUserCertificationRepository(db *gorm.DB) UserCertificationRepository {
	baseRepo := base.NewBaseRepository[models.UserCertification](db)

	return &userCertificationRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByUserID 根据用户ID查找认证
func (r *userCertificationRepositoryImpl) FindByUserID(ctx context.Context, userID uint) (*models.UserCertification, error) {
	var certification models.UserCertification
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&certification).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &certification, nil
}
