package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// ProductRepository 产品仓储接口
type ProductRepository interface {
	base.Repository[models.Product]
}

// NewProductRepository 创建产品仓储
func NewProductRepository(db *gorm.DB) ProductRepository {
	baseRepo := base.NewBaseRepository[models.Product](db)

	return &productRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// productRepositoryImpl 产品仓储实现
type productRepositoryImpl struct {
	base.Repository[models.Product]
	db *gorm.DB
}
