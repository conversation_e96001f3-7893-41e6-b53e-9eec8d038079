package repo

import (
	"context"
	"errors"
	"sort"

	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// MenuRepository 菜单仓储接口
type MenuRepository interface {
	base.Repository[models.Menu]

	// FindAllMenus 查询所有启用菜单
	FindAllMenus(ctx context.Context) ([]*models.Menu, error)
	// FindMenusByUserID 根据用户ID查询菜单（通过权限关联）
	FindMenusByUserID(ctx context.Context, userID uint) ([]*models.Menu, error)
	// FindMenusByPermissionCodes 根据权限代码查询菜单
	FindMenusByPermissionCodes(ctx context.Context, permCodes []string) ([]*models.Menu, error)
	// FindByCode 根据代码查询菜单
	FindByCode(ctx context.Context, code string) (*models.Menu, error)
	// FindByPath 根据路径查询菜单
	FindByPath(ctx context.Context, path string) (*models.Menu, error)
	// FindByName 根据名称查询菜单
	FindByName(ctx context.Context, name string) (*models.Menu, error)
	// FindChildrenByParentID 根据父级ID查询子菜单
	FindChildrenByParentID(ctx context.Context, parentID uint) ([]*models.Menu, error)
	// CreateIfNotExists 根据Code查询是否存在，不存在则创建
	CreateIfNotExists(ctx context.Context, menu *models.Menu) (*models.Menu, error)
}

// menuRepositoryImpl 菜单仓储实现
type menuRepositoryImpl struct {
	base.Repository[models.Menu]
	db *gorm.DB
}

// NewMenuRepository 创建菜单仓储
func NewMenuRepository(db *gorm.DB) MenuRepository {
	baseRepo := base.NewBaseRepository[models.Menu](db)
	return &menuRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindAllMenus 查询所有启用菜单
func (r *menuRepositoryImpl) FindAllMenus(ctx context.Context) ([]*models.Menu, error) {
	var menus []*models.Menu
	err := r.db.WithContext(ctx).
		Where("status = ?", models.MenuStatusEnabled).
		Order("sort ASC").
		Find(&menus).Error
	return menus, err
}

// FindMenusByUserID 根据用户ID查询菜单（通过权限关联）
func (r *menuRepositoryImpl) FindMenusByUserID(ctx context.Context, userID uint) ([]*models.Menu, error) {
	var menus []*models.Menu
	err := r.db.WithContext(ctx).
		Table("menus m").
		Select("DISTINCT m.*").
		Joins("INNER JOIN menu_permission mp ON m.id = mp.menu_id").
		Joins("INNER JOIN permissions p ON mp.permission_id = p.id").
		Joins("INNER JOIN casbin_rules cr ON (cr.v1 = p.code OR cr.v1 = p.api)").
		Where("m.status = ? AND p.status = ? AND cr.v0 IN (?)",
			models.MenuStatusEnabled,
			models.PermissionStatusEnabled,
			r.getUserRoles(ctx, userID)).
		Order("m.sort ASC").
		Find(&menus).Error
	return menus, err
}

// FindMenusByPermissionCodes 根据权限代码查询菜单
func (r *menuRepositoryImpl) FindMenusByPermissionCodes(ctx context.Context, permCodes []string) ([]*models.Menu, error) {
	if len(permCodes) == 0 {
		return []*models.Menu{}, nil
	}

	var menus []*models.Menu
	err := r.db.WithContext(ctx).
		Table("menu m").
		Select("DISTINCT m.*").
		Joins("INNER JOIN menu_permission mp ON m.id = mp.menu_id").
		Joins("INNER JOIN permission p ON mp.permission_id = p.id").
		Where("m.status = ? AND p.status = ? AND p.code IN (?)",
			models.MenuStatusEnabled,
			models.PermissionStatusEnabled,
			permCodes).
		Order("m.sort ASC").
		Find(&menus).Error

	// 递归查找所有父级菜单
	if len(menus) > 0 {
		parentMenus, err := r.findParentMenus(ctx, menus)
		if err != nil {
			return menus, nil // 忽略父级查询错误
		}
		menus = append(menus, parentMenus...)

		// 去重
		menuMap := make(map[uint]*models.Menu)
		for _, menu := range menus {
			menuMap[menu.ID] = menu
		}

		result := make([]*models.Menu, 0, len(menuMap))
		for _, menu := range menuMap {
			result = append(result, menu)
		}

		// 重新按sort字段排序，确保顺序一致
		sort.Slice(result, func(i, j int) bool {
			return result[i].Sort < result[j].Sort
		})

		return result, nil
	}

	return menus, err
}

// findParentMenus 递归查找父级菜单
func (r *menuRepositoryImpl) findParentMenus(ctx context.Context, menus []*models.Menu) ([]*models.Menu, error) {
	parentIDs := make([]uint, 0)
	for _, menu := range menus {
		if menu.ParentID > 0 {
			parentIDs = append(parentIDs, menu.ParentID)
		}
	}

	if len(parentIDs) == 0 {
		return []*models.Menu{}, nil
	}

	var parents []*models.Menu
	err := r.db.WithContext(ctx).
		Where("id IN (?) AND status = ?", parentIDs, models.MenuStatusEnabled).
		Order("sort ASC").
		Find(&parents).Error
	if err != nil {
		return []*models.Menu{}, err
	}

	// 递归查找更上级的父菜单
	grandParents, _ := r.findParentMenus(ctx, parents)
	return append(parents, grandParents...), nil
}

// getUserRoles 获取用户角色（这里简化实现，实际可能需要查询casbin）
func (r *menuRepositoryImpl) getUserRoles(ctx context.Context, userID uint) []string {
	// 这里应该从casbin中获取用户角色
	// 暂时返回空，在实际使用时需要集成casbin
	return []string{}
}

// FindByCode 根据代码查询菜单
func (r *menuRepositoryImpl) FindByCode(ctx context.Context, code string) (*models.Menu, error) {
	var menu models.Menu
	err := r.db.WithContext(ctx).Where("code = ?", code).First(&menu).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &menu, nil
}

// FindByPath 根据路径查询菜单
func (r *menuRepositoryImpl) FindByPath(ctx context.Context, path string) (*models.Menu, error) {
	var menu models.Menu
	err := r.db.WithContext(ctx).Where("path = ?", path).First(&menu).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &menu, nil
}

// FindByName 根据名称查询菜单
func (r *menuRepositoryImpl) FindByName(ctx context.Context, name string) (*models.Menu, error) {
	var menu models.Menu
	err := r.db.WithContext(ctx).Where("name = ?", name).First(&menu).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &menu, nil
}

// FindChildrenByParentID 根据父级ID查询子菜单
func (r *menuRepositoryImpl) FindChildrenByParentID(ctx context.Context, parentID uint) ([]*models.Menu, error) {
	var menus []*models.Menu
	err := r.db.WithContext(ctx).
		Where("parent_id = ? AND status = ?", parentID, models.MenuStatusEnabled).
		Order("sort ASC").
		Find(&menus).Error
	return menus, err
}

// CreateIfNotExists 根据Code查询是否存在，不存在则创建
func (r *menuRepositoryImpl) CreateIfNotExists(ctx context.Context, menu *models.Menu) (*models.Menu, error) {
	var existingMenu models.Menu

	// 使用 FirstOrCreate 方法：先查找，不存在则创建
	result := r.db.WithContext(ctx).Where("code = ?", menu.Code).FirstOrCreate(&existingMenu, menu)

	if result.Error != nil {
		return nil, result.Error
	}

	return &existingMenu, nil
}
