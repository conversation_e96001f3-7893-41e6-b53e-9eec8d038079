package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// PaymentRepository 支付仓储接口
type PaymentRepository interface {
	base.Repository[models.Payment]
	// FindByAssetIDAndMode 根据资产ID和模式查询支付
	FindByAssetIDAndMode(ctx context.Context, assetID uint, mode int8) (*models.Payment, error)
	// FindUserAccountByAssetID 根据资产ID查询用户账户数量
	FindUserAccountByAssetID(ctx context.Context, userID uint, assetID uint) ([]*models.UserAccount, error)
}

// paymentRepositoryImpl 支付仓储实现
type paymentRepositoryImpl struct {
	base.Repository[models.Payment]
	db *gorm.DB
}

// NewPaymentRepository 创建支付仓储
func NewPaymentRepository(db *gorm.DB) PaymentRepository {
	baseRepo := base.NewBaseRepository[models.Payment](db)

	return &paymentRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByAssetIDAndMode 根据资产ID和模式查询支付
func (r *paymentRepositoryImpl) FindByAssetIDAndMode(ctx context.Context, assetID uint, mode int8) (*models.Payment, error) {
	var payment models.Payment
	err := r.db.WithContext(ctx).Where("asset_id = ? AND mode = ?", assetID, mode).First(&payment).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &payment, nil
}

// FindUserAccountByAssetID 根据资产ID查询用户账户数量
func (r *paymentRepositoryImpl) FindUserAccountByAssetID(ctx context.Context, userID uint, assetID uint) ([]*models.UserAccount, error) {
	var userAccounts []*models.UserAccount
	err := r.db.WithContext(ctx).Where("user_id = ? AND asset_id = ?", userID, assetID).Find(&userAccounts).Error
	if err != nil {
		return nil, err
	}
	return userAccounts, nil
}
