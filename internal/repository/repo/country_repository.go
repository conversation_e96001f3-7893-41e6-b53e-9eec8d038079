package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// CountryRepository 国家仓储接口
type CountryRepository interface {
	base.Repository[models.Country]
}

// countryRepositoryImpl 国家仓储实现
type countryRepositoryImpl struct {
	base.Repository[models.Country]
	db *gorm.DB
}

// NewCountryRepository 创建国家仓储
func NewCountryRepository(db *gorm.DB) CountryRepository {
	baseRepo := base.NewBaseRepository[models.Country](db)

	return &countryRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
