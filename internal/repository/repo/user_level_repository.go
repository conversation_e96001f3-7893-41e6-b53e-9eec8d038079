package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// UserLevelRepository 用户等级仓储接口
type UserLevelRepository interface {
	base.Repository[models.UserLevel]
}

// userLevelRepositoryImpl 用户等级仓储实现
type userLevelRepositoryImpl struct {
	base.Repository[models.UserLevel]
	db *gorm.DB
}

// NewUserLevelRepository 创建用户等级仓储
func NewUserLevelRepository(db *gorm.DB) UserLevelRepository {
	baseRepo := base.NewBaseRepository[models.UserLevel](db)

	return &userLevelRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
