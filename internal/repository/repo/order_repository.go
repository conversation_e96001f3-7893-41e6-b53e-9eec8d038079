package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// OrderRepository 订单仓储接口
type OrderRepository interface {
	base.Repository[models.Order]
}

// NewOrderRepository 创建订单仓储
func NewOrderRepository(db *gorm.DB) OrderRepository {
	baseRepo := base.NewBaseRepository[models.Order](db)

	return &orderRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// orderRepositoryImpl 订单仓储实现
type orderRepositoryImpl struct {
	base.Repository[models.Order]
	db *gorm.DB
}
