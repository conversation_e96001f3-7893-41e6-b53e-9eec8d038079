package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// UserNoticeRepository 用户通知仓储接口
type UserNoticeRepository interface {
	base.Repository[models.UserNotice]
}

// userNoticeRepositoryImpl 用户通知仓储实现
type userNoticeRepositoryImpl struct {
	base.Repository[models.UserNotice]
	db *gorm.DB
}

// NewUserNoticeRepository 创建用户通知仓储
func NewUserNoticeRepository(db *gorm.DB) UserNoticeRepository {
	baseRepo := base.NewBaseRepository[models.UserNotice](db)

	return &userNoticeRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
