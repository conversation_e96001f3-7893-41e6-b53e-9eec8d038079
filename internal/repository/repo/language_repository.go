package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// LanguageRepository 语言仓储接口
type LanguageRepository interface {
	base.Repository[models.Language]
}

// languageRepositoryImpl 语言仓储实现
type languageRepositoryImpl struct {
	base.Repository[models.Language]
	db *gorm.DB
}

// NewLanguageRepository 创建语言仓储
func NewLanguageRepository(db *gorm.DB) LanguageRepository {
	baseRepo := base.NewBaseRepository[models.Language](db)

	return &languageRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
