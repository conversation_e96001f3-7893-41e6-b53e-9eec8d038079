package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// UserSettingRepository 用户设置仓储接口
type UserSettingRepository interface {
	base.Repository[models.UserSetting]
	// FindByUserIDAndField 根据用户ID和字段查找设置
	FindByUserIDAndField(ctx context.Context, userID uint, field string) (*models.UserSetting, error)
	// DeleteByUserIDAndField 根据用户ID和字段删除设置
	DeleteByUserIDAndField(ctx context.Context, userID uint, field string) error
}

// NewUserSettingRepository 创建用户设置仓储
func NewUserSettingRepository(db *gorm.DB) UserSettingRepository {
	baseRepo := base.NewBaseRepository[models.UserSetting](db)

	return &userSettingRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// userSettingRepositoryImpl 用户设置仓储实现
type userSettingRepositoryImpl struct {
	base.Repository[models.UserSetting]
	db *gorm.DB
}

// FindByUserIDAndField 根据用户ID和字段查找设置
func (r *userSettingRepositoryImpl) FindByUserIDAndField(ctx context.Context, userID uint, field string) (*models.UserSetting, error) {
	var setting models.UserSetting
	err := r.db.WithContext(ctx).Where("user_id = ? AND field = ?", userID, field).First(&setting).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &setting, nil
}

// DeleteByUserIDAndField 根据用户ID和字段删除设置
func (r *userSettingRepositoryImpl) DeleteByUserIDAndField(ctx context.Context, userID uint, field string) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND field = ?", userID, field).Delete(&models.UserSetting{}).Error
}
