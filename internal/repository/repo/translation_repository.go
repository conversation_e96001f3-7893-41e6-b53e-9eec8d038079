package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// TranslationRepository 翻译仓储接口
type TranslationRepository interface {
	base.Repository[models.Translation]
	// FindByKeyAndLang 根据键和语言查询翻译
	FindByKeyAndLang(ctx context.Context, key string, lang string) (*models.Translation, error)
	// UpdateByKeyAndLang 根据键和语言更新翻译
	UpdateByKeyAndLang(ctx context.Context, key string, value string, lang string) error
}

// translationRepositoryImpl 翻译仓储实现
type translationRepositoryImpl struct {
	base.Repository[models.Translation]
	db *gorm.DB
}

// NewTranslationRepository 创建翻译仓储
func NewTranslationRepository(db *gorm.DB) TranslationRepository {
	baseRepo := base.NewBaseRepository[models.Translation](db)

	return &translationRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByKeyAndLang 根据键和语言查询翻译
func (r *translationRepositoryImpl) FindByKeyAndLang(ctx context.Context, key string, lang string) (*models.Translation, error) {
	var translation models.Translation
	err := r.db.WithContext(ctx).Where("`key` = ? AND `lang` = ?", key, lang).First(&translation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &translation, nil
		}
		return nil, err
	}
	return &translation, nil
}

// UpdateByKeyAndLang 根据键和语言更新翻译
func (r *translationRepositoryImpl) UpdateByKeyAndLang(ctx context.Context, key string, value string, lang string) error {
	return r.db.WithContext(ctx).Model(&models.Translation{}).
		Where("`key` = ? AND `lang` = ?", key, lang).
		Update("value", value).Error
}
