package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// ArticleRepository 文章仓储接口
type ArticleRepository interface {
	base.Repository[models.Article]
}

// NewArticleRepository 创建文章仓储
func NewArticleRepository(db *gorm.DB) ArticleRepository {
	baseRepo := base.NewBaseRepository[models.Article](db)

	return &articleRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// articleRepositoryImpl 文章仓储实现
type articleRepositoryImpl struct {
	base.Repository[models.Article]
	db *gorm.DB
}
