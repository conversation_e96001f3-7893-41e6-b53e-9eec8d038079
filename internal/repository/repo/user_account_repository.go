package repo

import (
	"context"
	"errors"
	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// UserAccountRepository 用户账户仓储接口
type UserAccountRepository interface {
	base.Repository[models.UserAccount]
	// FindByIDAndUserID 根据ID和用户ID查询用户账户
	FindByIDAndUserID(ctx context.Context, id uint, userID uint) (*models.UserAccount, error)
	// FindByIDAndAssetID 根据ID和资产ID查询用户账户
	FindByIDAndAssetID(ctx context.Context, id uint, assetID uint) (*models.UserAccount, error)
	// FindByUserIDAndAssetID 根据用户ID和资产ID查询用户账户
	FindByUserIDAndAssetID(ctx context.Context, userID uint, assetID uint) (*models.UserAccount, error)
}

// userAccountRepositoryImpl 用户账户仓储实现
type userAccountRepositoryImpl struct {
	base.Repository[models.UserAccount]
	db *gorm.DB
}

// NewUserAccountRepository 创建用户账户仓储
func NewUserAccountRepository(db *gorm.DB) UserAccountRepository {
	baseRepo := base.NewBaseRepository[models.UserAccount](db)

	return &userAccountRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByIDAndUserID 根据ID和用户ID查询用户账户
func (r *userAccountRepositoryImpl) FindByIDAndUserID(ctx context.Context, id uint, userID uint) (*models.UserAccount, error) {
	var userAccount models.UserAccount
	err := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).First(&userAccount).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &userAccount, nil
}

// FindByIDAndAssetID 根据ID和资产ID查询用户账户
func (r *userAccountRepositoryImpl) FindByIDAndAssetID(ctx context.Context, id uint, assetID uint) (*models.UserAccount, error) {
	var userAccount models.UserAccount
	err := r.db.WithContext(ctx).Where("id = ? AND asset_id = ?", id, assetID).Where("status = ?", models.UserAccountStatusEnabled).First(&userAccount).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &userAccount, nil
}

// FindByUserIDAndAssetID 根据用户ID和资产ID查询用户账户
func (r *userAccountRepositoryImpl) FindByUserIDAndAssetID(ctx context.Context, userID uint, assetID uint) (*models.UserAccount, error) {
	var userAccount models.UserAccount
	err := r.db.WithContext(ctx).Where("user_id = ? AND asset_id = ?", userID, assetID).Where("status = ?", models.UserAccountStatusEnabled).First(&userAccount).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &userAccount, nil
}
