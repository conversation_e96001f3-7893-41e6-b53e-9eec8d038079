package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// LevelRepository 等级仓储接口
type LevelRepository interface {
	base.Repository[models.Level]
}

// NewLevelRepository 创建等级仓储
func NewLevelRepository(db *gorm.DB) LevelRepository {
	baseRepo := base.NewBaseRepository[models.Level](db)

	return &levelRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// levelRepositoryImpl 等级仓储实现
type levelRepositoryImpl struct {
	base.Repository[models.Level]
	db *gorm.DB
}
