package repo

import (
	"context"
	"errors"

	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// MenuPermissionRepository 菜单权限关联仓储接口
type MenuPermissionRepository interface {
	base.Repository[models.MenuPermission]

	// FindByMenuID 根据菜单ID查询权限关联
	FindByMenuID(ctx context.Context, menuID uint) ([]*models.MenuPermission, error)
	// FindByPermissionID 根据权限ID查询菜单关联
	FindByPermissionID(ctx context.Context, permissionID uint) ([]*models.MenuPermission, error)
	// FindByMenuAndPermission 根据菜单ID和权限ID查询关联
	FindByMenuAndPermission(ctx context.Context, menuID, permissionID uint) (*models.MenuPermission, error)
	// GetPermissionsByMenuID 根据菜单ID获取关联的权限列表
	GetPermissionsByMenuID(ctx context.Context, menuID uint) ([]*models.Permission, error)
	// GetMenusByPermissionID 根据权限ID获取关联的菜单列表
	GetMenusByPermissionID(ctx context.Context, permissionID uint) ([]*models.Menu, error)
	// DeleteByMenuID 根据菜单ID删除所有关联
	DeleteByMenuID(ctx context.Context, menuID uint) error
	// DeleteByPermissionID 根据权限ID删除所有关联
	DeleteByPermissionID(ctx context.Context, permissionID uint) error
	// DeleteByMenuAndPermission 删除指定的菜单权限关联
	DeleteByMenuAndPermission(ctx context.Context, menuID, permissionID uint) error
	// BatchCreateMenuPermissions 批量创建菜单权限关联
	BatchCreateMenuPermissions(ctx context.Context, menuID uint, permissionIDs []uint) error
	// UpdateMenuPermissions 更新菜单的权限关联（先删除再创建）
	UpdateMenuPermissions(ctx context.Context, menuID uint, permissionIDs []uint) error
	// CreateIfNotExists 根据菜单ID和权限ID查询是否存在，不存在则创建
	CreateIfNotExists(ctx context.Context, menuID, permissionID uint) (*models.MenuPermission, error)
}

// menuPermissionRepositoryImpl 菜单权限关联仓储实现
type menuPermissionRepositoryImpl struct {
	base.Repository[models.MenuPermission]
	db *gorm.DB
}

// NewMenuPermissionRepository 创建菜单权限关联仓储
func NewMenuPermissionRepository(db *gorm.DB) MenuPermissionRepository {
	baseRepo := base.NewBaseRepository[models.MenuPermission](db)
	return &menuPermissionRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByMenuID 根据菜单ID查询权限关联
func (r *menuPermissionRepositoryImpl) FindByMenuID(ctx context.Context, menuID uint) ([]*models.MenuPermission, error) {
	var relations []*models.MenuPermission
	err := r.db.WithContext(ctx).
		Where("menu_id = ?", menuID).
		Preload("Menu").
		Preload("Permission").
		Find(&relations).Error
	return relations, err
}

// FindByPermissionID 根据权限ID查询菜单关联
func (r *menuPermissionRepositoryImpl) FindByPermissionID(ctx context.Context, permissionID uint) ([]*models.MenuPermission, error) {
	var relations []*models.MenuPermission
	err := r.db.WithContext(ctx).
		Where("permission_id = ?", permissionID).
		Preload("Menu").
		Preload("Permission").
		Find(&relations).Error
	return relations, err
}

// FindByMenuAndPermission 根据菜单ID和权限ID查询关联
func (r *menuPermissionRepositoryImpl) FindByMenuAndPermission(ctx context.Context, menuID, permissionID uint) (*models.MenuPermission, error) {
	var relation models.MenuPermission
	err := r.db.WithContext(ctx).
		Where("menu_id = ? AND permission_id = ?", menuID, permissionID).
		First(&relation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &relation, nil
}

// GetPermissionsByMenuID 根据菜单ID获取关联的权限列表
func (r *menuPermissionRepositoryImpl) GetPermissionsByMenuID(ctx context.Context, menuID uint) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.db.WithContext(ctx).
		Table("permissions p").
		Select("p.*").
		Joins("INNER JOIN menu_permission mp ON p.id = mp.permission_id").
		Where("mp.menu_id = ? AND p.status = ?", menuID, 10). // 10 表示启用状态
		Order("mp.sort ASC, p.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetMenusByPermissionID 根据权限ID获取关联的菜单列表
func (r *menuPermissionRepositoryImpl) GetMenusByPermissionID(ctx context.Context, permissionID uint) ([]*models.Menu, error) {
	var menus []*models.Menu
	err := r.db.WithContext(ctx).
		Table("menus m").
		Select("m.*").
		Joins("INNER JOIN menu_permission mp ON m.id = mp.menu_id").
		Where("mp.permission_id = ? AND m.status = ?", permissionID, 10). // 10 表示启用状态
		Order("mp.sort ASC, m.sort ASC").
		Find(&menus).Error
	return menus, err
}

// DeleteByMenuID 根据菜单ID删除所有关联
func (r *menuPermissionRepositoryImpl) DeleteByMenuID(ctx context.Context, menuID uint) error {
	return r.db.WithContext(ctx).
		Where("menu_id = ?", menuID).
		Delete(&models.MenuPermission{}).Error
}

// DeleteByPermissionID 根据权限ID删除所有关联
func (r *menuPermissionRepositoryImpl) DeleteByPermissionID(ctx context.Context, permissionID uint) error {
	return r.db.WithContext(ctx).
		Where("permission_id = ?", permissionID).
		Delete(&models.MenuPermission{}).Error
}

// DeleteByMenuAndPermission 删除指定的菜单权限关联
func (r *menuPermissionRepositoryImpl) DeleteByMenuAndPermission(ctx context.Context, menuID, permissionID uint) error {
	return r.db.WithContext(ctx).
		Where("menu_id = ? AND permission_id = ?", menuID, permissionID).
		Delete(&models.MenuPermission{}).Error
}

// BatchCreateMenuPermissions 批量创建菜单权限关联
func (r *menuPermissionRepositoryImpl) BatchCreateMenuPermissions(ctx context.Context, menuID uint, permissionIDs []uint) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	relations := make([]*models.MenuPermission, 0, len(permissionIDs))
	for i, permID := range permissionIDs {
		relations = append(relations, &models.MenuPermission{
			MenuID:       menuID,
			PermissionID: permID,
			IsRequired:   true,
			Sort:         i + 1,
		})
	}

	return r.db.WithContext(ctx).Create(&relations).Error
}

// UpdateMenuPermissions 更新菜单的权限关联（先删除再创建）
func (r *menuPermissionRepositoryImpl) UpdateMenuPermissions(ctx context.Context, menuID uint, permissionIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 删除现有关联
		if err := tx.Where("menu_id = ?", menuID).Delete(&models.MenuPermission{}).Error; err != nil {
			return err
		}

		// 2. 如果没有新权限，直接返回
		if len(permissionIDs) == 0 {
			return nil
		}

		// 3. 创建新关联
		relations := make([]*models.MenuPermission, 0, len(permissionIDs))
		for i, permID := range permissionIDs {
			relations = append(relations, &models.MenuPermission{
				MenuID:       menuID,
				PermissionID: permID,
				IsRequired:   true,
				Sort:         i + 1,
			})
		}

		return tx.Create(&relations).Error
	})
}

// CreateIfNotExists 根据菜单ID和权限ID查询是否存在，不存在则创建
func (r *menuPermissionRepositoryImpl) CreateIfNotExists(ctx context.Context, menuID, permissionID uint) (*models.MenuPermission, error) {
	var existingRelation models.MenuPermission

	// 使用 FirstOrCreate 方法：先查找，不存在则创建
	// 需要先设置字段值，这样在创建时会使用这些值
	existingRelation.MenuID = menuID
	existingRelation.PermissionID = permissionID

	result := r.db.WithContext(ctx).
		Where("menu_id = ? AND permission_id = ?", menuID, permissionID).
		FirstOrCreate(&existingRelation)

	if result.Error != nil {
		return nil, result.Error
	}

	return &existingRelation, nil
}
