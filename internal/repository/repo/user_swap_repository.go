package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// UserSwapRepository 用户闪兑仓储接口
type UserSwapRepository interface {
	base.Repository[models.UserSwap]
}

// userSwapRepositoryImpl 用户闪兑仓储实现
type userSwapRepositoryImpl struct {
	base.Repository[models.UserSwap]
	db *gorm.DB
}

// NewUserSwapRepository 创建用户闪兑仓储
func NewUserSwapRepository(db *gorm.DB) UserSwapRepository {
	baseRepo := base.NewBaseRepository[models.UserSwap](db)

	return &userSwapRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
