package repo

import (
	"context"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
	"time"
)

// OperateLogRepository 操作日志仓储接口
type OperateLogRepository interface {
	base.Repository[models.OperateLog]
	// DeleteOldLogs 删除指定时间之前的日志
	DeleteOldLogs(ctx context.Context, beforeTime time.Time) error
	// FindUsernameByUserID 根据用户ID查询用户名
	FindUsernameByUserID(ctx context.Context, userID uint) (string, error)
	// FindUsernameByManagerID 根据管理员ID查询用户名
	FindUsernameByManagerID(ctx context.Context, managerID uint) (string, error)
}

// operateLogRepositoryImpl 操作日志仓储实现
type operateLogRepositoryImpl struct {
	base.Repository[models.OperateLog]
	db *gorm.DB
}

// NewOperateLogRepository 创建操作日志仓储
func NewOperateLogRepository(db *gorm.DB) OperateLogRepository {
	baseRepo := base.NewBaseRepository[models.OperateLog](db)

	return &operateLogRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// DeleteOldLogs 删除指定时间之前的日志
func (r *operateLogRepositoryImpl) DeleteOldLogs(ctx context.Context, beforeTime time.Time) error {
	return r.db.WithContext(ctx).Where("created_at < ?", beforeTime).Delete(&models.OperateLog{}).Error
}

// FindUsernameByUserID 根据用户ID查询用户名
func (r *operateLogRepositoryImpl) FindUsernameByUserID(ctx context.Context, userID uint) (string, error) {
	var username string
	err := r.db.WithContext(ctx).Model(&models.User{}).Select("username").Where("id = ?", userID).Scan(&username).Error
	return username, err
}

// FindUsernameByManagerID 根据管理员ID查询用户名
func (r *operateLogRepositoryImpl) FindUsernameByManagerID(ctx context.Context, managerID uint) (string, error) {
	var username string
	err := r.db.WithContext(ctx).Model(&models.Manager{}).Select("username").Where("id = ?", managerID).Scan(&username).Error
	return username, err
}
