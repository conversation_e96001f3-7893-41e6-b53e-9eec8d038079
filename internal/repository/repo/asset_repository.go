package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// AssetRepository 资产仓储接口
type AssetRepository interface {
	base.Repository[models.Asset]
}

// NewAssetRepository 创建资产仓储
func NewAssetRepository(db *gorm.DB) AssetRepository {
	baseRepo := base.NewBaseRepository[models.Asset](db)

	return &assetRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// assetRepositoryImpl 资产仓储实现
type assetRepositoryImpl struct {
	base.Repository[models.Asset]
	db *gorm.DB
}
