package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// UserTransferRepository 用户转账仓储接口
type UserTransferRepository interface {
	base.Repository[models.UserTransfer]
}

// userTransferRepositoryImpl 用户转账仓储实现
type userTransferRepositoryImpl struct {
	base.Repository[models.UserTransfer]
	db *gorm.DB
}

// NewUserTransferRepository 创建用户转账仓储
func NewUserTransferRepository(db *gorm.DB) UserTransferRepository {
	baseRepo := base.NewBaseRepository[models.UserTransfer](db)

	return &userTransferRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
