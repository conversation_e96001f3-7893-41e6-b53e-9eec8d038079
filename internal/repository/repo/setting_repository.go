package repo

import (
	"context"
	"gin/internal/models"
	"gin/internal/repository/base"
	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

// SettingRepository 系统设置仓储接口
type SettingRepository interface {
	base.Repository[models.Setting]
	// FindByField 按字段查询
	FindByField(ctx context.Context, field string) (*models.Setting, error)
	// GetFieldValues 获取字段值
	GetFieldValues(field models.SettingField, value interface{}) error
}

// settingRepositoryImpl 系统设置仓储实现
type settingRepositoryImpl struct {
	base.Repository[models.Setting]
	db *gorm.DB
}

// NewSettingRepository 创建系统设置仓储
func NewSettingRepository(db *gorm.DB) SettingRepository {
	baseRepo := base.NewBaseRepository[models.Setting](db)

	return &settingRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByField 根据字段查询应用设置
func (r *settingRepositoryImpl) FindByField(ctx context.Context, field string) (*models.Setting, error) {
	var setting models.Setting
	if err := r.db.WithContext(ctx).Where("field = ?", field).First(&setting).Error; err != nil {
		return nil, err
	}
	return &setting, nil
}

// GetFieldValues 获取字段值
func (r *settingRepositoryImpl) GetFieldValues(field models.SettingField, value interface{}) error {
	var setting models.Setting
	if err := r.db.Where("field = ?", field).Find(&setting).Error; err != nil {
		return err
	}

	switch setting.Type {
	case models.SettingTypeArray, models.SettingTypeObject:
		return json.Unmarshal([]byte(setting.Value), value)
	default:
		value = setting.Value
	}
	return nil
}
