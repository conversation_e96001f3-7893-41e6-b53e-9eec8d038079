package repo

import (
	"context"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	base.Repository[models.User]
	// FindByUsername 根据用户名查找用户
	FindByUsername(ctx context.Context, username string) (*models.User, error)
	// FindByEmail 根据邮箱查找用户
	FindByEmail(ctx context.Context, email string) (*models.User, error)
	// FindByTelephone 根据手机号查找用户
	FindByTelephone(ctx context.Context, telephone string) (*models.User, error)
	// FindByInviteCode 根据邀请码查找用户
	FindByInviteCode(ctx context.Context, inviteCode string) (*models.User, error)
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepositoryImpl{
		Repository: base.NewBaseRepository[models.User](db),
		db:         db,
	}
}

// userRepositoryImpl 用户仓储实现
type userRepositoryImpl struct {
	base.Repository[models.User]
	db *gorm.DB
}

// FindByUsername 根据用户名查找用户
func (r *userRepositoryImpl) FindByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByEmail 根据邮箱查找用户
func (r *userRepositoryImpl) FindByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByTelephone 根据手机号查找用户
func (r *userRepositoryImpl) FindByTelephone(ctx context.Context, telephone string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Where("telephone = ?", telephone).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByInviteCode 根据邀请码查找用户
func (r *userRepositoryImpl) FindByInviteCode(ctx context.Context, inviteCode string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Where("invite_code = ?", inviteCode).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}
