package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// UserBillRepository 用户账单仓储接口
type UserBillRepository interface {
	base.Repository[models.UserBill]
}

// userBillRepositoryImpl 用户账单仓储实现
type userBillRepositoryImpl struct {
	base.Repository[models.UserBill]
	db *gorm.DB
}

// NewUserBillRepository 创建用户账单仓储
func NewUserBillRepository(db *gorm.DB) UserBillRepository {
	baseRepo := base.NewBaseRepository[models.UserBill](db)

	return &userBillRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
