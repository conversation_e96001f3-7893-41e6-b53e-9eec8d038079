package repo

import (
	"context"
	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// UserAssetRepository 用户资产仓储接口
type UserAssetRepository interface {
	base.Repository[models.UserAsset]
	// FindByUserIDAndAssetIDWithCreate 根据用户ID和资产ID查询用户资产, 如果用户资产不存在, 那么创建用户资产
	FindByUserIDAndAssetIDWithCreate(ctx context.Context, user *models.User, assetID uint) (*models.UserAsset, error)
}

// userAssetRepositoryImpl 用户资产仓储实现
type userAssetRepositoryImpl struct {
	base.Repository[models.UserAsset]
	db *gorm.DB
}

// NewUserAssetRepository 创建用户资产仓储
func NewUserAssetRepository(db *gorm.DB) UserAssetRepository {
	baseRepo := base.NewBaseRepository[models.UserAsset](db)

	return &userAssetRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindByUserIDAndAssetIDWithCreate 根据用户ID和资产ID查询用户资产, 如果用户资产不存在, 那么创建用户资产
func (r *userAssetRepositoryImpl) FindByUserIDAndAssetIDWithCreate(ctx context.Context, user *models.User, assetID uint) (*models.UserAsset, error) {
	// 查询用户资产
	var userAsset models.UserAsset
	if err := r.db.WithContext(ctx).Where("user_id = ? AND asset_id = ?", user.ID, assetID).Find(&userAsset).Error; err != nil {
		return nil, err
	}

	// 如果用户资产不存在, 那么创建用户资产
	if userAsset.ID == 0 {
		userAsset = models.UserAsset{
			UserID:  user.ID,
			AssetID: assetID,
		}
		if err := r.db.WithContext(ctx).Create(&userAsset).Error; err != nil {
			return nil, err
		}
	}
	return &userAsset, nil
}
