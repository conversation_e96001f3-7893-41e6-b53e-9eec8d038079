package repo

import (
	"context"
	"errors"

	"gin/internal/models"
	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// PermissionRepository 权限仓储接口
type PermissionRepository interface {
	base.Repository[models.Permission]

	// FindAllByStatus 查询所有启用权限
	FindAllByStatus(ctx context.Context) ([]*models.Permission, error)
	// FindByCode 根据代码查询权限
	FindByCode(ctx context.Context, code string) (*models.Permission, error)
	// FindByAPI 根据API路径和方法查询权限
	FindByAPI(ctx context.Context, api string, method string) (*models.Permission, error)
	// FindByResource 根据资源标识查询权限
	FindByResource(ctx context.Context, resource string) ([]*models.Permission, error)
	// FindByName 根据名称查询权限
	FindByName(ctx context.Context, name string) (*models.Permission, error)
	// FindByAction 根据操作类型查询权限
	FindByAction(ctx context.Context, action string) ([]*models.Permission, error)
	// FindByResourceAndAction 根据资源和操作查询权限
	FindByResourceAndAction(ctx context.Context, resource, action string) (*models.Permission, error)
	// CreateIfNotExists 根据Code查询是否存在，不存在则创建
	CreateIfNotExists(ctx context.Context, permission *models.Permission) (*models.Permission, error)
}

// permissionRepositoryImpl 权限仓储实现
type permissionRepositoryImpl struct {
	base.Repository[models.Permission]
	db *gorm.DB
}

// NewPermissionRepository 创建权限仓储
func NewPermissionRepository(db *gorm.DB) PermissionRepository {
	baseRepo := base.NewBaseRepository[models.Permission](db)
	return &permissionRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// FindAllByStatus 查询所有启用权限
func (r *permissionRepositoryImpl) FindAllByStatus(ctx context.Context) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.db.WithContext(ctx).
		Where("status = ?", models.PermissionStatusEnabled).
		Order("id ASC").
		Find(&permissions).Error
	return permissions, err
}

// FindByCode 根据代码查询权限
func (r *permissionRepositoryImpl) FindByCode(ctx context.Context, code string) (*models.Permission, error) {
	var permission models.Permission
	err := r.db.WithContext(ctx).Where("code = ?", code).First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

// FindByAPI 根据API路径和方法查询权限
func (r *permissionRepositoryImpl) FindByAPI(ctx context.Context, api string, method string) (*models.Permission, error) {
	var permission models.Permission
	err := r.db.WithContext(ctx).Where("api = ? AND method = ?", api, method).First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

// FindByResource 根据资源标识查询权限
func (r *permissionRepositoryImpl) FindByResource(ctx context.Context, resource string) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.db.WithContext(ctx).
		Where("resource = ? AND status = ?", resource, models.PermissionStatusEnabled).
		Order("action ASC").
		Find(&permissions).Error
	return permissions, err
}

// FindByName 根据名称查询权限
func (r *permissionRepositoryImpl) FindByName(ctx context.Context, name string) (*models.Permission, error) {
	var permission models.Permission
	err := r.db.WithContext(ctx).Where("name = ?", name).First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

// FindByAction 根据操作类型查询权限
func (r *permissionRepositoryImpl) FindByAction(ctx context.Context, action string) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.db.WithContext(ctx).
		Where("action = ? AND status = ?", action, models.PermissionStatusEnabled).
		Order("resource ASC").
		Find(&permissions).Error
	return permissions, err
}

// FindByResourceAndAction 根据资源和操作查询权限
func (r *permissionRepositoryImpl) FindByResourceAndAction(ctx context.Context, resource, action string) (*models.Permission, error) {
	var permission models.Permission
	err := r.db.WithContext(ctx).
		Where("resource = ? AND action = ?", resource, action).
		First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

// CreateIfNotExists 根据Code查询是否存在，不存在则创建
func (r *permissionRepositoryImpl) CreateIfNotExists(ctx context.Context, permission *models.Permission) (*models.Permission, error) {
	var existingPermission models.Permission

	// 使用 FirstOrCreate 方法：先查找，不存在则创建
	result := r.db.WithContext(ctx).Where("code = ?", permission.Code).FirstOrCreate(&existingPermission, permission)

	if result.Error != nil {
		return nil, result.Error
	}

	return &existingPermission, nil
}
