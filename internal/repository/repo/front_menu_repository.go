package repo

import (
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// FrontMenuRepository 前台菜单仓储接口
type FrontMenuRepository interface {
	base.Repository[models.FrontMenu]
}

// frontMenuRepositoryImpl 前台菜单仓储实现
type frontMenuRepositoryImpl struct {
	base.Repository[models.FrontMenu]
	db *gorm.DB
}

// NewFrontMenuRepository 创建前台菜单仓储
func NewFrontMenuRepository(db *gorm.DB) FrontMenuRepository {
	baseRepo := base.NewBaseRepository[models.FrontMenu](db)

	return &frontMenuRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}
