package repo

import (
	"context"
	"gin/internal/models"
	"gin/internal/repository/base"
	"gorm.io/gorm"
)

// ManagerNoticeRepository 管理员通知仓储接口
type ManagerNoticeRepository interface {
	base.Repository[models.ManagerNotice]
	// MarkAsRead 标记管理员通知为已读
	MarkAsRead(ctx context.Context, id uint) error
}

// managerNoticeRepositoryImpl 管理员通知仓储实现
type managerNoticeRepositoryImpl struct {
	base.Repository[models.ManagerNotice]
	db *gorm.DB
}

// NewManagerNoticeRepository 创建管理员通知仓储
func NewManagerNoticeRepository(db *gorm.DB) ManagerNoticeRepository {
	baseRepo := base.NewBaseRepository[models.ManagerNotice](db)

	return &managerNoticeRepositoryImpl{
		Repository: baseRepo,
		db:         db,
	}
}

// MarkAsRead 标记管理员通知为已读
func (r *managerNoticeRepositoryImpl) MarkAsRead(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.ManagerNotice{}).
		Where("id = ?", id).
		Update("is_read", true).Error
}
