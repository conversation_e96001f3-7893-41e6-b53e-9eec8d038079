package base

import (
	"context"
	"time"

	"gorm.io/gorm"
)

// Repository 基础仓库接口 - 使用泛型提供类型安全
type Repository[T any] interface {

	// Create 创建记录
	Create(ctx context.Context, entity *T) error
	// Update 更新记录
	Update(ctx context.Context, entity *T) error
	// Delete 软删除记录
	Delete(ctx context.Context, id uint) error
	// HardDelete 硬删除记录
	HardDelete(ctx context.Context, id uint) error
	// Restore 恢复软删除的记录
	Restore(ctx context.Context, id uint) error

	// FindByID 根据ID查找记录
	FindByID(ctx context.Context, id uint) (*T, error)
	// FindAll 查找所有记录
	FindAll(ctx context.Context) ([]*T, error)
	// FindWithQuery 使用查询构建器查找记录
	FindWithQuery(ctx context.Context, qb QueryBuilder) ([]*T, error)
	// FindWithPagination 使用查询构建器进行分页查找
	FindWithPagination(ctx context.Context, qb QueryBuilder) (*PaginationResult[T], error)

	// BulkCreate 批量创建记录
	BulkCreate(ctx context.Context, entities []*T) error
	// BulkUpdate 批量更新记录
	BulkUpdate(ctx context.Context, entities []*T) error
	// BulkDelete 批量软删除记录
	BulkDelete(ctx context.Context, ids []uint) error
	// BulkHardDelete 批量硬删除记录
	BulkHardDelete(ctx context.Context, ids []uint) error

	// Count 计算记录数
	Count(ctx context.Context, qb QueryBuilder) (int64, error)
	// Exists 检查记录是否存在
	Exists(ctx context.Context, id uint) (bool, error)

	// WithPreload 预加载和关联
	WithPreload(relations ...string) Repository[T]

	// DB 获取底层DB（用于复杂查询）
	DB() *gorm.DB
}

// QueryBuilder 查询构建器接口
type QueryBuilder interface {
	Build(db *gorm.DB) *gorm.DB
	ApplyPagination(db *gorm.DB) *gorm.DB
	GetPagination() *Pagination
	Clone() QueryBuilder
}

// PaginationResult 分页结果
type PaginationResult[T any] struct {
	Data     []*T  `json:"data"`
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
	Pages    int   `json:"pages"`
}

// Pagination 分页参数
type Pagination struct {
	Page     int  `json:"page"`
	PageSize int  `json:"page_size"`
	Enabled  bool `json:"enabled"`
}

// RepositoryConfig 仓库配置
type RepositoryConfig struct {
	CacheEnabled    bool          `json:"cache_enabled"`
	StrictMode      bool          `json:"strict_mode"`
	DefaultPageSize int           `json:"default_page_size"`
	MaxPageSize     int           `json:"max_page_size"`
	TimeoutDuration time.Duration `json:"timeout_duration"`
}

// DefaultConfig 默认配置
var DefaultConfig = &RepositoryConfig{
	CacheEnabled:    false,
	StrictMode:      false,
	DefaultPageSize: 10,
	MaxPageSize:     1000,
	TimeoutDuration: 30 * time.Second,
}
