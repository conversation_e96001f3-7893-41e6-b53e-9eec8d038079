package base

import (
	"context"
	"gorm.io/gorm"
)

// Manager 简化的事务管理器
type Manager struct {
	db *gorm.DB
}

// New 创建事务管理器
func New(db *gorm.DB) *Manager {
	return &Manager{db: db}
}

// contextKey 用于在context中存储事务的key
type contextKey string

const txKey contextKey = "transaction"

// Execute 执行事务
func (m *Manager) Execute(ctx context.Context, fn func(ctx context.Context) error) error {
	// 如果已经在事务中，直接执行函数
	if m.IsInTransaction(ctx) {
		return fn(ctx)
	}

	// 开启新事务
	return m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 将事务存储到context中
		txCtx := context.WithValue(ctx, txKey, tx)
		return fn(txCtx)
	})
}

// GetDB 获取当前上下文的数据库连接
func (m *Manager) GetDB(ctx context.Context) *gorm.DB {
	if tx, ok := ctx.Value(txKey).(*gorm.DB); ok {
		return tx
	}
	return m.db
}

// IsInTransaction 检查当前是否在事务中
func (m *Manager) IsInTransaction(ctx context.Context) bool {
	_, ok := ctx.Value(txKey).(*gorm.DB)
	return ok
}
