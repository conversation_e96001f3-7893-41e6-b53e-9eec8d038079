package base

import (
	"context"
	"errors"
	"fmt"
	"math"
	"reflect"

	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// baseRepositoryImpl BaseRepository的通用实现
type baseRepositoryImpl[T any] struct {
	db              *gorm.DB
	modelType       reflect.Type
	tableName       string
	primaryKeyField string
	preloads        []string
	config          *RepositoryConfig
	txManager       *Manager
}

// NewBaseRepository 创建新的BaseRepository实例
func NewBaseRepository[T any](db *gorm.DB, config ...*RepositoryConfig) Repository[T] {
	var cfg *RepositoryConfig
	if len(config) > 0 && config[0] != nil {
		cfg = config[0]
	} else {
		cfg = DefaultConfig
	}

	var model T
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	// 获取表名（假设有 TableName 方法或使用默认命名）
	tableName := getTableName(model)

	return &baseRepositoryImpl[T]{
		db:              db,
		modelType:       modelType,
		tableName:       tableName,
		primaryKeyField: "id", // 默认主键字段
		config:          cfg,
		txManager:       New(db),
	}
}

// getTableName 获取表名
func getTableName[T any](model T) string {
	// 尝试调用 TableName 方法
	if tn, ok := any(model).(interface{ TableName() string }); ok {
		return tn.TableName()
	}

	// 使用默认命名规则 - 将结构体名转为蛇形命名并复数化
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	// 使用 GORM 的命名策略
	name := schema.NamingStrategy{}
	return name.TableName(modelType.Name())
}

// Create 创建记录
func (r *baseRepositoryImpl[T]) Create(ctx context.Context, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}

	db := r.getDB(ctx)
	return db.WithContext(ctx).Create(entity).Error
}

// Update 更新记录
func (r *baseRepositoryImpl[T]) Update(ctx context.Context, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}

	db := r.getDB(ctx)
	return db.WithContext(ctx).Save(entity).Error
}

// Delete 软删除记录
func (r *baseRepositoryImpl[T]) Delete(ctx context.Context, id uint) error {
	var model T
	db := r.getDB(ctx)
	return db.WithContext(ctx).Delete(&model, id).Error
}

// HardDelete 硬删除记录
func (r *baseRepositoryImpl[T]) HardDelete(ctx context.Context, id uint) error {
	var model T
	db := r.getDB(ctx)
	return db.WithContext(ctx).Unscoped().Delete(&model, id).Error
}

// Restore 恢复软删除的记录
func (r *baseRepositoryImpl[T]) Restore(ctx context.Context, id uint) error {
	var model T
	db := r.getDB(ctx)
	return db.WithContext(ctx).Model(&model).
		Unscoped().
		Where(r.primaryKeyField+" = ?", id).
		Update("deleted_at", nil).Error
}

// FindByID 根据ID查询记录
func (r *baseRepositoryImpl[T]) FindByID(ctx context.Context, id uint) (*T, error) {
	var entity T
	db := r.db.WithContext(ctx)

	// 应用预加载
	db = r.applyPreloads(db)

	err := db.First(&entity, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &entity, nil
}

// FindAll 查询所有记录
func (r *baseRepositoryImpl[T]) FindAll(ctx context.Context) ([]*T, error) {
	var entities []*T
	db := r.db.WithContext(ctx)

	// 应用预加载
	db = r.applyPreloads(db)

	err := db.Find(&entities).Error
	return entities, err
}

// FindWithQuery 使用查询构建器查询
func (r *baseRepositoryImpl[T]) FindWithQuery(ctx context.Context, qb QueryBuilder) ([]*T, error) {
	var entities []*T
	db := qb.Build(r.db.WithContext(ctx))

	// 应用预加载
	db = r.applyPreloads(db)

	err := db.Find(&entities).Error
	return entities, err
}

// FindWithPagination 分页查询
func (r *baseRepositoryImpl[T]) FindWithPagination(ctx context.Context, qb QueryBuilder) (*PaginationResult[T], error) {
	var entities []*T
	var total int64

	// 构建查询
	baseDB := qb.Build(r.db.WithContext(ctx))

	// 计算总数
	var model T
	countDB := baseDB.Model(&model)
	if err := countDB.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count records: %w", err)
	}

	// 应用分页
	db := qb.ApplyPagination(baseDB)

	// 应用预加载
	db = r.applyPreloads(db)

	// 执行查询
	if err := db.Find(&entities).Error; err != nil {
		return nil, fmt.Errorf("failed to find records: %w", err)
	}

	// 构建分页结果
	pagination := qb.GetPagination()
	pages := int(math.Ceil(float64(total) / float64(pagination.PageSize)))

	return &PaginationResult[T]{
		Data:     entities,
		Total:    total,
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
		Pages:    pages,
	}, nil
}

// BulkCreate 批量创建
func (r *baseRepositoryImpl[T]) BulkCreate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).CreateInBatches(entities, 100).Error
}

// BulkUpdate 批量更新
func (r *baseRepositoryImpl[T]) BulkUpdate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, entity := range entities {
			if err := tx.Save(entity).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BulkDelete 批量删除
func (r *baseRepositoryImpl[T]) BulkDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	var model T
	return r.db.WithContext(ctx).Delete(&model, ids).Error
}

// BulkHardDelete 批量硬删除
func (r *baseRepositoryImpl[T]) BulkHardDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	var model T
	return r.db.WithContext(ctx).Unscoped().Delete(&model, ids).Error
}

// Count 统计记录数
func (r *baseRepositoryImpl[T]) Count(ctx context.Context, qb QueryBuilder) (int64, error) {
	var total int64
	var model T

	db := qb.Build(r.db.WithContext(ctx))
	err := db.Model(&model).Count(&total).Error

	return total, err
}

// Exists 检查记录是否存在
func (r *baseRepositoryImpl[T]) Exists(ctx context.Context, id uint) (bool, error) {
	var count int64
	var model T

	err := r.db.WithContext(ctx).Model(&model).Where(r.primaryKeyField+" = ?", id).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// WithPreload 设置预加载
func (r *baseRepositoryImpl[T]) WithPreload(relations ...string) Repository[T] {
	clone := *r
	clone.preloads = append(clone.preloads, relations...)
	return &clone
}

// DB 获取数据库连接
func (r *baseRepositoryImpl[T]) DB() *gorm.DB {
	return r.db
}

// applyPreloads 应用预加载
func (r *baseRepositoryImpl[T]) applyPreloads(db *gorm.DB) *gorm.DB {
	for _, relation := range r.preloads {
		db = db.Preload(relation)
	}
	return db
}

// getDB 获取数据库连接（事务感知）
func (r *baseRepositoryImpl[T]) getDB(ctx context.Context) *gorm.DB {
	if r.txManager != nil {
		return r.txManager.GetDB(ctx)
	}
	return r.db
}
