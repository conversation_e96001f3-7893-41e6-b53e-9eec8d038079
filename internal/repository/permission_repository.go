package repository

import (
	"context"
	"gin/internal/infrastructure/model"
	"gin/internal/models"
	"reflect"

	"gorm.io/gorm"
)

// PermissionRepo 权限仓库接口
type PermissionRepo interface {
	BaseRepository
	FindAllMenus(ctx context.Context) ([]*models.Permission, error)
	FindMenusByPermissions(ctx context.Context, permCodes []string) ([]*models.Permission, error)
	FindByCode(ctx context.Context, code string) (*models.Permission, error)
	FindByAPI(ctx context.Context, api string, method string) (*models.Permission, error)
	FindByParentID(ctx context.Context, parentID uint64) ([]*models.Permission, error)
	FindByType(ctx context.Context, permType int) ([]*models.Permission, error)
	FindByRoleID(ctx context.Context, roleID uint64) ([]*models.Permission, error)
	BatchCreate(ctx context.Context, permissions []*models.Permission) error
	BatchDelete(ctx context.Context, ids []uint64) error
}

// permissionRepoImpl 权限仓库实现
type permissionRepoImpl struct {
	*baseRepositoryImpl
}

// NewPermissionRepository 创建权限仓库
func NewPermissionRepository(db *gorm.DB) PermissionRepo {
	queryBuilder := createPermissionQueryBuilder()
	baseRepo := NewBaseRepository(db, reflect.TypeOf(models.Permission{}), queryBuilder, "sys_permissions", "id")
	return &permissionRepoImpl{
		baseRepositoryImpl: baseRepo.(*baseRepositoryImpl),
	}
}

// createPermissionQueryBuilder 创建权限查询构建器
func createPermissionQueryBuilder() *model.QueryBuilder {
	qb := model.NewQueryBuilder()
	return qb.RegisterFields(map[string]model.QueryField{
		"id":        {Column: "id", Operation: model.OpEq, ValueType: model.TypeInt},
		"parent_id": {Column: "parent_id", Operation: model.OpEq, ValueType: model.TypeInt},
		"name":      {Column: "name", Operation: model.OpLike, ValueType: model.TypeString},
		"code":      {Column: "code", Operation: model.OpEq, ValueType: model.TypeString},
		"type":      {Column: "type", Operation: model.OpEq, ValueType: model.TypeInt},
		"status":    {Column: "status", Operation: model.OpEq, ValueType: model.TypeInt},
		"api":       {Column: "api", Operation: model.OpEq, ValueType: model.TypeString},
		"method":    {Column: "method", Operation: model.OpEq, ValueType: model.TypeString},
	})
}

// FindAllMenus 查询所有菜单
func (r *permissionRepoImpl) FindAllMenus(ctx context.Context) ([]*models.Permission, error) {
	var menus []*models.Permission
	err := r.WithContext(ctx).DB().Where("status = ? AND (type = ? OR type = ?)", 10, 1, 2).
		Order("sort").
		Find(&menus).Error
	return menus, err
}

// FindMenusByPermissions 根据权限代码查询菜单
func (r *permissionRepoImpl) FindMenusByPermissions(ctx context.Context, permCodes []string) ([]*models.Permission, error) {
	var menus []*models.Permission
	err := r.WithContext(ctx).DB().Where("status = ? AND (type = ? OR type = ?) AND code IN ?", 10, 1, 2, permCodes).
		Order("sort").
		Find(&menus).Error

	// 查询所有父级菜单
	if err == nil && len(menus) > 0 {
		var parentIDs []uint64
		parentIDMap := make(map[uint64]bool)

		// 收集所有父级ID，并去重
		for _, menu := range menus {
			if menu.ParentID > 0 && !parentIDMap[menu.ParentID] {
				parentIDs = append(parentIDs, menu.ParentID)
				parentIDMap[menu.ParentID] = true
			}
		}

		if len(parentIDs) > 0 {
			var parentMenus []*models.Permission
			err = r.WithContext(ctx).DB().Where("status = ? AND type = ? AND id IN ?", 10, 1, parentIDs).
				Order("sort").
				Find(&parentMenus).Error

			if err == nil {
				// 递归查询更高层级的父菜单
				var grandParentIDs []uint64
				grandParentIDMap := make(map[uint64]bool)

				for _, parent := range parentMenus {
					if parent.ParentID > 0 && !grandParentIDMap[parent.ParentID] {
						grandParentIDs = append(grandParentIDs, parent.ParentID)
						grandParentIDMap[parent.ParentID] = true
					}
				}

				if len(grandParentIDs) > 0 {
					var grandParentMenus []*models.Permission
					err = r.WithContext(ctx).DB().Where("status = ? AND type = ? AND id IN ?", 10, 1, grandParentIDs).
						Order("sort").
						Find(&grandParentMenus).Error

					if err == nil {
						parentMenus = append(parentMenus, grandParentMenus...)
					}
				}

				// 合并所有菜单
				menus = append(menus, parentMenus...)
			}
		}
	}

	return menus, err
}

// FindByID 根据ID查询权限
func (r *permissionRepoImpl) FindByID(id uint64, result interface{}) error {
	return r.baseRepositoryImpl.FindByID(id, result)
}

// FindByCode 根据代码查询权限
func (r *permissionRepoImpl) FindByCode(ctx context.Context, code string) (*models.Permission, error) {
	var permission models.Permission
	err := r.WithContext(ctx).DB().Where("code = ?", code).First(&permission).Error
	return &permission, err
}

// FindByAPI 根据API路径和方法查询权限
func (r *permissionRepoImpl) FindByAPI(ctx context.Context, api string, method string) (*models.Permission, error) {
	var permission models.Permission
	err := r.WithContext(ctx).DB().Where("api = ? AND method = ?", api, method).First(&permission).Error
	return &permission, err
}

// Create 创建权限
func (r *permissionRepoImpl) Create(entity interface{}) error {
	return r.baseRepositoryImpl.Create(entity)
}

// Update 更新权限
func (r *permissionRepoImpl) Update(entity interface{}) error {
	return r.baseRepositoryImpl.Update(entity)
}

// Delete 删除权限
func (r *permissionRepoImpl) Delete(id uint64) error {
	return r.baseRepositoryImpl.Delete(id)
}

// BatchCreate 批量创建权限
func (r *permissionRepoImpl) BatchCreate(ctx context.Context, permissions []*models.Permission) error {
	return r.WithContext(ctx).DB().Create(permissions).Error
}

// BatchDelete 批量删除权限
func (r *permissionRepoImpl) BatchDelete(ctx context.Context, ids []uint64) error {
	return r.WithContext(ctx).DB().Delete(&models.Permission{}, "id IN ?", ids).Error
}

// FindByParentID 根据父ID查询权限
func (r *permissionRepoImpl) FindByParentID(ctx context.Context, parentID uint64) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.WithContext(ctx).DB().Where("parent_id = ? AND status = ?", parentID, 10).
		Order("sort").
		Find(&permissions).Error
	return permissions, err
}

// FindByType 根据类型查询权限
func (r *permissionRepoImpl) FindByType(ctx context.Context, permType int) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.WithContext(ctx).DB().Where("type = ? AND status = ?", permType, 10).
		Order("sort").
		Find(&permissions).Error
	return permissions, err
}

// FindByRoleID 根据角色ID查询权限
func (r *permissionRepoImpl) FindByRoleID(ctx context.Context, roleID uint64) ([]*models.Permission, error) {
	var permissions []*models.Permission
	err := r.WithContext(ctx).DB().Table("sys_permissions p").
		Joins("JOIN sys_role_permissions rp ON p.id = rp.permission_id").
		Where("rp.role_id = ? AND p.status = ?", roleID, 10).
		Order("p.sort").
		Find(&permissions).Error
	return permissions, err
}
