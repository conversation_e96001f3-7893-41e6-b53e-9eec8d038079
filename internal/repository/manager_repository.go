package repository

import (
	"context"
	"errors"
	"gin/internal/infrastructure/model"
	"gin/internal/models"
	"reflect"

	"gorm.io/gorm"
)

// ManagerRepo 管理员用户仓库接口
type ManagerRepo interface {
	BaseRepository
	FindByUsername(ctx context.Context, username string) (*models.Manager, error)
	FindByEmail(ctx context.Context, email string) (*models.Manager, error)
	FindByMobile(ctx context.Context, mobile string) (*models.Manager, error)
	HardDeleteByID(ctx context.Context, userID uint64) error
}

// managerRepoImpl 管理员用户仓库实现
type managerRepoImpl struct {
	*baseRepositoryImpl
}

// NewManagerRepository 创建管理员用户仓库
func NewManagerRepository(db *gorm.DB) ManagerRepo {
	queryBuilder := newManagerQueryBuilder()
	baseRepo := NewBaseRepository(db, reflect.TypeOf(models.Manager{}), queryBuilder, "manager", "id")
	return &managerRepoImpl{
		baseRepositoryImpl: baseRepo.(*baseRepositoryImpl),
	}
}

// newManagerQueryBuilder 创建查询构建器
func newManagerQueryBuilder() *model.QueryBuilder {
	return model.NewQueryBuilder().RegisterFields(map[string]model.QueryField{
		"username":  {Column: "username", Operation: model.OpLike, ValueType: model.TypeString},
		"nickname":  {Column: "nickname", Operation: model.OpLike, ValueType: model.TypeString},
		"realName":  {Column: "nickname", Operation: model.OpLike, ValueType: model.TypeString},
		"status":    {Column: "status", Operation: model.OpEq, ValueType: model.TypeInt},
		"tenant_id": {Column: "tenant_id", Operation: model.OpEq, ValueType: model.TypeInt},
		"email":     {Column: "email", Operation: model.OpLike, ValueType: model.TypeString},
		"mobile":    {Column: "mobile", Operation: model.OpLike, ValueType: model.TypeString},
		"is_admin":  {Column: "is_admin", Operation: model.OpEq, ValueType: model.TypeBool},
		"startTime": {Column: "created_at", Operation: model.OpGte, ValueType: model.TypeTime},
		"endTime":   {Column: "created_at", Operation: model.OpLte, ValueType: model.TypeTime},
		"dept_id":   {Column: "dept_id", Operation: model.OpEq, ValueType: model.TypeInt},
		"sex":       {Column: "sex", Operation: model.OpEq, ValueType: model.TypeInt},
	})
}

func (r *managerRepoImpl) FindByUsername(ctx context.Context, username string) (*models.Manager, error) {
	var m models.Manager
	err := r.WithContext(ctx).(*baseRepositoryImpl).dbWithCtx().Where("user_name = ?", username).First(&m).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &m, err
}

func (r *managerRepoImpl) FindByEmail(ctx context.Context, email string) (*models.Manager, error) {
	var m models.Manager
	err := r.WithContext(ctx).(*baseRepositoryImpl).dbWithCtx().Where("email = ?", email).First(&m).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &m, err
}

func (r *managerRepoImpl) FindByMobile(ctx context.Context, mobile string) (*models.Manager, error) {
	var m models.Manager
	err := r.WithContext(ctx).(*baseRepositoryImpl).dbWithCtx().Where("mobile = ?", mobile).First(&m).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &m, err
}

func (r *managerRepoImpl) HardDeleteByID(ctx context.Context, userID uint64) error {
	return r.WithContext(ctx).(*baseRepositoryImpl).dbWithCtx().Unscoped().Where("id = ?", userID).Delete(&models.Manager{}).Error
}
