package repository

import (
	"gin/internal/repository/base"
	"gin/internal/repository/repo"
	"gin/internal/transaction"
	"gorm.io/gorm"
)

// RepositoryFactory 仓库工厂
type RepositoryFactory struct {
	db        *gorm.DB
	txManager transaction.TransactionManager
}

// NewRepositoryFactory 创建仓库工厂
func NewRepositoryFactory(db *gorm.DB, txManager transaction.TransactionManager) *RepositoryFactory {
	return &RepositoryFactory{
		db:        db,
		txManager: txManager,
	}
}

// CreateUserRepository 创建用户仓库
func (f *RepositoryFactory) CreateUserRepository() repo.UserRepository {
	userRepo := repo.NewUserRepository(f.db)
	
	// 如果用户仓库实现了SetTransactionManager方法，则设置事务管理器
	if txAware, ok := userRepo.(interface {
		SetTransactionManager(base.TransactionManager) repo.UserRepository
	}); ok {
		// 创建适配器将transaction.TransactionManager适配为base.TransactionManager
		adapter := &transactionManagerAdapter{f.txManager}
		return txAware.SetTransactionManager(adapter)
	}
	
	return userRepo
}

// CreateUserAssetRepository 创建用户资产仓库
func (f *RepositoryFactory) CreateUserAssetRepository() repo.UserAssetRepository {
	userAssetRepo := repo.NewUserAssetRepository(f.db)
	
	// 如果用户资产仓库实现了SetTransactionManager方法，则设置事务管理器
	if txAware, ok := userAssetRepo.(interface {
		SetTransactionManager(base.TransactionManager) repo.UserAssetRepository
	}); ok {
		adapter := &transactionManagerAdapter{f.txManager}
		return txAware.SetTransactionManager(adapter)
	}
	
	return userAssetRepo
}

// CreateAssetRepository 创建资产仓库
func (f *RepositoryFactory) CreateAssetRepository() repo.AssetRepository {
	assetRepo := repo.NewAssetRepository(f.db)
	
	// 如果资产仓库实现了SetTransactionManager方法，则设置事务管理器
	if txAware, ok := assetRepo.(interface {
		SetTransactionManager(base.TransactionManager) repo.AssetRepository
	}); ok {
		adapter := &transactionManagerAdapter{f.txManager}
		return txAware.SetTransactionManager(adapter)
	}
	
	return assetRepo
}

// CreateTranslationRepository 创建翻译仓库
func (f *RepositoryFactory) CreateTranslationRepository() repo.TranslationRepository {
	translationRepo := repo.NewTranslationRepository(f.db)
	
	// 如果翻译仓库实现了SetTransactionManager方法，则设置事务管理器
	if txAware, ok := translationRepo.(interface {
		SetTransactionManager(base.TransactionManager) repo.TranslationRepository
	}); ok {
		adapter := &transactionManagerAdapter{f.txManager}
		return txAware.SetTransactionManager(adapter)
	}
	
	return translationRepo
}

// transactionManagerAdapter 事务管理器适配器
type transactionManagerAdapter struct {
	txManager transaction.TransactionManager
}

// GetDB 获取数据库连接
func (a *transactionManagerAdapter) GetDB(ctx context.Context) *gorm.DB {
	return a.txManager.GetDB(ctx)
}

// IsInTransaction 检查是否在事务中
func (a *transactionManagerAdapter) IsInTransaction(ctx context.Context) bool {
	return a.txManager.IsInTransaction(ctx)
}

// RepositoryManager 仓库管理器
type RepositoryManager struct {
	factory *RepositoryFactory
	
	// 缓存仓库实例
	userRepo        repo.UserRepository
	userAssetRepo   repo.UserAssetRepository
	assetRepo       repo.AssetRepository
	translationRepo repo.TranslationRepository
}

// NewRepositoryManager 创建仓库管理器
func NewRepositoryManager(db *gorm.DB, txManager transaction.TransactionManager) *RepositoryManager {
	return &RepositoryManager{
		factory: NewRepositoryFactory(db, txManager),
	}
}

// UserRepository 获取用户仓库
func (m *RepositoryManager) UserRepository() repo.UserRepository {
	if m.userRepo == nil {
		m.userRepo = m.factory.CreateUserRepository()
	}
	return m.userRepo
}

// UserAssetRepository 获取用户资产仓库
func (m *RepositoryManager) UserAssetRepository() repo.UserAssetRepository {
	if m.userAssetRepo == nil {
		m.userAssetRepo = m.factory.CreateUserAssetRepository()
	}
	return m.userAssetRepo
}

// AssetRepository 获取资产仓库
func (m *RepositoryManager) AssetRepository() repo.AssetRepository {
	if m.assetRepo == nil {
		m.assetRepo = m.factory.CreateAssetRepository()
	}
	return m.assetRepo
}

// TranslationRepository 获取翻译仓库
func (m *RepositoryManager) TranslationRepository() repo.TranslationRepository {
	if m.translationRepo == nil {
		m.translationRepo = m.factory.CreateTranslationRepository()
	}
	return m.translationRepo
}
