package query

import (
	"fmt"
	"reflect"

	"gin/internal/repository/base"

	"gorm.io/gorm"
)

// Operator 查询操作符
type Operator string

// SortDirection 排序方向
type SortDirection string

// ValueType 查询值类型
type ValueType string

// 查询操作符常量
const (
	OpEq      Operator = "eq"       // 等于
	OpNeq     Operator = "neq"      // 不等于
	OpLike    Operator = "like"     // 模糊查询
	OpGt      Operator = "gt"       // 大于
	OpGte     Operator = "gte"      // 大于等于
	OpLt      Operator = "lt"       // 小于
	OpLte     Operator = "lte"      // 小于等于
	OpIn      Operator = "in"       // 包含
	OpNotIn   Operator = "not_in"   // 不包含
	OpBetween Operator = "between"  // 范围
	OpIsNull  Operator = "is_null"  // 为空
	OpNotNull Operator = "not_null" // 不为空
)

// 排序方向常量
const (
	SortASC  SortDirection = "ASC"
	SortDESC SortDirection = "DESC"
)

// 查询值类型常量
const (
	TypeString  ValueType = "string"  // 字符串
	TypeInt     ValueType = "int"     // 整数
	TypeFloat   ValueType = "float"   // 浮点数
	TypeBool    ValueType = "bool"    // 布尔值
	TypeTime    ValueType = "time"    // 时间
	TypeDate    ValueType = "date"    // 日期
	TypeArray   ValueType = "array"   // 数组
	TypeComplex ValueType = "complex" // 复杂类型
)

// Field 查询字段定义
type Field struct {
	Column      string     // 数据库列名
	Operation   Operator   // 默认操作符
	ValueType   ValueType  // 值类型
	Options     []string   // 可选值列表，用于验证
	AllowedOps  []Operator // 允许的操作符列表
	Description string     // 字段描述
}

// Sort 排序条件
type Sort struct {
	Field     string
	Direction SortDirection
}

// Condition 查询条件
type Condition struct {
	Field     string
	Operator  Operator
	Value     interface{}
	ValueType ValueType
}

// ConditionGroup 条件组（支持OR逻辑）
type ConditionGroup struct {
	Operator   string // AND, OR
	Conditions []Condition
}

// EnhancedQueryBuilder 增强的查询构建器
type EnhancedQueryBuilder struct {
	// 字段定义映射
	fields map[string]Field

	// 查询组件
	conditions      []Condition
	conditionGroups []ConditionGroup
	sorts           []Sort
	groups          []string
	preloads        []string
	selects         []string
	joins           []string

	// 分页
	pagination *base.Pagination

	// 配置选项
	strictMode  bool // 严格模式：只允许预定义字段
	autoConvert bool // 自动类型转换
	skipOnError bool // 转换错误时跳过条件

	// 性能优化
	cacheEnabled bool
	queryCache   map[string]*gorm.DB
}

// NewEnhancedQueryBuilder 创建增强查询构建器
func NewEnhancedQueryBuilder() *EnhancedQueryBuilder {
	return &EnhancedQueryBuilder{
		fields:          make(map[string]Field),
		conditions:      make([]Condition, 0),
		conditionGroups: make([]ConditionGroup, 0),
		sorts:           make([]Sort, 0),
		groups:          make([]string, 0),
		preloads:        make([]string, 0),
		selects:         make([]string, 0),
		joins:           make([]string, 0),
		pagination:      &base.Pagination{Page: 1, PageSize: 10, Enabled: false},
		strictMode:      false,
		autoConvert:     true,
		skipOnError:     true,
		cacheEnabled:    false,
		queryCache:      make(map[string]*gorm.DB),
	}
}

// Fluent API 配置方法

// SetStrictMode 设置严格模式
func (eqb *EnhancedQueryBuilder) SetStrictMode(enabled bool) *EnhancedQueryBuilder {
	eqb.strictMode = enabled
	return eqb
}

// SetAutoConvert 设置自动类型转换
func (eqb *EnhancedQueryBuilder) SetAutoConvert(enabled bool) *EnhancedQueryBuilder {
	eqb.autoConvert = enabled
	return eqb
}

// SetSkipOnError 设置错误时跳过
func (eqb *EnhancedQueryBuilder) SetSkipOnError(enabled bool) *EnhancedQueryBuilder {
	eqb.skipOnError = enabled
	return eqb
}

// EnableQueryCache 启用查询缓存
func (eqb *EnhancedQueryBuilder) EnableQueryCache() *EnhancedQueryBuilder {
	eqb.cacheEnabled = true
	return eqb
}

// 字段注册方法

// RegisterField 注册查询字段
func (eqb *EnhancedQueryBuilder) RegisterField(paramName string, field Field) *EnhancedQueryBuilder {
	// 如果没有设置允许的操作符，使用默认操作符
	if len(field.AllowedOps) == 0 {
		field.AllowedOps = []Operator{field.Operation}
	}
	eqb.fields[paramName] = field
	return eqb
}

// RegisterFields 批量注册查询字段
func (eqb *EnhancedQueryBuilder) RegisterFields(fields map[string]Field) *EnhancedQueryBuilder {
	for k, v := range fields {
		eqb.RegisterField(k, v)
	}
	return eqb
}

// 条件方法

// AddCondition 添加查询条件（使用默认操作符）
func (eqb *EnhancedQueryBuilder) AddCondition(field string, value interface{}) *EnhancedQueryBuilder {
	if fieldDef, exists := eqb.fields[field]; exists {
		return eqb.AddConditionWithOperator(field, fieldDef.Operation, value)
	}

	// 非严格模式下，允许未注册字段使用等于操作符
	if !eqb.strictMode {
		return eqb.AddConditionWithOperator(field, OpEq, value)
	}

	return eqb
}

// AddConditions 批量添加条件
func (eqb *EnhancedQueryBuilder) AddConditions(conditions map[string]interface{}) *EnhancedQueryBuilder {
	for field, value := range conditions {
		eqb.AddCondition(field, value)
	}
	return eqb
}

// AddConditionWithOperator 添加带操作符的查询条件
func (eqb *EnhancedQueryBuilder) AddConditionWithOperator(field string, operator Operator, value interface{}) *EnhancedQueryBuilder {
	// 跳过空值
	if value == nil {
		return eqb
	}

	// 处理指针类型
	if reflect.TypeOf(value).Kind() == reflect.Ptr {
		if reflect.ValueOf(value).IsNil() {
			return eqb
		}
		value = reflect.ValueOf(value).Elem().Interface()
	}

	// 跳过空字符串
	if strVal, ok := value.(string); ok && strVal == "" {
		return eqb
	}

	var valueType = TypeComplex
	var convertedValue = value

	// 检查字段是否已注册
	if fieldDef, exists := eqb.fields[field]; exists {
		// 严格模式下检查操作符是否允许
		if eqb.strictMode && !eqb.isOperatorAllowed(fieldDef, operator) {
			if !eqb.skipOnError {
				panic(fmt.Sprintf("operator %s not allowed for field %s", operator, field))
			}
			return eqb
		}

		valueType = fieldDef.ValueType

		// 自动类型转换
		if eqb.autoConvert {
			var err error
			convertedValue, err = eqb.convertValue(value, fieldDef.ValueType)
			if err != nil {
				if !eqb.skipOnError {
					panic(fmt.Sprintf("failed to convert value for field %s: %v", field, err))
				}
				return eqb
			}
		}

		// 使用注册的列名
		field = fieldDef.Column
	}

	eqb.conditions = append(eqb.conditions, Condition{
		Field:     field,
		Operator:  operator,
		Value:     convertedValue,
		ValueType: valueType,
	})

	return eqb
}

// Where 开始一个新的条件组
func (eqb *EnhancedQueryBuilder) Where() *ConditionGroupBuilder {
	return &ConditionGroupBuilder{
		queryBuilder: eqb,
		operator:     "AND",
		conditions:   make([]Condition, 0),
	}
}

// OrWhere 开始一个OR条件组
func (eqb *EnhancedQueryBuilder) OrWhere() *ConditionGroupBuilder {
	return &ConditionGroupBuilder{
		queryBuilder: eqb,
		operator:     "OR",
		conditions:   make([]Condition, 0),
	}
}

// ConditionGroupBuilder 条件组构建器
type ConditionGroupBuilder struct {
	queryBuilder *EnhancedQueryBuilder
	operator     string
	conditions   []Condition
}

// Eq 等于条件
func (cgb *ConditionGroupBuilder) Eq(field string, value interface{}) *ConditionGroupBuilder {
	cgb.addCondition(field, OpEq, value)
	return cgb
}

// Like 模糊查询条件
func (cgb *ConditionGroupBuilder) Like(field string, value interface{}) *ConditionGroupBuilder {
	cgb.addCondition(field, OpLike, value)
	return cgb
}

// In IN条件
func (cgb *ConditionGroupBuilder) In(field string, values interface{}) *ConditionGroupBuilder {
	cgb.addCondition(field, OpIn, values)
	return cgb
}

// Between 范围条件
func (cgb *ConditionGroupBuilder) Between(field string, start, end interface{}) *ConditionGroupBuilder {
	cgb.addCondition(field, OpBetween, []interface{}{start, end})
	return cgb
}

// End 结束条件组构建
func (cgb *ConditionGroupBuilder) End() *EnhancedQueryBuilder {
	if len(cgb.conditions) > 0 {
		cgb.queryBuilder.conditionGroups = append(cgb.queryBuilder.conditionGroups, ConditionGroup{
			Operator:   cgb.operator,
			Conditions: cgb.conditions,
		})
	}
	return cgb.queryBuilder
}

// addCondition 添加条件到组
func (cgb *ConditionGroupBuilder) addCondition(field string, operator Operator, value interface{}) {
	// 复用主构建器的逻辑
	tempBuilder := &EnhancedQueryBuilder{
		fields:      cgb.queryBuilder.fields,
		strictMode:  cgb.queryBuilder.strictMode,
		autoConvert: cgb.queryBuilder.autoConvert,
		skipOnError: cgb.queryBuilder.skipOnError,
	}

	tempBuilder.AddConditionWithOperator(field, operator, value)
	if len(tempBuilder.conditions) > 0 {
		cgb.conditions = append(cgb.conditions, tempBuilder.conditions[0])
	}
}

// 便捷条件方法

// AddLikeCondition 添加模糊查询条件
func (eqb *EnhancedQueryBuilder) AddLikeCondition(field string, value interface{}) *EnhancedQueryBuilder {
	return eqb.AddConditionWithOperator(field, OpLike, value)
}

// AddRangeCondition 添加范围查询条件
func (eqb *EnhancedQueryBuilder) AddRangeCondition(field string, start, end interface{}) *EnhancedQueryBuilder {
	rangeValue := []interface{}{start, end}
	return eqb.AddConditionWithOperator(field, OpBetween, rangeValue)
}

// AddInCondition 添加IN查询条件
func (eqb *EnhancedQueryBuilder) AddInCondition(field string, values interface{}) *EnhancedQueryBuilder {
	return eqb.AddConditionWithOperator(field, OpIn, values)
}

// 排序方法

// AddSort 添加排序
func (eqb *EnhancedQueryBuilder) AddSort(field string, direction SortDirection) *EnhancedQueryBuilder {
	// 跳过空字段
	if field == "" {
		if !eqb.skipOnError {
			panic("field name cannot be empty for sorting")
		}
		return eqb
	}

	// 验证排序方向
	if direction != SortASC && direction != SortDESC {
		if !eqb.skipOnError {
			panic(fmt.Sprintf("invalid sort direction: %s, must be ASC or DESC", direction))
		}
		return eqb
	}

	// 检查字段是否已注册，使用列名
	if fieldDef, exists := eqb.fields[field]; exists {
		field = fieldDef.Column
	} else if eqb.strictMode {
		if !eqb.skipOnError {
			panic(fmt.Sprintf("field %s not registered in strict mode", field))
		}
		return eqb
	}

	eqb.sorts = append(eqb.sorts, Sort{
		Field:     field,
		Direction: direction,
	})
	return eqb
}

// OrderBy 开始排序链式调用
func (eqb *EnhancedQueryBuilder) OrderBy() *SortBuilder {
	return &SortBuilder{
		queryBuilder: eqb,
		sorts:        make([]Sort, 0),
	}
}

// SortBuilder 排序构建器
type SortBuilder struct {
	queryBuilder *EnhancedQueryBuilder
	sorts        []Sort
}

// Desc 降序排序
func (sb *SortBuilder) Desc(field string) *SortBuilder {
	// 跳过空字段
	if field == "" {
		if !sb.queryBuilder.skipOnError {
			panic("field name cannot be empty for sorting")
		}
		return sb
	}

	// 检查字段是否已注册，使用列名
	if fieldDef, exists := sb.queryBuilder.fields[field]; exists {
		field = fieldDef.Column
	} else if sb.queryBuilder.strictMode {
		if !sb.queryBuilder.skipOnError {
			panic(fmt.Sprintf("field %s not registered in strict mode", field))
		}
		return sb
	}

	sb.sorts = append(sb.sorts, Sort{Field: field, Direction: SortDESC})
	return sb
}

// Asc 升序排序
func (sb *SortBuilder) Asc(field string) *SortBuilder {
	// 跳过空字段
	if field == "" {
		if !sb.queryBuilder.skipOnError {
			panic("field name cannot be empty for sorting")
		}
		return sb
	}

	// 检查字段是否已注册，使用列名
	if fieldDef, exists := sb.queryBuilder.fields[field]; exists {
		field = fieldDef.Column
	} else if sb.queryBuilder.strictMode {
		if !sb.queryBuilder.skipOnError {
			panic(fmt.Sprintf("field %s not registered in strict mode", field))
		}
		return sb
	}

	sb.sorts = append(sb.sorts, Sort{Field: field, Direction: SortASC})
	return sb
}

// End 结束排序构建
func (sb *SortBuilder) End() *EnhancedQueryBuilder {
	sb.queryBuilder.sorts = append(sb.queryBuilder.sorts, sb.sorts...)
	return sb.queryBuilder
}

// 其他查询组件方法

// AddGroup 添加分组
func (eqb *EnhancedQueryBuilder) AddGroup(field string) *EnhancedQueryBuilder {
	// 跳过空字段
	if field == "" {
		if !eqb.skipOnError {
			panic("field name cannot be empty for grouping")
		}
		return eqb
	}

	// 检查字段是否已注册，使用列名
	if fieldDef, exists := eqb.fields[field]; exists {
		field = fieldDef.Column
	} else if eqb.strictMode {
		if !eqb.skipOnError {
			panic(fmt.Sprintf("field %s not registered in strict mode", field))
		}
		return eqb
	}

	eqb.groups = append(eqb.groups, field)
	return eqb
}

// AddPreload 添加预加载
func (eqb *EnhancedQueryBuilder) AddPreload(relation string) *EnhancedQueryBuilder {
	eqb.preloads = append(eqb.preloads, relation)
	return eqb
}

// AddSelect 添加选择字段
func (eqb *EnhancedQueryBuilder) AddSelect(fields ...string) *EnhancedQueryBuilder {
	eqb.selects = append(eqb.selects, fields...)
	return eqb
}

// AddJoin 添加连接
func (eqb *EnhancedQueryBuilder) AddJoin(join string) *EnhancedQueryBuilder {
	eqb.joins = append(eqb.joins, join)
	return eqb
}

// 分页方法

// SetPage 设置页码
func (eqb *EnhancedQueryBuilder) SetPage(page int) *EnhancedQueryBuilder {
	if page > 0 {
		eqb.pagination.Page = page
		eqb.pagination.Enabled = true
	}
	return eqb
}

// SetPageSize 设置每页数量
func (eqb *EnhancedQueryBuilder) SetPageSize(pageSize int) *EnhancedQueryBuilder {
	if pageSize > 0 {
		eqb.pagination.PageSize = pageSize
		eqb.pagination.Enabled = true
	}
	return eqb
}

// SetPagination 设置分页
func (eqb *EnhancedQueryBuilder) SetPagination(page, pageSize int) *EnhancedQueryBuilder {
	return eqb.SetPage(page).SetPageSize(pageSize)
}

// GetPagination 获取分页参数
func (eqb *EnhancedQueryBuilder) GetPagination() *base.Pagination {
	return eqb.pagination
}
