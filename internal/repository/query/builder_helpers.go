package query

import (
	"fmt"
	"gin/internal/repository/base"
	"reflect"
	"strings"
	"time"

	"gorm.io/gorm"
)

// Build 构建查询
func (eqb *EnhancedQueryBuilder) Build(db *gorm.DB) *gorm.DB {
	// 添加选择字段
	if len(eqb.selects) > 0 {
		db = db.Select(strings.Join(eqb.selects, ", "))
	}

	// 添加连接
	for _, join := range eqb.joins {
		db = db.Joins(join)
	}

	// 添加基础查询条件
	for _, cond := range eqb.conditions {
		db = eqb.applyCondition(db, cond)
	}

	// 添加条件组
	for _, group := range eqb.conditionGroups {
		db = eqb.applyConditionGroup(db, group)
	}

	// 添加分组
	if len(eqb.groups) > 0 {
		db = db.Group(strings.Join(eqb.groups, ", "))
	}

	// 添加排序
	for _, sort := range eqb.sorts {
		db = db.Order(sort.Field + " " + string(sort.Direction))
	}

	// 添加预加载
	for _, preload := range eqb.preloads {
		db = db.Preload(preload)
	}

	return db
}

// ApplyPagination 应用分页
func (eqb *EnhancedQueryBuilder) ApplyPagination(db *gorm.DB) *gorm.DB {
	if eqb.pagination.Enabled {
		offset := (eqb.pagination.Page - 1) * eqb.pagination.PageSize
		db = db.Offset(offset).Limit(eqb.pagination.PageSize)
	}
	return db
}

// Clone 克隆查询构建器
func (eqb *EnhancedQueryBuilder) Clone() base.QueryBuilder {
	clone := &EnhancedQueryBuilder{
		fields:          make(map[string]Field),
		conditions:      make([]Condition, len(eqb.conditions)),
		conditionGroups: make([]ConditionGroup, len(eqb.conditionGroups)),
		sorts:           make([]Sort, len(eqb.sorts)),
		groups:          make([]string, len(eqb.groups)),
		preloads:        make([]string, len(eqb.preloads)),
		selects:         make([]string, len(eqb.selects)),
		joins:           make([]string, len(eqb.joins)),
		pagination:      &base.Pagination{},
		strictMode:      eqb.strictMode,
		autoConvert:     eqb.autoConvert,
		skipOnError:     eqb.skipOnError,
		cacheEnabled:    eqb.cacheEnabled,
		queryCache:      make(map[string]*gorm.DB),
	}

	// 深拷贝字段定义
	for k, v := range eqb.fields {
		clone.fields[k] = v
	}

	// 拷贝切片
	copy(clone.conditions, eqb.conditions)
	copy(clone.conditionGroups, eqb.conditionGroups)
	copy(clone.sorts, eqb.sorts)
	copy(clone.groups, eqb.groups)
	copy(clone.preloads, eqb.preloads)
	copy(clone.selects, eqb.selects)
	copy(clone.joins, eqb.joins)

	// 拷贝分页
	*clone.pagination = *eqb.pagination

	return clone
}

// applyCondition 应用查询条件
func (eqb *EnhancedQueryBuilder) applyCondition(db *gorm.DB, cond Condition) *gorm.DB {
	switch cond.Operator {
	case OpEq:
		return db.Where(cond.Field+" = ?", cond.Value)
	case OpNeq:
		return db.Where(cond.Field+" != ?", cond.Value)
	case OpLike:
		if strVal, ok := cond.Value.(string); ok {
			// 如果没有包含通配符，自动添加
			if !strings.Contains(strVal, "%") {
				strVal = "%" + strVal + "%"
			}
			return db.Where(cond.Field+" LIKE ?", strVal)
		}
	case OpGt:
		return db.Where(cond.Field+" > ?", cond.Value)
	case OpGte:
		return db.Where(cond.Field+" >= ?", cond.Value)
	case OpLt:
		return db.Where(cond.Field+" < ?", cond.Value)
	case OpLte:
		return db.Where(cond.Field+" <= ?", cond.Value)
	case OpIn:
		if reflect.TypeOf(cond.Value).Kind() == reflect.Slice {
			return db.Where(cond.Field+" IN ?", cond.Value)
		}
	case OpNotIn:
		if reflect.TypeOf(cond.Value).Kind() == reflect.Slice {
			return db.Where(cond.Field+" NOT IN ?", cond.Value)
		}
	case OpBetween:
		if arr, ok := cond.Value.([]interface{}); ok && len(arr) == 2 {
			if arr[0] != nil && arr[1] != nil {
				return db.Where(cond.Field+" BETWEEN ? AND ?", arr[0], arr[1])
			} else if arr[0] != nil {
				return db.Where(cond.Field+" >= ?", arr[0])
			} else if arr[1] != nil {
				return db.Where(cond.Field+" <= ?", arr[1])
			}
		}
	case OpIsNull:
		return db.Where(cond.Field + " IS NULL")
	case OpNotNull:
		return db.Where(cond.Field + " IS NOT NULL")
	}

	return db
}

// applyConditionGroup 应用条件组
func (eqb *EnhancedQueryBuilder) applyConditionGroup(db *gorm.DB, group ConditionGroup) *gorm.DB {
	if len(group.Conditions) == 0 {
		return db
	}

	// 构建条件组的SQL和参数
	var conditions []string
	var args []interface{}

	for _, cond := range group.Conditions {
		condSQL, condArgs := eqb.buildConditionSQL(cond)
		if condSQL != "" {
			conditions = append(conditions, condSQL)
			args = append(args, condArgs...)
		}
	}

	if len(conditions) > 0 {
		sql := "(" + strings.Join(conditions, " "+group.Operator+" ") + ")"
		db = db.Where(sql, args...)
	}

	return db
}

// buildConditionSQL 构建单个条件的SQL
func (eqb *EnhancedQueryBuilder) buildConditionSQL(cond Condition) (string, []interface{}) {
	switch cond.Operator {
	case OpEq:
		return cond.Field + " = ?", []interface{}{cond.Value}
	case OpNeq:
		return cond.Field + " != ?", []interface{}{cond.Value}
	case OpLike:
		if strVal, ok := cond.Value.(string); ok {
			if !strings.Contains(strVal, "%") {
				strVal = "%" + strVal + "%"
			}
			return cond.Field + " LIKE ?", []interface{}{strVal}
		}
	case OpGt:
		return cond.Field + " > ?", []interface{}{cond.Value}
	case OpGte:
		return cond.Field + " >= ?", []interface{}{cond.Value}
	case OpLt:
		return cond.Field + " < ?", []interface{}{cond.Value}
	case OpLte:
		return cond.Field + " <= ?", []interface{}{cond.Value}
	case OpIn:
		if reflect.TypeOf(cond.Value).Kind() == reflect.Slice {
			return cond.Field + " IN ?", []interface{}{cond.Value}
		}
	case OpNotIn:
		if reflect.TypeOf(cond.Value).Kind() == reflect.Slice {
			return cond.Field + " NOT IN ?", []interface{}{cond.Value}
		}
	case OpBetween:
		if arr, ok := cond.Value.([]interface{}); ok && len(arr) == 2 {
			if arr[0] != nil && arr[1] != nil {
				return cond.Field + " BETWEEN ? AND ?", []interface{}{arr[0], arr[1]}
			} else if arr[0] != nil {
				return cond.Field + " >= ?", []interface{}{arr[0]}
			} else if arr[1] != nil {
				return cond.Field + " <= ?", []interface{}{arr[1]}
			}
		}
	case OpIsNull:
		return cond.Field + " IS NULL", []interface{}{}
	case OpNotNull:
		return cond.Field + " IS NOT NULL", []interface{}{}
	}

	return "", []interface{}{}
}

// isOperatorAllowed 检查操作符是否被允许
func (eqb *EnhancedQueryBuilder) isOperatorAllowed(field Field, operator Operator) bool {
	for _, allowedOp := range field.AllowedOps {
		if allowedOp == operator {
			return true
		}
	}
	return false
}

// convertValue 转换值类型
func (eqb *EnhancedQueryBuilder) convertValue(value interface{}, valueType ValueType) (interface{}, error) {
	switch valueType {
	case TypeString:
		return fmt.Sprintf("%v", value), nil

	case TypeInt:
		switch v := value.(type) {
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			return v, nil
		case string:
			if v == "" {
				return 0, nil
			}
			var intVal int
			_, err := fmt.Sscanf(v, "%d", &intVal)
			return intVal, err
		default:
			return 0, fmt.Errorf("cannot convert %v to int", value)
		}

	case TypeFloat:
		switch v := value.(type) {
		case float32, float64:
			return v, nil
		case string:
			if v == "" {
				return 0.0, nil
			}
			var floatVal float64
			_, err := fmt.Sscanf(v, "%f", &floatVal)
			return floatVal, err
		default:
			return 0.0, fmt.Errorf("cannot convert %v to float", value)
		}

	case TypeBool:
		switch v := value.(type) {
		case bool:
			return v, nil
		case string:
			return strings.ToLower(v) == "true" || v == "1", nil
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			return v != 0, nil
		default:
			return false, fmt.Errorf("cannot convert %v to bool", value)
		}

	case TypeTime, TypeDate:
		switch v := value.(type) {
		case time.Time:
			return v, nil
		case string:
			if v == "" {
				return time.Time{}, fmt.Errorf("empty time string")
			}
			// 尝试多种时间格式
			formats := []string{
				"2006-01-02 15:04:05",
				"2006-01-02",
				"2006/01/02 15:04:05",
				"2006/01/02",
				time.RFC3339,
			}

			for _, format := range formats {
				t, err := time.Parse(format, v)
				if err == nil {
					return t, nil
				}
			}
			return time.Time{}, fmt.Errorf("cannot parse time: %s", v)
		default:
			return time.Time{}, fmt.Errorf("cannot convert %v to time", value)
		}

	case TypeArray:
		if reflect.TypeOf(value).Kind() == reflect.Slice {
			return value, nil
		}
		if strVal, ok := value.(string); ok && strVal != "" {
			// 尝试将字符串解析为数组，例如 "1,2,3"
			return strings.Split(strVal, ","), nil
		}
		return nil, fmt.Errorf("cannot convert %v to array", value)

	default:
		// 对于复杂类型或未知类型，直接返回原值
		return value, nil
	}
}

// BuildFromMap 从map构建查询条件
func (eqb *EnhancedQueryBuilder) BuildFromMap(params map[string]interface{}) *EnhancedQueryBuilder {
	for key, value := range params {
		// 特殊处理分页参数
		switch key {
		case "page":
			if page, ok := value.(int); ok {
				eqb.SetPage(page)
			}
		case "page_size", "pageSize":
			if pageSize, ok := value.(int); ok {
				eqb.SetPageSize(pageSize)
			}
		case "sort":
			if sortStr, ok := value.(string); ok {
				eqb.parseSortString(sortStr)
			}
		default:
			// 添加查询条件
			eqb.AddCondition(key, value)
		}
	}
	return eqb
}

// parseSortString 解析排序字符串
func (eqb *EnhancedQueryBuilder) parseSortString(sortStr string) {
	if sortStr == "" {
		return
	}

	// 支持格式: "field:desc,field2:asc" 或 "field desc,field2 asc"
	sorts := strings.Split(sortStr, ",")
	for _, sort := range sorts {
		sort = strings.TrimSpace(sort)
		if sort == "" {
			continue
		}

		// 尝试冒号分隔符
		if strings.Contains(sort, ":") {
			parts := strings.Split(sort, ":")
			if len(parts) == 2 {
				field := strings.TrimSpace(parts[0])
				direction := strings.TrimSpace(strings.ToUpper(parts[1]))
				if direction == "DESC" {
					eqb.AddSort(field, SortDESC)
				} else {
					eqb.AddSort(field, SortASC)
				}
			}
		} else {
			// 尝试空格分隔符
			parts := strings.Fields(sort)
			if len(parts) >= 2 {
				field := parts[0]
				direction := strings.ToUpper(parts[1])
				if direction == "DESC" {
					eqb.AddSort(field, SortDESC)
				} else {
					eqb.AddSort(field, SortASC)
				}
			} else if len(parts) == 1 {
				// 默认升序
				eqb.AddSort(parts[0], SortASC)
			}
		}
	}
}

// ParseTimeRange 解析时间范围
func (eqb *EnhancedQueryBuilder) ParseTimeRange(params map[string]interface{}, startKey, endKey string) error {
	// 处理开始时间
	if startTimeStr, ok := params[startKey].(string); ok && startTimeStr != "" {
		startTime, err := time.Parse("2006-01-02", startTimeStr)
		if err != nil {
			return err
		}
		params[startKey] = startTime
	}

	// 处理结束时间
	if endTimeStr, ok := params[endKey].(string); ok && endTimeStr != "" {
		endTime, err := time.Parse("2006-01-02", endTimeStr)
		if err != nil {
			return err
		}
		// 设置为当天结束时间
		endTime = endTime.Add(24*time.Hour - time.Second)
		params[endKey] = endTime
	}

	return nil
}

// ValidateConditions 验证条件
func (eqb *EnhancedQueryBuilder) ValidateConditions() error {
	for _, cond := range eqb.conditions {
		if fieldDef, exists := eqb.fields[cond.Field]; exists {
			if len(fieldDef.Options) > 0 {
				// 验证值是否在允许的选项中
				if !eqb.isValueInOptions(cond.Value, fieldDef.Options) {
					return fmt.Errorf("invalid value %v for field %s", cond.Value, cond.Field)
				}
			}
		}
	}
	return nil
}

// isValueInOptions 检查值是否在选项中
func (eqb *EnhancedQueryBuilder) isValueInOptions(value interface{}, options []string) bool {
	valueStr := fmt.Sprintf("%v", value)
	for _, option := range options {
		if option == valueStr {
			return true
		}
	}
	return false
}
