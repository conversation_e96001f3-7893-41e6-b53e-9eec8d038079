package repository

import (
	"context"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/model"
	"reflect"

	"gorm.io/gorm"
)

type BaseRepository interface {
	DB() *gorm.DB
	Create(entity interface{}) error
	Update(entity interface{}) error
	Delete(id uint64) error
	HardDelete(id uint64) error
	Restore(id uint64) error
	FindByID(id uint64, result interface{}) error
	FindWithPagination(pagination *common.Pagination, params map[string]interface{}, results interface{}) (*common.PageResult, error)
	FindAll(results interface{}) error
	Count(params map[string]interface{}) (int64, error)
	WithPreload(relations ...string) BaseRepository
	WithContext(ctx context.Context) BaseRepository
}

type baseRepositoryImpl struct {
	db              *gorm.DB
	ctx             context.Context
	modelType       reflect.Type
	queryBuilder    *model.QueryBuilder
	tableName       string
	primaryKeyField string
	preloads        []string
}

func NewBaseRepository(db *gorm.DB, modelType reflect.Type, queryBuilder *model.QueryBuilder, tableName, primaryKeyField string) BaseRepository {
	return &baseRepositoryImpl{
		db:              db,
		modelType:       modelType,
		queryBuilder:    queryBuilder,
		tableName:       tableName,
		primaryKeyField: primaryKeyField,
		ctx:             context.Background(), // 默认使用空 context
	}
}

func (r *baseRepositoryImpl) DB() *gorm.DB {
	return r.db
}

func (r *baseRepositoryImpl) WithContext(ctx context.Context) BaseRepository {
	clone := *r
	clone.ctx = ctx
	return &clone
}

func (r *baseRepositoryImpl) WithPreload(relations ...string) BaseRepository {
	clone := *r
	clone.preloads = append(clone.preloads, relations...)
	return &clone
}

func (r *baseRepositoryImpl) dbWithCtx() *gorm.DB {
	return r.db.WithContext(r.ctx)
}

func (r *baseRepositoryImpl) applyPreloads(db *gorm.DB) *gorm.DB {
	for _, relation := range r.preloads {
		db = db.Preload(relation)
	}
	return db
}

func (r *baseRepositoryImpl) Create(entity interface{}) error {
	return r.dbWithCtx().Create(entity).Error
}

func (r *baseRepositoryImpl) Update(entity interface{}) error {
	return r.dbWithCtx().Save(entity).Error
}

func (r *baseRepositoryImpl) Delete(id uint64) error {
	return r.dbWithCtx().Delete(reflect.New(r.modelType).Interface(), id).Error
}

func (r *baseRepositoryImpl) HardDelete(id uint64) error {
	return r.dbWithCtx().Unscoped().Delete(reflect.New(r.modelType).Interface(), id).Error
}

func (r *baseRepositoryImpl) Restore(id uint64) error {
	return r.dbWithCtx().Model(reflect.New(r.modelType).Interface()).
		Unscoped().
		Where(r.primaryKeyField+" = ?", id).
		Update("deleted_at", nil).Error
}

func (r *baseRepositoryImpl) FindByID(id uint64, result interface{}) error {
	db := r.applyPreloads(r.dbWithCtx())
	return db.First(result, id).Error
}

func (r *baseRepositoryImpl) FindWithPagination(pagination *common.Pagination, params map[string]interface{}, results interface{}) (*common.PageResult, error) {
	var total int64

	// 处理排序参数 - 优先使用 params 中的排序参数，如果没有则使用 pagination 中的
	var orderBy string
	if field, ok := params["orderBy"].(string); ok && field != "" {
		order := "ASC"
		if dir, ok := params["order"].(string); ok && (dir == "DESC" || dir == "desc") {
			order = "DESC"
		}
		orderBy = field + " " + order
		delete(params, "orderBy")
		delete(params, "order")
	} else {
		// 使用 pagination 中的排序参数
		orderBy = pagination.GetOrderSQL()
	}

	db := r.queryBuilder.BuildQuery(r.dbWithCtx(), params)
	db = r.applyPreloads(db)

	// 应用排序
	if orderBy != "" {
		db = db.Order(orderBy)
	}

	// 计算总数
	if err := db.Model(reflect.New(r.modelType).Interface()).Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	if err := db.Offset(pagination.GetOffset()).Limit(pagination.GetLimit()).Find(results).Error; err != nil {
		return nil, err
	}

	// 创建分页结果
	return common.NewPageResult(results, total, pagination), nil
}

func (r *baseRepositoryImpl) FindAll(results interface{}) error {
	return r.dbWithCtx().Find(results).Error
}

func (r *baseRepositoryImpl) Count(params map[string]interface{}) (int64, error) {
	var total int64
	db := r.queryBuilder.BuildQuery(r.dbWithCtx(), params)
	err := db.Model(reflect.New(r.modelType).Interface()).Count(&total).Error
	return total, err
}
