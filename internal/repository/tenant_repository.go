package repository

import (
	"context"
	"errors"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/model"
	"gin/internal/models"
	"reflect"
	"time"

	"gorm.io/gorm"
)

// TenantRepo 租户仓库接口
type TenantRepo interface {
	BaseRepository
	FindByDomain(ctx context.Context, domain string) (*models.Tenant, error)
	FindByCode(ctx context.Context, code string) (*models.Tenant, error)
	FindActive(ctx context.Context) ([]*models.Tenant, error)
	FindInactive(ctx context.Context) ([]*models.Tenant, error)
	FindExpired(ctx context.Context) ([]*models.Tenant, error)
	FindExpiring(ctx context.Context, days int) ([]*models.Tenant, error)
	ActivateTenant(ctx context.Context, id uint64) error
	DeactivateTenant(ctx context.Context, id uint64) error
	ExtendExpiration(ctx context.Context, id uint64, months int) error
	SetMaxUsers(ctx context.Context, id uint64, maxUsers int) error
	CountActive(ctx context.Context) (int64, error)
	Search(ctx context.Context, keyword string, isActive *bool, page, pageSize int) ([]*models.Tenant, int64, error)
}

// tenantRepoImpl 租户仓库实现
type tenantRepoImpl struct {
	*baseRepositoryImpl
}

// NewTenantRepository 创建租户仓库
func NewTenantRepository(db *gorm.DB) TenantRepo {
	queryBuilder := createTenantQueryBuilder()
	baseRepo := NewBaseRepository(db, reflect.TypeOf(models.Tenant{}), queryBuilder, "tenant", "id")
	return &tenantRepoImpl{
		baseRepositoryImpl: baseRepo.(*baseRepositoryImpl),
	}
}

// createTenantQueryBuilder 创建租户查询构建器
func createTenantQueryBuilder() *model.QueryBuilder {
	qb := model.NewQueryBuilder()
	return qb.RegisterFields(map[string]model.QueryField{
		"name":           {Column: "name", Operation: model.OpLike, ValueType: model.TypeString},
		"domain":         {Column: "domain", Operation: model.OpLike, ValueType: model.TypeString},
		"code":           {Column: "code", Operation: model.OpLike, ValueType: model.TypeString},
		"contact_name":   {Column: "contact_name", Operation: model.OpLike, ValueType: model.TypeString},
		"contact_email":  {Column: "contact_email", Operation: model.OpLike, ValueType: model.TypeString},
		"contact_phone":  {Column: "contact_phone", Operation: model.OpLike, ValueType: model.TypeString},
		"is_active":      {Column: "is_active", Operation: model.OpEq, ValueType: model.TypeBool},
		"expired_before": {Column: "expired_at", Operation: model.OpLt, ValueType: model.TypeTime},
		"expired_after":  {Column: "expired_at", Operation: model.OpGt, ValueType: model.TypeTime},
	})
}

// Create 创建新租户
func (r *tenantRepoImpl) Create(entity interface{}) error {
	tenant, ok := entity.(*models.Tenant)
	if !ok {
		return errors.New("实体类型错误，应为 *models.Tenant")
	}

	// 验证租户数据
	if err := tenant.Validate(); err != nil {
		return err
	}

	// 检查域名是否已存在
	existing, err := r.FindByDomain(context.Background(), tenant.Domain)
	if err != nil {
		return err
	}
	if existing != nil {
		return errors.New("租户域名已存在")
	}

	// 检查编码是否已存在
	if tenant.Code != "" {
		existing, err = r.FindByCode(context.Background(), tenant.Code)
		if err != nil {
			return err
		}
		if existing != nil {
			return errors.New("租户编码已存在")
		}
	}

	// 创建租户
	return r.baseRepositoryImpl.Create(tenant)
}

// Update 更新租户信息
func (r *tenantRepoImpl) Update(entity interface{}) error {
	tenant, ok := entity.(*models.Tenant)
	if !ok {
		return errors.New("实体类型错误，应为 *models.Tenant")
	}

	// 验证租户数据
	if err := tenant.Validate(); err != nil {
		return err
	}

	// 检查租户是否存在
	var existingTenant models.Tenant
	err := r.DB().First(&existingTenant, tenant.ID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("租户不存在")
		}
		return err
	}

	// 检查域名是否已被其他租户使用
	var count int64
	err = r.DB().Model(&models.Tenant{}).Where("domain = ? AND id != ?", tenant.Domain, tenant.ID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("租户域名已被其他租户使用")
	}

	// 检查编码是否已被其他租户使用
	if tenant.Code != "" {
		err = r.DB().Model(&models.Tenant{}).Where("code = ? AND id != ?", tenant.Code, tenant.ID).Count(&count).Error
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("租户编码已被其他租户使用")
		}
	}

	// 更新租户
	tenant.UpdatedAt = time.Now()
	return r.baseRepositoryImpl.Update(tenant)
}

// FindByID 根据ID查找租户
func (r *tenantRepoImpl) FindByID(id uint64, result interface{}) error {
	return r.baseRepositoryImpl.FindByID(id, result)
}

// FindByDomain 根据域名查找租户
func (r *tenantRepoImpl) FindByDomain(ctx context.Context, domain string) (*models.Tenant, error) {
	var tenant models.Tenant
	err := r.WithContext(ctx).DB().Where("domain = ?", domain).First(&tenant).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &tenant, nil
}

// FindByCode 根据编码查找租户
func (r *tenantRepoImpl) FindByCode(ctx context.Context, code string) (*models.Tenant, error) {
	var tenant models.Tenant
	err := r.WithContext(ctx).DB().Where("code = ?", code).First(&tenant).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &tenant, nil
}

// FindAll 查询所有租户
func (r *tenantRepoImpl) FindAll(results interface{}) error {
	return r.baseRepositoryImpl.FindAll(results)
}

// FindActive 查询所有激活租户
func (r *tenantRepoImpl) FindActive(ctx context.Context) ([]*models.Tenant, error) {
	var tenants []*models.Tenant
	err := r.WithContext(ctx).DB().Where("is_active = ?", true).Find(&tenants).Error
	return tenants, err
}

// FindInactive 查询所有未激活租户
func (r *tenantRepoImpl) FindInactive(ctx context.Context) ([]*models.Tenant, error) {
	var tenants []*models.Tenant
	err := r.WithContext(ctx).DB().Where("is_active = ?", false).Find(&tenants).Error
	return tenants, err
}

// FindExpired 查询所有已过期租户
func (r *tenantRepoImpl) FindExpired(ctx context.Context) ([]*models.Tenant, error) {
	var tenants []*models.Tenant
	now := time.Now()
	err := r.WithContext(ctx).DB().Where("expired_at < ?", now).Find(&tenants).Error
	return tenants, err
}

// FindExpiring 查询即将过期的租户
func (r *tenantRepoImpl) FindExpiring(ctx context.Context, days int) ([]*models.Tenant, error) {
	var tenants []*models.Tenant
	now := time.Now()
	expireDate := now.AddDate(0, 0, days)
	err := r.WithContext(ctx).DB().Where("expired_at BETWEEN ? AND ?", now, expireDate).Find(&tenants).Error
	return tenants, err
}

// ActivateTenant 激活租户
func (r *tenantRepoImpl) ActivateTenant(ctx context.Context, id uint64) error {
	result := r.WithContext(ctx).DB().Model(&models.Tenant{}).Where("id = ?", id).Update("is_active", true)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("租户不存在")
	}
	return nil
}

// DeactivateTenant 停用租户
func (r *tenantRepoImpl) DeactivateTenant(ctx context.Context, id uint64) error {
	result := r.WithContext(ctx).DB().Model(&models.Tenant{}).Where("id = ?", id).Update("is_active", false)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("租户不存在")
	}
	return nil
}

// ExtendExpiration 延长租户过期时间
func (r *tenantRepoImpl) ExtendExpiration(ctx context.Context, id uint64, months int) error {
	// 查找租户
	var tenant models.Tenant
	err := r.FindByID(id, &tenant)
	if err != nil {
		return err
	}

	// 延长过期时间
	tenant.ExtendExpiration(months)
	return r.Update(&tenant)
}

// SetMaxUsers 设置租户最大用户数
func (r *tenantRepoImpl) SetMaxUsers(ctx context.Context, id uint64, maxUsers int) error {
	if maxUsers <= 0 {
		return errors.New("最大用户数必须大于0")
	}

	result := r.WithContext(ctx).DB().Model(&models.Tenant{}).Where("id = ?", id).Updates(map[string]interface{}{
		"max_users":  maxUsers,
		"updated_at": time.Now(),
	})

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("租户不存在")
	}
	return nil
}

// Count 统计租户总数
func (r *tenantRepoImpl) Count(params map[string]interface{}) (int64, error) {
	return r.baseRepositoryImpl.Count(params)
}

// CountActive 统计激活租户数
func (r *tenantRepoImpl) CountActive(ctx context.Context) (int64, error) {
	var count int64
	err := r.WithContext(ctx).DB().Model(&models.Tenant{}).Where("is_active = ?", true).Count(&count).Error
	return count, err
}

// Search 根据条件搜索租户
func (r *tenantRepoImpl) Search(ctx context.Context, keyword string, isActive *bool, page, pageSize int) ([]*models.Tenant, int64, error) {
	var tenants []*models.Tenant
	var total int64

	// 构建查询参数
	params := make(map[string]interface{})
	if keyword != "" {
		// 使用自定义查询，因为需要多字段模糊匹配
		db := r.WithContext(ctx).DB().Model(&models.Tenant{})
		db = db.Where("name LIKE ? OR domain LIKE ? OR code LIKE ? OR contact_name LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")

		// 添加激活状态条件
		if isActive != nil {
			db = db.Where("is_active = ?", *isActive)
		}

		// 统计总数
		err := db.Count(&total).Error
		if err != nil {
			return nil, 0, err
		}

		// 分页查询
		if page > 0 && pageSize > 0 {
			offset := (page - 1) * pageSize
			db = db.Offset(offset).Limit(pageSize)
		}

		// 排序
		db = db.Order("id DESC")

		// 执行查询
		err = db.Find(&tenants).Error
		return tenants, total, err
	} else {
		// 使用基础仓库的分页查询
		if isActive != nil {
			params["is_active"] = *isActive
		}

		// 创建分页对象
		pagination := &common.Pagination{
			Page:     page,
			PageSize: pageSize,
			OrderBy:  "id",
			Order:    "DESC",
		}

		// 使用基础仓库的分页查询
		pageResult, err := r.WithContext(ctx).FindWithPagination(pagination, params, &tenants)
		if err != nil {
			return nil, 0, err
		}

		return tenants, pageResult.Total, nil
	}
}
