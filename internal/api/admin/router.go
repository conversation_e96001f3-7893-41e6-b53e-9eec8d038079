package admin

import (
	"gin/internal/api/admin/admins"
	"gin/internal/api/admin/indexs"
	"gin/internal/infrastructure/context"
	"gin/internal/middleware"
	"net/http"

	"github.com/gin-gonic/gin"
)

// RegisterAdminRoutes 注册后台路由
func RegisterAdminRoutes(engine *gin.Engine) {
	// 创建管理员控制器
	adminController := indexs.NewAdminController()

	adminGroup := engine.Group("/api")
	{
		// 公开接口 - 无需认证和权限验证
		adminGroup.POST("/auth/login", context.Handle(adminController.Login))

		// 仅需认证接口 - 需要JWT认证但不需要权限验证
		authenticated := adminGroup.Group("")
		authenticated.Use(middleware.JWTAuth())
		{
			// 获取用户菜单不需要权限验证，只需要认证
			authenticated.GET("/menu/all", context.Handle(indexs.GetAdminUserMenus))
			// 获取用户信息不需要权限验证，只需要认证
			authenticated.GET("/admin/info", context.Handle(indexs.Info))
			authenticated.GET("/admin/codes", context.Handle(indexs.Code))
			// 添加登出接口
			authenticated.POST("/auth/logout", context.Handle(adminController.Logout))
		}

		// 需要权限验证接口 - 既需要JWT认证也需要权限验证
		authorized := adminGroup.Group("")
		authorized.Use(middleware.JWTAuth())
		authorized.Use(middleware.Authorize())
		{
			system := authorized.Group("/system")
			{
				// 用户管理
				users := system.Group("/admin")
				{
					// 管理员用户相关接口
					users.GET("", context.Handle(adminController.GetAdminUsers))         // 获取管理员用户列表
					users.POST("", context.Handle(adminController.CreateAdminUser))      // 创建管理员用户
					users.GET("/:id", context.Handle(adminController.GetAdminUserByID))  // 获取管理员用户
					users.PUT("/:id", context.Handle(adminController.UpdateAdminUser))    // 更新管理员用户
					users.DELETE("/:id", context.Handle(adminController.DeleteAdminUser)) // 删除管理员用户

					// 密码和状态管理
					users.PUT("/:id/password", context.Handle(adminController.ChangePassword)) // 修改密码
					users.PUT("/:id/reset-password", context.Handle(adminController.ResetPassword)) // 重置密码
					users.PUT("/:id/status", context.Handle(adminController.ChangeStatus))     // 修改状态

					// 保留原有的创建接口
					users.POST("create", context.HandlerJSON(admins.ManageCreate))
				}

				// 角色管理
				roles := system.Group("/role")
				{
					// 保留原有的注释代码
					//roles.POST("", r.handlers.RoleHandler.Create)
					//roles.PUT("/:id", r.handlers.RoleHandler.Update)
					//roles.DELETE("/:id", r.handlers.RoleHandler.Delete)
					//roles.GET("/:id", r.handlers.RoleHandler.Get)
					//roles.GET("/list", r.handlers.RoleHandler.List)
					//roles.PUT("/:id/status", r.handlers.RoleHandler.UpdateStatus)
					//roles.PUT("/:id/dataScope", r.handlers.RoleHandler.UpdateDataScope)
				}
			}

			// 字典管理 (保留原有的注释代码)
			//dict := authorized.Group("/dict")
			//{
			//	// 字典类型管理
			//	types := dict.Group("/types")
			//	{
			//		types.POST("", r.handlers.DictHandler.CreateType)
			//		types.PUT("/:id", r.handlers.DictHandler.UpdateType)
			//		types.DELETE("/:id", r.handlers.DictHandler.DeleteType)
			//		types.GET("/:id", r.handlers.DictHandler.GetType)
			//		types.GET("", r.handlers.DictHandler.ListTypes)
			//	}
			//
			//	// 字典数据管理
			//	data := dict.Group("/data")
			//	{
			//		data.POST("", r.handlers.DictHandler.CreateData)
			//		data.PUT("/:id", r.handlers.DictHandler.UpdateData)
			//		data.DELETE("/:id", r.handlers.DictHandler.DeleteData)
			//		data.GET("/:id", r.handlers.DictHandler.GetData)
			//		data.GET("/type/:typeId", r.handlers.DictHandler.ListDataByType)
			//	}
			//}
		}
	}
}
