package indexs

import (
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/context"
	"gin/internal/infrastructure/logger"
	"gin/internal/infrastructure/response"
	"gin/internal/models"
	"gin/internal/repository"
	"gin/internal/service"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// Menus 获取菜单
func Menus(ctx *context.CustomContext) {
	// 初始化仓库和服务
	adminUserRepository := repository.NewAdminUserRepository()
	tenantRepository := repository.NewTenantRepository()
	permissionService := service.NewPermissionService()

	// 获取用户信息
	adminUserInfo, err := adminUserRepository.FindByID(ctx.Claims.UserID)
	if err != nil {
		logger.Error("获取管理员信息失败", zap.Uint64("userID", ctx.Claims.UserID), zap.Error(err))
		response.AbortInternalServerError(ctx.Context, "获取用户信息失败")
		return
	}

	if adminUserInfo == nil {
		logger.Warn("管理员不存在", zap.Uint64("userID", ctx.Claims.UserID))
		response.AbortNotFound(ctx.Context, "用户不存在")
		return
	}

	// 获取菜单
	var menus []*models.Permission

	// 超级管理员获取所有菜单
	if adminUserInfo.IsAdmin && adminUserInfo.TenantID == 0 {
		menus, err = permissionService.GetAllPermissionMenus()
		if err != nil {
			logger.Error("获取所有菜单失败", zap.Error(err))
			response.AbortInternalServerError(ctx.Context, "获取菜单失败")
			return
		}
	} else {
		// 获取租户信息
		tenant, err := tenantRepository.FindByID(adminUserInfo.TenantID)
		if err != nil {
			logger.Error("获取租户信息失败", zap.Uint64("tenantID", adminUserInfo.TenantID), zap.Error(err))
			response.AbortInternalServerError(ctx.Context, "获取租户信息失败")
			return
		}

		// 设置租户代码
		tenantCode := "*" // 默认使用通配符
		if tenant != nil {
			tenantCode = tenant.Code
		}

		// 获取用户权限
		userID := strconv.FormatUint(adminUserInfo.ID, 10)
		enforcer := casbin.GetEnforcer()
		permissions := enforcer.GetPermissionsForUserInDomain(userID, tenantCode)

		// 提取菜单权限代码
		permCodes := extractMenuPermissionCodes(permissions)
		logger.Info("提取的菜单权限代码",
			zap.Uint64("userID", adminUserInfo.ID),
			zap.Strings("permCodes", permCodes))

		// 根据权限代码获取菜单
		if len(permCodes) > 0 {
			menus, err = permissionService.GetMenusByPermissions(permCodes)
			if err != nil {
				logger.Error("根据权限获取菜单失败", zap.Error(err))
				response.AbortInternalServerError(ctx.Context, "获取菜单失败")
				return
			}
		} else {
			logger.Warn("用户没有任何菜单权限", zap.Uint64("userID", adminUserInfo.ID))
			// 返回空菜单列表
			menus = []*models.Permission{}
		}
	}

	// 构建菜单树
	menuTree := permissionService.BuildPermissionMenuTree(menus, 0)
	response.Success(ctx.Context, menuTree)
}

// extractMenuPermissionCodes 从 Casbin 权限中提取菜单权限代码
func extractMenuPermissionCodes(permissions [][]string) []string {
	// 使用 map 去重
	codeMap := make(map[string]bool)

	// 遍历所有权限
	for _, perm := range permissions {
		// Casbin 权限格式通常是 [sub, dom, obj, act]
		if len(perm) >= 3 {
			// 获取权限对象（菜单代码）
			code := perm[2]

			// 过滤出菜单权限代码，忽略 API 路径
			if !strings.HasPrefix(code, "/") {
				// 如果是菜单权限代码，添加到集合中
				codeMap[code] = true
			}
		}
	}

	// 将 map 转换为切片
	var result []string
	for code := range codeMap {
		result = append(result, code)
	}

	return result
}
