package handler

import (
	"gin/internal/handler/web"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/logger"
	"gin/internal/middleware"
	"gin/internal/repository/repo"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"
)

const (
	WebPrefix = "/api"
)

// WebRouteDefinition 前台路由定义
type WebRouteDefinition struct {
	Path        string
	Method      string
	Handler     gin.HandlerFunc
	Description string
	Group       string
	Auth        bool
}

// WebRouter 前台路由
type WebRouter struct {
	engine         *gin.Engine
	logger         logger.Logger
	config         *config.Config
	webHandler     *WebHandler
	routeGroups    map[string]*gin.RouterGroup
	mu             sync.RWMutex
	initialized    bool
	operateLogRepo repo.OperateLogRepository
}

type WebHandler struct {
	IndexHandler             *web.IndexHandler
	ArticleHandler           *web.ArticleHandler
	UserHandler              *web.UserHandler
	UserLevelHandler         *web.UserLevelHandler
	UserNoticeHandler        *web.UserNoticeHandler
	UserCertificationHandler *web.UserCertificationHandler
	UserAccountHandler       *web.UserAccountHandler
	UserWalletHandler        *web.UserWalletHandler
	UserSwapHandler          *web.UserSwapHandler
	UserTransferHandler      *web.UserTransferHandler
}

// NewWebHandler 创建前台路由
func NewWebHandler(engine *gin.Engine, logger logger.Logger, conf *config.Config, webHandler *WebHandler, operateLogRepo repo.OperateLogRepository) *WebRouter {
	return &WebRouter{
		engine:         engine,
		logger:         logger,
		config:         conf,
		webHandler:     webHandler,
		routeGroups:    make(map[string]*gin.RouterGroup),
		operateLogRepo: operateLogRepo,
	}
}

// Route 路由装饰器
func (r *WebRouter) Route(path, method string, handler gin.HandlerFunc) *WebRouteBuilder {
	return &WebRouteBuilder{
		router: r,
		config: WebRouteDefinition{
			Path:    path,
			Method:  method,
			Handler: handler,
		},
	}
}

// WebRouteBuilder 前台路由构建器
type WebRouteBuilder struct {
	router *WebRouter
	config WebRouteDefinition
}

// Description 添加路由描述
func (rb *WebRouteBuilder) Description(description string) *WebRouteBuilder {
	rb.config.Description = description
	return rb
}

// Group 设置路由组
func (rb *WebRouteBuilder) Group(group string) *WebRouteBuilder {
	rb.config.Group = group
	return rb
}

// Auth 标记路由需要认证（仅JWT，不做授权）
func (rb *WebRouteBuilder) Auth() *WebRouteBuilder {
	rb.config.Auth = true
	return rb
}

// Register 注册路由
func (rb *WebRouteBuilder) Register() {
	rb.router.registerRoute(rb.config)
}

// registerRoute 注册单个路由
func (r *WebRouter) registerRoute(route WebRouteDefinition) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 确定路由组
	groupName := route.Group
	if groupName == "" {
		if route.Auth {
			groupName = "authenticated"
		} else {
			groupName = "public"
		}
	}

	// 获取或创建路由组
	group := r.getOrCreateRouteGroup(groupName)

	// 注册路由
	group.Handle(route.Method, route.Path, route.Handler)
}

// getOrCreateRouteGroup 获取或创建路由组
func (r *WebRouter) getOrCreateRouteGroup(groupName string) *gin.RouterGroup {
	if group, exists := r.routeGroups[groupName]; exists {
		return group
	}

	var group *gin.RouterGroup
	switch groupName {
	case "public":
		group = r.engine.Group(WebPrefix)
	case "authenticated":
		authGroup := r.engine.Group(WebPrefix)
		authGroup.Use(middleware.JWTAuth())
		group = authGroup
	default:
		// 自定义路由组
		group = r.engine.Group(WebPrefix + "/" + groupName)
	}

	r.routeGroups[groupName] = group
	return group
}

// Register 注册所有前台路由
func (r *WebRouter) Register() {
	if r.initialized {
		return
	}

	// 定义所有路由
	r.defineRoutes()

	r.initialized = true
}

// defineRoutes 定义所有前台路由
func (r *WebRouter) defineRoutes() {

	// 首页相关接口
	r.Route("/inits", http.MethodGet, r.webHandler.IndexHandler.Inits()).Description("初始化数据").Register()
	r.Route("/home", http.MethodGet, r.webHandler.IndexHandler.Home).Description("首页数据").Register()
	r.Route("/captcha", http.MethodGet, r.webHandler.IndexHandler.Captcha).Description("获取验证码").Register()
	r.Route("/login", http.MethodPost, r.webHandler.UserHandler.Login).Description("用户登陆").Register()
	r.Route("/register", http.MethodPost, r.webHandler.UserHandler.Register).Description("用户注册").Register()
	r.Route("/payments/:mode", http.MethodGet, r.webHandler.IndexHandler.WalletPayments).Description("钱包支付列表").Register()
	r.Route("/options/bills", http.MethodGet, r.webHandler.UserWalletHandler.BillOptions).Description("账单类型选项").Register()
	r.Route("/helpers", http.MethodGet, r.webHandler.IndexHandler.Helpers).Description("帮助中心").Register()
	r.Route("/articles", http.MethodGet, r.webHandler.ArticleHandler.List).Description("文章列表").Register()
	r.Route("/articles/:id", http.MethodGet, r.webHandler.ArticleHandler.Details).Description("文章详情").Register()

	// 用户相关接口
	r.Route("/upload", http.MethodPost, r.webHandler.IndexHandler.Upload).Auth().Description("文件上传").Register()
	r.Route("/users", http.MethodGet, r.webHandler.UserHandler.UserInfo).Auth().Description("用户信息").Register()
	//r.Route("/users", http.MethodPut, r.webHandler.UserHandler.Update).Auth().Description("用户更新").Register()
	r.Route("/user/invite", http.MethodGet, r.webHandler.UserHandler.InviteList).Auth().Description("用户邀请列表").Register()
	r.Route("/user/password/:type", http.MethodPut, r.webHandler.UserHandler.ChangePassword).Auth().Description("用户密码更新").Register()
	r.Route("/user/email", http.MethodPut, r.webHandler.UserHandler.BindingEmail).Auth().Description("绑定邮箱").Register()
	r.Route("/user/telephone", http.MethodPut, r.webHandler.UserHandler.BindingTelephone).Auth().Description("绑定手机号码").Register()
	r.Route("/user/verify", http.MethodPost, r.webHandler.UserCertificationHandler.Create).Auth().Description("创建用户认证").Register()
	r.Route("/user/verify", http.MethodGet, r.webHandler.UserCertificationHandler.Details).Auth().Description("用户认证详情").Register()
	r.Route("/user/assets", http.MethodGet, r.webHandler.UserHandler.WalletAssets).Auth().Description("用户资产列表").Register()
	r.Route("/user/levels", http.MethodGet, r.webHandler.UserLevelHandler.List).Auth().Description("用户等级列表").Register()
	r.Route("/user/levels", http.MethodPost, r.webHandler.UserLevelHandler.Create).Auth().Description("创建用户等级").Register()

	// 用户账户
	r.Route("/user/accounts", http.MethodGet, r.webHandler.UserAccountHandler.List).Auth().Description("用户账户列表").Register()
	r.Route("/user/accounts", http.MethodPost, r.webHandler.UserAccountHandler.Create).Auth().Description("创建用户账户").Register()
	r.Route("/user/accounts/:id", http.MethodPut, r.webHandler.UserAccountHandler.Update).Auth().Description("更新用户账户").Register()
	r.Route("/user/accounts/:id", http.MethodDelete, r.webHandler.UserAccountHandler.Delete).Auth().Description("删除用户账户").Register()

	// 用户通知
	r.Route("/user/notice", http.MethodGet, r.webHandler.UserNoticeHandler.UserList).Auth().Description("用户通知列表").Register()
	r.Route("/user/notice/:id", http.MethodGet, r.webHandler.UserNoticeHandler.Details).Auth().Description("用户通知详情").Register()

	// 用户钱包
	r.Route("/user/wallets", http.MethodGet, r.webHandler.UserWalletHandler.List).Auth().Description("用户钱包列表").Register()
	r.Route("/user/wallets/bills", http.MethodGet, r.webHandler.UserWalletHandler.Bill).Auth().Description("用户钱包账单列表").Register()
	r.Route("/user/wallets/deposit", http.MethodPost, r.webHandler.UserWalletHandler.Deposit).Auth().Description("用户钱包充值").Register()
	r.Route("/user/wallets/withdraw", http.MethodPost, r.webHandler.UserWalletHandler.Withdraw).Auth().Description("用户钱包提现").Register()
	r.Route("/user/wallets/swaps", http.MethodGet, r.webHandler.UserSwapHandler.List).Auth().Description("用户闪兑列表").Register()
	r.Route("/user/wallets/swaps", http.MethodPost, r.webHandler.UserSwapHandler.Create).Auth().Description("创建用户闪兑").Register()
	r.Route("/user/wallets/transfers", http.MethodGet, r.webHandler.UserTransferHandler.List).Auth().Description("用户转账列表").Register()
	r.Route("/user/wallets/transfers", http.MethodPost, r.webHandler.UserTransferHandler.Create).Auth().Description("创建用户转账").Register()
}
