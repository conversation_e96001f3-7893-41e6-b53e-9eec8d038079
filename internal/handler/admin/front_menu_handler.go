package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// FrontMenuHandler 前端菜单控制器
type FrontMenuHandler struct {
	frontMenuService interfaces.IFrontMenuService
}

// NewFrontMenuHandler 创建前端菜单控制器
func NewFrontMenuHandler(frontMenuService interfaces.IFrontMenuService) *FrontMenuHandler {
	return &FrontMenuHandler{
		frontMenuService: frontMenuService,
	}
}

// List 获取前端菜单列表
func (h *FrontMenuHandler) List(ctx *gin.Context) {
	var req admin.FrontMenuListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.frontMenuService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取前端菜单列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// Update 更新前端菜单
func (h *FrontMenuHandler) Update(ctx *gin.Context) {
	// 获取前端菜单ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.FrontMenuUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.frontMenuService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新前端菜单失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
