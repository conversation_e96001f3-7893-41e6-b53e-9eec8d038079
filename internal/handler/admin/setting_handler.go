package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// SettingHandler 应用设置处理器
type SettingHandler struct {
	settingService interfaces.ISettingService
}

// NewSettingHandler 创建应用设置处理器
func NewSettingHandler(settingService interfaces.ISettingService) *SettingHandler {
	return &SettingHandler{settingService: settingService}
}

// List 获取应用设置列表
func (h *SettingHandler) List(ctx *gin.Context) {
	var req admin.SettingListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.settingService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取应用设置列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Update 更新应用设置
func (h *SettingHandler) Update(ctx *gin.Context) {
	// 获取应用设置ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.SettingUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.settingService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新应用设置失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// UpdateData 更新应用设置数据
func (h *SettingHandler) UpdateData(ctx *gin.Context) {
	// 获取应用设置ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.SettingDataUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.settingService.UpdateData(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新应用设置数据失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
