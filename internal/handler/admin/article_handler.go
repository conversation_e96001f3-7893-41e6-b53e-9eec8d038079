package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// ArticleHandler 文章处理器
type ArticleHandler struct {
	articleService interfaces.IArticleService
}

// NewArticleHandler 创建文章处理器
func NewArticleHandler(articleService interfaces.IArticleService) *ArticleHandler {
	return &ArticleHandler{
		articleService: articleService,
	}
}

// Create 创建文章
func (h *ArticleHandler) Create(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.ArticleCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.articleService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建文章失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新文章
func (h *ArticleHandler) Update(ctx *gin.Context) {
	// 获取文章ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.ArticleUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.articleService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新文章失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Delete 删除文章
func (h *ArticleHandler) Delete(ctx *gin.Context) {
	// 获取文章ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.articleService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除文章失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// GetByID 根据ID获取文章
func (h *ArticleHandler) GetByID(ctx *gin.Context) {
	// 获取文章ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	article, err := h.articleService.GetByID(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取文章失败: "+err.Error())
		return
	}

	if article == nil {
		common.AbortNotFound(ctx, "文章不存在")
		return
	}

	common.Success(ctx, article)
}

// List 获取文章列表
func (h *ArticleHandler) List(ctx *gin.Context) {
	var req admin.ArticleListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.articleService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取文章列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// BatchDelete 批量删除文章
func (h *ArticleHandler) BatchDelete(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.ArticleBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.articleService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除文章失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
