package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserBillHandler 用户账单处理器
type UserBillHandler struct {
	userBillService interfaces.IUserBillService
}

// NewUserBillHandler 创建用户账单处理器
func NewUserBillHandler(userBillService interfaces.IUserBillService) *UserBillHandler {
	return &UserBillHandler{userBillService: userBillService}
}

// List 获取用户账单列表
func (h *UserBillHandler) List(ctx *gin.Context) {
	var req admin.UserBillListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userBillService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取用户账单列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Update 更新用户账单
func (h *UserBillHandler) Update(ctx *gin.Context) {
	// 获取用户账单ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserBillUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userBillService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新用户账单失败: "+err.Error())
		return
	}
}

// Delete 删除用户账单
func (h *UserBillHandler) Delete(ctx *gin.Context) {
	// 获取用户账单ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userBillService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除用户账单失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除用户账单
func (h *UserBillHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userBillService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除用户账单失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
