package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// CategoryHandler 分类处理器
type CategoryHandler struct {
	categoryService interfaces.ICategoryService
}

// NewCategoryHandler 创建分类处理器
func NewCategoryHandler(categoryService interfaces.ICategoryService) *CategoryHandler {
	return &CategoryHandler{
		categoryService: categoryService,
	}
}

// List 获取分类列表
func (h *CategoryHandler) List(ctx *gin.Context) {
	var req admin.CategoryListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.categoryService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取分类列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建分类
func (h *CategoryHandler) Create(ctx *gin.Context) {
	var req admin.CategoryCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.categoryService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建分类失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新分类
func (h *CategoryHandler) Update(ctx *gin.Context) {
	// 获取分类ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.CategoryUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.categoryService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新分类失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除分类
func (h *CategoryHandler) Delete(ctx *gin.Context) {
	// 获取分类ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.categoryService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除分类失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
