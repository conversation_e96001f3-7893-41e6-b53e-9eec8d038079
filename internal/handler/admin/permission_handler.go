package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/logger"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PermissionHandler struct {
	logger            logger.Logger
	cache             *cache.RedisClient
	permissionService interfaces.IPermissionService
}

func NewPermissionHandler(
	logger logger.Logger,
	cache *cache.RedisClient,
	permissionService interfaces.IPermissionService) *PermissionHandler {
	return &PermissionHandler{
		logger:            logger,
		cache:             cache,
		permissionService: permissionService,
	}
}

// List 获取权限列表
func (h *PermissionHandler) List(ctx *gin.Context) {
	var req admin.PermissionListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.permissionService.List(ctx, &req)
	if err != nil {
		h.logger.Error("获取权限列表失败", zap.Error(err))
		common.AbortInternalServerError(ctx, "获取权限列表失败")
		return
	}

	// 构建响应
	common.SuccessWithPagination(ctx, result, total)
}

// Detail 获取权限详情
func (h *PermissionHandler) Detail(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	permission, err := h.permissionService.GetPermissionByID(ctx, id)
	if err != nil {
		h.logger.Error("获取权限详情失败", zap.Uint("id", id), zap.Error(err))
		common.AbortInternalServerError(ctx, "获取权限详情失败")
		return
	}
	if permission == nil {
		common.AbortNotFound(ctx, "权限不存在")
		return
	}

	common.Success(ctx, permission)
}

// Create 创建权限
func (h *PermissionHandler) Create(ctx *gin.Context) {
	var req admin.PermissionCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	permission, err := h.permissionService.Create(ctx, &req)
	if err != nil {
		h.logger.Error("创建权限失败", zap.Any("req", req), zap.Error(err))
		common.AbortInternalServerError(ctx, err.Error())
		return
	}

	common.Success(ctx, permission)
}

// Update 更新权限
func (h *PermissionHandler) Update(ctx *gin.Context) {
	// 获取权限ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.PermissionUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层更新权限
	err := h.permissionService.Update(ctx, id, &req)
	if err != nil {
		h.logger.Error("更新权限失败", zap.Uint("id", id), zap.Error(err))
		common.AbortInternalServerError(ctx, "更新权限失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// GetAll 获取所有权限
func (h *PermissionHandler) GetAll(ctx *gin.Context) {
	permissions, err := h.permissionService.GetAllPermissions(ctx)
	if err != nil {
		h.logger.Error("获取权限失败", zap.Error(err))
		common.AbortInternalServerError(ctx, "获取权限失败")
		return
	}
	common.Success(ctx, permissions)
}
