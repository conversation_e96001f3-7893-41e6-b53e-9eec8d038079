package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserWalletHandler 用户钱包处理器
type UserWalletHandler struct {
	userWalletService interfaces.IUserWalletService
}

// NewUserWalletHandler 创建用户钱包处理器
func NewUserWalletHandler(userWalletService interfaces.IUserWalletService) *UserWalletHandler {
	return &UserWalletHandler{userWalletService: userWalletService}
}

// List 获取用户钱包列表
func (h *UserWalletHandler) List(walletType int8) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req admin.UserWalletListReq
		if err := ctx.ShouldBindQuery(&req); err != nil {
			common.BadRequest(ctx, "参数验证失败", err)
			return
		}

		// 调用服务层方法
		result, total, err := h.userWalletService.List(ctx, walletType, &req)
		if err != nil {
			common.Error(ctx, "获取用户钱包列表失败: "+err.Error())
			return
		}
		common.SuccessWithPagination(ctx, result, total)
	}

}

// Create 创建用户钱包
func (h *UserWalletHandler) Create(walletType int8) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req admin.UserWalletCreateReq
		if err := ctx.ShouldBindJSON(&req); err != nil {
			common.BadRequest(ctx, "参数验证失败", err)
			return
		}

		// 调用服务层方法
		_, err := h.userWalletService.Create(ctx, "zh-CN", req.UserID, &req)
		if err != nil {
			common.Error(ctx, "创建用户钱包失败: "+err.Error())
			return
		}
		common.Success(ctx, nil)
	}
}

// Update 更新用户钱包
func (h *UserWalletHandler) Update(ctx *gin.Context) {
	// 获取用户钱包ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserWalletUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userWalletService.Update(ctx, id, 1, &req)
	if err != nil {
		common.Error(ctx, "更新用户钱包失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除用户钱包
func (h *UserWalletHandler) Delete(ctx *gin.Context) {
	// 获取用户钱包ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userWalletService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除用户钱包失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// DepositStatus 更新充值状态
func (h *UserWalletHandler) DepositStatus(ctx *gin.Context) {
	// 获取用户钱包ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserWalletStatusReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userWalletService.UpdateDepositStatus(ctx, "zh-CN", id, req.Status, &req)
	if err != nil {
		common.Error(ctx, "更新充值状态失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// WithdrawStatus 更新提现状态
func (h *UserWalletHandler) WithdrawStatus(ctx *gin.Context) {
	// 获取用户钱包ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserWalletStatusReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userWalletService.UpdateWithdrawStatus(ctx, "zh-CN", id, req.Status, &req)
	if err != nil {
		common.Error(ctx, "更新提现状态失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
