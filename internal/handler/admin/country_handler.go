package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// CountryHandler 国家处理器
type CountryHandler struct {
	countryService interfaces.ICountryService
}

// NewCountryHandler 创建国家处理器
func NewCountryHandler(countryService interfaces.ICountryService) *CountryHandler {
	return &CountryHandler{
		countryService: countryService,
	}
}

// Create 创建国家
func (h *CountryHandler) Create(ctx *gin.Context) {
	var req admin.CountryCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	// 调用服务层方法
	err := h.countryService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建国家失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新国家
func (h *CountryHandler) Update(ctx *gin.Context) {
	// 获取国家ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.CountryUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.countryService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新国家失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Delete 删除国家
func (h *CountryHandler) Delete(ctx *gin.Context) {
	// 获取国家ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.countryService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除国家失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// GetByID 根据ID获取国家
func (h *CountryHandler) GetByID(ctx *gin.Context) {
	// 获取国家ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	country, err := h.countryService.GetByID(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取国家失败: "+err.Error())
		return
	}

	if country == nil {
		common.AbortNotFound(ctx, "国家不存在")
		return
	}

	common.Success(ctx, country)
}

// List 获取国家列表
func (h *CountryHandler) List(ctx *gin.Context) {
	var req admin.CountryListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.countryService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取国家列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// BatchDelete 批量删除国家
func (h *CountryHandler) BatchDelete(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.CountryBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.countryService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除国家失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
