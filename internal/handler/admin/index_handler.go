package admin

import (
	"github.com/gin-gonic/gin"
)

type IndexHandler struct {
}

func NewIndexHandler() *IndexHandler {
	return &IndexHandler{}
}

// Analysis 分析页数据
func (h *IndexHandler) Analysis(c *gin.Context) {
	c.<PERSON><PERSON><PERSON>(200, gin.H{
		"message": "Hello World",
	})
}

// Console 工作台数据
func (h *IndexHandler) Console(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Hello World",
	})
}
