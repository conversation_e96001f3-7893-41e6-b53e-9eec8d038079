package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"

	"github.com/gin-gonic/gin"
)

// AssetHandler 资产管理处理器
type AssetHandler struct {
	assetService interfaces.IAssetService
}

// NewAssetHandler 创建资产处理器
func NewAssetHandler(assetService interfaces.IAssetService) *AssetHandler {
	return &AssetHandler{
		assetService: assetService,
	}
}

// List 获取资产列表
func (h *AssetHandler) List(ctx *gin.Context) {
	var req admin.AssetListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	result, total, err := h.assetService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取资产列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建资产
func (h *AssetHandler) Create(ctx *gin.Context) {
	var req admin.AssetCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	err := h.assetService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建资产失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新资产
func (h *AssetHandler) Update(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	var req admin.AssetUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	err := h.assetService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新资产失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除资产
func (h *AssetHandler) Delete(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	err := h.assetService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除资产失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除资产
func (h *AssetHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	err := h.assetService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除资产失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
