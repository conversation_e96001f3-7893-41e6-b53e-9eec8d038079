package admin

import (
	"context"
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
)

type MonitorHandler struct {
	// 添加缓存相关字段
	cache      *systemInfoCache
	cacheMutex sync.RWMutex
}

// systemInfoCache 系统信息缓存
type systemInfoCache struct {
	data      *admin.MonitorSearchReq
	timestamp time.Time
	ttl       time.Duration
}

// isValid 检查缓存是否有效
func (c *systemInfoCache) isValid() bool {
	return time.Since(c.timestamp) < c.ttl
}

func NewMonitorHandler() *MonitorHandler {
	return &MonitorHandler{
		cache: &systemInfoCache{
			ttl: 5 * time.Second, // 缓存5秒
		},
	}
}

func (h *MonitorHandler) Server(ctx *gin.Context) {
	// 检查缓存
	h.cacheMutex.RLock()
	if h.cache.isValid() {
		data := h.cache.data
		h.cacheMutex.RUnlock()
		common.Success(ctx, data)
		return
	}
	h.cacheMutex.RUnlock()

	// 使用带超时的context，防止长时间阻塞
	timeoutCtx, cancel := context.WithTimeout(ctx.Request.Context(), 3*time.Second)
	defer cancel()

	// 并发获取系统信息
	var (
		cpuUsed   *admin.CpuUsage
		memUsed   *admin.MemUsage
		systemSes *admin.SystemSes
		diskList  []disk.UsageStat
		wg        sync.WaitGroup
		mu        sync.Mutex
		errors    []error
	)

	// 并发获取CPU信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := h.getCPUInfo(timeoutCtx, &cpuUsed, &mu); err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
		}
	}()

	// 并发获取内存信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := h.getMemoryInfo(timeoutCtx, &memUsed, &mu); err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
		}
	}()

	// 并发获取系统信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := h.getSystemInfo(timeoutCtx, ctx, &systemSes, &mu); err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
		}
	}()

	// 并发获取磁盘信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := h.getDiskInfo(timeoutCtx, &diskList, &mu); err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
		}
	}()

	wg.Wait()

	// 如果有错误且所有信息都获取失败，返回错误
	if len(errors) > 0 && cpuUsed == nil && memUsed == nil && systemSes == nil {
		common.Error(ctx, "获取系统信息失败")
		return
	}

	// 构造响应数据
	result := &admin.MonitorSearchReq{
		SystemSes:   systemSes,
		StorageSes:  diskList,
		CpuUsage:    cpuUsed,
		MemoryUsage: memUsed,
	}

	// 更新缓存
	h.cacheMutex.Lock()
	h.cache.data = result
	h.cache.timestamp = time.Now()
	h.cacheMutex.Unlock()

	common.Success(ctx, result)
}

// getCPUInfo 获取CPU信息
func (h *MonitorHandler) getCPUInfo(ctx context.Context, cpuUsed **admin.CpuUsage, mu *sync.Mutex) error {
	defer func() {
		if r := recover(); r != nil {
			// 记录panic但不中断程序
		}
	}()

	cpuInfo := &admin.CpuUsage{
		CpuNum: runtime.NumCPU(),
	}

	// 使用非阻塞方式获取CPU使用率
	cpuPercent, err := cpu.Percent(100*time.Millisecond, false)
	if err == nil && len(cpuPercent) > 0 {
		cpuInfo.CpuUsed = cpuPercent[0]
	}

	// 获取负载信息
	if loadInfo, err := load.AvgWithContext(ctx); err == nil {
		cpuInfo.CpuAvg5 = loadInfo.Load5
		cpuInfo.CpuAvg15 = loadInfo.Load15
	}

	mu.Lock()
	*cpuUsed = cpuInfo
	mu.Unlock()
	return nil
}

// getMemoryInfo 获取内存信息
func (h *MonitorHandler) getMemoryInfo(ctx context.Context, memUsed **admin.MemUsage, mu *sync.Mutex) error {
	defer func() {
		if r := recover(); r != nil {
			// 记录panic但不中断程序
		}
	}()

	memory := &admin.MemUsage{}

	if v, err := mem.VirtualMemoryWithContext(ctx); err == nil {
		memory.MemTotal = v.Total
		memory.MemUsed = v.Used
		memory.MemFree = memory.MemTotal - memory.MemUsed
		if usage, err := strconv.ParseFloat(fmt.Sprintf("%.2f", v.UsedPercent), 64); err == nil {
			memory.MemUsage = usage
		}
	}

	mu.Lock()
	*memUsed = memory
	mu.Unlock()
	return nil
}

// getSystemInfo 获取系统信息
func (h *MonitorHandler) getSystemInfo(ctx context.Context, ginCtx *gin.Context, systemSes **admin.SystemSes, mu *sync.Mutex) error {
	defer func() {
		if r := recover(); r != nil {
			// 记录panic但不中断程序
		}
	}()

	system := &admin.SystemSes{
		SysComputerIp: ginCtx.ClientIP(),
		SysOsArch:     runtime.GOARCH,
		SysOsName:     runtime.GOOS,
		GoName:        "GoLang",
		GoVersion:     runtime.Version(),
		GoStartTime:   time.Now(), // 这里应该是程序启动时间，建议改为全局变量
	}

	// 获取系统详细信息
	if sysInfo, err := host.InfoWithContext(ctx); err == nil {
		system.SysComputerName = sysInfo.Hostname
		if sysInfo.OS != "" {
			system.SysOsName = sysInfo.OS
		}
		if sysInfo.KernelArch != "" {
			system.SysOsArch = sysInfo.KernelArch
		}
	}

	// 计算运行时长（应该基于程序启动时间）
	system.GoRunTime = time.Now().Unix() - system.GoStartTime.Unix()

	mu.Lock()
	*systemSes = system
	mu.Unlock()
	return nil
}

// getDiskInfo 获取磁盘信息
func (h *MonitorHandler) getDiskInfo(ctx context.Context, diskList *[]disk.UsageStat, mu *sync.Mutex) error {
	defer func() {
		if r := recover(); r != nil {
			// 记录panic但不中断程序
		}
	}()

	var disks []disk.UsageStat

	diskInfo, err := disk.PartitionsWithContext(ctx, true)
	if err != nil {
		return err
	}

	// 并发获取每个分区的使用情况
	var diskWg sync.WaitGroup
	var diskMu sync.Mutex
	diskChan := make(chan disk.UsageStat, len(diskInfo))

	for _, p := range diskInfo {
		diskWg.Add(1)
		go func(partition disk.PartitionStat) {
			defer diskWg.Done()
			if diskDetail, err := disk.UsageWithContext(ctx, partition.Mountpoint); err == nil {
				if usage, err := strconv.ParseFloat(fmt.Sprintf("%.2f", diskDetail.UsedPercent), 64); err == nil {
					diskDetail.UsedPercent = usage
				}
				diskMu.Lock()
				diskChan <- *diskDetail
				diskMu.Unlock()
			}
		}(p)
	}

	// 等待所有磁盘信息获取完成
	go func() {
		diskWg.Wait()
		close(diskChan)
	}()

	// 收集磁盘信息
	for usageStat := range diskChan {
		disks = append(disks, usageStat)
	}

	mu.Lock()
	*diskList = disks
	mu.Unlock()
	return nil
}
