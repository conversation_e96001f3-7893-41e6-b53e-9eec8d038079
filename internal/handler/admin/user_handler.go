package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService interfaces.IUserService
}

func NewUserHandler(userService interfaces.IUserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

func (h *UserHandler) List(ctx *gin.Context) {
	var req admin.UserListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取用户列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

func (h *UserHandler) Create(ctx *gin.Context) {
	var req admin.UserCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建用户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

func (h *UserHandler) Update(ctx *gin.Context) {
	// 获取用户ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新用户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

func (h *UserHandler) Delete(ctx *gin.Context) {
	// 获取用户ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除用户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

func (h *UserHandler) BatchDelete(ctx *gin.Context) {
	var req admin.UserBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除用户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
