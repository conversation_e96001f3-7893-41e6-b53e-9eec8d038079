package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserSwapHandler 用户闪兑处理器
type UserSwapHandler struct {
	userSwapService interfaces.IUserSwapService
}

// NewUserSwapHandler 创建用户闪兑处理器
func NewUserSwapHandler(userSwapService interfaces.IUserSwapService) *UserSwapHandler {
	return &UserSwapHandler{userSwapService: userSwapService}
}

// List 获取用户闪兑列表
func (h *UserSwapHandler) List(ctx *gin.Context) {
	var req admin.UserSwapListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userSwapService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取用户闪兑列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建用户闪兑
func (h *UserSwapHandler) Create(ctx *gin.Context) {
	var req admin.UserSwapCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	_, err := h.userSwapService.Create(ctx, "zh-CN", &req)
	if err != nil {
		common.Error(ctx, "创建用户闪兑失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新用户闪兑
func (h *UserSwapHandler) Update(ctx *gin.Context) {
	// 获取用户闪兑ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserSwapUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userSwapService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新用户闪兑失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除用户闪兑
func (h *UserSwapHandler) Delete(ctx *gin.Context) {
	// 获取用户闪兑ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userSwapService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除用户闪兑失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除用户闪兑
func (h *UserSwapHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userSwapService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除用户闪兑失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
