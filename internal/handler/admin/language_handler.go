package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// LangHandler 语言处理器
type LangHandler struct {
	languageService interfaces.ILanguageService
}

// NewLangHandler 创建语言处理器
func NewLangHandler(languageService interfaces.ILanguageService) *LangHandler {
	return &LangHandler{
		languageService: languageService,
	}
}

// Create 创建语言
func (h *LangHandler) Create(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.LanguageCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.languageService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建语言失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新语言
func (h *LangHandler) Update(ctx *gin.Context) {
	// 获取语言ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.LanguageUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.languageService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新语言失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Delete 删除语言
func (h *LangHandler) Delete(ctx *gin.Context) {
	// 获取语言ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.languageService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除语言失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// BatchDelete 批量删除语言
func (h *LangHandler) BatchDelete(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.LanguageBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.languageService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除语言失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// List 获取语言列表
func (h *LangHandler) List(ctx *gin.Context) {
	var req admin.LanguageListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.languageService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取语言列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}
