package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService interfaces.IOrderService
}

// NewOrderHandler 创建订单处理器
func NewOrderHandler(orderService interfaces.IOrderService) *OrderHandler {
	return &OrderHandler{orderService: orderService}
}

// List 获取订单列表
func (h *OrderHandler) List(ctx *gin.Context) {
	var req admin.OrderListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.orderService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取订单列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建订单
func (h *OrderHandler) Create(ctx *gin.Context) {
	var req admin.OrderCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.orderService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建订单失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新订单
func (h *OrderHandler) Update(ctx *gin.Context) {
	// 获取订单ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.OrderUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.orderService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新订单失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除订单
func (h *OrderHandler) Delete(ctx *gin.Context) {
	// 获取订单ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.orderService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除订单失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
