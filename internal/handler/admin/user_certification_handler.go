package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserCertificationHandler 用户认证处理器
type UserCertificationHandler struct {
	userCertificationService interfaces.IUserCertificationService
}

// NewUserCertificationHandler 创建用户认证处理器
func NewUserCertificationHandler(userCertificationService interfaces.IUserCertificationService) *UserCertificationHandler {
	return &UserCertificationHandler{userCertificationService: userCertificationService}
}

// List 获取用户认证列表
func (h *UserCertificationHandler) List(ctx *gin.Context) {
	var req admin.UserCertificationListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userCertificationService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取用户认证列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建用户认证
func (h *UserCertificationHandler) Create(ctx *gin.Context) {
	var req admin.UserCertificationCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	_, err := h.userCertificationService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建用户认证失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新用户认证
func (h *UserCertificationHandler) Update(ctx *gin.Context) {
	// 获取用户认证ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserCertificationUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userCertificationService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新用户认证失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Status 审核用户认证
func (h *UserCertificationHandler) Status(ctx *gin.Context) {
	// 获取用户认证ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserCertificationStatusReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userCertificationService.Status(ctx, id, req.Status, &req)
	if err != nil {
		common.Error(ctx, "审核用户认证失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除用户认证
func (h *UserCertificationHandler) Delete(ctx *gin.Context) {
	// 获取用户认证ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userCertificationService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除用户认证失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除用户认证
func (h *UserCertificationHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userCertificationService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除用户认证失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
