package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
	"strconv"
)

// LoginLogHandler 登录日志处理器
type LoginLogHandler struct {
	LoginLogService interfaces.ILoginLogService
}

// NewLoginLogHandler 创建一个新的登录日志处理器
func NewLoginLogHandler(loginLogService interfaces.ILoginLogService) *LoginLogHandler {
	return &LoginLogHandler{
		LoginLogService: loginLogService,
	}
}

// List 获取登录日志列表
func (h *LoginLogHandler) List(c *gin.Context) {
	var req admin.LoginLogListRequest
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		common.AbortValidationFailure(c, err.Error(), err)
		return
	}

	logs, total, err := h.LoginLogService.List(c, &req)
	if err != nil {
		common.AbortInternalServerError(c, "获取登录日志列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(c, logs, total)
}

// Delete 删除登录日志
func (h *LoginLogHandler) Delete(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	err := h.LoginLogService.HardDelete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除登录日志失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// BatchDelete 批量删除登录日志
func (h *LoginLogHandler) BatchDelete(c *gin.Context) {
	var req admin.LoginLogBatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数验证失败", err)
		return
	}

	err := h.LoginLogService.BulkHardDelete(c, req.IDs)
	if err != nil {
		common.AbortInternalServerError(c, "批量删除登录日志失败: "+err.Error())
		return
	}

	common.Success(c, nil)
}

// CleanupOldLogs 清理旧的登录日志
func (h *LoginLogHandler) CleanupOldLogs(c *gin.Context) {
	days, err := strconv.Atoi(c.Query("days"))
	if err != nil || days <= 0 {
		common.AbortValidationFailure(c, "无效的天数", err)
		return
	}

	err = h.LoginLogService.CleanupOldLogs(c.Request.Context(), days)
	if err != nil {
		common.AbortInternalServerError(c, "清理登录日志失败: "+err.Error())
		return
	}
	common.Success(c, "登录日志清理成功")
}
