package admin

import (
	"context"
	infraContext "gin/internal/infrastructure/context"
	"gin/pkg/convert"

	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MenuHandler 菜单控制器
type MenuHandler struct {
	logger         logger.Logger
	menuService    interfaces.IMenuService
	managerService interfaces.IManagerService
}

// NewMenuHandler 创建菜单控制器
func NewMenuHandler(logger logger.Logger, menuService interfaces.IMenuService, managerService interfaces.IManagerService) *MenuHandler {
	return &MenuHandler{
		logger:         logger,
		menuService:    menuService,
		managerService: managerService,
	}
}

// List 获取菜单列表
func (h *MenuHandler) List(c *gin.Context) {
	ctx := context.Background()

	var req admin.MenuListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(c, "参数错误", err)
		return
	}

	result, total, err := h.menuService.List(ctx, &req)
	if err != nil {
		h.logger.Error("获取菜单列表失败", zap.Error(err))
		common.AbortInternalServerError(c, "获取菜单列表失败")
		return
	}

	// 构建菜单树
	menuTree := h.menuService.BuildMenuTree(result, 0)

	common.SuccessWithPagination(c, menuTree, total)
}

// All 获取所有菜单（树形结构）
func (h *MenuHandler) All(c *gin.Context) {
	ctx := context.Background()

	result, err := h.menuService.GetAllMenus(ctx)
	if err != nil {
		h.logger.Error("获取所有菜单失败", zap.Error(err))
		common.AbortInternalServerError(c, "获取菜单失败")
		return
	}

	// 构建菜单树
	menuTree := h.menuService.BuildMenuTree(result, 0)

	common.Success(c, menuTree)
}

// Menus 获取管理菜单
func (h *MenuHandler) Menus(ctx *gin.Context) {
	manager, err := h.managerService.FindByID(ctx, infraContext.GetManagerIdFromCtx(ctx))
	if err != nil {
		common.AbortInternalServerError(ctx, "获取管理信息失败")
		return
	}

	menus, err := h.menuService.GetMenusForUser(ctx, manager)
	if err != nil {
		h.logger.Error("获取用户菜单失败", zap.Uint("userId", manager.ID), zap.Error(err))
		common.AbortInternalServerError(ctx, "获取菜单失败")
		return
	}

	// 构建菜单树
	menuTree := h.menuService.BuildMenuTree(menus, 0)

	common.Success(ctx, menuTree)
}

// Detail 获取菜单详情
func (h *MenuHandler) Detail(c *gin.Context) {
	ctx := context.Background()
	id := convert.StringToUint(c.Param("id"))

	menu, err := h.menuService.GetMenuByID(ctx, id)
	if err != nil {
		h.logger.Error("获取菜单详情失败", zap.Uint("id", id), zap.Error(err))
		common.AbortInternalServerError(c, "获取菜单详情失败")
		return
	}
	if menu == nil {
		common.AbortNotFound(c, "菜单不存在")
		return
	}

	// 获取关联的权限
	permissions, err := h.menuService.GetMenuPermissions(ctx, id)
	if err != nil {
		h.logger.Error("获取菜单权限失败", zap.Uint("menuId", id), zap.Error(err))
		// 不返回错误，只记录日志
		permissions = []*models.Permission{}
	}

	common.Success(c, admin.MenuResponse{
		Menu:        menu,
		Permissions: permissions,
	})
}

// Create 创建菜单
func (h *MenuHandler) Create(c *gin.Context) {
	ctx := context.Background()

	var req admin.MenuCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数验证失败", err)
		return
	}

	menu, err := h.menuService.Create(ctx, &req)
	if err != nil {
		h.logger.Error("创建菜单失败", zap.Any("req", req), zap.Error(err))
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, menu)
}

// Update 更新菜单
func (h *MenuHandler) Update(c *gin.Context) {
	ctx := context.Background()
	id := convert.StringToUint(c.Param("id"))

	var req admin.MenuUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数验证失败", err)
		return
	}

	if err := h.menuService.Update(ctx, id, &req); err != nil {
		h.logger.Error("更新菜单失败", zap.Uint("id", id), zap.Any("req", req), zap.Error(err))
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, nil)
}

// Delete 删除菜单
func (h *MenuHandler) Delete(c *gin.Context) {
	ctx := context.Background()
	id := convert.StringToUint(c.Param("id"))

	if err := h.menuService.Delete(ctx, id); err != nil {
		h.logger.Error("删除菜单失败", zap.Uint("id", id), zap.Error(err))
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, nil)
}

// BatchDelete 批量删除菜单
func (h *MenuHandler) BatchDelete(c *gin.Context) {
	ctx := context.Background()

	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数错误", err)
		return
	}

	if err := h.menuService.BatchDelete(ctx, req.IDs); err != nil {
		h.logger.Error("批量删除菜单失败", zap.Any("ids", req.IDs), zap.Error(err))
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, nil)
}

// AssignPermissions 为菜单分配权限
func (h *MenuHandler) AssignPermissions(c *gin.Context) {
	ctx := context.Background()
	id := convert.StringToUint(c.Param("id"))

	var req struct {
		PermissionIDs []uint `json:"permissionIds" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数错误", err)
		return
	}

	if err := h.menuService.AssignPermissionsToMenu(ctx, id, req.PermissionIDs); err != nil {
		h.logger.Error("分配菜单权限失败",
			zap.Uint("menuId", id),
			zap.Any("permissionIds", req.PermissionIDs),
			zap.Error(err))
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, nil)
}

// RemovePermissions 移除菜单权限
func (h *MenuHandler) RemovePermissions(c *gin.Context) {
	ctx := context.Background()
	id := convert.StringToUint(c.Param("id"))

	var req struct {
		PermissionIDs []uint `json:"permissionIds" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数错误", err)
		return
	}

	if err := h.menuService.RemovePermissionsFromMenu(ctx, id, req.PermissionIDs); err != nil {
		h.logger.Error("移除菜单权限失败",
			zap.Uint("menuId", id),
			zap.Any("permissionIds", req.PermissionIDs),
			zap.Error(err))
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, nil)
}

// GetPermissions 获取菜单关联的权限
func (h *MenuHandler) GetPermissions(c *gin.Context) {
	ctx := context.Background()
	id := convert.StringToUint(c.Param("id"))

	permissions, err := h.menuService.GetMenuPermissions(ctx, id)
	if err != nil {
		h.logger.Error("获取菜单权限失败", zap.Uint("menuId", id), zap.Error(err))
		common.AbortInternalServerError(c, "获取菜单权限失败")
		return
	}

	common.Success(c, permissions)
}
