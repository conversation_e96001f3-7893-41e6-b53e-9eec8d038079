package admin

import (
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserAssetHandler 用户资产处理器
type UserAssetHandler struct {
	userAssetService interfaces.IUserAssetService
}

// NewUserAssetHandler 创建用户资产处理器
func NewUserAssetHandler(userAssetService interfaces.IUserAssetService) *UserAssetHandler {
	return &UserAssetHandler{
		userAssetService: userAssetService,
	}
}

// List 获取用户资产列表
func (h *UserAssetHandler) List(ctx *gin.Context) {
	// todo
}

// Create 创建用户资产
func (h *UserAssetHandler) Create(ctx *gin.Context) {
	// todo
}

// Update 更新用户资产
func (h *UserAssetHandler) Update(ctx *gin.Context) {
	// todo
}

// Delete 删除用户资产
func (h *UserAssetHandler) Delete(ctx *gin.Context) {
	// todo
}

// BatchDelete 批量删除用户资产
func (h *UserAssetHandler) BatchDelete(ctx *gin.Context) {
	// todo
}
