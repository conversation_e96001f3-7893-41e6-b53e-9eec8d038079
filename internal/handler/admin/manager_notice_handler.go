package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	infraContext "gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// ManagerNoticeHandler 管理员通知处理器
type ManagerNoticeHandler struct {
	managerNoticeService interfaces.IManagerNoticeService
}

// NewManagerNoticeHandler 创建管理员通知处理器
func NewManagerNoticeHandler(managerNoticeService interfaces.IManagerNoticeService) *ManagerNoticeHandler {
	return &ManagerNoticeHandler{
		managerNoticeService: managerNoticeService,
	}
}

// ListForSender 获取发送者是当前管理的通知列表
func (h *ManagerNoticeHandler) ListForSender(ctx *gin.Context) {

	var req admin.ManagerNoticeListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	managerID := infraContext.GetManagerIdFromCtx(ctx)

	result, total, err := h.managerNoticeService.ListForSender(ctx, managerID, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取管理员通知列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建管理员通知
func (h *ManagerNoticeHandler) Create(ctx *gin.Context) {
	var req admin.ManagerNoticeCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	managerID := infraContext.GetManagerIdFromCtx(ctx)

	err := h.managerNoticeService.Create(ctx, managerID, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建管理员通知失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Update 更新管理员通知
func (h *ManagerNoticeHandler) Update(ctx *gin.Context) {
	var req admin.ManagerNoticeUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	id := convert.StringToUint(ctx.Param("id"))

	err := h.managerNoticeService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新管理员通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除管理员通知
func (h *ManagerNoticeHandler) Delete(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	err := h.managerNoticeService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除管理员通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除管理员通知
func (h *ManagerNoticeHandler) BatchDelete(ctx *gin.Context) {
	var req admin.ManagerNoticeBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	err := h.managerNoticeService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除管理员通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// ListForReceiver 获取接收者是当前管理的通知列表
func (h *ManagerNoticeHandler) ListForReceiver(ctx *gin.Context) {
	var req admin.ManagerNoticeListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	managerID := infraContext.GetManagerIdFromCtx(ctx)

	result, total, err := h.managerNoticeService.ListForReceiver(ctx, managerID, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取管理员通知列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// MarkAsRead 标记为已读
func (h *ManagerNoticeHandler) MarkAsRead(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	err := h.managerNoticeService.MarkAsRead(ctx, infraContext.GetManagerIdFromCtx(ctx), id)
	if err != nil {
		common.AbortInternalServerError(ctx, "标记为已读失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// DeleteForReceiver 删除接收者是当前管理的通知
func (h *ManagerNoticeHandler) DeleteForReceiver(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	err := h.managerNoticeService.DeleteForReceiver(ctx, infraContext.GetManagerIdFromCtx(ctx), id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
