package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
	"strconv"
)

// OperateLogHandler 操作日志处理器
type OperateLogHandler struct {
	OperateLogService interfaces.IOperateLogService
}

// NewOperateLogHandler 创建操作日志处理器
func NewOperateLogHandler(operateLogService interfaces.IOperateLogService) *OperateLogHandler {
	return &OperateLogHandler{
		OperateLogService: operateLogService,
	}
}

// List 获取操作日志列表
func (h *OperateLogHandler) List(c *gin.Context) {
	var req admin.OperateLogListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(c, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.OperateLogService.List(c, &req)
	if err != nil {
		common.AbortInternalServerError(c, "获取操作日志列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(c, result, total)
}

// Delete 删除操作日志
func (h *OperateLogHandler) Delete(c *gin.Context) {
	id := convert.StringToUint(c.Param("id"))

	err := h.OperateLogService.HardDelete(c, id)
	if err != nil {
		common.AbortInternalServerError(c, "删除操作日志失败: "+err.Error())
		return
	}

	common.Success(c, nil)
}

// BatchDelete 批量删除操作日志
func (h *OperateLogHandler) BatchDelete(c *gin.Context) {
	var req admin.OperateLogBatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(c, "参数验证失败", err)
		return
	}

	err := h.OperateLogService.BulkHardDelete(c, req.IDs)
	if err != nil {
		common.AbortInternalServerError(c, "批量删除操作日志失败: "+err.Error())
		return
	}

	common.Success(c, nil)
}

// CleanupOldLogs 清理指定天数之前的操作日志
func (h *OperateLogHandler) CleanupOldLogs(c *gin.Context) {
	days, err := strconv.Atoi(c.Query("days"))
	if err != nil || days <= 0 {
		common.AbortValidationFailure(c, "无效的天数", err)
		return
	}

	err = h.OperateLogService.CleanupOldLogs(c.Request.Context(), days)
	if err != nil {
		common.AbortInternalServerError(c, "清理操作日志失败: "+err.Error())
		return
	}
	common.Success(c, "操作日志清理成功")
}
