package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// LevelHandler 等级处理器
type LevelHandler struct {
	levelService interfaces.ILevelService
}

// NewLevelHandler 创建等级处理器
func NewLevelHandler(levelService interfaces.ILevelService) *LevelHandler {
	return &LevelHandler{
		levelService: levelService,
	}
}

// List 获取等级列表
func (h *LevelHandler) List(ctx *gin.Context) {
	var req admin.LevelListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.levelService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取等级列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)

}

// Create 创建等级
func (h *LevelHandler) Create(ctx *gin.Context) {
	var req admin.LevelCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.levelService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新等级
func (h *LevelHandler) Update(ctx *gin.Context) {
	// 获取等级ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.LevelUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.levelService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除等级
func (h *LevelHandler) Delete(ctx *gin.Context) {
	// 获取等级ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.levelService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除等级
func (h *LevelHandler) BatchDelete(ctx *gin.Context) {
	var req admin.LevelBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.levelService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
