package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserNoticeHandler 用户通知处理器
type UserNoticeHandler struct {
	userNoticeService interfaces.IUserNoticeService
}

// NewUserNoticeHandler 创建用户通知处理器
func NewUserNoticeHandler(userNoticeService interfaces.IUserNoticeService) *UserNoticeHandler {
	return &UserNoticeHandler{
		userNoticeService: userNoticeService,
	}
}

// List 获取用户通知列表
func (h *UserNoticeHandler) List(ctx *gin.Context) {
	var req admin.UserNoticeListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userNoticeService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取用户通知列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建用户通知
func (h *UserNoticeHandler) Create(ctx *gin.Context) {
	var req admin.UserNoticeCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userNoticeService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建用户通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新用户通知
func (h *UserNoticeHandler) Update(ctx *gin.Context) {
	var req admin.UserNoticeUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userNoticeService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新用户通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除用户通知
func (h *UserNoticeHandler) Delete(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userNoticeService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除用户通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除用户通知
func (h *UserNoticeHandler) BatchDelete(ctx *gin.Context) {
	var req admin.UserNoticeBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userNoticeService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除用户通知失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
