package admin

import (
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/config"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// UploadHandler 文件上传处理器
type UploadHandler struct {
}

// NewUploadHandler 创建文件上传处理器
func NewUploadHandler() *UploadHandler {
	return &UploadHandler{}
}

// Upload 文件上传
func (h *UploadHandler) Upload(ctx *gin.Context) {
	// 获取上传类型
	uploadType := ctx.DefaultQuery("type", "single")

	switch uploadType {
	case "single":
		h.uploadSingle(ctx)
	case "multiple":
		h.uploadMultiple(ctx)
	default:
		common.AbortValidationFailure(ctx, "不支持的上传类型", nil)
	}
}

// uploadSingle 单文件上传
func (h *UploadHandler) uploadSingle(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		common.AbortValidationFailure(ctx, "获取上传文件失败", err)
		return
	}

	// 验证文件
	if err := h.validateFile(file); err != nil {
		common.AbortValidationFailure(ctx, err.Error(), err)
		return
	}

	// 保存文件
	uploadResp, err := h.saveFile(file)
	if err != nil {
		common.AbortInternalServerError(ctx, "保存文件失败: "+err.Error())
		return
	}

	common.Success(ctx, uploadResp)
}

// uploadMultiple 多文件上传
func (h *UploadHandler) uploadMultiple(ctx *gin.Context) {
	// 解析multipart form
	form, err := ctx.MultipartForm()
	if err != nil {
		common.AbortValidationFailure(ctx, "解析表单失败", err)
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		common.AbortValidationFailure(ctx, "没有找到上传文件", nil)
		return
	}

	response := &admin.MultiUploadResponse{
		Success:      make([]admin.UploadResponse, 0),
		Failed:       make([]admin.UploadError, 0),
		SuccessCount: 0,
		FailedCount:  0,
	}

	// 处理每个文件
	for _, file := range files {
		// 验证文件
		if err := h.validateFile(file); err != nil {
			response.Failed = append(response.Failed, admin.UploadError{
				FileName: file.Filename,
				Error:    err.Error(),
			})
			response.FailedCount++
			continue
		}

		// 保存文件
		uploadResp, err := h.saveFile(file)
		if err != nil {
			response.Failed = append(response.Failed, admin.UploadError{
				FileName: file.Filename,
				Error:    err.Error(),
			})
			response.FailedCount++
			continue
		}

		response.Success = append(response.Success, *uploadResp)
		response.SuccessCount++
	}

	common.Success(ctx, response)
}

// validateFile 验证文件
func (h *UploadHandler) validateFile(file *multipart.FileHeader) error {
	uploadConfig := h.getUploadConfig()

	// 检查文件大小
	if file.Size > uploadConfig.MaxFileSize {
		return fmt.Errorf("文件大小超出限制，最大允许 %d 字节", uploadConfig.MaxFileSize)
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if len(uploadConfig.AllowedTypes) > 0 {
		allowed := false
		for _, allowedExt := range uploadConfig.AllowedTypes {
			if ext == allowedExt {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("不支持的文件类型: %s", ext)
		}
	}

	return nil
}

// saveFile 保存文件
func (h *UploadHandler) saveFile(file *multipart.FileHeader) (*admin.UploadResponse, error) {
	// 生成唯一文件名
	fileID := uuid.New().String()
	ext := filepath.Ext(file.Filename)
	fileName := fileID + ext

	now := time.Now()
	relativePath := filepath.Join(now.Format("2006"), now.Format("01"))
	uploadDir := filepath.Join(h.getUploadPath(), relativePath)

	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, fmt.Errorf("创建上传目录失败: %v", err)
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, fileName)
	if err := h.saveUploadedFile(file, filePath); err != nil {
		return nil, fmt.Errorf("保存文件失败: %v", err)
	}

	// 构建响应
	response := &admin.UploadResponse{
		FileName:   file.Filename,
		Size:       file.Size,
		URL:        h.buildFileURL(relativePath, fileName),
		UploadTime: now,
	}

	return response, nil
}

// saveUploadedFile 保存上传的文件
func (h *UploadHandler) saveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer func(src multipart.File) {
		_ = src.Close()
	}(src)

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer func(out *os.File) {
		_ = out.Close()
	}(out)

	_, err = io.Copy(out, src)
	return err
}

// buildFileURL 构建文件访问URL
func (h *UploadHandler) buildFileURL(relativePath, fileName string) string {
	uploadConfig := h.getUploadConfig()
	return fmt.Sprintf("%s/%s/%s",
		strings.TrimRight(uploadConfig.BaseURL, "/"),
		strings.Trim(relativePath, "/"),
		fileName)
}

// getUploadPath 获取上传路径
func (h *UploadHandler) getUploadPath() string {
	uploadConfig := h.getUploadConfig()
	return uploadConfig.StoragePath
}

// getUploadConfig 获取上传配置
func (h *UploadHandler) getUploadConfig() *config.UploadConfig {
	return config.GetDefaultUploadConfig()
}

// GetConfig 获取上传配置接口
func (h *UploadHandler) GetConfig(ctx *gin.Context) {
	uploadConfig := h.getUploadConfig()
	response := &admin.UploadConfig{
		MaxFileSize:  uploadConfig.MaxFileSize,
		AllowedTypes: uploadConfig.AllowedTypes,
	}
	common.Success(ctx, response)
}

// Delete 删除文件
func (h *UploadHandler) Delete(ctx *gin.Context) {
	filePath := ctx.Query("path")
	if filePath == "" {
		common.AbortValidationFailure(ctx, "文件路径不能为空", nil)
		return
	}

	// 安全检查：确保路径在上传目录内
	uploadPath := h.getUploadPath()
	fullPath := filepath.Join(uploadPath, filePath)

	// 检查路径是否安全
	if !strings.HasPrefix(fullPath, uploadPath) {
		common.AbortValidationFailure(ctx, "非法的文件路径", nil)
		return
	}

	// 删除文件
	if err := os.Remove(fullPath); err != nil {
		common.AbortInternalServerError(ctx, "删除文件失败: "+err.Error())
		return
	}

	common.Success(ctx, "文件删除成功")
}
