package admin

import (
	"gin/internal/dto/common"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/context"
	"gin/internal/models"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
	"strconv"
	"strings"
)

type PermissionHandler struct {
	cache             *cache.RedisClient
	enforcer          casbin.IEnforcer
	permissionService interfaces.IPermissionService
	managerService    interfaces.IManagerService
	tenantService     interfaces.ITenantService
}

func NewPermissionHandler(
	cache *cache.RedisClient,
	enforcer casbin.IEnforcer,
	permissionService interfaces.IPermissionService,
	managerService interfaces.IManagerService,
	tenantService interfaces.ITenantService) *PermissionHandler {
	return &PermissionHandler{
		cache:             cache,
		enforcer:          enforcer,
		permissionService: permissionService,
		managerService:    managerService,
		tenantService:     tenantService,
	}
}

// Menus 获取菜单
func (h *PermissionHandler) Menus(ctx *gin.Context) {
	// 获取用户信息
	managerInfo, err := h.managerService.GetManagerByID(ctx, context.GetUserIdFromCtx(ctx))
	if err != nil {
		//logger.Error("获取管理员信息失败", zap.Uint64("userID", ctx.Claims.UserID), zap.Error(err))
		common.AbortInternalServerError(ctx, "获取用户信息失败")
		return
	}

	if managerInfo == nil {
		//logger.Warn("管理员不存在", zap.Uint64("userID", ctx.Claims.UserID))
		common.AbortNotFound(ctx, "用户不存在")
		return
	}

	// 获取菜单
	var menus []*models.Permission

	// 超级管理员获取所有菜单
	if managerInfo.IsAdmin && managerInfo.TenantID == 0 {
		menus, err = h.permissionService.GetAllPermissionMenus(ctx)
		if err != nil {
			//logger.Error("获取所有菜单失败", zap.Error(err))
			common.AbortInternalServerError(ctx, "获取菜单失败")
			return
		}
	} else {
		// 获取租户信息
		tenantInfo, err := h.tenantService.GetTenantByID(ctx, managerInfo.TenantID)
		if err != nil {
			//logger.Error("获取租户信息失败", zap.Uint64("tenantID", adminUserInfo.TenantID), zap.Error(err))
			common.AbortInternalServerError(ctx, "获取租户信息失败")
			return
		}

		// 设置租户代码
		tenantCode := "*" // 默认使用通配符
		if tenantInfo != nil {
			tenantCode = tenantInfo.Code
		}

		// 获取用户权限
		userID := strconv.FormatUint(managerInfo.ID, 10)
		permissions := h.enforcer.GetEnforcer().GetPermissionsForUserInDomain(userID, tenantCode)

		// 提取菜单权限代码
		permCodes := extractMenuPermissionCodes(permissions)

		// 根据权限代码获取菜单
		if len(permCodes) > 0 {
			menus, err = h.permissionService.GetMenusByPermissions(ctx, permCodes)
			if err != nil {
				//logger.Error("根据权限获取菜单失败", zap.Error(err))
				common.AbortInternalServerError(ctx, "获取菜单失败")
				return
			}
		} else {
			//logger.Warn("用户没有任何菜单权限", zap.Uint64("userID", adminUserInfo.ID))
			// 返回空菜单列表
			menus = []*models.Permission{}
		}
	}

	// 构建菜单树
	menuTree := h.permissionService.BuildPermissionMenuTree(menus, 0)
	common.Success(ctx, menuTree)
}

// extractMenuPermissionCodes 从 Casbin 权限中提取菜单权限代码
func extractMenuPermissionCodes(permissions [][]string) []string {
	// 使用 map 去重
	codeMap := make(map[string]bool)

	// 遍历所有权限
	for _, perm := range permissions {
		// Casbin 权限格式通常是 [sub, dom, obj, act]
		if len(perm) >= 3 {
			// 获取权限对象（菜单代码）
			code := perm[2]

			// 过滤出菜单权限代码，忽略 API 路径
			if !strings.HasPrefix(code, "/") {
				// 如果是菜单权限代码，添加到集合中
				codeMap[code] = true
			}
		}
	}

	// 将 map 转换为切片
	var result []string
	for code := range codeMap {
		result = append(result, code)
	}

	return result
}
