package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
	"strconv"
)

// TranslationHandler 翻译处理器
type TranslationHandler struct {
	translationService interfaces.ITranslationService
}

// NewTranslationHandler 创建翻译处理器
func NewTranslationHandler(translationService interfaces.ITranslationService) *TranslationHandler {
	return &TranslationHandler{
		translationService: translationService,
	}
}

// Create 创建翻译
func (h *TranslationHandler) Create(ctx *gin.Context) {
	var req admin.TranslationCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	// 调用服务层方法
	err := h.translationService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建翻译失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新翻译
func (h *TranslationHandler) Update(ctx *gin.Context) {
	// 获取翻译ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.AbortValidationFailure(ctx, "无效的翻译ID", err)
		return
	}

	// 绑定请求参数
	var req admin.TranslationUpdateRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err = h.translationService.Update(ctx, uint(id), &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新翻译失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Delete 删除翻译
func (h *TranslationHandler) Delete(ctx *gin.Context) {
	// 获取翻译ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.AbortValidationFailure(ctx, "无效的翻译ID", err)
		return
	}

	// 调用服务层方法
	err = h.translationService.Delete(ctx, uint(id))
	if err != nil {
		common.AbortInternalServerError(ctx, "删除翻译失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// GetByID 根据ID获取翻译
func (h *TranslationHandler) GetByID(ctx *gin.Context) {
	// 获取翻译ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.AbortValidationFailure(ctx, "无效的翻译ID", err)
		return
	}

	// 调用服务层方法
	translation, err := h.translationService.GetByID(ctx, uint(id))
	if err != nil {
		common.AbortInternalServerError(ctx, "获取翻译失败: "+err.Error())
		return
	}

	if translation == nil {
		common.AbortNotFound(ctx, "翻译不存在")
		return
	}

	common.Success(ctx, translation)
}

// List 获取翻译列表
func (h *TranslationHandler) List(ctx *gin.Context) {
	var req admin.TranslationListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, err := h.translationService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取翻译列表失败: "+err.Error())
		return
	}

	common.Success(ctx, result)
}

// BatchDelete 批量删除翻译
func (h *TranslationHandler) BatchDelete(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.TranslationBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.translationService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除翻译失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
