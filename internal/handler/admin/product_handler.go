package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// ProductHandler 产品处理器
type ProductHandler struct {
	productService interfaces.IProductService
}

// NewProductHandler 创建产品处理器
func NewProductHandler(productService interfaces.IProductService) *ProductHandler {
	return &ProductHandler{productService: productService}
}

// List 获取产品列表
func (h *ProductHandler) List(ctx *gin.Context) {
	var req admin.ProductListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.productService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取产品列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建产品
func (h *ProductHandler) Create(ctx *gin.Context) {
	var req admin.ProductCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.productService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建产品失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新产品
func (h *ProductHandler) Update(ctx *gin.Context) {
	// 获取产品ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.ProductUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.productService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新产品失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除产品
func (h *ProductHandler) Delete(ctx *gin.Context) {
	// 获取产品ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.productService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除产品失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
