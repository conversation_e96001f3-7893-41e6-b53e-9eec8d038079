package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserTransferHandler 用户转账处理器
type UserTransferHandler struct {
	userTransferService interfaces.IUserTransferService
}

// NewUserTransferHandler 创建用户转账处理器
func NewUserTransferHandler(userTransferService interfaces.IUserTransferService) *UserTransferHandler {
	return &UserTransferHandler{userTransferService: userTransferService}
}

// List 获取用户转账列表
func (h *UserTransferHandler) List(ctx *gin.Context) {
	var req admin.UserTransferListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userTransferService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取用户转账列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建用户转账
func (h *UserTransferHandler) Create(ctx *gin.Context) {
	var req admin.UserTransferCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	_, err := h.userTransferService.Create(ctx, "zh-CN", &req)
	if err != nil {
		common.Error(ctx, "创建用户转账失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新用户转账
func (h *UserTransferHandler) Update(ctx *gin.Context) {
	// 获取用户转账ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserTransferUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userTransferService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新用户转账失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除用户转账
func (h *UserTransferHandler) Delete(ctx *gin.Context) {
	// 获取用户转账ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userTransferService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除用户转账失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除用户转账
func (h *UserTransferHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userTransferService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除用户转账失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
