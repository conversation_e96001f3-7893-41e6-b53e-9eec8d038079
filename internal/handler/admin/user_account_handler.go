package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// UserAccountHandler 用户账户处理器
type UserAccountHandler struct {
	userAccountService interfaces.IUserAccountService
}

// NewUserAccountHandler 创建用户账户处理器
func NewUserAccountHandler(userAccountService interfaces.IUserAccountService) *UserAccountHandler {
	return &UserAccountHandler{userAccountService: userAccountService}
}

// List 获取用户账户列表
func (h *UserAccountHandler) List(ctx *gin.Context) {
	var req admin.UserAccountListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userAccountService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取用户账户列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建用户账户
func (h *UserAccountHandler) Create(ctx *gin.Context) {
	var req admin.UserAccountCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	_, err := h.userAccountService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建用户账户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新用户账户
func (h *UserAccountHandler) Update(ctx *gin.Context) {
	// 获取用户账户ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserAccountUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userAccountService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新用户账户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除用户账户
func (h *UserAccountHandler) Delete(ctx *gin.Context) {
	// 获取用户账户ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userAccountService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除用户账户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除用户账户
func (h *UserAccountHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userAccountService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除用户账户失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
