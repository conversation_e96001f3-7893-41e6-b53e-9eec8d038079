package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

type UserLevelHandler struct {
	userLevelService interfaces.IUserLevelService
}

func NewUserLevelHandler(userLevelService interfaces.IUserLevelService) *UserLevelHandler {
	return &UserLevelHandler{
		userLevelService: userLevelService,
	}
}

func (h *UserLevelHandler) List(ctx *gin.Context) {
	var req admin.UserLevelListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userLevelService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取用户等级列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

func (h *UserLevelHandler) Create(ctx *gin.Context) {
	var req admin.UserLevelCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userLevelService.Create(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建用户等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

func (h *UserLevelHandler) Update(ctx *gin.Context) {
	// 获取用户等级ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.UserLevelUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userLevelService.Update(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新用户等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

func (h *UserLevelHandler) Delete(ctx *gin.Context) {
	// 获取用户等级ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.userLevelService.Delete(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除用户等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

func (h *UserLevelHandler) BatchDelete(ctx *gin.Context) {
	var req admin.UserLevelBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userLevelService.BatchDelete(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除用户等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
