package admin

import (
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"github.com/gin-gonic/gin"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService interfaces.IPaymentService
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(paymentService interfaces.IPaymentService) *PaymentHandler {
	return &PaymentHandler{paymentService: paymentService}
}

// List 获取支付列表
func (h *PaymentHandler) List(ctx *gin.Context) {
	var req admin.PaymentListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.paymentService.List(ctx, &req)
	if err != nil {
		common.Error(ctx, "获取支付列表失败: "+err.Error())
		return
	}

	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建支付
func (h *PaymentHandler) Create(ctx *gin.Context) {
	var req admin.PaymentCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	_, err := h.paymentService.Create(ctx, &req)
	if err != nil {
		common.Error(ctx, "创建支付失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Update 更新支付
func (h *PaymentHandler) Update(ctx *gin.Context) {
	// 获取支付ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.PaymentUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.paymentService.Update(ctx, id, &req)
	if err != nil {
		common.Error(ctx, "更新支付失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除支付
func (h *PaymentHandler) Delete(ctx *gin.Context) {
	// 获取支付ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.paymentService.Delete(ctx, id)
	if err != nil {
		common.Error(ctx, "删除支付失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}

// BatchDelete 批量删除支付
func (h *PaymentHandler) BatchDelete(ctx *gin.Context) {
	var req common.BatchDeleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.BadRequest(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.paymentService.BatchDelete(ctx, &req)
	if err != nil {
		common.Error(ctx, "批量删除支付失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
