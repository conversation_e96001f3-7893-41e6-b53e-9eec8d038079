package admin

import (
	"gin/internal/service"
	"github.com/gin-gonic/gin"
)

type RoleHandler struct {
	roleService service.RoleService
}

func NewRoleHandler(roleService service.RoleService) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
	}
}

// GetRoles 获取角色列表
func (h *RoleHandler) GetRoles(ctx *gin.Context) {
	//var req v1.GetRoleListRequest
	//if err := ctx.ShouldBind(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
	//	return
	//}
	//data, err := h.adminService.GetRoles(ctx, &req)
	//if err != nil {
	//	v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
	//	return
	//}
	//
	//v1.HandleSuccess(ctx, data)
}

// RoleCreate 创建角色
func (h *RoleHandler) RoleCreate(ctx *gin.Context) {
	//var req v1.RoleCreateRequest
	//if err := ctx.ShouldBind(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
	//	return
	//}
	//if err := h.adminService.RoleCreate(ctx, &req); err != nil {
	//	v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
	//	return
	//}
	//v1.HandleSuccess(ctx, nil)
}

// RoleUpdate 更新角色
func (h *RoleHandler) RoleUpdate(ctx *gin.Context) {
	//var req v1.RoleUpdateRequest
	//if err := ctx.ShouldBind(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
	//	return
	//}
	//if err := h.adminService.RoleUpdate(ctx, &req); err != nil {
	//	v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
	//	return
	//}
	//v1.HandleSuccess(ctx, nil)
}

// RoleDelete 删除角色
func (h *RoleHandler) RoleDelete(ctx *gin.Context) {
	//var req v1.RoleDeleteRequest
	//if err := ctx.ShouldBind(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
	//	return
	//}
	//if err := h.adminService.RoleDelete(ctx, req.ID); err != nil {
	//	v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
	//	return
	//}
	//v1.HandleSuccess(ctx, nil)
}

// GetRolePermissions 获取角色权限
func (h *ManagerHandler) GetRolePermissions(ctx *gin.Context) {
	//var req v1.GetRolePermissionsRequest
	//if err := ctx.ShouldBind(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
	//	return
	//}
	//data, err := h.adminService.GetRolePermissions(ctx, req.Role)
	//if err != nil {
	//	v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
	//	return
	//}
	//v1.HandleSuccess(ctx, data)
}

// UpdateRolePermission 更新角色权限
func (h *RoleHandler) UpdateRolePermission(ctx *gin.Context) {
	//var req v1.UpdateRolePermissionRequest
	//if err := ctx.ShouldBind(&req); err != nil {
	//	v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
	//	return
	//}
	//err := h.adminService.UpdateRolePermission(ctx, &req)
	//if err != nil {
	//	v1.HandleError(ctx, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
	//	return
	//}
	//v1.HandleSuccess(ctx, nil)
}
