package admin

import (
	"context"
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/cache"
	infraContext "gin/internal/infrastructure/context"
	"gin/internal/models"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"gin/pkg/jwt"
	"github.com/gin-gonic/gin"
)

type ManagerHandler struct {
	cache          *cache.RedisClient
	managerService interfaces.IManagerService
}

func NewManagerHandler(cache *cache.RedisClient, managerService interfaces.IManagerService) *ManagerHandler {
	return &ManagerHandler{
		cache:          cache,
		managerService: managerService,
	}
}

// Code 获取管理员的权限码
func (h *ManagerHandler) Code(ctx *gin.Context) {
	// TODO 获取管理员的权限码
	// 返回权限码列表
	common.Success(ctx, "ok")
}

// Login 登录
func (h *ManagerHandler) Login(ctx *gin.Context) {
	var req admin.LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	_, token, err := h.managerService.Login(ctx, req.Username, req.Password, h.cache)
	if err != nil {
		common.AbortUnauthorized(ctx, err.Error())
		return
	}

	common.Success(ctx, admin.LoginResponse{
		Token:        token,
		RefreshToken: token,
	})
}

// Logout 退出登录
func (h *ManagerHandler) Logout(ctx *gin.Context) {
	v, exists := ctx.Get(infraContext.ClaimsContextKey)
	if !exists {
		common.AbortUnauthorized(ctx, "用户未登录")
		return
	}

	claims := v.(*jwt.TokenClaims)
	err := h.managerService.Logout(ctx, claims.TokenId, claims.UserId, "", ctx.ClientIP(), ctx.Request.UserAgent(), h.cache)
	if err != nil {
		common.AbortInternalServerError(ctx, "退出登录失败")
		return
	}
	common.Success(ctx, nil)
}

// ChangePassword 修改密码
func (h *ManagerHandler) ChangePassword(ctx *gin.Context) {
	var req admin.ChangePasswordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	if err := h.managerService.ChangePassword(ctx, infraContext.GetManagerIdFromCtx(ctx), req.OldPassword, req.NewPassword); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}
	common.Success(ctx, nil)
}

// UpdateManagerSettings 个人设置
func (h *ManagerHandler) UpdateManagerSettings(ctx *gin.Context) {
	var req admin.SettingsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}
	id := infraContext.GetManagerIdFromCtx(ctx)
	if err := h.managerService.Update(ctx, id, &admin.ManageUpdateRequest{
		Avatar:     &req.Avatar,
		Nickname:   &req.Nickname,
		Email:      &req.Email,
		Mobile:     &req.Mobile,
		Gender:     &req.Gender,
		LoginMode:  &req.LoginMode,
		MaxDevices: &req.MaxDevices,
	}); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}

	common.Success(ctx, nil)
}

// ResetPassword 重置密码
func (h *ManagerHandler) ResetPassword(ctx *gin.Context) {
	var req admin.ResetPasswordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	if err := h.managerService.ResetPassword(ctx, convert.StringToUint(ctx.Param("id")), req.NewPassword); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Create 创建管理员
func (h *ManagerHandler) Create(ctx *gin.Context) {
	var req admin.ManageCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	manager := &models.Manager{
		Username: req.Username,
		Nickname: req.Nickname,
		Mobile:   req.Mobile,
		Email:    req.Email,
		Avatar:   req.Avatar,
		Gender:   req.Gender,
		Status:   req.Status,
	}

	if err := h.managerService.Create(ctx, manager, req.Password); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}
	common.Success(ctx, manager.ID)
}

// Update 更新管理员
func (h *ManagerHandler) Update(ctx *gin.Context) {
	var req admin.ManageUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}
	id := convert.StringToUint(ctx.Param("id"))

	if err := h.managerService.Update(ctx, id, &req); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}
	common.Success(ctx, nil)
}

// Delete 删除管理员用户
func (h *ManagerHandler) Delete(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	if err := h.managerService.Delete(ctx, id); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}
	common.Success(ctx, nil)
}

// List 获取管理员用户列表
func (h *ManagerHandler) List(ctx *gin.Context) {
	var req admin.ManageListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		fmt.Println(err)
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	result, total, err := h.managerService.List(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取管理员列表失败")
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}

// GetManagerInfo 获取管理信息
func (h *ManagerHandler) GetManagerInfo(ctx *gin.Context) {
	managerInfo, err := h.managerService.FindByID(ctx, infraContext.GetManagerIdFromCtx(ctx))
	if err != nil {
		common.AbortInternalServerError(ctx, "获取管理员信息失败")
		return
	}

	common.Success(ctx, admin.ManageResponse{
		ID:                   managerInfo.ID,
		Username:             managerInfo.Username,
		Nickname:             managerInfo.Nickname,
		Gender:               managerInfo.Gender,
		Avatar:               managerInfo.Avatar,
		Email:                managerInfo.Email,
		Mobile:               managerInfo.Mobile,
		Status:               managerInfo.Status,
		LoginMode:            managerInfo.LoginMode,
		MaxDevices:           managerInfo.MaxDevices,
		LastLoginIP:          managerInfo.LastLoginIP,
		LastLoginTime:        managerInfo.LastLoginTime,
		LastPasswordChangeAt: managerInfo.LastPasswordChangeAt,
		LoginFailCount:       managerInfo.LoginFailCount,
		ExpiredAt:            managerInfo.ExpiredAt,
		CreatedAt:            managerInfo.CreatedAt,
		Buttons:              make([]string, 0),
		Roles:                []string{"R_SUPER"},
	})
}

// 角色管理相关方法 - 简化版本
func (h *ManagerHandler) handleRoleOperation(ctx *gin.Context, operation func(context.Context, uint, []string) error) {
	var req admin.UserRoleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	id := convert.StringToUint(ctx.Param("id"))

	if err := operation(ctx, id, req.RoleCodes); err != nil {
		common.AbortInternalServerError(ctx, err.Error())
		return
	}
	common.Success(ctx, nil)
}

// AssignRoles 为用户分配角色
func (h *ManagerHandler) AssignRoles(ctx *gin.Context) {
	h.handleRoleOperation(ctx, h.managerService.AssignRoles)
}

// RemoveRoles 移除用户角色
func (h *ManagerHandler) RemoveRoles(ctx *gin.Context) {
	h.handleRoleOperation(ctx, h.managerService.RemoveRoles)
}

// ReplaceUserRoles 替换用户的所有角色
func (h *ManagerHandler) ReplaceUserRoles(ctx *gin.Context) {
	h.handleRoleOperation(ctx, h.managerService.ReplaceUserRoles)
}

// GetUserRoles 获取用户角色
func (h *ManagerHandler) GetUserRoles(ctx *gin.Context) {
	id := convert.StringToUint(ctx.Param("id"))

	roles, err := h.managerService.GetUserRoles(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取用户角色失败")
		return
	}
	common.Success(ctx, map[string]interface{}{"roles": roles})
}
