package admin

import (
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/models"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"strconv"

	"github.com/gin-gonic/gin"
)

type RoleHandler struct {
	roleService interfaces.IRoleService
}

func NewRoleHandler(roleService interfaces.IRoleService) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
	}
}

// List 获取角色列表
func (h *RoleHandler) List(ctx *gin.Context) {
	var req admin.RoleListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.roleService.ListRole(ctx, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取角色列表失败")
		return
	}

	// 构建响应
	common.SuccessWithPagination(ctx, result, total)
}

// Create 创建角色
func (h *RoleHandler) Create(ctx *gin.Context) {
	var req admin.RoleCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err.Error())
		return
	}

	// 创建角色模型
	role := &models.Role{
		Name:   req.Name,
		Code:   req.Code,
		Status: req.Status,
		Sort:   req.Sort,
		Remark: req.Remark,
	}

	// 调用服务层方法
	err := h.roleService.CreateRole(ctx, role)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建角色失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Update 更新角色
func (h *RoleHandler) Update(ctx *gin.Context) {
	// 获取角色ID
	id := convert.StringToUint(ctx.Param("id"))

	// 绑定请求参数
	var req admin.RoleUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		fmt.Println(err)
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.roleService.UpdateRole(ctx, id, &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "更新角色失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// Delete 删除角色
func (h *RoleHandler) Delete(ctx *gin.Context) {
	// 获取角色ID
	id := convert.StringToUint(ctx.Param("id"))

	// 调用服务层方法
	err := h.roleService.DeleteRole(ctx, id)
	if err != nil {
		common.AbortInternalServerError(ctx, "删除角色失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// BatchDelete 批量删除角色
func (h *RoleHandler) BatchDelete(ctx *gin.Context) {
	// 绑定请求参数
	var req admin.RoleBatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.roleService.BatchDeleteRole(ctx, req.IDs)
	if err != nil {
		common.AbortInternalServerError(ctx, "批量删除角色失败: "+err.Error())
		return
	}

	common.Success(ctx, nil)
}

// GetPermissions 获取角色权限
func (h *RoleHandler) GetPermissions(ctx *gin.Context) {
	// 获取角色ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.AbortValidationFailure(ctx, "无效的角色ID", err)
		return
	}

	// 这里需要调用权限服务获取角色的权限列表
	// 由于代码中没有提供相关服务接口，这里只是提供一个框架
	// 实际实现需要根据项目中的权限服务来完成
	// permissions, err := h.permissionService.GetPermissions(ctx, id)
	// if err != nil {
	// 	common.AbortInternalServerError(ctx, "获取角色权限失败")
	// 	return
	// }

	// 返回空结果，实际项目中需要返回真实数据
	common.Success(ctx, gin.H{
		"roleId":  id,
		"menuIds": []uint64{},
	})
}

// UpdatePermission 更新角色权限
func (h *RoleHandler) UpdatePermission(ctx *gin.Context) {
	// 获取角色ID
	idStr := ctx.Param("id")
	_, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.AbortValidationFailure(ctx, "无效的角色ID", err)
		return
	}

	// 绑定请求参数
	var req admin.RolePermissionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	common.Success(ctx, nil)
}
