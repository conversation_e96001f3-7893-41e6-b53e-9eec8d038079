package websocket

import (
	"gin/internal/service"
	handler2 "gin/pkg/socket/handler"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	gorillaws "github.com/gorilla/websocket"
)

// Handler WebSocket处理器
type Handler struct {
	service  *service.WebSocketService
	upgrader gorillaws.Upgrader
}

// NewHandler 创建新的WebSocket处理器
func NewHandler(wsService *service.WebSocketService) *Handler {
	return &Handler{
		service: wsService,
		upgrader: gorillaws.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				return true // 生产环境中应该配置具体的跨域检查逻辑
			},
		},
	}
}

// AuthFunc 用户认证函数类型
type AuthFunc func(c *gin.Context) (uint, error)

// ConnectUser 处理用户WebSocket连接请求
func (h *Handler) ConnectUser(authFunc AuthFunc) gin.HandlerFunc {
	return h.connect(authFunc, "user")
}

// ConnectAdmin 处理管理员WebSocket连接请求
func (h *Handler) ConnectAdmin(authFunc AuthFunc) gin.HandlerFunc {
	return h.connect(authFunc, "admin")
}

// ConnectUserPublic 处理不需要认证的用户WebSocket连接
func (h *Handler) ConnectUserPublic() gin.HandlerFunc {
	return h.connectWithoutAuth("user")
}

// connect 通用连接处理方法
func (h *Handler) connect(authFunc AuthFunc, hubType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 用户认证
		ownerID, err := authFunc(c)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication failed"})
			return
		}

		// 升级HTTP连接到WebSocket
		ws, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upgrade to WebSocket"})
			return
		}

		// 根据类型选择Hub
		var hub *handler2.Hub
		if hubType == "admin" {
			hub = h.service.GetAdminHub()
		} else {
			hub = h.service.GetUserHub()
		}

		// 创建消息处理器
		messageHandler := service.NewSimpleMessageHandler(h.service, hubType)

		// 创建连接
		connectionID := uuid.New().String()
		conn := handler2.NewConnection(hub, ws, connectionID, messageHandler)
		conn.SetOwnerID(ownerID)

		// 注册连接到Hub
		if err := hub.RegisterConnection(conn); err != nil {
			_ = ws.Close()
			c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Server is busy"})
			return
		}

		// 启动连接处理
		conn.Start()
	}
}

// connectWithoutAuth 不需要认证的连接处理
func (h *Handler) connectWithoutAuth(hubType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 升级HTTP连接到WebSocket
		ws, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upgrade to WebSocket"})
			return
		}

		// 根据类型选择Hub
		var hub *handler2.Hub
		if hubType == "admin" {
			hub = h.service.GetAdminHub()
		} else {
			hub = h.service.GetUserHub()
		}

		// 创建消息处理器
		messageHandler := service.NewSimpleMessageHandler(h.service, hubType)

		// 创建连接（匿名用户）
		connectionID := uuid.New().String()
		conn := handler2.NewConnection(hub, ws, connectionID, messageHandler)

		// 注册连接到Hub
		if err := hub.RegisterConnection(conn); err != nil {
			_ = ws.Close()
			c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Server is busy"})
			return
		}

		// 启动连接处理
		conn.Start()
	}
}

// GetStats 获取WebSocket连接统计信息
func (h *Handler) GetStats() gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := h.service.GetStats()
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    stats,
		})
	}
}

// BroadcastToUsers 向用户广播消息API
func (h *Handler) BroadcastToUsers() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Type    string      `json:"type" binding:"required"`
			Content interface{} `json:"content" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err := h.service.BroadcastToUsers(req.Type, req.Content)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Message broadcasted to users successfully",
		})
	}
}

// BroadcastToAdmins 向管理员广播消息API
func (h *Handler) BroadcastToAdmins() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Type    string      `json:"type" binding:"required"`
			Content interface{} `json:"content" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err := h.service.BroadcastToAdmins(req.Type, req.Content)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Message broadcasted to admins successfully",
		})
	}
}

// SendToUser 向指定用户发送消息API
func (h *Handler) SendToUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			OwnerID uint        `json:"owner_id" binding:"required"`
			Type    string      `json:"type" binding:"required"`
			Content interface{} `json:"content" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err := h.service.SendToUser(req.OwnerID, req.Type, req.Content)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Message sent to user successfully",
		})
	}
}

// SendToAdmin 向指定管理员发送消息API
func (h *Handler) SendToAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			OwnerID uint        `json:"owner_id" binding:"required"`
			Type    string      `json:"type" binding:"required"`
			Content interface{} `json:"content" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err := h.service.SendToAdmin(req.OwnerID, req.Type, req.Content)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Message sent to admin successfully",
		})
	}
}
