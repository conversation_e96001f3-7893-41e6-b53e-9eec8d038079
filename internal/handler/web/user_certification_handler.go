package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserCertificationHandler 用户认证处理器
type UserCertificationHandler struct {
	userCertificationService interfaces.IUserCertificationService
}

// NewUserCertificationHandler 创建用户认证处理器
func NewUserCertificationHandler(userCertificationService interfaces.IUserCertificationService) *UserCertificationHandler {
	return &UserCertificationHandler{userCertificationService: userCertificationService}
}

// Create 创建用户认证
func (h *UserCertificationHandler) Create(c *gin.Context) {
	bodyParams := web.UserCertificationReq{}
	if err := c.ShouldBindJSON(&bodyParams); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}
	err := h.userCertificationService.UserCreate(c.Request.Context(), context.GetUserIdFromCtx(c), &bodyParams)
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}
	common.Success(c, nil)
}

// Details 用户认证详情
func (h *UserCertificationHandler) Details(c *gin.Context) {
	userCertification, err := h.userCertificationService.Details(c.Request.Context(), context.GetUserIdFromCtx(c))
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}
	common.Success(c, userCertification)
}
