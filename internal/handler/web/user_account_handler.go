package web

import (
	"gin/internal/service/interfaces"
)

// UserAccountHandler 用户账户处理
type UserAccountHandler struct {
	userAccountService interfaces.IUserAccountService
}

// NewUserAccountHandler 创建用户账户处理
func NewUserAccountHandler(userAccountService interfaces.IUserAccountService) *UserAccountHandler {
	return &UserAccountHandler{userAccountService: userAccountService}
}

//// List 用户账户列表
//func (h *UserAccountHandler) List(c *gin.Context) {
//	queryParams := web.UserAccountListReq{}
//	if err := c.ShouldBindQuery(&queryParams); err != nil {
//		common.AbortBadRequest(c, "参数错误", err.Error())
//		return
//	}
//
//	// 获取用户账户列表
//	userAccounts, err := h.userAccountService.ListByUserID(c.Request.Context(), userID, &queryParams)
//	if err != nil {
//		common.AbortInternalServerError(c, err)
//		return
//	}
//
//	common.Success(c, userAccounts)
//}
//
//// Create 创建用户账户
//func (h *UserAccountHandler) Create(c *gin.Context) {
//	bodyParams := webdto.UserAccountCreateParams{}
//	if err := c.ShouldBindJSON(&bodyParams); err != nil {
//		common.AbortInternalServerError(c, err)
//		return
//	}
//
//	// 创建用户账户
//	userID := c.GetUint(jwt.ContextUserIDKey)
//	locale := utils.GetLocale(c)
//	_, err := h.userAccountService.UserCreate(c.Request.Context(), locale, userID, &bodyParams)
//	if err != nil {
//		common.AbortInternalServerError(c, err)
//		return
//	}
//
//	common.Success(c, "ok")
//}
//
//// Update 更新用户账户
//func (h *UserAccountHandler) Update(c *gin.Context) {
//	bodyParams := webdto.UserAccountUpdateParams{}
//	if err := c.ShouldBindJSON(&bodyParams); err != nil {
//		common.ErrorCommon(c, err)
//		return
//	}
//
//	// 更新用户账户
//	userID := c.GetUint(jwt.ContextUserIDKey)
//	idParam := common.StrconvStrToUint(c.Query("id"))
//	locale := utils.GetLocale(c)
//	if err := h.userAccountService.UserUpdate(c.Request.Context(), locale, userID, idParam, &bodyParams); err != nil {
//		common.ErrorCommon(c, err)
//		return
//	}
//
//	common.Success(c, "ok")
//}
//
//// Delete 删除用户账户
//func (h *UserAccountHandler) Delete(c *gin.Context) {
//	bodyParams := webdto.UserAccountDeleteParams{}
//	if err := c.ShouldBindJSON(&bodyParams); err != nil {
//		common.ErrorCommon(c, err)
//		return
//	}
//
//	// 删除用户账户
//	userID := c.GetUint(jwt.ContextUserIDKey)
//	idParam := common.StrconvStrToUint(c.Query("id"))
//	locale := utils.GetLocale(c)
//	if err := h.userAccountService.UserDelete(c.Request.Context(), locale, userID, idParam, &bodyParams); err != nil {
//		common.ErrorCommon(c, err)
//		return
//	}
//
//	common.Success(c, "ok")
//}
