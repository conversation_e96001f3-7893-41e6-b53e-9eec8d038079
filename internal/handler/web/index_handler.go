package web

import (
	"fmt"
	"gin/internal/constant"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/captcha"
	"gin/internal/infrastructure/context"
	"gin/internal/models"
	"gin/internal/service/interfaces"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// IndexHandler 基础控制器
type IndexHandler struct {
	settingService     interfaces.ISettingService
	countryService     interfaces.ICountryService
	languageService    interfaces.ILanguageService
	translationService interfaces.ITranslationService
	paymentService     interfaces.IPaymentService
	frontMenuService   interfaces.IFrontMenuService
	articleService     interfaces.IArticleService
	managerService     interfaces.IManagerService
	captchaService     captcha.ICaptcha
}

// NewIndexHandler 创建基础控制器
func NewIndexHandler(settingService interfaces.ISettingService, countryService interfaces.ICountryService, languageService interfaces.ILanguageService, translationService interfaces.ITranslationService, frontMenuService interfaces.IFrontMenuService, paymentService interfaces.IPaymentService, articleService interfaces.IArticleService, managerService interfaces.IManagerService, captchaService captcha.ICaptcha) *IndexHandler {
	return &IndexHandler{settingService: settingService, countryService: countryService, languageService: languageService, translationService: translationService, frontMenuService: frontMenuService, paymentService: paymentService, articleService: articleService, managerService: managerService, captchaService: captchaService}
}

// Inits 初始化数据
func (h *IndexHandler) Inits() gin.HandlerFunc {
	return func(c *gin.Context) {
		locale := context.GetLang(c)
		languages := h.languageService.GetInitsLanguages(c.Request.Context())
		if locale == "" || !h.hasLocale(locale, languages) {
			locale = languages[0].Locale
		}

		data := &web.InitsResult{
			Name:      h.settingService.GetByFieldString(models.SettingSiteName),
			Logo:      h.settingService.GetByFieldString(models.SettingSiteLogo),
			Favicon:   h.settingService.GetByFieldString(models.SettingSiteFavicon),
			Keywords:  h.settingService.GetKeywords(c.Request.Context(), locale),
			Copyright: h.settingService.GetCopyright(c.Request.Context(), locale),
			Introduce: h.settingService.GetIntroduce(c.Request.Context(), locale),
			Service: web.InitsService{
				Name: "",
				Link: "",
				Icon: "",
			},
			Settings: web.InitsSettings{
				Basic:    &constant.SettingBasicTemplate{},
				Login:    h.settingService.GetTemplateLogin(c.Request.Context(), locale),
				Register: h.settingService.GetTemplateRegister(c.Request.Context(), locale),
			},
			Languages:    h.languageService.GetInitsLanguages(c.Request.Context()),
			Countrys:     h.countryService.GetInitsCountryList(c.Request.Context()),
			Translations: h.translationService.InitsTranslations(c.Request.Context(), locale),
			Menus:        &web.InitsMenus{},
		}

		// 获取社交信息
		var socials []*constant.SettingSiteSocial
		_ = h.settingService.GetByField(models.SettingSiteSocial, &socials)
		if len(socials) > 0 {
			data.Service.Name = socials[0].Name
			data.Service.Link = socials[0].Link
			data.Service.Icon = socials[0].Icon
		}

		// 如果登陆状态, 那么获取管理信息里面的客服信息
		//claims, _ := jwt.ParseHandlerToken(c, &cfg.JWT)
		//if claims != nil {
		//	// 如果是登陆状态, 那么获取管理信息里面的客服信息
		//	manager, _ := h.managerService.FindByID(c.Request.Context(), context.GetManagerIdFromCtx(c))
		//	if manager != nil {
		//		data.Service.Name = manager.Nickname
		//		data.Service.Link = manager.CustomerServiceURL
		//		data.Service.Icon = manager.Avatar
		//	}
		//}

		// 获取菜单
		menus := h.frontMenuService.GetInitsFrontMenus(c.Request.Context(), locale)
		data.Menus = menus

		// 获取站点配置里面的数据
		_ = h.settingService.GetByField(models.SettingTemplateBasic, data.Settings.Basic)
		common.Success(c, data)
	}
}

// Home 首页
func (h *IndexHandler) Home(c *gin.Context) {
	locale := context.GetLang(c)
	data := &web.HomeRes{
		Banner:       &constant.SettingBanner{},
		Notices:      h.articleService.GetNotices(c.Request.Context(), locale),
		Announcement: h.settingService.GetNoticeTips(c.Request.Context(), locale),
		Socials:      []*constant.SettingSiteSocial{},
	}

	_ = h.settingService.GetByField(models.SettingSiteBanner, &data.Banner)  // 获取站点轮播图
	_ = h.settingService.GetByField(models.SettingSiteSocial, &data.Socials) // 获取站点社交
	common.Success(c, data)
}

// Captcha 验证码
func (h *IndexHandler) Captcha(c *gin.Context) {
	id, b64s, err := h.captchaService.Generate()
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, gin.H{
		"id":   id,
		"b64s": b64s,
	})
}

// WalletPayments 钱包支付列表
func (h *IndexHandler) WalletPayments(c *gin.Context) {
	locale := context.GetLang(c)
	mode := c.Param("mode")
	modeInt, _ := strconv.ParseInt(mode, 10, 8)

	data, err := h.paymentService.FindListByMode(c.Request.Context(), locale, int8(modeInt))
	if err != nil {
		common.AbortBadRequest(c, "获取支付列表失败", err)
		return
	}

	common.Success(c, data)
}

// Helpers 帮助中心
func (h *IndexHandler) Helpers(c *gin.Context) {
	locale := context.GetLang(c)
	data := &web.HelpersResult{
		Socials: []*constant.SettingSiteSocial{},
		Items:   h.articleService.GetHelpers(c, locale),
	}

	_ = h.settingService.GetByField(models.SettingSiteSocial, &data.Socials) // 获取站点社交
	common.Success(c, data)
}

// Upload 上传文件
func (h *IndexHandler) Upload(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		common.AbortBadRequest(c, "获取上传文件失败", err)
		return
	}

	// 验证文件大小 (限制为500MB)
	const maxSize = 500 << 20 // 500MB
	if file.Size > maxSize {
		common.AbortBadRequest(c, "文件大小超出限制", fmt.Errorf("文件大小超过限制"))
		return
	}

	// 生成安全的文件名
	filename := fmt.Sprintf("%d_%s", time.Now().UnixNano(), filepath.Base(file.Filename))

	// 确保上传目录存在
	uploadDir := "static/uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		common.AbortInternalServerError(c, "保存文件失败: "+err.Error())
		return
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, filename)
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		common.AbortInternalServerError(c, "保存文件失败: "+err.Error())
		return
	}

	// 返回文件URL
	fileURL := fmt.Sprintf("/%s", filePath)
	common.Success(c, fileURL)
}

// hasLocale 判断是否存在语言
func (h *IndexHandler) hasLocale(locale string, locales []*web.InitsLanguages) bool {
	for _, v := range locales {
		if v.Locale == locale {
			return true
		}
	}
	return false
}
