package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserLevelHandler 用户等级处理器
type UserLevelHandler struct {
	// 系统等级服务
	levelService interfaces.ILevelService
	// 用户等级服务
	userLevelService interfaces.IUserLevelService
}

// NewUserLevelHandler 创建用户等级处理器
func NewUserLevelHandler(userLevelService interfaces.IUserLevelService) *UserLevelHandler {
	return &UserLevelHandler{
		userLevelService: userLevelService,
	}
}

// List 获取用户等级列表
func (h *UserLevelHandler) List(ctx *gin.Context) {
	levels, total, err := h.levelService.ListWithLocale(ctx, context.GetLang(ctx))
	if err != nil {
		common.AbortInternalServerError(ctx, "获取用户等级列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, levels, total)
}

// Create 创建用户等级
func (h *UserLevelHandler) Create(ctx *gin.Context) {
	var req web.CreateUserLevelRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	err := h.userLevelService.CreateByUser(ctx, context.GetUserIdFromCtx(ctx), &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "创建用户等级失败: "+err.Error())
		return
	}
	common.Success(ctx, nil)
}
