package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ArticleHandler 文章处理器
type ArticleHandler struct {
	articleService interfaces.IArticleService
}

// NewArticleHandler 创建文章处理
func NewArticleHandler(articleService interfaces.IArticleService) *ArticleHandler {
	return &ArticleHandler{articleService: articleService}
}

// List 文章列表
func (h *ArticleHandler) List(ctx *gin.Context) {
	queryParams := web.ArticleListRequest{}
	if err := ctx.ShouldBindQuery(&queryParams); err != nil {
		return
	}

	//typeInt, _ := strconv.ParseInt(ctx.Param("type"), 10, 64)
	locale := context.GetLang(ctx)
	articles, total, err := h.articleService.ListWithLocale(ctx, locale, &queryParams)
	if err != nil {
		return
	}

	common.SuccessWithPagination(ctx, articles, total)
}

// Details 文章详情
func (h *ArticleHandler) Details(ctx *gin.Context) {
	articleIDInt, _ := strconv.ParseInt(ctx.Param("id"), 10, 64)
	locale := context.GetLang(ctx)

	article, err := h.articleService.Details(ctx.Request.Context(), locale, uint(articleIDInt))
	if err != nil {
		return
	}

	common.Success(ctx, article)
}
