package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserSwapHandler 用户闪兑处理器
type UserSwapHandler struct {
	userSwapService interfaces.IUserSwapService
	assetService    interfaces.IAssetService
}

// NewUserSwapHandler 创建用户闪兑处理器
func NewUserSwapHandler(userSwapService interfaces.IUserSwapService, assetService interfaces.IAssetService) *UserSwapHandler {
	return &UserSwapHandler{userSwapService: userSwapService, assetService: assetService}
}

// List 用户闪兑列表
func (h *UserSwapHandler) List(c *gin.Context) {
	queryParams := web.UserSwapListReq{}
	if err := c.ShouldBindQuery(&queryParams); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}

	userSwaps, total, err := h.userSwapService.UserList(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &queryParams)
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.SuccessWithPagination(c, userSwaps, total)
}

// Create 创建用户闪兑
func (h *UserSwapHandler) Create(c *gin.Context) {
	bodyParams := web.UserSwapCreateReq{}
	if err := c.ShouldBindJSON(&bodyParams); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}

	err := h.userSwapService.UserCreate(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &bodyParams)
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, "ok")
}
