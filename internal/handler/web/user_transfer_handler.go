package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserTransferHandler 用户转账处理器
type UserTransferHandler struct {
	userTransferService interfaces.IUserTransferService
	assetService        interfaces.IAssetService
}

// NewUserTransferHandler 创建用户转账处理器
func NewUserTransferHandler(userTransferService interfaces.IUserTransferService, assetService interfaces.IAssetService) *UserTransferHandler {
	return &UserTransferHandler{userTransferService: userTransferService, assetService: assetService}
}

// List 用户转账列表
func (h *UserTransferHandler) List(c *gin.Context) {
	queryParams := web.UserTransferListReq{}
	if err := c.ShouldBindQuery(&queryParams); err != nil {
		common.AbortBadRequest(c, "", err)
		return
	}

	userTransfers, total, err := h.userTransferService.UserList(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &queryParams)
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.SuccessWithPagination(c, userTransfers, total)
}

// Create 用户转账创建
func (h *UserTransferHandler) Create(c *gin.Context) {
	bodyParams := web.UserTransferCreateReq{}
	if err := c.ShouldBindJSON(&bodyParams); err != nil {
		common.AbortBadRequest(c, "", err)
		return
	}

	err := h.userTransferService.UserCreate(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &bodyParams)
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, "ok")
}
