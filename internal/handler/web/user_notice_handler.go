package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserNoticeHandler 用户通知处理器
type UserNoticeHandler struct {
	userNoticeService interfaces.IUserNoticeService
}

// NewUserNoticeHandler 创建用户通知处理器
func NewUserNoticeHandler(userNoticeService interfaces.IUserNoticeService) *UserNoticeHandler {
	return &UserNoticeHandler{
		userNoticeService: userNoticeService,
	}
}

// List 获取用户通知列表
func (h *UserNoticeHandler) List(ctx *gin.Context) {
	var req web.UserNoticeListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		common.AbortValidationFailure(ctx, "参数验证失败", err)
		return
	}

	// 调用服务层方法
	result, total, err := h.userNoticeService.ListForReceiver(ctx, context.GetUserIdFromCtx(ctx), &req)
	if err != nil {
		common.AbortInternalServerError(ctx, "获取用户通知列表失败: "+err.Error())
		return
	}
	common.SuccessWithPagination(ctx, result, total)
}
