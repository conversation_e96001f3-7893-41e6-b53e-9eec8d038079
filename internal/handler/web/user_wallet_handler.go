package web

import (
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/context"
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserWalletHandler 用户钱包处理器
type UserWalletHandler struct {
	userWalletService interfaces.IUserWalletService
	userBillService   interfaces.IUserBillService
}

// NewUserWalletHandler 创建用户钱包处理器
func NewUserWalletHandler(userWalletService interfaces.IUserWalletService, userBillService interfaces.IUserBillService) *UserWalletHandler {
	return &UserWalletHandler{userWalletService: userWalletService, userBillService: userBillService}
}

// List 钱包列表
func (h *UserWalletHandler) List(c *gin.Context) {
	queryReq := web.WalletListReq{}
	if err := c.ShouldBindQuery(&queryReq); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}

	//userWallets, err := h.userWalletService.UserWalletList(c.Request.Context(), context.GetUserIdFromCtx(c), &queryReq)
	//if err != nil {
	//	common.ErrorCommon(c, err)
	//	return
	//}
	//
	//common.Success(c, userWallets)
}

// Bill 账单
func (h *UserWalletHandler) Bill(c *gin.Context) {
	queryReq := web.WalletBillListReq{}
	if err := c.ShouldBindQuery(&queryReq); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}

	userBills, total, err := h.userBillService.UserBillList(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &queryReq)
	if err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.SuccessWithPagination(c, userBills, total)
}

// BillOptions 账单类型选项
func (h *UserWalletHandler) BillOptions(c *gin.Context) {
	//options := h.userBillService.GetLocaleBillSelectOptions(c.Request.Context(), context.GetLang(c))
	common.Success(c, nil)
}

// Deposit 钱包充值
func (h *UserWalletHandler) Deposit(c *gin.Context) {
	var params web.WalletDepositReq
	if err := c.ShouldBindJSON(&params); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}

	if err := h.userWalletService.UserCreateDeposit(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &params); err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, "ok")
}

// Withdraw 钱包提现
func (h *UserWalletHandler) Withdraw(c *gin.Context) {
	var params web.WalletWithdrawReq
	if err := c.ShouldBindJSON(&params); err != nil {
		common.AbortBadRequest(c, "参数错误", err)
		return
	}

	if err := h.userWalletService.UserCreateWithdraw(c.Request.Context(), context.GetLang(c), context.GetUserIdFromCtx(c), &params); err != nil {
		common.AbortInternalServerError(c, err.Error())
		return
	}

	common.Success(c, "ok")
}
