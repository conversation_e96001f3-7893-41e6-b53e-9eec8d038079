package web

import (
	"gin/internal/service/interfaces"
	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService interfaces.IUserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService interfaces.IUserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// Register 注册
func (h *UserHandler) Register(ctx *gin.Context) {
	// todo
}

// Login 登录
func (h *UserHandler) Login(ctx *gin.Context) {
	// todo
}

// Logout 登出
func (h *UserHandler) Logout(ctx *gin.Context) {
	// todo
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(ctx *gin.Context) {
	// todo
}
