package handler

import (
	"fmt"
	"gin/internal/handler/admin"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/logger"
	"gin/internal/middleware"
	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
)

type AdminHandler struct {
	IndexHandler      *admin.IndexHandler
	ManagerHandler    *admin.ManagerHandler
	PermissionHandler *admin.PermissionHandler
}

func NewAdminHandler(engine *gin.Engine, logger *logger.Logger, conf *config.Config, e *casbin.SyncedCachedEnforcer, adminHandler *AdminHandler) {

	adminGroup := engine.Group("/api")
	{
		// 公开接口 - 无需认证和权限验证
		adminGroup.POST("/auth/login", adminHandler.ManagerHandler.Login)

		// 仅需认证接口 - 需要JWT认证但不需要权限验证
		authenticated := adminGroup.Group("")
		authenticated.Use(middleware.JWTAuth())
		{
			// 获取用户菜单不需要权限验证，只需要认证
			authenticated.GET("/menu/all", adminHandler.PermissionHandler.Menus)
			// 获取用户信息不需要权限验证，只需要认证
			authenticated.GET("/manager/info", adminHandler.ManagerHandler.GetManager)
			authenticated.GET("/admin/codes", adminHandler.IndexHandler.Code)
			// 添加登出接口
			authenticated.POST("/auth/logout", adminHandler.ManagerHandler.Logout)
		}

		// 需要权限验证接口 - 既需要JWT认证也需要权限验证
		authorized := adminGroup.Group("")
		authorized.Use(middleware.JWTAuth())
		authorized.Use(middleware.Authorize())
		{
			system := authorized.Group("/system")
			{
				// 管理员管理
				managerGroup := system.Group("/manager")
				{
					// 管理员用户相关接口
					managerGroup.GET("", adminHandler.ManagerHandler.GetAdminUsers)  // 获取管理员用户列表
					managerGroup.POST("", adminHandler.ManagerHandler.ManagerCreate) // 创建管理员用户
					//managerGroup.GET("/:id", adminHandler.ManagerHandler.GetAdminUserByID) // 获取管理员用户
					managerGroup.PUT("/:id", adminHandler.ManagerHandler.ManagerUpdate)    // 更新管理员用户
					managerGroup.DELETE("/:id", adminHandler.ManagerHandler.ManagerDelete) // 删除管理员用户

					// 密码和状态管理
					//managerGroup.PUT("/:id/password", context.Handle(adminController.ChangePassword))      // 修改密码
					//managerGroup.PUT("/:id/reset-password", context.Handle(adminController.ResetPassword)) // 重置密码
					//managerGroup.PUT("/:id/status", context.Handle(adminController.ChangeStatus))          // 修改状态
				}

				// 角色管理
				roleGroup := system.Group("/role")
				{
					fmt.Println(roleGroup)
					//roleGroup.GET("/list", context.HandlerQuery(admins.RoleList))
					//roles.POST("", r.handlers.RoleHandler.Create)
					//roles.PUT("/:id", r.handlers.RoleHandler.Update)
					//roles.DELETE("/:id", r.handlers.RoleHandler.Delete)
					//roleGroup.GET("/:id", r.handlers.RoleHandler.Get)
					//roles.GET("/list", r.handlers.RoleHandler.List)
					//roles.PUT("/:id/status", r.handlers.RoleHandler.UpdateStatus)
					//roles.PUT("/:id/dataScope", r.handlers.RoleHandler.UpdateDataScope)
				}
			}

			// 字典管理 (保留原有的注释代码)
			//dict := authorized.Group("/dict")
			//{
			//	// 字典类型管理
			//	types := dict.Group("/types")
			//	{
			//		types.POST("", r.handlers.DictHandler.CreateType)
			//		types.PUT("/:id", r.handlers.DictHandler.UpdateType)
			//		types.DELETE("/:id", r.handlers.DictHandler.DeleteType)
			//		types.GET("/:id", r.handlers.DictHandler.GetType)
			//		types.GET("", r.handlers.DictHandler.ListTypes)
			//	}
			//
			//	// 字典数据管理
			//	data := dict.Group("/data")
			//	{
			//		data.POST("", r.handlers.DictHandler.CreateData)
			//		data.PUT("/:id", r.handlers.DictHandler.UpdateData)
			//		data.DELETE("/:id", r.handlers.DictHandler.DeleteData)
			//		data.GET("/:id", r.handlers.DictHandler.GetData)
			//		data.GET("/type/:typeId", r.handlers.DictHandler.ListDataByType)
			//	}
			//}
		}
	}
}
