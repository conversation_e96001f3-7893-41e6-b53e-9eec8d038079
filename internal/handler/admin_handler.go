package handler

import (
	"context"
	"fmt"
	"gin/internal/handler/admin"
	"gin/internal/infrastructure/config"
	"gin/internal/infrastructure/logger"
	"gin/internal/middleware"
	"gin/internal/models"
	"gin/internal/repository/repo"
	"net/http"
	"strings"
	"sync"

	"gin/internal/constant"
	"gin/pkg/utils"

	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
)

const (
	AdmPrefix = "/adm"
)

// RouteDefinition 路由定义
type RouteDefinition struct {
	Path        string
	Method      string
	Handler     gin.HandlerFunc
	Auth        bool
	Resource    string
	Permission  string
	Description string
	Group       string
	IsMenu      bool
}

// AdminRouter 管理员路由
type AdminRouter struct {
	engine             *gin.Engine
	logger             logger.Logger
	config             *config.Config
	enforcer           *casbin.SyncedCachedEnforcer
	adminHandler       *AdminHandler
	permissionRepo     repo.PermissionRepository
	menuRepo           repo.MenuRepository
	menuPermissionRepo repo.MenuPermissionRepository
	operateLogRepo     repo.OperateLogRepository
	routeGroups        map[string]*gin.RouterGroup
	mu                 sync.RWMutex
	initialized        bool
}

type AdminHandler struct {
	ManagerHandler           *admin.ManagerHandler
	ManagerNoticeHandler     *admin.ManagerNoticeHandler
	MenuHandler              *admin.MenuHandler
	FrontMenuHandler         *admin.FrontMenuHandler
	PermissionHandler        *admin.PermissionHandler
	RoleHandler              *admin.RoleHandler
	LevelHandler             *admin.LevelHandler
	MonitorHandler           *admin.MonitorHandler
	LoginLogHandler          *admin.LoginLogHandler
	OperateLogHandler        *admin.OperateLogHandler
	CountryHandler           *admin.CountryHandler
	LangHandler              *admin.LangHandler
	TranslationHandler       *admin.TranslationHandler
	UploadHandler            *admin.UploadHandler
	ArticleHandler           *admin.ArticleHandler
	UserHandler              *admin.UserHandler
	UserCertificationHandler *admin.UserCertificationHandler
	UserAccountHandler       *admin.UserAccountHandler
	UserLevelHandler         *admin.UserLevelHandler
	UserNoticeHandler        *admin.UserNoticeHandler
	UserAssetHandler         *admin.UserAssetHandler
	UserWalletHandler        *admin.UserWalletHandler
	UserSwapHandler          *admin.UserSwapHandler
	UserTransferHandler      *admin.UserTransferHandler
	UserBillHandler          *admin.UserBillHandler
	IndexHandler             *admin.IndexHandler
	AssetHandler             *admin.AssetHandler
	PaymentHandler           *admin.PaymentHandler
	CategoryHandler          *admin.CategoryHandler
	ProductHandler           *admin.ProductHandler
	OrderHandler             *admin.OrderHandler
	SettingHandler           *admin.SettingHandler
}

// NewAdminHandler 创建管理员路由
func NewAdminHandler(
	engine *gin.Engine,
	logger logger.Logger,
	conf *config.Config,
	e *casbin.SyncedCachedEnforcer,
	adminHandler *AdminHandler,
	permRepo repo.PermissionRepository,
	menuRepo repo.MenuRepository,
	menuPermissionRepo repo.MenuPermissionRepository,
	operateLogRepo repo.OperateLogRepository,
) *AdminRouter {
	// 静态文件服务器
	engine.Static("/uploads", "./static/uploads")

	return &AdminRouter{
		engine:             engine,
		logger:             logger,
		config:             conf,
		enforcer:           e,
		adminHandler:       adminHandler,
		permissionRepo:     permRepo,
		menuRepo:           menuRepo,
		menuPermissionRepo: menuPermissionRepo,
		operateLogRepo:     operateLogRepo,
		routeGroups:        make(map[string]*gin.RouterGroup),
	}
}

// Route 路由装饰器
func (r *AdminRouter) Route(path, method string, handler gin.HandlerFunc) *RouteBuilder {
	return &RouteBuilder{
		router: r,
		config: RouteDefinition{
			Path:    path,
			Method:  method,
			Handler: handler,
		},
	}
}

// RouteBuilder 路由构建器
type RouteBuilder struct {
	router *AdminRouter
	config RouteDefinition
}

// Auth 添加认证要求
func (rb *RouteBuilder) Auth() *RouteBuilder {
	rb.config.Auth = true
	return rb
}

// Menu 添加菜单要求
func (rb *RouteBuilder) Menu() *RouteBuilder {
	rb.config.IsMenu = true
	return rb
}

// Permission 添加权限要求
func (rb *RouteBuilder) Permission(resource, permission, description string) *RouteBuilder {
	rb.config.Resource = resource
	rb.config.Permission = permission
	rb.config.Description = description
	return rb
}

// Description 添加路由描述
func (rb *RouteBuilder) Description(description string) *RouteBuilder {
	rb.config.Description = description
	return rb
}

// Group 设置路由组
func (rb *RouteBuilder) Group(group string) *RouteBuilder {
	rb.config.Group = group
	return rb
}

// Register 注册路由
func (rb *RouteBuilder) Register() {
	rb.router.registerRoute(rb.config)
}

// registerRoute 注册单个路由
func (r *AdminRouter) registerRoute(route RouteDefinition) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 确定路由组
	groupName := route.Group
	if groupName == "" {
		if route.Permission != "" {
			groupName = "authorized"
		} else if route.Auth {
			groupName = "authenticated"
		} else {
			groupName = "public"
		}
	}

	// 获取或创建路由组
	group := r.getOrCreateRouteGroup(groupName)

	// 注册路由
	group.Handle(route.Method, route.Path, route.Handler)

	// 如果是权限路由，创建权限记录
	if route.Permission != "" {
		go r.createPermissionRecordAsync(route)
	}
}

// getOrCreateRouteGroup 获取或创建路由组
func (r *AdminRouter) getOrCreateRouteGroup(groupName string) *gin.RouterGroup {
	if group, exists := r.routeGroups[groupName]; exists {
		return group
	}

	var group *gin.RouterGroup
	switch groupName {
	case "public":
		group = r.engine.Group(AdmPrefix)
	case "authenticated":
		authGroup := r.engine.Group(AdmPrefix)
		authGroup.Use(middleware.JWTAuth())
		group = authGroup
	case "authorized":
		authGroup := r.engine.Group(AdmPrefix)
		authGroup.Use(middleware.JWTAuth())
		authGroup.Use(middleware.Authorize(r.enforcer))
		group = authGroup
	default:
		// 自定义路由组
		group = r.engine.Group(AdmPrefix + "/" + groupName)
	}

	// 全局挂载操作日志中间件
	group.Use(middleware.OperateLogMiddleware(r.operateLogRepo, models.OperateModeBackend))

	r.routeGroups[groupName] = group
	return group
}

// createPermissionRecordAsync 异步创建权限记录
func (r *AdminRouter) createPermissionRecordAsync(route RouteDefinition) {
	ctx := context.Background()

	// 构建唯一的权限代码：resource:permission:path
	// 使用路径信息确保权限代码的唯一性
	permissionCode := fmt.Sprintf("%s:%s:%s", route.Resource, route.Permission, route.Path)

	// 创建权限对象
	permission := &models.Permission{
		Name:     route.Description,
		Code:     permissionCode,
		Resource: route.Resource,
		Action:   route.Permission,
		Method:   route.Method,
		API:      AdmPrefix + route.Path,
		Status:   models.PermissionStatusEnabled,
		IsSystem: true,
		Remark:   fmt.Sprintf("系统自动创建 - %s", route.Description),
	}

	// 使用 CreateIfNotExists 方法：如果不存在则创建，存在则返回现有记录
	createdPermission, err := r.permissionRepo.CreateIfNotExists(ctx, permission)
	if err != nil {
		r.logger.Error(fmt.Sprintf("创建权限记录失败: %v", err))
		return
	}

	// 给系统默认角色分配权限
	r.assignPermissionToDefaultRoles(createdPermission)

	if route.IsMenu {
		// 查找对应的菜单
		menu, err := r.menuRepo.FindByCode(ctx, permissionCode)
		if err != nil {
			r.logger.Error(fmt.Sprintf("查询菜单失败: %v", err))
			return
		}

		if menu != nil {
			// 创建菜单权限关联
			_, err = r.menuPermissionRepo.CreateIfNotExists(ctx, menu.ID, createdPermission.ID)
			if err != nil {
				r.logger.Error(fmt.Sprintf("关联菜单权限失败: %v", err))
				return
			}
		}
	}
}

// assignPermissionToDefaultRoles 给系统默认角色分配权限
func (r *AdminRouter) assignPermissionToDefaultRoles(permission *models.Permission) {
	// 获取系统默认角色配置
	roleConfig, exists := constant.RolePermissionsMap[constant.InitialSuperManageRoleCode]
	if !exists {
		return
	}

	// 检查是否应该给超级管理员分配这个权限
	if !r.shouldAssignPermission(permission.Code, roleConfig) {
		return
	}

	// 给超级管理员角色分配API权限
	if permission.API != "" && permission.Method != "" {
		action := utils.ConvertMethodToAction(permission.Method)
		_, err := r.enforcer.AddPolicy(constant.InitialSuperManageRoleCode, permission.API, action)
		if err != nil {
			r.logger.Error(fmt.Sprintf("给超级管理员分配API权限失败: %v", err))
		}
	}

	// 给超级管理员角色分配权限代码
	action := utils.ConvertMethodToAction(permission.Method)
	_, err := r.enforcer.AddPolicy(constant.InitialSuperManageRoleCode, permission.Code, action)
	if err != nil {
		r.logger.Error(fmt.Sprintf("给超级管理员分配权限代码失败: %v", err))
	}
}

// shouldAssignPermission 判断是否应该给角色分配这个权限
func (r *AdminRouter) shouldAssignPermission(permissionCode string, config constant.RolePermissionConfig) bool {
	// 检查过滤权限
	for _, filter := range config.FilterPermissions {
		if r.matchPermission(permissionCode, filter) {
			return false
		}
	}

	// 检查接受权限
	for _, accept := range config.Permissions {
		if accept == "*" || r.matchPermission(permissionCode, accept) {
			return true
		}
	}
	return false
}

// matchPermission 匹配权限支持通配符
func (r *AdminRouter) matchPermission(permission, pattern string) bool {
	if pattern == "*" {
		return true
	}
	if strings.HasSuffix(pattern, ":*") {
		prefix := strings.TrimSuffix(pattern, ":*")
		return strings.HasPrefix(permission, prefix+":")
	}
	return permission == pattern
}

// assignRolesToDefaultManagers 给系统默认管理员分配角色
func (r *AdminRouter) assignRolesToDefaultManagers() {
	// 遍历系统默认管理员配置
	for username, roleNames := range constant.ManagerRoleMap {
		// 直接用用户名分配角色
		for _, roleName := range roleNames {
			_, err := r.enforcer.AddGroupingPolicy(username, roleName)
			if err != nil {
				r.logger.Error(fmt.Sprintf("给管理员 %s 分配角色 %s 失败: %v", username, roleName, err))
			}
		}
	}
}

// Register 注册所有路由
func (r *AdminRouter) Register() {
	if r.initialized {
		return
	}

	// 定义所有路由
	r.defineRoutes()

	// 给系统默认管理员分配角色
	r.assignRolesToDefaultManagers()

	r.initialized = true
}

// defineRoutes 定义所有路由
func (r *AdminRouter) defineRoutes() {
	// 公开接口
	r.Route("/auth/login", http.MethodPost, r.adminHandler.ManagerHandler.Login).Description("管理员登录").Register()

	// 认证接口
	r.Route("/menu", http.MethodGet, r.adminHandler.MenuHandler.Menus).Auth().Description("获取菜单").Register()
	r.Route("/manager/info", http.MethodGet, r.adminHandler.ManagerHandler.GetManagerInfo).Auth().Description("获取管理员信息").Register()
	r.Route("/auth/logout", http.MethodPost, r.adminHandler.ManagerHandler.Logout).Auth().Description("退出登录").Register()
	r.Route("/manager/password", http.MethodPut, r.adminHandler.ManagerHandler.ChangePassword).Auth().Description("修改密码").Register()
	r.Route("/manager/settings", http.MethodPut, r.adminHandler.ManagerHandler.UpdateManagerSettings).Auth().Description("修改设置").Register()
	r.Route("/manager/notices/sender", http.MethodGet, r.adminHandler.ManagerNoticeHandler.ListForSender).Auth().Description("获取发送者通知列表").Register()
	r.Route("/manager/notices/receiver", http.MethodGet, r.adminHandler.ManagerNoticeHandler.ListForReceiver).Auth().Description("获取接收者通知列表").Register()
	r.Route("/manager/notices/:id/read", http.MethodPut, r.adminHandler.ManagerNoticeHandler.MarkAsRead).Auth().Description("标记为已读").Register()
	r.Route("/manager/notices/:id", http.MethodDelete, r.adminHandler.ManagerNoticeHandler.DeleteForReceiver).Auth().Description("删除通知").Register()
	r.Route("/upload", http.MethodPost, r.adminHandler.UploadHandler.Upload).Auth().Description("文件上传").Register()
	r.Route("/upload/config", http.MethodGet, r.adminHandler.UploadHandler.GetConfig).Auth().Description("获取上传配置").Register()
	r.Route("/upload", http.MethodDelete, r.adminHandler.UploadHandler.Delete).Auth().Description("删除文件").Register()

	//仪表盘数据
	r.Route("/dashboard/analysis", http.MethodGet, r.adminHandler.IndexHandler.Analysis).Auth().Menu().Permission("dashboard", "analysis", "查看分析页数据").Register()
	r.Route("/dashboard/console", http.MethodGet, r.adminHandler.IndexHandler.Console).Auth().Menu().Permission("dashboard", "console", "查看工作台数据").Register()

	// 用户管理
	r.Route("/user/users", http.MethodGet, r.adminHandler.UserHandler.List).Auth().Menu().Permission("user:users", "list", "查看用户列表").Register()
	r.Route("/user/users", http.MethodPost, r.adminHandler.UserHandler.Create).Auth().Permission("user:users", "create", "创建用户").Register()
	r.Route("/user/users/:id", http.MethodPut, r.adminHandler.UserHandler.Update).Auth().Permission("user:users", "update", "更新用户").Register()
	r.Route("/user/users/:id", http.MethodDelete, r.adminHandler.UserHandler.Delete).Auth().Permission("user:users", "delete", "删除用户").Register()

	// 用户认证
	r.Route("/user/certification", http.MethodGet, r.adminHandler.UserCertificationHandler.List).Auth().Menu().Permission("user:certification", "list", "查看用户认证列表").Register()
	r.Route("/user/certification", http.MethodPost, r.adminHandler.UserCertificationHandler.Create).Auth().Permission("user:certification", "create", "创建用户认证").Register()
	r.Route("/user/certification/:id", http.MethodPut, r.adminHandler.UserCertificationHandler.Update).Auth().Permission("user:certification", "update", "更新用户认证").Register()
	r.Route("/user/certification/:id", http.MethodDelete, r.adminHandler.UserCertificationHandler.Delete).Auth().Permission("user:certification", "delete", "删除用户认证").Register()
	r.Route("/user/certification/batch", http.MethodDelete, r.adminHandler.UserCertificationHandler.BatchDelete).Auth().Permission("user:certification", "delete", "批量删除用户认证").Register()

	// 提现账户
	r.Route("/user/account", http.MethodGet, r.adminHandler.UserAccountHandler.List).Auth().Menu().Permission("user:account", "list", "查看用户账户列表").Register()
	r.Route("/user/account", http.MethodPost, r.adminHandler.UserAccountHandler.Create).Auth().Permission("user:account", "create", "创建用户账户").Register()
	r.Route("/user/account/:id", http.MethodPut, r.adminHandler.UserAccountHandler.Update).Auth().Permission("user:account", "update", "更新用户账户").Register()
	r.Route("/user/account/:id", http.MethodDelete, r.adminHandler.UserAccountHandler.Delete).Auth().Permission("user:account", "delete", "删除用户账户").Register()
	r.Route("/user/account/batch", http.MethodDelete, r.adminHandler.UserAccountHandler.BatchDelete).Auth().Permission("user:account", "delete", "批量删除用户账户").Register()

	// 用户通知
	r.Route("/user/notice", http.MethodGet, r.adminHandler.UserNoticeHandler.List).Auth().Menu().Permission("user:notice", "list", "查看用户通知列表").Register()
	r.Route("/user/notice", http.MethodPost, r.adminHandler.UserNoticeHandler.Create).Auth().Permission("user:notice", "create", "创建用户通知").Register()
	r.Route("/user/notice/:id", http.MethodPut, r.adminHandler.UserNoticeHandler.Update).Auth().Permission("user:notice", "update", "更新用户通知").Register()
	r.Route("/user/notice/:id", http.MethodDelete, r.adminHandler.UserNoticeHandler.Delete).Auth().Permission("user:notice", "delete", "删除用户通知").Register()
	r.Route("/user/notice/batch", http.MethodDelete, r.adminHandler.UserNoticeHandler.BatchDelete).Auth().Permission("user:notice", "delete", "批量删除用户通知").Register()

	// 用户等级
	r.Route("/user/level", http.MethodGet, r.adminHandler.UserLevelHandler.List).Auth().Menu().Permission("user:level", "list", "查看用户等级列表").Register()
	r.Route("/user/level", http.MethodPost, r.adminHandler.UserLevelHandler.Create).Auth().Permission("user:level", "create", "创建用户等级").Register()
	r.Route("/user/level/:id", http.MethodPut, r.adminHandler.UserLevelHandler.Update).Auth().Permission("user:level", "update", "更新用户等级").Register()
	r.Route("/user/level/:id", http.MethodDelete, r.adminHandler.UserLevelHandler.Delete).Auth().Permission("user:level", "delete", "删除用户等级").Register()
	r.Route("/user/level/batch", http.MethodDelete, r.adminHandler.UserLevelHandler.BatchDelete).Auth().Permission("user:level", "delete", "批量删除用户等级").Register()

	// 财务管理 - 用户资产
	r.Route("/wallet/asset", http.MethodGet, r.adminHandler.UserAssetHandler.List).Auth().Menu().Permission("wallet:asset", "list", "查看用户资产列表").Register()
	r.Route("/wallet/asset", http.MethodPost, r.adminHandler.UserAssetHandler.Create).Auth().Permission("wallet:asset", "create", "创建用户资产").Register()
	r.Route("/wallet/asset/:id", http.MethodPut, r.adminHandler.UserAssetHandler.Update).Auth().Permission("wallet:asset", "update", "更新用户资产").Register()
	r.Route("/wallet/asset/:id", http.MethodDelete, r.adminHandler.UserAssetHandler.Delete).Auth().Permission("wallet:asset", "delete", "删除用户资产").Register()
	r.Route("/wallet/asset/batch", http.MethodDelete, r.adminHandler.UserAssetHandler.BatchDelete).Auth().Permission("wallet:asset", "delete", "批量删除用户资产").Register()

	// 提现订单
	r.Route("/wallet/withdraw", http.MethodGet, r.adminHandler.UserWalletHandler.List(models.UserWalletTypeWithdrawal)).Auth().Menu().Permission("wallet:withdraw", "list", "查看提现订单列表").Register()
	r.Route("/wallet/withdraw", http.MethodPost, r.adminHandler.UserWalletHandler.Create(models.UserWalletTypeWithdrawal)).Auth().Permission("wallet:withdraw", "create", "创建提现订单").Register()
	r.Route("/wallet/withdraw/:id", http.MethodPut, r.adminHandler.UserWalletHandler.Update).Auth().Permission("wallet:withdraw", "update", "更新提现订单").Register()
	r.Route("/wallet/withdraw/:id", http.MethodDelete, r.adminHandler.UserWalletHandler.Delete).Auth().Permission("wallet:withdraw", "delete", "删除提现订单").Register()

	// 充值订单
	r.Route("/wallet/deposit", http.MethodGet, r.adminHandler.UserWalletHandler.List(models.UserWalletTypeDeposit)).Auth().Menu().Permission("wallet:deposit", "list", "查看充值订单列表").Register()
	r.Route("/wallet/deposit", http.MethodPost, r.adminHandler.UserWalletHandler.Create(models.UserWalletTypeDeposit)).Auth().Permission("wallet:deposit", "create", "创建充值订单").Register()
	r.Route("/wallet/deposit/:id", http.MethodPut, r.adminHandler.UserWalletHandler.Update).Auth().Permission("wallet:deposit", "update", "更新充值订单").Register()
	r.Route("/wallet/deposit/:id", http.MethodDelete, r.adminHandler.UserWalletHandler.Delete).Auth().Permission("wallet:deposit", "delete", "删除充值订单").Register()

	// 兑换订单
	r.Route("/wallet/swap", http.MethodGet, r.adminHandler.UserSwapHandler.List).Auth().Menu().Permission("wallet:swap", "list", "查看兑换订单列表").Register()
	r.Route("/wallet/swap", http.MethodPost, r.adminHandler.UserSwapHandler.Create).Auth().Permission("wallet:swap", "create", "创建兑换订单").Register()
	r.Route("/wallet/swap/:id", http.MethodPut, r.adminHandler.UserSwapHandler.Update).Auth().Permission("wallet:swap", "update", "更新兑换订单").Register()
	r.Route("/wallet/swap/:id", http.MethodDelete, r.adminHandler.UserSwapHandler.Delete).Auth().Permission("wallet:swap", "delete", "删除兑换订单").Register()
	r.Route("/wallet/swap/batch", http.MethodDelete, r.adminHandler.UserSwapHandler.BatchDelete).Auth().Permission("wallet:swap", "delete", "批量删除兑换订单").Register()

	// 转账订单
	r.Route("/wallet/transfer", http.MethodGet, r.adminHandler.UserTransferHandler.List).Auth().Menu().Permission("wallet:transfer", "list", "查看转账订单列表").Register()
	r.Route("/wallet/transfer", http.MethodPost, r.adminHandler.UserTransferHandler.Create).Auth().Permission("wallet:transfer", "create", "创建转账订单").Register()
	r.Route("/wallet/transfer/:id", http.MethodPut, r.adminHandler.UserTransferHandler.Update).Auth().Permission("wallet:transfer", "update", "更新转账订单").Register()
	r.Route("/wallet/transfer/:id", http.MethodDelete, r.adminHandler.UserTransferHandler.Delete).Auth().Permission("wallet:transfer", "delete", "删除转账订单").Register()
	r.Route("/wallet/transfer/batch", http.MethodDelete, r.adminHandler.UserTransferHandler.BatchDelete).Auth().Permission("wallet:transfer", "delete", "批量删除转账订单").Register()

	// 账单记录
	r.Route("/wallet/bill", http.MethodGet, r.adminHandler.UserBillHandler.List).Auth().Menu().Permission("wallet:bill", "list", "查看账单记录列表").Register()
	r.Route("/wallet/bill/:id", http.MethodPut, r.adminHandler.UserBillHandler.Update).Auth().Permission("wallet:bill", "update", "更新账单记录").Register()
	r.Route("/wallet/bill/:id", http.MethodDelete, r.adminHandler.UserBillHandler.Delete).Auth().Permission("wallet:bill", "delete", "删除账单记录").Register()
	r.Route("/wallet/bill/batch", http.MethodDelete, r.adminHandler.UserBillHandler.BatchDelete).Auth().Permission("wallet:bill", "delete", "批量删除账单记录").Register()

	// 产品管理 - 产品分类
	r.Route("/product/category", http.MethodGet, r.adminHandler.CategoryHandler.List).Auth().Menu().Permission("product:category", "list", "查看产品分类列表").Register()
	r.Route("/product/category", http.MethodPost, r.adminHandler.CategoryHandler.Create).Auth().Permission("product:category", "create", "创建产品分类").Register()
	r.Route("/product/category/:id", http.MethodPut, r.adminHandler.CategoryHandler.Update).Auth().Permission("product:category", "update", "更新产品分类").Register()
	r.Route("/product/category/:id", http.MethodDelete, r.adminHandler.CategoryHandler.Delete).Auth().Permission("product:category", "delete", "删除产品分类").Register()

	// 产品管理 - 产品列表
	r.Route("/product/product", http.MethodGet, r.adminHandler.ProductHandler.List).Auth().Menu().Permission("product:product", "list", "查看产品列表").Register()
	r.Route("/product/product", http.MethodPost, r.adminHandler.ProductHandler.Create).Auth().Permission("product:product", "create", "创建产品").Register()
	r.Route("/product/product/:id", http.MethodPut, r.adminHandler.ProductHandler.Update).Auth().Permission("product:product", "update", "更新产品").Register()
	r.Route("/product/product/:id", http.MethodDelete, r.adminHandler.ProductHandler.Delete).Auth().Permission("product:product", "delete", "删除产品").Register()

	// 订单管理
	r.Route("/product/order", http.MethodGet, r.adminHandler.OrderHandler.List).Auth().Menu().Permission("product:order", "list", "查看订单列表").Register()
	r.Route("/product/order", http.MethodPost, r.adminHandler.OrderHandler.Create).Auth().Permission("product:order", "create", "创建订单").Register()
	r.Route("/product/order/:id", http.MethodPut, r.adminHandler.OrderHandler.Update).Auth().Permission("product:order", "update", "更新订单").Register()
	r.Route("/product/order/:id", http.MethodDelete, r.adminHandler.OrderHandler.Delete).Auth().Permission("product:order", "delete", "删除订单").Register()

	// 后台管理 - 菜单管理
	r.Route("/manager/menu", http.MethodGet, r.adminHandler.MenuHandler.List).Auth().Menu().Permission("manager:menu", "list", "查看菜单列表").Register()
	r.Route("/manager/menu", http.MethodPost, r.adminHandler.MenuHandler.Create).Auth().Permission("manager:menu", "create", "创建菜单").Register()
	r.Route("/manager/menu/:id", http.MethodPut, r.adminHandler.MenuHandler.Update).Auth().Permission("manager:menu", "update", "更新菜单").Register()
	r.Route("/manager/menu/:id", http.MethodDelete, r.adminHandler.MenuHandler.Delete).Auth().Permission("manager:menu", "delete", "删除菜单").Register()
	r.Route("/manager/menu/batch", http.MethodDelete, r.adminHandler.MenuHandler.BatchDelete).Auth().Permission("manager:menu", "delete", "批量删除菜单").Register()

	// 后台管理 - 管理列表
	r.Route("/manager/manager", http.MethodGet, r.adminHandler.ManagerHandler.List).Auth().Menu().Permission("manager:manager", "list", "查看管理列表").Register()
	r.Route("/manager/manager", http.MethodPost, r.adminHandler.ManagerHandler.Create).Auth().Permission("manager:manager", "create", "创建管理").Register()
	r.Route("/manager/manager/:id", http.MethodPut, r.adminHandler.ManagerHandler.Update).Auth().Permission("manager:manager", "update", "更新管理").Register()
	r.Route("/manager/manager/:id", http.MethodDelete, r.adminHandler.ManagerHandler.Delete).Auth().Permission("manager:manager", "delete", "删除管理").Register()

	// 后台管理 - 角色管理
	r.Route("/manager/role", http.MethodGet, r.adminHandler.RoleHandler.List).Auth().Menu().Permission("manager:role", "list", "查看角色列表").Register()
	r.Route("/manager/role", http.MethodPost, r.adminHandler.RoleHandler.Create).Auth().Permission("manager:role", "create", "创建角色").Register()
	r.Route("/manager/role/:id", http.MethodPut, r.adminHandler.RoleHandler.Update).Auth().Permission("manager:role", "update", "更新角色").Register()
	r.Route("/manager/role/:id", http.MethodDelete, r.adminHandler.RoleHandler.Delete).Auth().Permission("manager:role", "delete", "删除角色").Register()

	// 后台管理 - 权限管理
	r.Route("/manager/permission", http.MethodGet, r.adminHandler.PermissionHandler.List).Auth().Menu().Permission("manager:permission", "list", "查看权限列表").Register()
	r.Route("/manager/permission", http.MethodPost, r.adminHandler.PermissionHandler.Create).Auth().Permission("manager:permission", "create", "创建权限").Register()
	r.Route("/manager/permission/:id", http.MethodPut, r.adminHandler.PermissionHandler.Update).Auth().Permission("manager:permission", "update", "更新权限").Register()

	// 后台管理 - 通知管理
	r.Route("/manager/notice", http.MethodGet, r.adminHandler.ManagerNoticeHandler.ListForSender).Auth().Menu().Permission("manager:notice", "list", "查看通知列表").Register()
	r.Route("/manager/notice", http.MethodPost, r.adminHandler.ManagerNoticeHandler.Create).Auth().Permission("manager:notice", "create", "创建通知").Register()
	r.Route("/manager/notice/:id", http.MethodPut, r.adminHandler.ManagerNoticeHandler.Update).Auth().Permission("manager:notice", "update", "更新通知").Register()
	r.Route("/manager/notice/:id", http.MethodDelete, r.adminHandler.ManagerNoticeHandler.Delete).Auth().Permission("manager:notice", "delete", "删除通知").Register()
	r.Route("/manager/notice/batch", http.MethodDelete, r.adminHandler.ManagerNoticeHandler.BatchDelete).Auth().Permission("manager:notice", "delete", "批量删除通知").Register()

	// 系统配置 - 项目配置
	r.Route("/system/setting", http.MethodGet, r.adminHandler.SettingHandler.List).Auth().Menu().Permission("system:setting", "list", "查看项目配置列表").Register()
	r.Route("/system/setting/:id", http.MethodPut, r.adminHandler.SettingHandler.Update).Auth().Permission("system:setting", "update", "更新项目配置").Register()
	r.Route("/system/setting/:id/data", http.MethodPut, r.adminHandler.SettingHandler.UpdateData).Auth().Permission("system:setting", "update", "更新项目配置数据").Register()

	// 系统配置 - 资产配置
	r.Route("/system/asset", http.MethodGet, r.adminHandler.AssetHandler.List).Auth().Menu().Permission("system:asset", "list", "查看资产配置列表").Register()
	r.Route("/system/asset", http.MethodPost, r.adminHandler.AssetHandler.Create).Auth().Permission("system:asset", "create", "创建资产配置").Register()
	r.Route("/system/asset/:id", http.MethodPut, r.adminHandler.AssetHandler.Update).Auth().Permission("system:asset", "update", "更新资产配置").Register()
	r.Route("/system/asset/:id", http.MethodDelete, r.adminHandler.AssetHandler.Delete).Auth().Permission("system:asset", "delete", "删除资产配置").Register()
	r.Route("/system/asset/batch", http.MethodDelete, r.adminHandler.AssetHandler.BatchDelete).Auth().Permission("system:asset", "delete", "批量删除资产配置").Register()

	// 系统配置 - 支付配置
	r.Route("/system/payment", http.MethodGet, r.adminHandler.PaymentHandler.List).Auth().Menu().Permission("system:payment", "list", "查看支付配置列表").Register()
	r.Route("/system/payment", http.MethodPost, r.adminHandler.PaymentHandler.Create).Auth().Permission("system:payment", "create", "创建支付配置").Register()
	r.Route("/system/payment/:id", http.MethodPut, r.adminHandler.PaymentHandler.Update).Auth().Permission("system:payment", "update", "更新支付配置").Register()
	r.Route("/system/payment/:id", http.MethodDelete, r.adminHandler.PaymentHandler.Delete).Auth().Permission("system:payment", "delete", "删除支付配置").Register()
	r.Route("/system/payment/batch", http.MethodDelete, r.adminHandler.PaymentHandler.BatchDelete).Auth().Permission("system:payment", "delete", "批量删除支付配置").Register()

	// 系统配置 - 菜单配置
	r.Route("/system/menu", http.MethodGet, r.adminHandler.FrontMenuHandler.List).Auth().Menu().Permission("system:front-menu", "list", "查看菜单配置列表").Register()
	r.Route("/system/menu/:id", http.MethodPut, r.adminHandler.FrontMenuHandler.Update).Auth().Permission("system:front-menu", "update", "更新菜单配置").Register()

	// 系统配置 - 等级配置
	r.Route("/manager/level", http.MethodGet, r.adminHandler.LevelHandler.List).Auth().Menu().Permission("manager:level", "list", "查看等级列表").Register()
	r.Route("/manager/level", http.MethodPost, r.adminHandler.LevelHandler.Create).Auth().Permission("manager:level", "create", "创建等级").Register()
	r.Route("/manager/level/:id", http.MethodPut, r.adminHandler.LevelHandler.Update).Auth().Permission("manager:level", "update", "更新等级").Register()
	r.Route("/manager/level/:id", http.MethodDelete, r.adminHandler.LevelHandler.Delete).Auth().Permission("manager:level", "delete", "删除等级").Register()
	r.Route("/manager/level/batch", http.MethodDelete, r.adminHandler.LevelHandler.BatchDelete).Auth().Permission("manager:level", "delete", "批量删除等级").Register()

	// 系统配置 - 文章配置
	r.Route("/system/article", http.MethodGet, r.adminHandler.ArticleHandler.List).Auth().Menu().Permission("system:article", "list", "查看文章列表").Register()
	r.Route("/system/article", http.MethodPost, r.adminHandler.ArticleHandler.Create).Auth().Permission("system:article", "create", "创建文章").Register()
	r.Route("/system/article/:id", http.MethodPut, r.adminHandler.ArticleHandler.Update).Auth().Permission("system:article", "update", "更新文章").Register()
	r.Route("/system/article/:id", http.MethodDelete, r.adminHandler.ArticleHandler.Delete).Auth().Permission("system:article", "delete", "删除文章").Register()
	r.Route("/system/article/batch", http.MethodDelete, r.adminHandler.ArticleHandler.BatchDelete).Auth().Permission("system:article", "delete", "批量删除文章").Register()

	// 系统配置 - 语言配置
	r.Route("/system/language", http.MethodGet, r.adminHandler.LangHandler.List).Auth().Menu().Permission("system:language", "list", "查看语言列表").Register()
	r.Route("/system/language", http.MethodPost, r.adminHandler.LangHandler.Create).Auth().Permission("system:language", "create", "创建语言").Register()
	r.Route("/system/language/:id", http.MethodPut, r.adminHandler.LangHandler.Update).Auth().Permission("system:language", "update", "更新语言").Register()
	r.Route("/system/language/:id", http.MethodDelete, r.adminHandler.LangHandler.Delete).Auth().Permission("system:language", "delete", "删除语言").Register()
	r.Route("/system/language/batch", http.MethodDelete, r.adminHandler.LangHandler.BatchDelete).Auth().Permission("system:language", "delete", "批量删除语言").Register()

	// 系统配置 - 国家配置
	r.Route("/system/country", http.MethodGet, r.adminHandler.CountryHandler.List).Auth().Menu().Permission("system:country", "list", "查看国家列表").Register()
	r.Route("/system/country", http.MethodPost, r.adminHandler.CountryHandler.Create).Auth().Permission("system:country", "create", "创建国家").Register()
	r.Route("/system/country/:id", http.MethodPut, r.adminHandler.CountryHandler.Update).Auth().Permission("system:country", "update", "更新国家").Register()
	r.Route("/system/country/:id", http.MethodDelete, r.adminHandler.CountryHandler.Delete).Auth().Permission("system:country", "delete", "删除国家").Register()
	r.Route("/system/country/batch", http.MethodDelete, r.adminHandler.CountryHandler.BatchDelete).Auth().Permission("system:country", "delete", "批量删除国家").Register()

	// 系统配置 - 翻译配置
	r.Route("/system/translation", http.MethodGet, r.adminHandler.TranslationHandler.List).Auth().Menu().Permission("system:translation", "list", "查看翻译列表").Register()
	r.Route("/system/translation", http.MethodPost, r.adminHandler.TranslationHandler.Create).Auth().Permission("system:translation", "create", "创建翻译").Register()
	r.Route("/system/translation/:id", http.MethodPut, r.adminHandler.TranslationHandler.Update).Auth().Permission("system:translation", "update", "更新翻译").Register()
	r.Route("/system/translation/:id", http.MethodDelete, r.adminHandler.TranslationHandler.Delete).Auth().Permission("system:translation", "delete", "删除翻译").Register()
	r.Route("/system/translation/batch", http.MethodDelete, r.adminHandler.TranslationHandler.BatchDelete).Auth().Permission("system:translation", "delete", "批量删除翻译").Register()

	// 系统监控 - 服务监控
	r.Route("/monitor/server", http.MethodGet, r.adminHandler.MonitorHandler.Server).Auth().Menu().Permission("monitor:server", "list", "查看服务监控").Register()

	// 系统监控 - 登录日志
	r.Route("/monitor/loginLog", http.MethodGet, r.adminHandler.LoginLogHandler.List).Auth().Menu().Permission("monitor:login-log", "list", "查看登录日志").Register()
	r.Route("/monitor/loginLog/:id", http.MethodDelete, r.adminHandler.LoginLogHandler.Delete).Auth().Permission("monitor:login-log", "delete", "删除登录日志").Register()
	r.Route("/monitor/loginLog/batch", http.MethodDelete, r.adminHandler.LoginLogHandler.BatchDelete).Auth().Permission("monitor:login-log", "delete", "批量删除登录日志").Register()

	// 系统监控 - 操作日志
	r.Route("/monitor/operateLog", http.MethodGet, r.adminHandler.OperateLogHandler.List).Auth().Menu().Permission("monitor:operate-log", "list", "查看操作日志").Register()
	r.Route("/monitor/operateLog/:id", http.MethodDelete, r.adminHandler.OperateLogHandler.Delete).Auth().Permission("monitor:operate-log", "delete", "删除操作日志").Register()
	r.Route("/monitor/operateLog/batch", http.MethodDelete, r.adminHandler.OperateLogHandler.BatchDelete).Auth().Permission("monitor:operate-log", "delete", "批量删除操作日志").Register()
}
