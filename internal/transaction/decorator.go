package transaction

import (
	"context"
	"fmt"
	"reflect"
	"runtime"
)

// ServiceTransactionDecorator Service层事务装饰器
type ServiceTransactionDecorator struct {
	txManager TransactionManager
}

// NewServiceTransactionDecorator 创建Service事务装饰器
func NewServiceTransactionDecorator(txManager TransactionManager) *ServiceTransactionDecorator {
	return &ServiceTransactionDecorator{
		txManager: txManager,
	}
}

// WithTransaction 事务装饰器方法
func (d *ServiceTransactionDecorator) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return d.txManager.Execute(ctx, fn)
}

// WithTransactionResult 带返回值的事务装饰器方法
func (d *ServiceTransactionDecorator) WithTransactionResult(ctx context.Context, fn func(ctx context.Context) (interface{}, error)) (interface{}, error) {
	return d.txManager.ExecuteWithResult(ctx, fn)
}

// WithNestedTransaction 嵌套事务装饰器（如果支持）
func (d *ServiceTransactionDecorator) WithNestedTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	if advTxManager, ok := d.txManager.(AdvancedTransactionManager); ok {
		return advTxManager.ExecuteNested(ctx, fn)
	}
	// 如果不支持嵌套事务，回退到普通事务
	return d.txManager.Execute(ctx, fn)
}

// TransactionalFunc 事务函数类型
type TransactionalFunc func(ctx context.Context) error

// TransactionalFuncWithResult 带返回值的事务函数类型
type TransactionalFuncWithResult[T any] func(ctx context.Context) (T, error)

// Transactional 事务注解式装饰器
func (d *ServiceTransactionDecorator) Transactional(fn TransactionalFunc) TransactionalFunc {
	return func(ctx context.Context) error {
		return d.WithTransaction(ctx, fn)
	}
}

// TransactionalWithResult 带返回值的事务注解式装饰器
func (d *ServiceTransactionDecorator) TransactionalWithResult[T any](fn TransactionalFuncWithResult[T]) TransactionalFuncWithResult[T] {
	return func(ctx context.Context) (T, error) {
		result, err := d.WithTransactionResult(ctx, func(ctx context.Context) (interface{}, error) {
			return fn(ctx)
		})
		if err != nil {
			var zero T
			return zero, err
		}
		return result.(T), nil
	}
}

// TransactionPropagation 事务传播行为
type TransactionPropagation int

const (
	// PropagationRequired 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务
	PropagationRequired TransactionPropagation = iota
	// PropagationRequiresNew 创建一个新的事务，如果当前存在事务，则把当前事务挂起
	PropagationRequiresNew
	// PropagationSupports 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务的方式继续运行
	PropagationSupports
	// PropagationNotSupported 以非事务方式运行，如果当前存在事务，则把当前事务挂起
	PropagationNotSupported
	// PropagationNever 以非事务方式运行，如果当前存在事务，则抛出异常
	PropagationNever
	// PropagationNested 如果当前存在事务，则创建一个事务作为当前事务的嵌套事务来运行
	PropagationNested
)

// TransactionConfig 事务配置
type TransactionConfig struct {
	Propagation    TransactionPropagation
	IsolationLevel string
	ReadOnly       bool
	Timeout        int
}

// WithTransactionConfig 使用配置执行事务
func (d *ServiceTransactionDecorator) WithTransactionConfig(ctx context.Context, config *TransactionConfig, fn func(ctx context.Context) error) error {
	switch config.Propagation {
	case PropagationRequired:
		return d.txManager.Execute(ctx, fn)
	case PropagationRequiresNew:
		// 总是创建新事务（这里简化实现，实际可能需要挂起当前事务）
		return d.txManager.Execute(context.Background(), fn)
	case PropagationSupports:
		// 如果有事务就用，没有就不用
		return fn(ctx)
	case PropagationNotSupported:
		// 不使用事务
		return fn(context.Background())
	case PropagationNever:
		// 如果存在事务则抛出异常
		if d.txManager.IsInTransaction(ctx) {
			return fmt.Errorf("transaction not allowed")
		}
		return fn(ctx)
	case PropagationNested:
		return d.WithNestedTransaction(ctx, fn)
	default:
		return d.txManager.Execute(ctx, fn)
	}
}

// LoggingTransactionDecorator 带日志的事务装饰器
type LoggingTransactionDecorator struct {
	*ServiceTransactionDecorator
	logger Logger
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
}

// NewLoggingTransactionDecorator 创建带日志的事务装饰器
func NewLoggingTransactionDecorator(txManager TransactionManager, logger Logger) *LoggingTransactionDecorator {
	return &LoggingTransactionDecorator{
		ServiceTransactionDecorator: NewServiceTransactionDecorator(txManager),
		logger:                      logger,
	}
}

// WithTransaction 带日志的事务执行
func (d *LoggingTransactionDecorator) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	funcName := getFunctionName(fn)
	d.logger.Debug("开始执行事务", "function", funcName)
	
	err := d.ServiceTransactionDecorator.WithTransaction(ctx, fn)
	
	if err != nil {
		d.logger.Error("事务执行失败", "function", funcName, "error", err)
	} else {
		d.logger.Debug("事务执行成功", "function", funcName)
	}
	
	return err
}

// getFunctionName 获取函数名称
func getFunctionName(fn interface{}) string {
	return runtime.FuncForPC(reflect.ValueOf(fn).Pointer()).Name()
}
