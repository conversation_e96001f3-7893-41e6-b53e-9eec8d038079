package transaction

import (
	"context"
	"gin/internal/repository"
	"gin/internal/service"
	"gorm.io/gorm"
)

// ServiceContainer 服务容器示例
type ServiceContainer struct {
	db              *gorm.DB
	txManager       TransactionManager
	repoManager     *repository.RepositoryManager
	txDecorator     *ServiceTransactionDecorator
	userService     *service.UserService
}

// NewServiceContainer 创建服务容器
func NewServiceContainer(db *gorm.DB) *ServiceContainer {
	// 创建事务管理器
	txManager := NewTransactionManager(db)
	
	// 创建仓库管理器
	repoManager := repository.NewRepositoryManager(db, txManager)
	
	// 创建事务装饰器
	txDecorator := NewServiceTransactionDecorator(txManager)
	
	// 创建用户服务
	userService := service.NewUserService(repoManager, txDecorator)
	
	return &ServiceContainer{
		db:          db,
		txManager:   txManager,
		repoManager: repoManager,
		txDecorator: txDecorator,
		userService: userService,
	}
}

// UserService 获取用户服务
func (c *ServiceContainer) UserService() *service.UserService {
	return c.userService
}

// TransactionManager 获取事务管理器
func (c *ServiceContainer) TransactionManager() TransactionManager {
	return c.txManager
}

// ExampleUsage 使用示例
func ExampleUsage() {
	// 假设已经有了数据库连接
	var db *gorm.DB
	
	// 创建服务容器
	container := NewServiceContainer(db)
	
	// 使用用户服务
	userService := container.UserService()
	
	// 示例1: 简单的事务操作（在Create方法内部已经使用了事务）
	ctx := context.Background()
	// err := userService.Create(ctx, &admin.UserCreateRequest{...})
	
	// 示例2: 手动控制事务
	txManager := container.TransactionManager()
	err := txManager.Execute(ctx, func(txCtx context.Context) error {
		// 在这个函数内部的所有数据库操作都会在同一个事务中执行
		// user1, err := userService.GetByID(txCtx, 1)
		// if err != nil {
		//     return err
		// }
		// 
		// user2, err := userService.GetByID(txCtx, 2)
		// if err != nil {
		//     return err
		// }
		// 
		// // 执行一些业务逻辑...
		// 
		// return userService.Update(txCtx, 1, &admin.UserUpdateRequest{...})
		return nil
	})
	
	if err != nil {
		// 处理错误
	}
	
	// 示例3: 嵌套事务（如果支持）
	if advTxManager, ok := txManager.(AdvancedTransactionManager); ok {
		err = advTxManager.Execute(ctx, func(outerTxCtx context.Context) error {
			// 外层事务操作
			// ...
			
			// 嵌套事务
			return advTxManager.ExecuteNested(outerTxCtx, func(innerTxCtx context.Context) error {
				// 内层事务操作
				// 如果这里发生错误，只会回滚到保存点，不会影响外层事务
				return nil
			})
		})
	}
}

// ComplexBusinessOperation 复杂业务操作示例
func ComplexBusinessOperation(container *ServiceContainer) error {
	ctx := context.Background()
	txManager := container.TransactionManager()
	
	return txManager.Execute(ctx, func(txCtx context.Context) error {
		// 步骤1: 创建用户
		// user, err := container.UserService().Create(txCtx, &admin.UserCreateRequest{...})
		// if err != nil {
		//     return err
		// }
		
		// 步骤2: 创建用户资产
		// err = container.UserAssetService().Create(txCtx, &admin.UserAssetCreateRequest{...})
		// if err != nil {
		//     return err
		// }
		
		// 步骤3: 发送欢迎邮件（这个操作可能需要嵌套事务）
		// if advTxManager, ok := txManager.(AdvancedTransactionManager); ok {
		//     err = advTxManager.ExecuteNested(txCtx, func(nestedCtx context.Context) error {
		//         // 记录邮件发送日志
		//         return container.EmailLogService().Create(nestedCtx, &models.EmailLog{...})
		//     })
		//     if err != nil {
		//         // 邮件日志记录失败不应该影响用户创建，所以这里可以选择忽略错误或记录日志
		//         log.Printf("Failed to log email: %v", err)
		//     }
		// }
		
		return nil
	})
}
