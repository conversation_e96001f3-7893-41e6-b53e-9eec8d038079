package transaction

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// TransactionManager 事务管理器接口
type TransactionManager interface {
	// Execute 执行事务
	Execute(ctx context.Context, fn func(ctx context.Context) error) error
	// ExecuteWithResult 执行事务并返回结果
	ExecuteWithResult(ctx context.Context, fn func(ctx context.Context) (interface{}, error)) (interface{}, error)
	// GetDB 获取当前上下文的数据库连接（事务或普通连接）
	GetDB(ctx context.Context) *gorm.DB
	// IsInTransaction 检查当前是否在事务中
	IsInTransaction(ctx context.Context) bool
}

// transactionManager 事务管理器实现
type transactionManager struct {
	db *gorm.DB
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager(db *gorm.DB) TransactionManager {
	return &transactionManager{
		db: db,
	}
}

// contextKey 用于在context中存储事务的key
type contextKey string

const (
	txKey contextKey = "transaction"
)

// Execute 执行事务
func (tm *transactionManager) Execute(ctx context.Context, fn func(ctx context.Context) error) error {
	// 如果已经在事务中，直接执行函数
	if tm.IsInTransaction(ctx) {
		return fn(ctx)
	}

	// 开启新事务
	return tm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 将事务存储到context中
		txCtx := context.WithValue(ctx, txKey, tx)
		return fn(txCtx)
	})
}

// ExecuteWithResult 执行事务并返回结果
func (tm *transactionManager) ExecuteWithResult(ctx context.Context, fn func(ctx context.Context) (interface{}, error)) (interface{}, error) {
	var result interface{}
	var fnErr error

	err := tm.Execute(ctx, func(txCtx context.Context) error {
		result, fnErr = fn(txCtx)
		return fnErr
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetDB 获取当前上下文的数据库连接
func (tm *transactionManager) GetDB(ctx context.Context) *gorm.DB {
	if tx, ok := ctx.Value(txKey).(*gorm.DB); ok {
		return tx
	}
	return tm.db
}

// IsInTransaction 检查当前是否在事务中
func (tm *transactionManager) IsInTransaction(ctx context.Context) bool {
	_, ok := ctx.Value(txKey).(*gorm.DB)
	return ok
}

// TransactionOptions 事务选项
type TransactionOptions struct {
	// IsolationLevel 隔离级别
	IsolationLevel string
	// ReadOnly 是否只读
	ReadOnly bool
	// Timeout 超时时间
	Timeout int
}

// AdvancedTransactionManager 高级事务管理器接口
type AdvancedTransactionManager interface {
	TransactionManager
	// ExecuteWithOptions 使用选项执行事务
	ExecuteWithOptions(ctx context.Context, options *TransactionOptions, fn func(ctx context.Context) error) error
	// ExecuteNested 执行嵌套事务（使用保存点）
	ExecuteNested(ctx context.Context, fn func(ctx context.Context) error) error
}

// advancedTransactionManager 高级事务管理器实现
type advancedTransactionManager struct {
	*transactionManager
	savepointCounter int
}

// NewAdvancedTransactionManager 创建高级事务管理器
func NewAdvancedTransactionManager(db *gorm.DB) AdvancedTransactionManager {
	return &advancedTransactionManager{
		transactionManager: &transactionManager{db: db},
		savepointCounter:   0,
	}
}

// ExecuteWithOptions 使用选项执行事务
func (atm *advancedTransactionManager) ExecuteWithOptions(ctx context.Context, options *TransactionOptions, fn func(ctx context.Context) error) error {
	if atm.IsInTransaction(ctx) {
		return fn(ctx)
	}

	// 配置事务选项
	tx := atm.db.WithContext(ctx)
	
	if options != nil {
		if options.IsolationLevel != "" {
			// 设置隔离级别（需要根据数据库类型调整）
			tx = tx.Set("gorm:isolation_level", options.IsolationLevel)
		}
		if options.ReadOnly {
			tx = tx.Set("gorm:read_only", true)
		}
	}

	return tx.Transaction(func(txDB *gorm.DB) error {
		txCtx := context.WithValue(ctx, txKey, txDB)
		return fn(txCtx)
	})
}

// ExecuteNested 执行嵌套事务（使用保存点）
func (atm *advancedTransactionManager) ExecuteNested(ctx context.Context, fn func(ctx context.Context) error) error {
	if !atm.IsInTransaction(ctx) {
		// 如果不在事务中，开启新事务
		return atm.Execute(ctx, fn)
	}

	// 在事务中，使用保存点
	tx := atm.GetDB(ctx)
	atm.savepointCounter++
	savepointName := fmt.Sprintf("sp_%d", atm.savepointCounter)

	// 创建保存点
	if err := tx.SavePoint(savepointName).Error; err != nil {
		return fmt.Errorf("failed to create savepoint: %w", err)
	}

	// 执行函数
	err := fn(ctx)
	if err != nil {
		// 回滚到保存点
		if rollbackErr := tx.RollbackTo(savepointName).Error; rollbackErr != nil {
			return fmt.Errorf("failed to rollback to savepoint: %w (original error: %v)", rollbackErr, err)
		}
		return err
	}

	// 释放保存点
	if err := tx.Exec(fmt.Sprintf("RELEASE SAVEPOINT %s", savepointName)).Error; err != nil {
		return fmt.Errorf("failed to release savepoint: %w", err)
	}

	return nil
}

// TransactionError 事务错误类型
type TransactionError struct {
	Operation string
	Cause     error
}

func (e *TransactionError) Error() string {
	return fmt.Sprintf("transaction %s failed: %v", e.Operation, e.Cause)
}

func (e *TransactionError) Unwrap() error {
	return e.Cause
}

// NewTransactionError 创建事务错误
func NewTransactionError(operation string, cause error) *TransactionError {
	return &TransactionError{
		Operation: operation,
		Cause:     cause,
	}
}

// IsTransactionError 检查是否为事务错误
func IsTransactionError(err error) bool {
	var txErr *TransactionError
	return errors.As(err, &txErr)
}
