package middleware

import (
	"bytes"
	"gin/internal/infrastructure/context"
	"gin/internal/models"
	"gin/internal/repository/repo"
	"gin/pkg/net"
	"github.com/gin-gonic/gin"
	"io"
)

// OperateLogMiddleware 中间件：采集请求/响应基础信息
func OperateLogMiddleware(operateLogRepo repo.OperateLogRepository, mode int8) gin.HandlerFunc {
	return func(c *gin.Context) {
		var reqBody []byte
		if c.Request.Body != nil {
			reqBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody)) // 还原
		}

		c.Next()

		if operateLogRepo == nil {
			return
		}

		// 提前取出需要的字段，避免 goroutine 里访问 gin.Context
		operateLog := models.OperateLog{
			UserID:    context.GetUserIdFromCtx(c),
			ManagerID: context.GetManagerIdFromCtx(c),
			Type:      models.InferOperateType(c.Request.Method),
			Mode:      mode,
			ReqPath:   c.Request.URL.Path,
			ReqParams: string(reqBody),
			Status:    models.InferOperateStatus(c.Writer.Status()),
			IP:        c.ClientIP(),
			Location:  net.ResolveIPRegion(c.ClientIP()),
			UserAgent: c.Request.UserAgent(),
		}

		go func(log models.OperateLog) {
			if log.UserID > 0 {
				username, err := operateLogRepo.FindUsernameByUserID(c, log.UserID)
				if err != nil {
					log.ErrorMsg = err.Error()
				}
				log.Username = username
			}
			if log.ManagerID > 0 {
				username, err := operateLogRepo.FindUsernameByManagerID(c, log.ManagerID)
				if err != nil {
					log.ErrorMsg = err.Error()
				}
				log.Username = username
			}
			_ = operateLogRepo.Create(c, &log)
		}(operateLog)
	}
}
