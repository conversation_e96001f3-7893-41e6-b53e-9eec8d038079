package middleware

import (
	"gin/internal/infrastructure/config"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type TokenBucket struct {
	capacity   int64         // 桶的容量
	rate       float64       // 令牌放入速率
	tokens     float64       // 当前令牌数量
	lastToken  time.Time     // 上一次放令牌的时间
	expiration time.Duration // 过期时间
	mtx        sync.Mutex    // 互斥锁
}

func NewTokenBucket(capacity int64, rate float64, expiration int) *TokenBucket {
	return &TokenBucket{
		capacity:   capacity,
		rate:       rate,
		tokens:     float64(capacity), // 初始化时填满令牌
		lastToken:  time.Now(),
		expiration: time.Duration(expiration) * time.Second,
	}
}

func (tb *TokenBucket) Allow() bool {
	tb.mtx.Lock()
	defer tb.mtx.Unlock()

	now := time.Now()

	// 如果超过过期时间，重置令牌桶
	if now.Sub(tb.lastToken) > tb.expiration {
		tb.tokens = float64(tb.capacity)
		tb.lastToken = now
		return true
	}

	// 计算需要放的令牌数量
	elapsed := now.Sub(tb.lastToken).Seconds()
	tb.tokens = tb.tokens + tb.rate*elapsed

	// 确保不超过容量
	if tb.tokens > float64(tb.capacity) {
		tb.tokens = float64(tb.capacity)
	}

	// 判断是否允许请求
	if tb.tokens >= 1 {
		tb.tokens--
		tb.lastToken = now
		return true
	}

	return false
}

func LimitMiddleware(cfg *config.Config) gin.HandlerFunc {
	tb := NewTokenBucket(
		cfg.Limiter.Max,
		float64(cfg.Limiter.Max)/float64(cfg.Limiter.Expiration), // 计算每秒生成的令牌数
		cfg.Limiter.Expiration,
	)

	return func(c *gin.Context) {
		if !tb.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    429,
				"message": "请求太频繁，请稍后再试",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
