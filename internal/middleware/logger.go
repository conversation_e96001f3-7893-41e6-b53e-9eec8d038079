package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算处理时间
		latency := time.Since(start)

		// 获取状态码
		statusCode := c.Writer.Status()

		// 构建日志字段
		fields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", query),
			zap.Int("status", statusCode),
			zap.Duration("latency", latency),
			zap.String("ip", c.ClientIP()),
			zap.String("user_agent", c.Request.UserAgent()),
			zap.Int64("body_size", int64(c.<PERSON>.<PERSON>ze())),
		}

		// 如果有认证用户信息，添加用户ID
		if userID := getUserID(c); userID != "" {
			fields = append(fields, zap.String("user_id", userID))
		}

		// 如果有错误，添加错误信息
		if len(c.Errors) > 0 {
			fields = append(fields, zap.String("error", c.Errors.String()))
		}

		// 根据状态码选择日志级别
		//switch {
		//case statusCode >= 500:
		//	logger.Error("HTTP Request", fields...)
		//case statusCode >= 400:
		//	logger.Warn("HTTP Request", fields...)
		//default:
		//	logger.Info("HTTP Request", fields...)
		//}
	}
}

// RequestLoggerWithConfig 带配置的请求日志中间件
func RequestLoggerWithConfig(config LoggerConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否跳过此路径
		path := c.Request.URL.Path
		for _, skipPath := range config.SkipPaths {
			if path == skipPath {
				c.Next()
				return
			}
		}

		start := time.Now()
		query := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算处理时间
		latency := time.Since(start)

		// 获取状态码
		statusCode := c.Writer.Status()

		// 构建基础日志字段
		fields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.Int("status", statusCode),
			zap.Duration("latency", latency),
			zap.String("ip", c.ClientIP()),
		}

		// 根据配置添加额外字段
		if config.EnableQuery && query != "" {
			fields = append(fields, zap.String("query", query))
		}

		if config.EnableUserAgent {
			fields = append(fields, zap.String("user_agent", c.Request.UserAgent()))
		}

		if config.EnableBodySize {
			fields = append(fields, zap.Int64("body_size", int64(c.Writer.Size())))
		}

		if config.EnableReferer {
			if referer := c.Request.Referer(); referer != "" {
				fields = append(fields, zap.String("referer", referer))
			}
		}

		if config.EnableRequestID {
			if requestID := c.GetString("request_id"); requestID != "" {
				fields = append(fields, zap.String("request_id", requestID))
			}
		}

		// 如果有认证用户信息，添加用户ID
		if userID := getUserID(c); userID != "" {
			fields = append(fields, zap.String("user_id", userID))
		}

		// 如果有错误，添加错误信息
		if len(c.Errors) > 0 {
			fields = append(fields, zap.String("error", c.Errors.String()))
		}

		// 根据状态码和配置选择日志级别
		//message := "HTTP Request"
		//switch {
		//case statusCode >= 500:
		//	logger.Error(message, fields...)
		//case statusCode >= 400:
		//	if config.LogClientErrors {
		//		logger.Warn(message, fields...)
		//	} else if config.LogInfoOnly {
		//		logger.Info(message, fields...)
		//	}
		//case statusCode >= 300:
		//	if config.LogRedirects {
		//		logger.Info(message, fields...)
		//	}
		//default:
		//	if config.LogSuccessRequests {
		//		logger.Info(message, fields...)
		//	}
		//}
	}
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	// 跳过记录的路径
	SkipPaths []string

	// 是否记录查询参数
	EnableQuery bool

	// 是否记录用户代理
	EnableUserAgent bool

	// 是否记录响应体大小
	EnableBodySize bool

	// 是否记录来源页面
	EnableReferer bool

	// 是否记录请求ID
	EnableRequestID bool

	// 是否记录客户端错误(4xx)
	LogClientErrors bool

	// 是否记录重定向(3xx)
	LogRedirects bool

	// 是否记录成功请求(2xx)
	LogSuccessRequests bool

	// 是否只记录INFO级别日志
	LogInfoOnly bool
}

// DefaultLoggerConfig 默认日志配置
func DefaultLoggerConfig() LoggerConfig {
	return LoggerConfig{
		SkipPaths:          []string{"/health", "/metrics"},
		EnableQuery:        true,
		EnableUserAgent:    true,
		EnableBodySize:     true,
		EnableReferer:      false,
		EnableRequestID:    true,
		LogClientErrors:    true,
		LogRedirects:       false,
		LogSuccessRequests: true,
		LogInfoOnly:        false,
	}
}

// ProductionLoggerConfig 生产环境日志配置
func ProductionLoggerConfig() LoggerConfig {
	return LoggerConfig{
		SkipPaths:          []string{"/health", "/metrics", "/favicon.ico"},
		EnableQuery:        false, // 生产环境不记录查询参数，避免敏感信息泄露
		EnableUserAgent:    false,
		EnableBodySize:     true,
		EnableReferer:      false,
		EnableRequestID:    true,
		LogClientErrors:    true,
		LogRedirects:       false,
		LogSuccessRequests: false, // 生产环境不记录成功请求，减少日志量
		LogInfoOnly:        false,
	}
}

// getUserID 从上下文中获取用户ID
func getUserID(c *gin.Context) string {
	// 尝试从JWT token中获取用户ID
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}

	// 尝试从其他地方获取用户标识
	if userIDStr, exists := c.Get("user"); exists {
		if id, ok := userIDStr.(string); ok {
			return id
		}
	}

	return ""
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试从请求头获取请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			// 如果没有，生成一个新的请求ID
			requestID = generateRequestID()
		}

		// 设置到上下文和响应头
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 这里可以使用UUID或其他方式生成唯一ID
	// 为了简单起见，使用时间戳
	return time.Now().Format("20060102150405") + randomString(6)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
