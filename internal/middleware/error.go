package middleware

import (
	"github.com/gin-gonic/gin"
)

// ErrorHandler 统一错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行后续的处理函数和中间件
		c.Next()

		// 如果没有错误，或者响应已经被发送，则直接返回
		if len(c.Errors) == 0 || c.Writer.Written() {
			return
		}

		// 处理第一个错误
		err := c.Errors[0].Err

		// 记录错误日志
		//logger.Error("请求处理出错",
		//	zap.String("method", c.Request.Method),
		//	zap.String("path", c.Request.URL.Path),
		//	zap.String("ip", c.ClientIP()),
		//	zap.Error(err),
		//)

		// 检查是否为应用错误
		//if appErr, ok := appErrors.AsAppError(err); ok {
		//	handleAppError(c, appErr)
		//	return
		//}

		handleUnknownError(c, err)
	}
}

// handleUnknownError 处理未知错误
func handleUnknownError(c *gin.Context, err error) {
	// 记录详细错误信息
	//logger.Error("未处理的错误",
	//	zap.String("error", err.Error()),
	//	zap.String("method", c.Request.Method),
	//	zap.String("path", c.Request.URL.Path),
	//	zap.String("ip", c.ClientIP()),
	//)

}
