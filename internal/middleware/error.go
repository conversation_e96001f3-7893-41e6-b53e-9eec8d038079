package middleware

import (
	"errors"
	"gin/internal/dto/common"
	"net/http"

	"github.com/gin-gonic/gin"
	val "github.com/go-playground/validator/v10" // 重命名导入
)

// ErrorHandler 捕获 c.Errors 中的错误并发送统一格式的响应
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行后续的处理函数和中间件
		c.Next()

		// 如果没有错误，或者响应已经被发送，则直接返回
		if len(c.Errors) == 0 || c.Writer.Written() {
			return
		}

		// 只处理第一个错误，避免发送多个响应
		// 您也可以选择遍历所有错误并组合信息，但通常处理第一个就足够
		err := c.Errors[0].Err

		// 检查特定错误类型
		var vErrs val.ValidationErrors
		// var bizErr *response.BusinessError // 假设您定义了自定义业务错误类型

		switch {
		// 处理验证错误
		case errors.As(err, &vErrs):
			common.AbortValidationFailure(c, "参数验证失败", vErrs)

		// // 处理自定义业务错误 (示例)
		// case errors.As(err, &bizErr):
		// 	response.AbortWithError(c, bizErr.HTTPStatus, bizErr.Code, bizErr.Message, bizErr.Err) // 假设 BusinessError 包含这些字段

		// 处理其他所有未捕获的错误
		default:
			// 向客户端发送通用的服务器错误响应
			common.AbortWithError(c, http.StatusInternalServerError, http.StatusInternalServerError, "服务器内部错误", nil) // 不暴露原始错误信息给客户端
		}

		// 清除错误，防止 Gin 默认的日志记录器再次记录它们（如果使用了 Gin 的 Logger）
		// c.Errors = nil // 取决于您是否希望 Gin 的 Logger 仍然记录
	}
}
