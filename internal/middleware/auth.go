package middleware

import (
	"fmt"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/context"
	"gin/pkg/jwt"
	"gin/pkg/utils"
	"regexp"
	"strings"
	"time"

	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			// 尝试从Cookie中获取
			token, _ = c.<PERSON>("token")
			if token == "" {
				common.AbortUnauthorized(c, "token required")
				return
			}
		} else {
			// 移除 Bearer 前缀
			token = strings.Replace(token, "Bearer ", "", 1)
		}

		// 解析Token
		claims, err := jwt.ParseToken(token)
		if err != nil {
			common.AbortUnauthorized(c, err.Error())
			return
		}

		// 检查Token是否在黑名单中
		// redisClient := cache.GetRedisClient()
		// blacklistKey := TokenBlacklistPrefix + claims.TokenID
		// exists, _ := redisClient.Exists(c, blacklistKey)
		// if exists > 0 {
		// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "token已失效，请重新登录"})
		// 	c.Abort()
		// 	return
		// }

		// 检查Token是否在白名单中（可选，取决于你的安全策略）
		// whitelistKey := TokenWhitelistPrefix + claims.TokenID
		// exists, _ = redisClient.Exists(c, whitelistKey)
		// if exists == 0 {
		//     c.JSON(http.StatusUnauthorized, gin.H{"error": "token未授权，请重新登录"})
		//     c.Abort()
		//     return
		// }

		// 如果Token即将过期，自动刷新（可选功能）
		if time.Until(claims.ExpiresAt.Time) < 30*time.Minute {
			// newToken, newTokenID, _ := utils.GenerateToken(claims.UserInfo, 24*time.Hour)
			// 将新Token设置到响应头
			// c.Header("New-Token", newToken)
			// 将旧Token加入黑名单
			// redisClient.Set(c, blacklistKey, "1", time.Until(claims.ExpiresAt.Time))
			// 将新Token加入白名单
			// redisClient.Set(c, TokenWhitelistPrefix+newTokenID, "1", 24*time.Hour)
		}
		// 将自定义上下文存储在 gin.Context 中，供后续中间件和处理函数使用
		c.Set(context.ClaimsContextKey, claims)

		c.Next()
	}
}

// Authorize 权限验证中间件
func Authorize(enforcer *casbin.SyncedCachedEnforcer) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否已存在Claims
		claimsValue, exists := c.Get(context.ClaimsContextKey)
		if !exists {
			common.AbortUnauthorized(c, "未登录")
			return
		}

		claims, ok := claimsValue.(*jwt.TokenClaims)
		if !ok {
			common.AbortUnauthorized(c, "无效的用户信息")
			return
		}

		path := c.Request.URL.Path
		method := c.Request.Method

		// 将HTTP方法转换为操作类型，与Casbin策略保持一致
		action := utils.ConvertMethodToAction(method)

		// 将实际路径转换为路径模式以匹配权限数据
		normalizedPath := normalizePathForPermissionCheck(path)

		// 检查用户是否有权限访问该API
		hasPermission, err := enforcer.Enforce(claims.Manager, normalizedPath, action)
		if err != nil {
			common.AbortInternalServerError(c, fmt.Sprintf("权限检查失败: %v", err))
			return
		}

		if !hasPermission {
			common.AbortForbidden(c, "无权限访问")
			return
		}

		c.Next()
	}
}

// normalizePathForPermissionCheck 将实际请求路径规范化为权限检查用的路径模式
func normalizePathForPermissionCheck(path string) string {
	// 定义路径模式映射规则
	pathPatterns := []struct {
		pattern     *regexp.Regexp
		replacement string
	}{
		// 匹配 /api/system/manager/数字 -> /api/system/manager/:id
		{regexp.MustCompile(`^/adm/system/manager/\d+$`), "/adm/system/manager/:id"},
		// 匹配 /api/system/manager/数字/roles -> /api/system/manager/:id/roles
		{regexp.MustCompile(`^/api/system/manager/\d+/roles$`), "/adm/system/manager/:id/roles"},
		// 匹配 /api/system/role/数字 -> /api/system/role/:id
		{regexp.MustCompile(`^/adm/system/role/\d+$`), "/adm/system/role/:id"},
		// 匹配 /api/system/permission/数字 -> /api/system/permission/:id
		{regexp.MustCompile(`^/adm/system/permission/\d+$`), "/adm/system/permission/:id"},
		// 匹配 /api/monitor/loginLog/数字 -> /api/monitor/loginLog/:id
		{regexp.MustCompile(`^/adm/monitor/loginLog/\d+$`), "/adm/monitor/loginLog/:id"},
	}

	// 尝试匹配并转换路径
	for _, pattern := range pathPatterns {
		if pattern.pattern.MatchString(path) {
			return pattern.replacement
		}
	}

	// 如果没有匹配到特定模式，返回原路径
	return path
}
