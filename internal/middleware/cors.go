package middleware

import (
	"gin/internal/infrastructure/config"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// CorsConfig CORS配置
type CorsConfig struct {
	AllowOrigins     []string
	AllowMethods     []string
	AllowHeaders     []string
	ExposeHeaders    []string
	AllowCredentials bool
	MaxAge           int
}

// DefaultCorsConfig 默认CORS配置
func DefaultCorsConfig() CorsConfig {
	return CorsConfig{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders: []string{
			"Origin", "Content-Length", "Content-Type", "Authorization",
			"Accept", "X-Requested-With", "X-CSRF-Token",
		},
		ExposeHeaders:    []string{},
		AllowCredentials: false,
		MaxAge:           12 * 3600, // 12小时
	}
}

// SecureCorsConfig 安全的CORS配置
func SecureCorsConfig(allowedOrigins []string) CorsConfig {
	return CorsConfig{
		AllowOrigins: allowedOrigins,
		AllowMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders: []string{
			"Origin", "Content-Length", "Content-Type", "Authorization",
			"Accept", "X-Requested-With", "X-CSRF-Token",
		},
		ExposeHeaders:    []string{"X-Total-Count"},
		AllowCredentials: true,
		MaxAge:           1 * 3600, // 1小时
	}
}

// CorsMiddleware 跨域中间件
func CorsMiddleware() gin.HandlerFunc {
	return CorsWithConfig(DefaultCorsConfig())
}

// SecureCorsMiddleware 安全的跨域中间件
func SecureCorsMiddleware(allowedOrigins []string) gin.HandlerFunc {
	return CorsWithConfig(SecureCorsConfig(allowedOrigins))
}

// CorsWithConfig 基于配置的CORS中间件
func CorsWithConfig(config CorsConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 检查来源是否被允许
		if len(config.AllowOrigins) > 0 {
			allowed := false
			for _, allowedOrigin := range config.AllowOrigins {
				if allowedOrigin == "*" || allowedOrigin == origin {
					allowed = true
					break
				}
			}

			if allowed {
				if config.AllowOrigins[0] == "*" && !config.AllowCredentials {
					c.Header("Access-Control-Allow-Origin", "*")
				} else {
					c.Header("Access-Control-Allow-Origin", origin)
					c.Header("Vary", "Origin")
				}
			}
		}

		// 设置允许的方法
		if len(config.AllowMethods) > 0 {
			c.Header("Access-Control-Allow-Methods", strings.Join(config.AllowMethods, ", "))
		}

		// 设置允许的头部
		if len(config.AllowHeaders) > 0 {
			c.Header("Access-Control-Allow-Headers", strings.Join(config.AllowHeaders, ", "))
		}

		// 设置暴露的头部
		if len(config.ExposeHeaders) > 0 {
			c.Header("Access-Control-Expose-Headers", strings.Join(config.ExposeHeaders, ", "))
		}

		// 设置是否允许凭证
		if config.AllowCredentials {
			c.Header("Access-Control-Allow-Credentials", "true")
		}

		// 设置预检请求缓存时间
		if config.MaxAge > 0 {
			c.Header("Access-Control-Max-Age", string(rune(config.MaxAge)))
		}

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// CorsFromConfig 从配置文件加载CORS设置
func CorsFromConfig(conf *config.Config) gin.HandlerFunc {
	// 这里可以从配置文件读取CORS设置
	// 目前使用默认配置
	return CorsMiddleware()
}
