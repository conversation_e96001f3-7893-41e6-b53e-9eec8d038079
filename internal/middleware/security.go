package middleware

import (
	"gin/internal/dto/common"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
)

// SecurityHeaders 添加安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止点击劫持
		c.<PERSON>er("X-Frame-Options", "DENY")

		// 防止MIME类型嗅探
		c.<PERSON>("X-Content-Type-Options", "nosniff")

		// XSS防护
		c.Header("X-XSS-Protection", "1; mode=block")

		// HTTPS强制
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

		// 内容安全策略
		c.<PERSON><PERSON>("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'")

		// 引用策略
		c.<PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")

		// 权限策略
		c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

		c.Next()
	}
}

// SQLInjectionProtection SQL注入防护中间件
func SQLInjectionProtection() gin.HandlerFunc {
	// SQL注入模式
	sqlPatterns := []string{
		`(?i)(union.*select)`,
		`(?i)(select.*from)`,
		`(?i)(insert.*into)`,
		`(?i)(delete.*from)`,
		`(?i)(update.*set)`,
		`(?i)(drop.*table)`,
		`(?i)(alter.*table)`,
		`(?i)(create.*table)`,
		`(?i)(exec.*\()`,
		`(?i)(script.*>)`,
		`(?i)(<.*script)`,
		`(?i)(javascript:)`,
		`(?i)(vbscript:)`,
		`(?i)(onload.*=)`,
		`(?i)(onerror.*=)`,
		`(?i)(onclick.*=)`,
		`(\||;|--|#|/\*|\*/|xp_|sp_)`,
	}

	var compiledPatterns []*regexp.Regexp
	for _, pattern := range sqlPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			compiledPatterns = append(compiledPatterns, compiled)
		}
	}

	return func(c *gin.Context) {
		// 检查查询参数
		for _, values := range c.Request.URL.Query() {
			for _, value := range values {
				if containsSQLInjection(value, compiledPatterns) {
					//logger.Warn("检测到SQL注入尝试",
					//	zap.String("ip", c.ClientIP()),
					//	zap.String("path", c.Request.URL.Path),
					//	zap.String("param", key),
					//	zap.String("value", value),
					//)
					common.AbortBadRequest(c, "请求参数不合法", "检测到潜在的安全威胁")
					return
				}
			}
		}

		// 检查POST数据（如果是表单数据）
		if c.Request.Method == "POST" && c.ContentType() == "application/x-www-form-urlencoded" {
			if err := c.Request.ParseForm(); err == nil {
				for _, values := range c.Request.PostForm {
					for _, value := range values {
						if containsSQLInjection(value, compiledPatterns) {
							//logger.Warn("检测到SQL注入尝试",
							//	zap.String("ip", c.ClientIP()),
							//	zap.String("path", c.Request.URL.Path),
							//	zap.String("form_field", key),
							//	zap.String("value", value),
							//)
							common.AbortBadRequest(c, "请求数据不合法", "检测到潜在的安全威胁")
							return
						}
					}
				}
			}
		}

		c.Next()
	}
}

// containsSQLInjection 检查字符串是否包含SQL注入模式
func containsSQLInjection(input string, patterns []*regexp.Regexp) bool {
	input = strings.ToLower(strings.TrimSpace(input))
	if len(input) == 0 {
		return false
	}

	for _, pattern := range patterns {
		if pattern.MatchString(input) {
			return true
		}
	}
	return false
}

// RequestSizeLimit 请求大小限制中间件
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			//logger.Warn("请求体过大",
			//	zap.String("ip", c.ClientIP()),
			//	zap.String("path", c.Request.URL.Path),
			//	zap.Int64("content_length", c.Request.ContentLength),
			//	zap.Int64("max_size", maxSize),
			//)
			common.AbortBadRequest(c, "请求体过大", "请求数据超出限制")
			return
		}

		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, maxSize)
		c.Next()
	}
}

// IPWhitelist IP白名单中间件
func IPWhitelist(allowedIPs []string) gin.HandlerFunc {
	allowedIPMap := make(map[string]bool)
	for _, ip := range allowedIPs {
		allowedIPMap[ip] = true
	}

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		if len(allowedIPs) > 0 && !allowedIPMap[clientIP] {
			//logger.Warn("IP访问被拒绝",
			//	zap.String("ip", clientIP),
			//	zap.String("path", c.Request.URL.Path),
			//)
			common.AbortWithError(c, http.StatusForbidden, http.StatusForbidden, "访问被拒绝", nil)
			return
		}

		c.Next()
	}
}

// UserAgentCheck 用户代理检查中间件
func UserAgentCheck() gin.HandlerFunc {
	// 恶意User-Agent模式
	maliciousPatterns := []string{
		`(?i)(sqlmap)`,
		`(?i)(nikto)`,
		`(?i)(masscan)`,
		`(?i)(nmap)`,
		`(?i)(whatweb)`,
		`(?i)(dirb)`,
		`(?i)(dirbuster)`,
		`(?i)(gobuster)`,
		`(?i)(wfuzz)`,
		`(?i)(burp)`,
	}

	var compiledPatterns []*regexp.Regexp
	for _, pattern := range maliciousPatterns {
		if compiled, err := regexp.Compile(pattern); err == nil {
			compiledPatterns = append(compiledPatterns, compiled)
		}
	}

	return func(c *gin.Context) {
		userAgent := c.GetHeader("User-Agent")

		// 检查是否为空或过短
		if len(userAgent) < 10 {
			//logger.Warn("可疑的User-Agent",
			//	zap.String("ip", c.ClientIP()),
			//	zap.String("path", c.Request.URL.Path),
			//	zap.String("user_agent", userAgent),
			//)
		}

		// 检查恶意模式
		for _, pattern := range compiledPatterns {
			if pattern.MatchString(userAgent) {
				//logger.Warn("检测到恶意User-Agent",
				//	zap.String("ip", c.ClientIP()),
				//	zap.String("path", c.Request.URL.Path),
				//	zap.String("user_agent", userAgent),
				//)
				common.AbortWithError(c, http.StatusForbidden, http.StatusForbidden, "访问被拒绝", nil)
				return
			}
		}

		c.Next()
	}
}
