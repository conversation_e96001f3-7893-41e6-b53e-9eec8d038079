package service

import (
	"context"
	"gin/internal/models"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// UserSettingService 用户设置服务实现
type UserSettingService struct {
	userSettingRepo repo.UserSettingRepository
}

// NewUserSettingService 创建用户设置服务
func NewUserSettingService(repo repo.UserSettingRepository) interfaces.IUserSettingService {
	return &UserSettingService{
		userSettingRepo: repo,
	}
}

// GetByUserIDAndField 获取用户设置
func (s *UserSettingService) GetByUserIDAndField(ctx context.Context, userID uint, field string) (*models.UserSetting, error) {
	return s.userSettingRepo.FindByUserIDAndField(ctx, userID, field)
}

// SetByUserIDAndField 设置用户设置
func (s *UserSettingService) SetByUserIDAndField(ctx context.Context, userID uint, field string, value string, data string) error {
	// 获取现有设置
	setting, err := s.userSettingRepo.FindByUserIDAndField(ctx, userID, field)
	if err != nil {
		return err
	}

	// 创建或更新设置
	if setting == nil {
		setting = &models.UserSetting{
			UserID: userID,
			Field:  field,
			Value:  value,
			Data:   data,
		}
		return s.userSettingRepo.Create(ctx, setting)
	} else {
		setting.Value = value
		setting.Data = data
		return s.userSettingRepo.Update(ctx, setting)
	}
}

// DeleteByUserIDAndField 删除用户设置
func (s *UserSettingService) DeleteByUserIDAndField(ctx context.Context, userID uint, field string) error {
	return s.userSettingRepo.DeleteByUserIDAndField(ctx, userID, field)
}
