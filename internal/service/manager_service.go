package service

import (
	"context"
	"errors"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/cache"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"gin/pkg/jwt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"go.uber.org/zap"
)

// ManagerService 管理员用户服务实现
type ManagerService struct {
	logger          logger.Logger
	managerRepo     repo.ManagerRepository
	loginLogService interfaces.ILoginLogService
	enforcer        casbin.IEnforcer
}

// NewManagerService 创建管理员用户服务
func NewManagerService(logger logger.Logger, managerRepo repo.ManagerRepository, loginLogService interfaces.ILoginLogService, enforcer casbin.IEnforcer) interfaces.IManagerService {
	return &ManagerService{
		logger:          logger,
		managerRepo:     managerRepo,
		loginLogService: loginLogService,
		enforcer:        enforcer,
	}
}

// FindByID 根据ID获取管理员用户
func (s *ManagerService) FindByID(ctx context.Context, id uint) (*models.Manager, error) {
	return s.managerRepo.FindByID(ctx, id)
}

// FindByUsername 根据用户名获取管理员用户
func (s *ManagerService) FindByUsername(ctx context.Context, Username string) (*models.Manager, error) {
	return s.managerRepo.FindByUsername(ctx, Username)
}

// validateManagerExists 验证管理员是否存在
func (s *ManagerService) validateManagerExists(ctx context.Context, id uint) (*models.Manager, error) {
	manager, err := s.managerRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if manager == nil {
		return nil, errors.New("用户不存在")
	}
	return manager, nil
}

// Create 创建管理员
func (s *ManagerService) Create(ctx context.Context, manager *models.Manager, plainPassword string) error {
	// 验证用户名唯一性
	existing, err := s.managerRepo.FindByUsername(ctx, manager.Username)
	if err != nil {
		return err
	}
	if existing != nil && existing.ID != 0 {
		return errors.New("用户名已存在")
	}

	// 设置密码
	manager.PasswordHash = plainPassword

	// 设置默认值和时间
	if manager.Status == 0 {
		manager.Status = 10
	}
	if manager.Avatar == "" {
		manager.Avatar = "/static/avatar/default_avatar.png"
	}
	if manager.ExpiredAt.IsZero() {
		manager.ExpiredAt = time.Now().AddDate(1, 0, 0)
	}
	if manager.LastLoginTime.IsZero() {
		manager.LastLoginTime = time.Now()
	}
	if manager.LastPasswordChangeAt.IsZero() {
		manager.LastPasswordChangeAt = time.Now()
	}

	return s.managerRepo.Create(ctx, manager)
}

// Update 更新管理员
func (s *ManagerService) Update(ctx context.Context, id uint, req *admin.ManageUpdateRequest) error {
	// 验证管理员存在
	manager, err := s.managerRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if manager == nil {
		return errors.New("用户不存在")
	}

	if req.Nickname != nil {
		manager.Nickname = *req.Nickname
	}
	if req.Mobile != nil {
		manager.Mobile = *req.Mobile
	}
	if req.Email != nil {
		manager.Email = *req.Email
	}
	if req.Avatar != nil {
		manager.Avatar = *req.Avatar
	}
	if req.Gender != nil {
		manager.Gender = *req.Gender
	}
	if req.Status != nil {
		manager.Status = *req.Status
	}
	if req.ExpiredAt != nil {
		manager.ExpiredAt = *req.ExpiredAt
	}
	if req.LoginMode != nil {
		manager.LoginMode = *req.LoginMode
	}
	if req.MaxDevices != nil {
		manager.MaxDevices = *req.MaxDevices
	}
	if manager.LoginMode == 3 && manager.MaxDevices == 0 {
		manager.MaxDevices = 1
	}
	if manager.LoginMode == 1 {
		manager.MaxDevices = 1
	}
	if manager.LoginMode == 2 {
		manager.MaxDevices = 0
	}
	// 密码处理
	if req.Password != nil {
		if len(*req.Password) < 6 {
			return errors.New("密码长度不能小于6位")
		}
		if err = manager.GeneratePasswordHash(*req.Password); err != nil {
			return err
		}
		manager.LastPasswordChangeAt = time.Now()
	}

	return s.managerRepo.Update(ctx, manager)
}

// Delete 删除管理员用户
func (s *ManagerService) Delete(ctx context.Context, id uint) error {
	_, err := s.validateManagerExists(ctx, id)
	if err != nil {
		return err
	}
	return s.managerRepo.Delete(ctx, id)
}

// VerifyPassword 验证用户密码
func (s *ManagerService) VerifyPassword(ctx context.Context, Username, password string) (*models.Manager, error) {
	manager, err := s.managerRepo.FindByUsername(ctx, Username)
	if err != nil {
		return nil, err
	}
	if manager == nil {
		return nil, errors.New("用户不存在")
	}
	if !manager.CompareHashAndPassword(password) {
		return nil, errors.New("密码错误")
	}
	return manager, nil
}

// updateManagerPassword 更新管理员密码的通用方法
func (s *ManagerService) updateManagerPassword(ctx context.Context, manager *models.Manager, newPassword string) error {
	if err := manager.GeneratePasswordHash(newPassword); err != nil {
		return err
	}
	return s.managerRepo.Update(ctx, manager)
}

// ChangePassword 修改密码
func (s *ManagerService) ChangePassword(ctx context.Context, id uint, oldPassword, newPassword string) error {
	manager, err := s.managerRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if manager == nil {
		return errors.New("管理员不存在")
	}

	if !manager.CompareHashAndPassword(oldPassword) {
		return errors.New("旧密码错误")
	}

	if len(newPassword) < 6 {
		return errors.New("新密码长度不能小于6位")
	}
	return s.updateManagerPassword(ctx, manager, newPassword)
}

// ResetPassword 重置密码
func (s *ManagerService) ResetPassword(ctx context.Context, id uint, newPassword string) error {
	manager, err := s.managerRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if manager == nil {
		return errors.New("管理员不存在")
	}
	if len(newPassword) < 6 {
		return errors.New("新密码长度不能小于6位")
	}
	return s.updateManagerPassword(ctx, manager, newPassword)
}

// Login 用户登录
func (s *ManagerService) Login(ctx *gin.Context, Username, password string, cache *cache.RedisClient) (*models.Manager, string, error) {

	// 检查登录失败次数
	key := "login:fail:" + Username
	failCount, _ := cache.Get(ctx, key)
	if count, _ := strconv.Atoi(failCount); count >= 5 {
		// 记录登录失败日志
		_ = s.loginLogService.CreateManagerLoginLog(ctx, 0, Username, ctx.ClientIP(), ctx.Request.UserAgent(), -1, "登录失败次数过多，请稍后再试")
		return nil, "", errors.New("登录失败次数过多，请稍后再试")
	}

	// 验证用户名和密码
	manager, err := s.VerifyPassword(ctx, Username, password)
	if err != nil {
		// 记录失败次数
		count := 1
		if failCount != "" {
			count, _ = strconv.Atoi(failCount)
			count++
		}
		_ = cache.Set(ctx, key, count, time.Hour) // 1小时过期

		// 记录登录失败日志
		_ = s.loginLogService.CreateManagerLoginLog(ctx, 0, Username, ctx.ClientIP(), ctx.Request.UserAgent(), -1, err.Error())

		return nil, "", err
	}

	// 检查用户状态
	if manager.Status != models.ManagerStatusEnabled {
		// 记录登录失败日志
		_ = s.loginLogService.CreateManagerLoginLog(ctx, manager.ID, Username, ctx.ClientIP(), ctx.Request.UserAgent(), models.LoginStatusFailed, "用户已被禁用")
		return nil, "", errors.New("用户已被禁用")
	}

	// 检查用户是否过期
	if time.Now().After(manager.ExpiredAt) {
		// 记录登录失败日志
		_ = s.loginLogService.CreateManagerLoginLog(ctx, manager.ID, Username, ctx.ClientIP(), ctx.Request.UserAgent(), models.LoginStatusFailed, "用户账号已过期")
		return nil, "", errors.New("用户账号已过期")
	}

	// 生成Token
	token, tokenID, err := jwt.GenerateManagerToken(manager.ID, manager.Username, 24*time.Hour)
	if err != nil {
		s.logger.Error("生成Token失败", zap.String("Username", Username), zap.Error(err))
		// 记录登录失败日志
		_ = s.loginLogService.CreateManagerLoginLog(ctx, manager.ID, Username, ctx.ClientIP(), ctx.Request.UserAgent(), models.LoginStatusFailed, "生成Token失败")
		return nil, "", errors.New("生成Token失败")
	}

	// 将Token加入白名单
	_ = cache.Set(ctx, jwt.TokenWhitelistPrefix+tokenID, "1", 24*time.Hour)

	// 登录成功，清除失败记录
	_ = cache.Del(ctx, key)

	// 更新最后登录时间和IP
	manager.LastLoginTime = time.Now()
	manager.LastLoginIP = ctx.ClientIP()
	_ = s.managerRepo.Update(ctx, manager)

	// 记录登录成功日志
	_ = s.loginLogService.CreateManagerLoginLog(ctx, manager.ID, manager.Username, ctx.ClientIP(), ctx.Request.UserAgent(), models.LoginStatusSuccess, "登录成功")

	return manager, token, nil
}

// Logout 用户登出
func (s *ManagerService) Logout(ctx context.Context, tokenID string, userID uint, Username string, ip string, userAgent string, cache *cache.RedisClient) error {
	// 从白名单中删除Token
	err := cache.Del(nil, jwt.TokenWhitelistPrefix+tokenID)
	if err != nil {
		s.logger.Error("从白名单中删除Token失败", zap.String("tokenID", tokenID), zap.Error(err))
		return err
	}

	// 记录登出日志
	_ = s.loginLogService.CreateManagerLoginLog(ctx, userID, Username, ip, userAgent, 10, "用户登出")

	return nil
}

// List 分页查询管理员
func (s *ManagerService) List(ctx context.Context, req *admin.ManageListRequest) ([]*models.Manager, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddLikeCondition("username", req.Username).
		AddLikeCondition("nickname", req.Nickname).
		AddCondition("mobile", req.Mobile).
		AddCondition("email", req.Email).
		AddCondition("gender", req.Gender).
		AddCondition("status", req.Status).
		AddConditionWithOperator("created_at", query.OpGte, req.StartTime).
		AddConditionWithOperator("created_at", query.OpLte, req.EndTime).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.managerRepo.DB())

	result, err := s.managerRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	return result.Data, result.Total, nil
}

// AssignRoles 为用户分配角色
func (s *ManagerService) AssignRoles(_ context.Context, userID uint, roleCodes []string) error {
	userIDStr := strconv.FormatUint(uint64(userID), 10)
	for _, roleCode := range roleCodes {
		if hasRole, _ := s.enforcer.HasRoleForUser(userIDStr, roleCode); !hasRole {
			if _, err := s.enforcer.AddRoleForUser(userIDStr, roleCode); err != nil {
				return err
			}
		}
	}
	return nil
}

// RemoveRoles 移除用户角色
func (s *ManagerService) RemoveRoles(_ context.Context, userID uint, roleCodes []string) error {
	userIDStr := strconv.FormatUint(uint64(userID), 10)
	for _, roleCode := range roleCodes {
		if _, err := s.enforcer.DeleteRoleForUser(userIDStr, roleCode); err != nil {
			return err
		}
	}
	return nil
}

// GetUserRoles 获取用户角色
func (s *ManagerService) GetUserRoles(_ context.Context, userID uint) ([]string, error) {
	return s.enforcer.GetRolesForUser(strconv.FormatUint(uint64(userID), 10))
}

// ReplaceUserRoles 替换用户的所有角色
func (s *ManagerService) ReplaceUserRoles(_ context.Context, userID uint, roleCodes []string) error {
	userIDStr := strconv.FormatUint(uint64(userID), 10)

	// 获取当前角色并移除
	if currentRoles, err := s.enforcer.GetRolesForUser(userIDStr); err == nil {
		for _, role := range currentRoles {
			_, _ = s.enforcer.DeleteRoleForUser(userIDStr, role)
		}
	}

	// 分配新角色
	for _, roleCode := range roleCodes {
		if _, err := s.enforcer.AddRoleForUser(userIDStr, roleCode); err != nil {
			return err
		}
	}
	return nil
}
