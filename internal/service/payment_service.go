package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"github.com/goccy/go-json"
)

// PaymentService 支付服务实现
type PaymentService struct {
	paymentRepo     repo.PaymentRepository
	translationRepo repo.TranslationRepository
}

// NewPaymentService 创建支付服务
func NewPaymentService(repo repo.PaymentRepository, translationRepo repo.TranslationRepository) interfaces.IPaymentService {
	return &PaymentService{
		paymentRepo:     repo,
		translationRepo: translationRepo,
	}
}

// GetByID 根据ID获取支付订单
func (s *PaymentService) GetByID(ctx context.Context, id uint) (*models.Payment, error) {
	return s.paymentRepo.FindByID(ctx, id)
}

// Create 创建支付订单
func (s *PaymentService) Create(ctx context.Context, req *admin.PaymentCreateReq) (*models.Payment, error) {
	payment := &models.Payment{
		Icon:    req.Icon,
		Name:    req.Name,
		AssetID: req.AssetID,
		Mode:    req.Mode,
	}
	err := s.paymentRepo.Create(ctx, payment)
	if err != nil {
		return nil, err
	}
	return payment, nil
}

// Update 更新支付订单
func (s *PaymentService) Update(ctx context.Context, id uint, req *admin.PaymentUpdateReq) error {
	payment, err := s.paymentRepo.FindByID(ctx, id)
	if err != nil || payment == nil {
		return err
	}

	if req.Icon != nil {
		payment.Icon = *req.Icon
	}
	if req.Name != nil {
		payment.Name = *req.Name
	}
	if req.SubtitleField != nil {
		payment.SubtitleField = *req.SubtitleField
	}
	if req.DescriptionField != nil {
		payment.DescriptionField = *req.DescriptionField
	}
	if req.MaxAmount != nil {
		payment.MaxAmount = *req.MaxAmount
	}
	if req.MinAmount != nil {
		payment.MinAmount = *req.MinAmount
	}
	if req.StartTime != nil {
		payment.StartAt = *req.StartTime
	}
	if req.EndTime != nil {
		payment.EndAt = *req.EndTime
	}
	if req.FixedFee != nil {
		payment.FixedFee = *req.FixedFee
	}
	if req.RateFee != nil {
		payment.RateFee = *req.RateFee
	}
	if req.Sort != nil {
		payment.Sort = *req.Sort
	}
	if req.IsRedirectCS != nil {
		payment.IsRedirectCS = *req.IsRedirectCS
	}
	if req.IsProof != nil {
		payment.IsProof = *req.IsProof
	}
	if req.Status != nil {
		payment.Status = *req.Status
	}
	if req.Data != nil {
		// 处理Data字段，可能需要序列化为JSON字符串
		data, ok := req.Data.(string)
		if ok {
			payment.Data = data
		}
	}

	return s.paymentRepo.Update(ctx, payment)
}

// List 获取支付订单列表
func (s *PaymentService) List(ctx context.Context, req *admin.PaymentListReq) ([]*models.Payment, int64, error) {

	// 查询数据
	result, err := s.paymentRepo.FindWithPagination(ctx, nil)
	if err != nil {
		return nil, 0, err
	}

	return result.Data, result.Total, nil
}

// Delete 删除支付订单
func (s *PaymentService) Delete(ctx context.Context, id uint) error {
	return s.paymentRepo.Delete(ctx, id)
}

// BatchDelete 批量删除支付订单
func (s *PaymentService) BatchDelete(ctx context.Context, req *models.Payment) error {
	// 这里假设req中包含了要批量删除的ID列表
	// 实际实现可能需要根据项目的具体需求调整
	return s.paymentRepo.Delete(ctx, req.ID)
}

// FindListByMode 根据模式查询支付
func (s *PaymentService) FindListByMode(ctx context.Context, locale string, mode int8) (*web.WalletPaymentsRes, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("mode", mode)
	queryBuilder.AddCondition("status", models.PaymentStatusEnabled)
	queryBuilder.AddSort("sort", "ASC")

	// 查询支付列表
	payments, err := s.paymentRepo.FindWithQuery(ctx, queryBuilder)
	if err != nil {
		return nil, err
	}

	data := &web.WalletPaymentsRes{
		Payments: make([]*web.WalletPayment, 0),
	}

	for _, payment := range payments {
		subtitle, _ := s.translationRepo.FindByKeyAndLang(ctx, payment.SubtitleField, locale)
		payment.Subtitle = subtitle.Value

		dataItem := &web.WalletPayment{
			ID:           payment.ID,
			AssetID:      payment.AssetID,
			Name:         payment.Name,
			Subtitle:     payment.Subtitle,
			Icon:         payment.Icon,
			Type:         payment.Type,
			MinAmount:    payment.MinAmount,
			MaxAmount:    payment.MaxAmount,
			FixedFee:     payment.FixedFee,
			RateFee:      payment.RateFee,
			IsProof:      payment.IsProof,
			IsRedirectCS: payment.IsRedirectCS,
		}

		if payment.Mode == models.PaymentModeWithdraw {
			//dataItem.Data = make([]*views.InputSelectOption, 0)
			//json.Unmarshal([]byte(payment.Data), &dataItem.Data)
		} else {
			switch payment.Type {
			case models.PaymentTypeBankCard:
				dataItem.Data = &models.PaymentDataBankCard{}
				err = json.Unmarshal([]byte(payment.Data), dataItem.Data)
				if err != nil {
					return nil, err
				}
			case models.PaymentTypeCrypto:
				dataItem.Data = &models.PaymentDataCrypto{}
				err = json.Unmarshal([]byte(payment.Data), dataItem.Data)
				if err != nil {
					return nil, err
				}
			default:
				dataItem.Data = payment.Data
			}
		}
		data.Payments = append(data.Payments, dataItem)
	}
	return data, nil
}
