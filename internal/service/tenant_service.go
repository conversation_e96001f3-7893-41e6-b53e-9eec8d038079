package service

import (
	"context"
	"errors"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository"
	"gin/internal/service/interfaces"
	"time"

	"go.uber.org/zap"
)

// TenantService 租户服务实现
type TenantService struct {
	tenantRepo repository.TenantRepo
}

// NewTenantService 创建租户服务
func NewTenantService(tenantRepo repository.TenantRepo) interfaces.ITenantService {
	return &TenantService{
		tenantRepo: tenantRepo,
	}
}

// GetTenantByID 根据ID获取租户
func (s *TenantService) GetTenantByID(ctx context.Context, id uint64) (*models.Tenant, error) {
	tenant := &models.Tenant{}
	err := s.tenantRepo.WithContext(ctx).FindByID(id, tenant)
	if err != nil {
		logger.Error("根据ID获取租户失败", zap.Uint64("id", id), zap.Error(err))
		return nil, err
	}
	return tenant, nil
}

// GetTenantByDomain 根据域名获取租户
func (s *TenantService) GetTenantByDomain(ctx context.Context, domain string) (*models.Tenant, error) {
	tenant, err := s.tenantRepo.FindByDomain(ctx, domain)
	if err != nil {
		logger.Error("根据域名获取租户失败", zap.String("domain", domain), zap.Error(err))
		return nil, err
	}
	return tenant, nil
}

// GetTenantByCode 根据编码获取租户
func (s *TenantService) GetTenantByCode(ctx context.Context, code string) (*models.Tenant, error) {
	tenant, err := s.tenantRepo.FindByCode(ctx, code)
	if err != nil {
		logger.Error("根据编码获取租户失败", zap.String("code", code), zap.Error(err))
		return nil, err
	}
	return tenant, nil
}

// CreateTenant 创建租户
func (s *TenantService) CreateTenant(ctx context.Context, tenant *models.Tenant) error {
	// 验证租户数据
	if err := tenant.Validate(); err != nil {
		return err
	}

	// 检查域名是否已存在
	existingTenant, err := s.tenantRepo.FindByDomain(ctx, tenant.Domain)
	if err != nil {
		logger.Error("检查域名是否存在失败", zap.String("domain", tenant.Domain), zap.Error(err))
		return err
	}
	if existingTenant != nil {
		return errors.New("租户域名已存在")
	}

	// 检查编码是否已存在
	if tenant.Code != "" {
		existingTenant, err = s.tenantRepo.FindByCode(ctx, tenant.Code)
		if err != nil {
			logger.Error("检查编码是否存在失败", zap.String("code", tenant.Code), zap.Error(err))
			return err
		}
		if existingTenant != nil {
			return errors.New("租户编码已存在")
		}
	}

	// 设置默认值
	if tenant.MaxUsers <= 0 {
		tenant.MaxUsers = 10 // 默认最大用户数
	}
	if tenant.ExpiredAt.IsZero() {
		tenant.ExpiredAt = time.Now().AddDate(1, 0, 0) // 默认一年后过期
	}

	// 设置创建和更新时间
	now := time.Now()
	tenant.CreatedAt = now
	tenant.UpdatedAt = now

	// 创建租户
	err = s.tenantRepo.WithContext(ctx).Create(tenant)
	if err != nil {
		logger.Error("创建租户失败", zap.String("name", tenant.Name), zap.Error(err))
		return err
	}

	return nil
}

// UpdateTenant 更新租户
func (s *TenantService) UpdateTenant(ctx context.Context, tenant *models.Tenant) error {
	// 检查租户是否存在
	existingTenant := &models.Tenant{}
	err := s.tenantRepo.WithContext(ctx).FindByID(tenant.ID, existingTenant)
	if err != nil {
		logger.Error("检查租户是否存在失败", zap.Uint64("id", tenant.ID), zap.Error(err))
		return err
	}
	if existingTenant == nil {
		return errors.New("租户不存在")
	}

	// 验证租户数据
	if err := tenant.Validate(); err != nil {
		return err
	}

	// 检查域名是否已被其他租户使用
	if tenant.Domain != existingTenant.Domain {
		otherTenant, err := s.tenantRepo.FindByDomain(ctx, tenant.Domain)
		if err != nil {
			logger.Error("检查域名是否存在失败", zap.String("domain", tenant.Domain), zap.Error(err))
			return err
		}
		if otherTenant != nil && otherTenant.ID != tenant.ID {
			return errors.New("域名已被其他租户使用")
		}
	}

	// 检查编码是否已被其他租户使用
	if tenant.Code != "" && tenant.Code != existingTenant.Code {
		otherTenant, err := s.tenantRepo.FindByCode(ctx, tenant.Code)
		if err != nil {
			logger.Error("检查编码是否存在失败", zap.String("code", tenant.Code), zap.Error(err))
			return err
		}
		if otherTenant != nil && otherTenant.ID != tenant.ID {
			return errors.New("编码已被其他租户使用")
		}
	}

	// 更新时间
	tenant.UpdatedAt = time.Now()

	// 更新租户
	err = s.tenantRepo.WithContext(ctx).Update(tenant)
	if err != nil {
		logger.Error("更新租户失败", zap.Uint64("id", tenant.ID), zap.Error(err))
		return err
	}

	return nil
}

// DeleteTenant 删除租户
func (s *TenantService) DeleteTenant(ctx context.Context, id uint64) error {
	// 检查租户是否存在
	existingTenant := &models.Tenant{}
	err := s.tenantRepo.WithContext(ctx).FindByID(id, existingTenant)
	if err != nil {
		logger.Error("检查租户是否存在失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}
	if existingTenant == nil {
		return errors.New("租户不存在")
	}

	// 删除租户
	err = s.tenantRepo.WithContext(ctx).Delete(id)
	if err != nil {
		logger.Error("删除租户失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}

	return nil
}

// ActivateTenant 激活租户
func (s *TenantService) ActivateTenant(ctx context.Context, id uint64) error {
	err := s.tenantRepo.ActivateTenant(ctx, id)
	if err != nil {
		logger.Error("激活租户失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}
	return nil
}

// DeactivateTenant 停用租户
func (s *TenantService) DeactivateTenant(ctx context.Context, id uint64) error {
	err := s.tenantRepo.DeactivateTenant(ctx, id)
	if err != nil {
		logger.Error("停用租户失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}
	return nil
}

// ExtendExpiration 延长租户过期时间
func (s *TenantService) ExtendExpiration(ctx context.Context, id uint64, months int) error {
	if months <= 0 {
		return errors.New("延长月数必须大于0")
	}

	err := s.tenantRepo.ExtendExpiration(ctx, id, months)
	if err != nil {
		logger.Error("延长租户过期时间失败", zap.Uint64("id", id), zap.Int("months", months), zap.Error(err))
		return err
	}
	return nil
}

// SetMaxUsers 设置租户最大用户数
func (s *TenantService) SetMaxUsers(ctx context.Context, id uint64, maxUsers int) error {
	if maxUsers <= 0 {
		return errors.New("最大用户数必须大于0")
	}

	err := s.tenantRepo.SetMaxUsers(ctx, id, maxUsers)
	if err != nil {
		logger.Error("设置租户最大用户数失败", zap.Uint64("id", id), zap.Int("maxUsers", maxUsers), zap.Error(err))
		return err
	}
	return nil
}

// GetActiveTenants 获取所有激活的租户
func (s *TenantService) GetActiveTenants(ctx context.Context) ([]*models.Tenant, error) {
	tenants, err := s.tenantRepo.FindActive(ctx)
	if err != nil {
		logger.Error("获取激活租户失败", zap.Error(err))
		return nil, err
	}
	return tenants, nil
}

// GetInactiveTenants 获取所有未激活的租户
func (s *TenantService) GetInactiveTenants(ctx context.Context) ([]*models.Tenant, error) {
	tenants, err := s.tenantRepo.FindInactive(ctx)
	if err != nil {
		logger.Error("获取未激活租户失败", zap.Error(err))
		return nil, err
	}
	return tenants, nil
}

// GetExpiredTenants 获取所有已过期的租户
func (s *TenantService) GetExpiredTenants(ctx context.Context) ([]*models.Tenant, error) {
	tenants, err := s.tenantRepo.FindExpired(ctx)
	if err != nil {
		logger.Error("获取已过期租户失败", zap.Error(err))
		return nil, err
	}
	return tenants, nil
}

// GetExpiringTenants 获取即将过期的租户
func (s *TenantService) GetExpiringTenants(ctx context.Context, days int) ([]*models.Tenant, error) {
	if days <= 0 {
		return nil, errors.New("天数必须大于0")
	}

	tenants, err := s.tenantRepo.FindExpiring(ctx, days)
	if err != nil {
		logger.Error("获取即将过期租户失败", zap.Int("days", days), zap.Error(err))
		return nil, err
	}
	return tenants, nil
}

// CountActiveTenants 统计激活租户数
func (s *TenantService) CountActiveTenants(ctx context.Context) (int64, error) {
	count, err := s.tenantRepo.CountActive(ctx)
	if err != nil {
		logger.Error("统计激活租户数失败", zap.Error(err))
		return 0, err
	}
	return count, nil
}

// SearchTenants 搜索租户
func (s *TenantService) SearchTenants(ctx context.Context, keyword string, isActive *bool, page, pageSize int) ([]*models.Tenant, int64, error) {
	tenants, total, err := s.tenantRepo.Search(ctx, keyword, isActive, page, pageSize)
	if err != nil {
		logger.Error("搜索租户失败", zap.String("keyword", keyword), zap.Error(err))
		return nil, 0, err
	}
	return tenants, total, nil
}

// ListTenant 分页查询租户
func (s *TenantService) ListTenant(ctx context.Context, pagination *common.Pagination, params map[string]interface{}) (*common.PageResult, error) {
	var tenants []*models.Tenant
	result, err := s.tenantRepo.WithContext(ctx).FindWithPagination(pagination, params, &tenants)
	if err != nil {
		return nil, err
	}

	return result, nil
}
