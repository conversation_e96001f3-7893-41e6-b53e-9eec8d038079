package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// ArticleService 文章服务实现
type ArticleService struct {
	articleRepo repo.ArticleRepository
	langRepo    repo.LanguageRepository
	translation interfaces.ITranslationService
}

// NewArticleService 创建文章服务
func NewArticleService(articleRepo repo.ArticleRepository, langRepo repo.LanguageRepository, translation interfaces.ITranslationService) interfaces.IArticleService {
	return &ArticleService{
		articleRepo: articleRepo,
		langRepo:    langRepo,
		translation: translation,
	}
}

// Create 创建文章
func (s *ArticleService) Create(ctx context.Context, req *admin.ArticleCreateRequest) error {
	article := &models.Article{
		Icon:    req.Icon,
		Title:   req.Title,
		Content: req.Content,
		Route:   req.Route,
		Sort:    req.Sort,
		Views:   req.Views,
		Type:    req.Type,
		IsTop:   req.IsTop,
		IsHot:   req.IsHot,
		Status:  req.Status,
	}

	return s.articleRepo.Create(ctx, article)
}

// Update 更新文章
func (s *ArticleService) Update(ctx context.Context, id uint, req *admin.ArticleUpdateRequest) error {
	article, err := s.articleRepo.FindByID(ctx, id)
	if err != nil || article == nil {
		return err
	}

	if req.Icon != nil {
		article.Icon = *req.Icon
	}
	if req.Title != nil {
		article.Title = *req.Title
	}
	if req.Content != nil {
		article.Content = *req.Content
	}
	if req.Route != nil {
		article.Route = *req.Route
	}
	if req.Sort != nil {
		article.Sort = *req.Sort
	}
	if req.Views != nil {
		article.Views = *req.Views
	}
	if req.Type != nil {
		article.Type = *req.Type
	}
	if req.IsTop != nil {
		article.IsTop = *req.IsTop
	}
	if req.IsHot != nil {
		article.IsHot = *req.IsHot
	}
	if req.Status != nil {
		article.Status = *req.Status
	}

	return s.articleRepo.Update(ctx, article)
}

// Delete 删除文章
func (s *ArticleService) Delete(ctx context.Context, id uint) error {
	return s.articleRepo.Delete(ctx, id)
}

// GetByID 根据ID获取文章
func (s *ArticleService) GetByID(ctx context.Context, id uint) (*models.Article, error) {
	return s.articleRepo.FindByID(ctx, id)
}

// List 获取文章列表
func (s *ArticleService) List(ctx context.Context, req *admin.ArticleListRequest) ([]*models.Article, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("title", req.Title).
		AddCondition("route", req.Route).
		AddCondition("type", req.Type).
		AddCondition("is_top", req.IsTop).
		AddCondition("is_hot", req.IsHot).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.articleRepo.DB())

	result, err := s.articleRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// ListWithLocale 获取文章列表(带语言)
func (s *ArticleService) ListWithLocale(ctx context.Context, locale string, req *web.ArticleListRequest) ([]*web.ArticleRes, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.articleRepo.DB())

	result, err := s.articleRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	articleResponses := make([]*web.ArticleRes, 0, len(result.Data))

	for _, article := range result.Data {
		articleResponses = append(articleResponses, &web.ArticleRes{
			ID:        article.ID,
			Icon:      article.Icon,
			Title:     article.Title,
			Content:   article.Content,
			Route:     article.Route,
			Sort:      article.Sort,
			Views:     article.Views,
			Type:      article.Type,
			IsTop:     article.IsTop,
			IsHot:     article.IsHot,
			Status:    article.Status,
			CreatedAt: article.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return articleResponses, result.Total, nil
}

// Details 获取文章详情
func (s *ArticleService) Details(ctx context.Context, locale string, id uint) (*web.ArticleRes, error) {
	article, err := s.articleRepo.FindByID(ctx, id)
	if err != nil || article == nil {
		return nil, err
	}

	return &web.ArticleRes{
		ID:        article.ID,
		Icon:      article.Icon,
		Title:     article.Title,
		Content:   article.Content,
		Route:     article.Route,
		Sort:      article.Sort,
		Views:     article.Views,
		Type:      article.Type,
		IsTop:     article.IsTop,
		IsHot:     article.IsHot,
		Status:    article.Status,
		CreatedAt: article.CreatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// BatchDelete 批量删除文章
func (s *ArticleService) BatchDelete(ctx context.Context, req *admin.ArticleBatchDeleteRequest) error {
	return s.articleRepo.BulkDelete(ctx, req.IDs)
}

// GetNotices 获取公告
func (s *ArticleService) GetNotices(ctx context.Context, locale string) []*web.ArticleNotContentRes {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("type", models.ArticleTypeNotice).
		AddCondition("status", models.ArticleStatusEnabled).
		Build(s.articleRepo.DB())

	articles, err := s.articleRepo.FindWithQuery(ctx, queryBuilder)
	if err != nil {
		return nil
	}

	notices := make([]*web.ArticleNotContentRes, 0, len(articles))
	for _, article := range articles {
		titleTranslate, _ := s.translation.FindByKeyAndLang(ctx, article.TitleField, locale)
		notices = append(notices, &web.ArticleNotContentRes{
			ID:        article.ID,
			Icon:      article.Icon,
			Route:     article.Route,
			Title:     titleTranslate.Value,
			CreatedAt: article.CreatedAt,
		})
	}
	return notices
}

// GetHelpers 获取帮助中心
func (s *ArticleService) GetHelpers(ctx context.Context, locale string) []*web.ArticleRes {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("type", models.ArticleTypeHelp).
		AddCondition("status", models.ArticleStatusEnabled).
		Build(s.articleRepo.DB())

	articles, err := s.articleRepo.FindWithQuery(ctx, queryBuilder)
	if err != nil {
		return nil
	}

	helopers := make([]*web.ArticleRes, 0, len(articles))
	for _, article := range articles {
		titleTranslate, _ := s.translation.FindByKeyAndLang(ctx, article.TitleField, locale)
		contentTranslate, _ := s.translation.FindByKeyAndLang(ctx, article.ContentField, locale)
		helopers = append(helopers, &web.ArticleRes{
			ID:        article.ID,
			Icon:      article.Icon,
			Sort:      article.Sort,
			Views:     article.Views,
			Type:      article.Type,
			IsTop:     article.IsTop,
			IsHot:     article.IsHot,
			Status:    article.Status,
			Route:     article.Route,
			Title:     titleTranslate.Value,
			Content:   contentTranslate.Value,
			CreatedAt: article.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return helopers
}
