package service

import (
	"context"
	"errors"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"gin/pkg/utils"
	"time"
)

// UserTransferService 用户转账服务实现
type UserTransferService struct {
	logger           logger.Logger
	userTransferRepo repo.UserTransferRepository
	managerRepo      repo.ManagerRepository
	userRepo         repo.UserRepository
	translationRepo  repo.TranslationRepository
	settingRepo      repo.SettingRepository
}

// NewUserTransferService 创建一个新的UserTransferService实例
func NewUserTransferService(logger logger.Logger, userTransferRepo repo.UserTransferRepository, managerRepo repo.ManagerRepository, userRepo repo.UserRepository, translationRepo repo.TranslationRepository, settingRepo repo.SettingRepository) interfaces.IUserTransferService {
	return &UserTransferService{
		logger:           logger,
		userTransferRepo: userTransferRepo,
		managerRepo:      managerRepo,
		userRepo:         userRepo,
		translationRepo:  translationRepo,
		settingRepo:      settingRepo,
	}
}

// List 根据查询条件获取用户转账列表, 并返回总数
func (s *UserTransferService) List(ctx context.Context, req *admin.UserTransferListReq) ([]*models.UserTransfer, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("manager_id", req.ManagerID)
	queryBuilder.AddCondition("send_user_id", req.SendUserID)
	queryBuilder.AddCondition("receive_user_id", req.ReceiveUserID)
	queryBuilder.AddCondition("asset_id", req.AssetID)
	queryBuilder.AddConditionWithOperator("created_at", query.OpGte, req.StartTime)
	queryBuilder.AddConditionWithOperator("created_at", query.OpLte, req.EndTime)
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userTransferRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// UserList 根据查询条件获取用户转账列表, 并返回总数
func (s *UserTransferService) UserList(ctx context.Context, locale string, userID uint, req *web.UserTransferListReq) ([]*web.UserTransferListRes, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("send_user_id", userID)
	queryBuilder.AddCondition("type", models.UserTransferTypeInternal)

	// 添加分页和排序
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userTransferRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	results := make([]*web.UserTransferListRes, 0)
	for _, userTransfer := range result.Data {
		results = append(results, &web.UserTransferListRes{
			ID:            userTransfer.ID,
			ReceiveUserID: userTransfer.ReceiveUserID,
			AssetID:       userTransfer.AssetID,
			Receiver: web.UserSmallInfo{
				ID:       userTransfer.ReceiveUser.ID,
				Username: userTransfer.ReceiveUser.Username,
				Nickname: userTransfer.ReceiveUser.Nickname,
				Avatar:   userTransfer.ReceiveUser.Avatar,
			},
			Asset: web.AssetRes{
				ID:       userTransfer.Asset.ID,
				Name:     userTransfer.Asset.Name,
				Subtitle: userTransfer.Asset.SubtitleField,
				Symbol:   userTransfer.Asset.Symbol,
				Icon:     userTransfer.Asset.Icon,
				Rate:     userTransfer.Asset.Rate,
			},
			Amount:       userTransfer.Amount,
			FixedFee:     userTransfer.FixedFee,
			RateFee:      userTransfer.RateFee,
			ActualAmount: userTransfer.ActualAmount,
			Status:       userTransfer.Status,
			Reason:       userTransfer.Reason,
			CreatedAt:    userTransfer.CreatedAt,
			UpdatedAt:    userTransfer.UpdatedAt,
		})
	}

	return results, result.Total, nil
}

// Create 创建用户转账
func (s *UserTransferService) Create(ctx context.Context, locale string, req *admin.UserTransferCreateReq) (*models.UserTransfer, error) {
	if req.SendUserID == req.ReceiveUserID {
		return nil, errors.New("发送用户和接收用户不能相同")
	}

	// 获取发送用户
	sendUser, err := s.userRepo.FindByID(ctx, req.SendUserID)
	if err != nil {
		return nil, err
	}

	// 获取接收用户
	receiveUser, err := s.userRepo.FindByID(ctx, req.ReceiveUserID)
	if err != nil {
		return nil, err
	}

	userTransfer := &models.UserTransfer{
		SendUserID:    sendUser.ID,
		ReceiveUserID: receiveUser.ID,
		AssetID:       req.AssetID,
		Amount:        req.Amount,
		ActualAmount:  req.Amount,
		Status:        models.UserTransferStatusCompleted,
		Type:          models.UserTransferTypeInternal,
	}
	//return s.userTransferRepo.Create(ctx, locale, sendUser, receiveUser, userTransfer)
	return userTransfer, nil
}

// UserCreate 用户创建转账
func (s *UserTransferService) UserCreate(ctx context.Context, locale string, userID uint, req *web.UserTransferCreateReq) error {
	// 获取发送用户
	sendUser, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	// 获取支付密码
	basicTemplate := constant.SettingBasicTemplate{}
	if err = s.settingRepo.GetFieldValues(models.SettingTemplateBasic, &basicTemplate); err != nil {
		return err
	}
	if utils.HasStringSlice(basicTemplate.Checkbox, constant.SettingBasicTemplateCheckboxTransferPassword) {
		if !sendUser.CompareHashAndSecurityKey(req.SecurityKey) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSecurityKeyIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
	}

	var receiveUser *models.User
	switch req.Type {
	case web.UserTransferTypeUserID:
		receiveUser, err = s.userRepo.FindByID(ctx, uint(req.Receiver.(float64)))
	case web.UserTransferTypeUsername:
		receiveUser, err = s.userRepo.FindByUsername(ctx, req.Receiver.(string))
	case web.UserTransferTypeEmail:
		receiveUser, err = s.userRepo.FindByEmail(ctx, req.Receiver.(string))
	case web.UserTransferTypeTelephone:
		receiveUser, err = s.userRepo.FindByTelephone(ctx, req.Receiver.(string))
	default:
		// 转账类型错误 - 提示系统错误
		systemTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSystemError.Error(), locale)
		return errors.New(systemTranslate.Value)
	}
	if err != nil {
		// 用户不存在
		noExistsTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrUserNotExists.Error(), locale)
		return errors.New(noExistsTranslate.Value)
	}

	_ = &models.UserTransfer{
		SendUserID:    sendUser.ID,
		ReceiveUserID: receiveUser.ID,
		AssetID:       req.AssetID,
		Amount:        req.Amount,
		ActualAmount:  req.Amount,
		Status:        models.UserTransferStatusCompleted,
		Type:          models.UserTransferTypeInternal,
	}
	//_, err = s.userTransferRepo.Create(ctx, locale, sendUser, receiveUser, userTransfer)
	//if err != nil {
	//	return err
	//}

	return nil
}

// Update 更新用户转账
func (s *UserTransferService) Update(ctx context.Context, id uint, req *admin.UserTransferUpdateReq) error {
	userTransfer, err := s.userTransferRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if req.Amount != nil {
		userTransfer.Amount = *req.Amount
	}
	if req.FixedFee != nil {
		userTransfer.FixedFee = *req.FixedFee
	}
	if req.RateFee != nil {
		userTransfer.RateFee = *req.RateFee
	}
	if req.ActualAmount != nil {
		userTransfer.ActualAmount = *req.ActualAmount
	}
	if req.Reason != nil {
		userTransfer.Reason = *req.Reason
	}
	if req.CreatedAt != nil {
		createdAt, err := time.Parse("2006-01-02 15:04:05", *req.CreatedAt)
		if err != nil {
			return err
		}
		userTransfer.CreatedAt = createdAt
	}

	return s.userTransferRepo.Update(ctx, userTransfer)
}

// Delete 根据ID删除用户转账
func (s *UserTransferService) Delete(ctx context.Context, id uint) error {
	return s.userTransferRepo.Delete(ctx, id)
}

// BatchDelete 批量删除用户转账
func (s *UserTransferService) BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error {
	return s.userTransferRepo.BulkDelete(ctx, req.IDs)
}
