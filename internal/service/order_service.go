package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// OrderService 订单服务实现
type OrderService struct {
	orderRepo repo.OrderRepository
}

// NewOrderService 创建订单服务
func NewOrderService(orderRepo repo.OrderRepository) interfaces.IOrderService {
	return &OrderService{
		orderRepo: orderRepo,
	}
}

// List 获取订单列表
func (s *OrderService) List(ctx context.Context, req *admin.OrderListReq) ([]*models.Order, int64, error) {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("product_id", req.ProductID).
		AddCondition("user_id", req.UserID).
		AddCondition("status", req.Status).
		Build(s.orderRepo.DB())

	result, err := s.orderRepo.FindWithPagination(ctx, builder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建订单
func (s *OrderService) Create(ctx context.Context, req *admin.OrderCreateReq) error {
	order := &models.Order{
		ProductID: req.ProductID,
		UserID:    req.UserID,
		Amount:    req.Amount,
	}
	return s.orderRepo.Create(ctx, order)
}

// Update 更新订单
func (s *OrderService) Update(ctx context.Context, id uint, req *admin.OrderUpdateReq) error {
	order, err := s.orderRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		return nil
	}

	if req.Status != nil {
		order.Status = *req.Status
	}
	return s.orderRepo.Update(ctx, order)
}

// Delete 删除订单
func (s *OrderService) Delete(ctx context.Context, id uint) error {
	return s.orderRepo.Delete(ctx, id)
}
