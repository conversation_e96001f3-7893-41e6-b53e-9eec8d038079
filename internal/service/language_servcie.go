package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// LanguageService 语言服务实现
type LanguageService struct {
	langRepo repo.LanguageRepository
}

// NewLanguageService 创建语言服务
func NewLanguageService(repo repo.LanguageRepository) interfaces.ILanguageService {
	return &LanguageService{
		langRepo: repo,
	}
}

// Create 创建语言
func (s *LanguageService) Create(ctx context.Context, req *admin.LanguageCreateRequest) error {
	lang := &models.Language{
		Code:       req.Code,
		Name:       req.Name,
		NameNative: req.NameNative,
		Icon:       req.Icon,
		Sort:       req.Sort,
	}
	return s.langRepo.Create(ctx, lang)
}

// Update 更新语言
func (s *LanguageService) Update(ctx context.Context, id uint, req *admin.LanguageUpdateRequest) error {
	lang, err := s.langRepo.FindByID(ctx, id)
	if err != nil || lang == nil {
		return err
	}

	if req.Name != nil {
		lang.Name = *req.Name
	}
	if req.NameNative != nil {
		lang.NameNative = *req.NameNative
	}
	if req.Icon != nil {
		lang.Icon = *req.Icon
	}
	if req.Sort != nil {
		lang.Sort = *req.Sort
	}

	return s.langRepo.Update(ctx, lang)
}

// Delete 删除语言
func (s *LanguageService) Delete(ctx context.Context, id uint) error {
	return s.langRepo.Delete(ctx, id)
}

// GetByID 根据ID获取语言
func (s *LanguageService) GetByID(ctx context.Context, id uint) (*models.Language, error) {
	return s.langRepo.FindByID(ctx, id)
}

// List 获取语言列表
func (s *LanguageService) List(ctx context.Context, req *admin.LanguageListRequest) ([]*models.Language, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("code", req.Code).
		AddCondition("name", req.Name).
		AddCondition("name_native", req.NameNative).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.langRepo.DB())

	result, err := s.langRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// BatchDelete 批量删除语言
func (s *LanguageService) BatchDelete(ctx context.Context, req *admin.LanguageBatchDeleteRequest) error {
	return s.langRepo.BulkDelete(ctx, req.IDs)
}

// GetInitsLanguages 获取语言列表
func (s *LanguageService) GetInitsLanguages(ctx context.Context) []*web.InitsLanguages {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("status", models.LanguageStatusEnabled).
		Build(s.langRepo.DB())

	langList, err := s.langRepo.FindWithQuery(ctx, builder)
	if err != nil {
		return nil
	}

	result := make([]*web.InitsLanguages, len(langList))
	for i, lang := range langList {
		result[i] = &web.InitsLanguages{
			Icon:   lang.Icon,
			Label:  lang.Name,
			Locale: lang.Code,
		}
	}
	return result
}
