package service

import (
	"context"
	"errors"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// UserService 用户服务实现
type UserService struct {
	userRepo        repo.UserRepository
	userAssetRepo   repo.UserAssetRepository
	assetRepo       repo.AssetRepository
	translationRepo repo.TranslationRepository
}

// NewUserService 创建用户服务
func NewUserService(repo repo.UserRepository, translationRepo repo.TranslationRepository, userAssetRepo repo.UserAssetRepository, assetRepo repo.AssetRepository) interfaces.IUserService {
	return &UserService{
		userRepo:        repo,
		translationRepo: translationRepo,
		userAssetRepo:   userAssetRepo,
		assetRepo:       assetRepo,
	}
}

// Create 创建用户
func (s *UserService) Create(ctx context.Context, req *admin.UserCreateRequest) error {
	txRepo := s.userRepo.WithTx(s.userRepo.DB())
	return s.userRepo.Transaction(ctx, func(tx txRepo) error {
		user := &models.User{
			Avatar:   "/static/avatar/default_avatar.png",
			Username: req.Username,
			Nickname: req.Username,
			Type:     req.Type,
		}

		// 创建用户
		if err := txUserRepo.Create(ctx, user); err != nil {
			return err
		}

		// 创建用户资产 - 需要获取同一个事务的assetRepo
		txAssetRepo := s.assetRepo.WithTx(txUserRepo.DB())
		if err := txAssetRepo.Create(ctx, &models.Asset{
			UserID: user.ID,
			Amount: decimal.Zero,
		}); err != nil {
			return err
		}

		return nil
	})
}

// Update 更新用户
func (s *UserService) Update(ctx context.Context, id uint, req *admin.UserUpdateRequest) error {
	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil || user == nil {
		return err
	}

	if req.Nickname != nil {
		user.Nickname = *req.Nickname
	}
	if req.Telephone != nil {
		user.Telephone = *req.Telephone
	}
	if req.Email != nil {
		user.Email = *req.Email
	}
	if req.Avatar != nil {
		user.Avatar = *req.Avatar
	}
	if req.Gender != nil {
		user.Gender = *req.Gender
	}
	if req.Status != nil {
		user.Status = *req.Status
	}
	if req.Type != nil {
		user.Type = *req.Type
	}
	if req.Description != nil {
		user.Description = *req.Description
		user.Description = *req.Description
	}

	return s.userRepo.Update(ctx, user)
}

// Delete 删除用户
func (s *UserService) Delete(ctx context.Context, id uint) error {
	return s.userRepo.Delete(ctx, id)
}

// GetByID 根据ID获取用户
func (s *UserService) GetByID(ctx context.Context, id uint) (*models.User, error) {
	return s.userRepo.FindByID(ctx, id)
}

// List 获取用户列表
func (s *UserService) List(ctx context.Context, req *admin.UserListRequest) ([]*models.User, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("username", req.Username).
		AddCondition("nickname", req.Nickname).
		AddCondition("email", req.Email).
		AddCondition("gender", req.Gender).
		AddCondition("status", req.Status).
		AddCondition("type", req.Type).
		AddConditionWithOperator("created_at", query.OpGte, req.StartTime).
		AddConditionWithOperator("created_at", query.OpLte, req.EndTime).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.userRepo.DB())

	result, err := s.userRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// BatchDelete 批量删除用户
func (s *UserService) BatchDelete(ctx context.Context, req *admin.UserBatchDeleteRequest) error {
	return s.userRepo.BulkDelete(ctx, req.IDs)
}

// FindWalletAssets 根据用户ID查询用户钱包资产
func (s *UserService) FindWalletAssets(ctx context.Context, locale string, userID uint) (*models.UserWalletList, error) {
	data := &models.UserWalletList{
		Account: &models.UserWalletAsset{},
		Wallet:  make([]*models.UserWalletAsset, 0),
	}

	// 查询钱包账户
	account, err := s.assetRepo.FindWalletAccount(ctx)
	if err != nil {
		return nil, err
	}

	// 获取资产副标题翻译
	subtitle, _ := s.translationRepo.FindByKeyAndLang(ctx, account.SubtitleField, locale)
	data.Account = &models.UserWalletAsset{
		ID:         account.ID,
		Name:       account.Name,
		NameNative: account.NameNative,
		Subtitle:   subtitle.Value,
		Symbol:     account.Symbol,
		Icon:       account.Icon,
		Rate:       account.Rate,
		Decimals:   account.Decimals,
		Data:       account.Data,
	}

	// 查询钱包资产列表
	//walletAssets, err := r.assetRepo.FindWalletAssetList(ctx)
	//if err != nil {
	//	return nil, err
	//}
	//for _, walletAsset := range walletAssets {
	//	// 获取资产副标题翻译
	//	subtitle := r.translateRepo.FindByField(ctx, locale, walletAsset.SubtitleField)
	//	data.Wallet = append(data.Wallet, &model.UserWalletAsset{
	//		ID:         walletAsset.ID,
	//		Name:       walletAsset.Name,
	//		NameNative: walletAsset.NameNative,
	//		Subtitle:   subtitle.Value,
	//		Symbol:     walletAsset.Symbol,
	//		Icon:       walletAsset.Icon,
	//		Rate:       walletAsset.Rate,
	//		Decimals:   walletAsset.Decimals,
	//		Data:       walletAsset.Data,
	//	})
	//}

	// 查询用户资产列表
	//queryBuilder := NewQueryBuilder().SetPaginationDisabled().AddCondition("user_id", userID)
	//userAssets, _, err := r.FindQueryBuilderList(ctx, queryBuilder)
	//if err != nil {
	//	return nil, err
	//}
	//
	//for _, userAsset := range userAssets {
	//	if userAsset.Asset.Type == model.AssetTypePlatform {
	//		data.Account.AvailableAmount = userAsset.AvailableAmount
	//		data.Account.FrozenAmount = userAsset.FrozenAmount
	//		data.Account.ProfitAmount = userAsset.ProfitAmount
	//	} else {
	//		for _, walletAsset := range data.Wallet {
	//			if userAsset.AssetID == walletAsset.ID {
	//				walletAsset.AvailableAmount = userAsset.AvailableAmount
	//				walletAsset.FrozenAmount = userAsset.FrozenAmount
	//				walletAsset.ProfitAmount = userAsset.ProfitAmount
	//				break
	//			}
	//		}
	//	}
	//}

	return data, nil
}

// Register 注册
func (s *UserService) Register(ctx context.Context, req *web.RegisterReq) error {

	return nil
}

// Login 登录
func (s *UserService) Login(ctx context.Context, username, password string) (*models.User, string, error) {
	return nil, "", nil
}

// Logout 登出
func (s *UserService) Logout(ctx context.Context, tokenID string, userID uint, username string, ip string, userAgent string) error {
	return nil
}

// UserInfo 用户信息
func (s *UserService) UserInfo(ctx context.Context, locale string, userID uint) (*web.UserInfoRes, error) {
	user, err := s.userRepo.WithPreload("UserLevel", "Certification").FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	return &web.UserInfoRes{
		ID:          user.ID,
		Username:    user.Username,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Gender:      user.Gender,
		Email:       user.Email,
		Telephone:   user.Telephone,
		Score:       user.Score,
		MinScore:    user.MinScore,
		Birthday:    user.Birthday,
		InviteCode:  user.InviteCode,
		Description: user.Description,
		LevelID:     user.LevelID,
		Status:      user.Status,
		UserLevel: web.UserInfoUserLevel{
			Icon:      user.UserLevel.Data.Icon,
			Symbol:    user.UserLevel.Data.Symbol,
			ExpiredAt: user.UserLevel.ExpiredAt,
		},
		Certification: web.UserInfoCertification{
			ID:        user.Certification.ID,
			Status:    user.Certification.Status,
			Reason:    user.Certification.Reason,
			CreatedAt: user.Certification.CreatedAt,
		},
	}, nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(ctx context.Context, locale string, id uint, pwdType int, req *web.ChangePasswordReq) error {
	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	switch pwdType {
	case web.UpdatePasswordTypeLogin:
		if !user.CompareHashAndPassword(req.OldPassword) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrOldPasswordIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
		err = user.GeneratePasswordHash(req.NewPassword)
		if err != nil {
			return err
		}
	case web.UpdatePasswordTypeSecurity:
		if !user.CompareHashAndSecurityKey(req.OldPassword) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrOldPasswordIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
		err = user.GenerateSecurityKey(req.NewPassword)
		if err != nil {
			return err
		}
	default:
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSystemError.Error(), locale)
		return errors.New(translate.Value)
	}

	return s.userRepo.Update(ctx, user)
}

// BindingEmail 绑定邮箱
func (s *UserService) BindingEmail(ctx context.Context, locale string, userID uint, req *web.BindingEmailReq) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	if user.Email != "" {
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSystemError.Error(), locale)
		return errors.New(translate.Value)
	}

	user.Email = req.Email
	return s.userRepo.Update(ctx, user)
}

// BindingTelephone 绑定手机号码
func (s *UserService) BindingTelephone(ctx context.Context, locale string, userID uint, req *web.BindingTelephoneReq) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	if user.Telephone != "" {
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSystemError.Error(), locale)
		return errors.New(translate.Value)
	}

	user.Telephone = req.Telephone
	return s.userRepo.Update(ctx, user)
}

// UserInviteList 用户邀请列表
func (s *UserService) UserInviteList(ctx context.Context, userID uint) (*web.InviteListRes, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("parent_id", userID)
	queryBuilder.AddSort("created_at", "DESC")
	queryBuilder.SetPagination(1, 50)
	result, err := s.userRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, err
	}

	data := &web.InviteListRes{
		Award:  0,
		Number: result.Total,
		Items:  make([]web.InviteListItem, 0),
	}

	for _, user := range result.Data {
		data.Items = append(data.Items, web.InviteListItem{
			UID:       user.ID,
			Avatar:    user.Avatar,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
		})
	}

	return data, nil
}
