package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// CategoryService 分类服务实现
type CategoryService struct {
	categoryRepo repo.CategoryRepository
}

// NewCategoryService 创建分类服务
func NewCategoryService(categoryRepo repo.CategoryRepository) interfaces.ICategoryService {
	return &CategoryService{
		categoryRepo: categoryRepo,
	}
}

// List 获取分类列表
func (s *CategoryService) List(ctx context.Context, req *admin.CategoryListReq) ([]*models.Category, int64, error) {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("name", req.Name).
		AddCondition("sort", req.Sort).
		Build(s.categoryRepo.DB())

	result, err := s.categoryRepo.FindWithPagination(ctx, builder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建分类
func (s *CategoryService) Create(ctx context.Context, req *admin.CategoryCreateReq) error {
	category := &models.Category{
		Name: req.Name,
		Sort: req.Sort,
	}
	return s.categoryRepo.Create(ctx, category)
}

// Update 更新分类
func (s *CategoryService) Update(ctx context.Context, id uint, req *admin.CategoryUpdateReq) error {
	category, err := s.categoryRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if category == nil {
		return nil
	}

	if req.Name != nil {
		category.Name = *req.Name
	}
	if req.Sort != nil {
		category.Sort = *req.Sort
	}
	return s.categoryRepo.Update(ctx, category)
}

// Delete 删除分类
func (s *CategoryService) Delete(ctx context.Context, id uint) error {
	return s.categoryRepo.Delete(ctx, id)
}
