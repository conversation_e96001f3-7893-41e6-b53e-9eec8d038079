package service

import (
	"context"
	"errors"
	"fmt"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"gin/pkg/convert"
	"gin/pkg/utils"
	"github.com/goccy/go-json"
)

// UserAccountService 用户账户服务实现
type UserAccountService struct {
	logger          logger.Logger
	userRepo        repo.UserRepository
	assetRepo       repo.AssetRepository
	userAccountRepo repo.UserAccountRepository
	paymentRepo     repo.PaymentRepository
	managerRepo     repo.ManagerRepository
	settingRepo     repo.SettingRepository
	translationRepo repo.TranslationRepository
}

// NewUserAccountService 创建用户账户服务实现
func NewUserAccountService(logger logger.Logger, userRepo repo.UserRepository, assetRepo repo.AssetRepository, userAccountRepo repo.UserAccountRepository, paymentRepo repo.PaymentRepository, managerRepo repo.ManagerRepository, settingRepo repo.SettingRepository, translateRepo repo.TranslationRepository) interfaces.IUserAccountService {
	return &UserAccountService{
		logger:          logger,
		userRepo:        userRepo,
		assetRepo:       assetRepo,
		userAccountRepo: userAccountRepo,
		paymentRepo:     paymentRepo,
		managerRepo:     managerRepo,
		settingRepo:     settingRepo,
		translationRepo: translateRepo,
	}
}

// List 根据查询条件获取用户账户列表, 并返回总数
func (s *UserAccountService) List(ctx context.Context, req *admin.UserAccountListReq) ([]*models.UserAccount, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", req.UserID)
	queryBuilder.AddCondition("payment_id", req.PaymentID)
	queryBuilder.AddCondition("status", req.Status)

	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userAccountRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// ListByUserID 根据用户ID获取用户账户列表
func (s *UserAccountService) ListByUserID(ctx context.Context, userID uint, req *web.UserAccountListReq) ([]*web.UserAccountListRes, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", userID)
	queryBuilder.AddCondition("asset_id", req.AssetID)
	queryBuilder.AddSort("id", "DESC")

	result, err := s.userAccountRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, err
	}

	results := make([]*web.UserAccountListRes, 0)
	for _, userAccount := range result.Data {
		item := &web.UserAccountListRes{
			ID:        userAccount.ID,
			AssetID:   userAccount.AssetID,
			Icon:      userAccount.Asset.Icon,
			Name:      userAccount.Asset.Symbol,
			PaymentID: userAccount.PaymentID,
			Type:      userAccount.Type,
		}
		err = json.Unmarshal([]byte(userAccount.Data), &item.Data)
		if err != nil {
			return nil, err
		}
		results = append(results, item)
	}
	return results, nil
}

// Create 创建用户账户
func (s *UserAccountService) Create(ctx context.Context, req *admin.UserAccountCreateReq) (*models.UserAccount, error) {
	// 获取用户
	user, err := s.userRepo.FindByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// 获取支付方式
	withdrawPayment, err := s.paymentRepo.FindByID(ctx, req.PaymentID)
	if err != nil {
		return nil, err
	}

	asset, err := s.assetRepo.FindByID(ctx, withdrawPayment.AssetID)
	if err != nil {
		return nil, err
	}

	userAccount := &models.UserAccount{
		UserID:    user.ID,
		PaymentID: req.PaymentID,
		AssetID:   withdrawPayment.AssetID,
		Name:      asset.Symbol,
		Type:      withdrawPayment.Type,
		Status:    models.UserAccountStatusEnabled,
	}
	switch withdrawPayment.Type {
	case models.PaymentTypeCrypto:
		cryptoData := withdrawPayment.GetWithdrawCryptoData()
		cryptoData.Address = req.CardNumber
		userAccount.Data = convert.JSONMarshal(cryptoData)
	case models.PaymentTypeBankCard:
		bankCardData := withdrawPayment.GetWithdrawBankCardData()
		bankCardData.CardNo = req.CardNumber
		bankCardData.RealName = user.Nickname
		bankCardData.AreaCode = "000000"
		bankCardData.Address = "xxx/xxx/xxx"
		userAccount.Data = convert.JSONMarshal(bankCardData)
	}
	err = s.userAccountRepo.Create(ctx, userAccount)
	if err != nil {
		return nil, err
	}
	return userAccount, nil
}

// UserCreate 用户创建用户账户
func (s *UserAccountService) UserCreate(ctx context.Context, locale string, userID uint, req *web.UserAccountCreateReq) (*models.UserAccount, error) {
	// 获取用户信息
	_, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取资产
	asset, err := s.assetRepo.FindByID(ctx, req.AssetID)
	if err != nil {
		return nil, err
	}

	// 判断用户绑定的数量有没有超过限制
	userAccounts, err := s.paymentRepo.FindUserAccountByAssetID(ctx, userID, asset.ID)
	if err != nil {
		return nil, err
	}

	// 获取提现配置 - 如果当前资产绑定的账户超过, 不能在绑定
	widthdrawSetting := constant.SettingWithdraw{}
	err = s.settingRepo.GetFieldValues(models.SettingSiteWithdraw, &widthdrawSetting)
	if err != nil {
		return nil, err
	}
	if len(userAccounts) >= int(widthdrawSetting.Account) {
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrBindAccountOverLimit.Error(), locale)
		return nil, fmt.Errorf("%s[%d/%d]", translate.Value, len(userAccounts), widthdrawSetting.Account)
	}

	// 获取支付方式
	payment, err := s.paymentRepo.FindByID(ctx, req.PaymentID)
	if err != nil {
		return nil, err
	}

	// 创建用户账户
	dataBytes, err := json.Marshal(req.Data)
	if err != nil {
		return nil, err
	}
	userAccount := &models.UserAccount{
		UserID:    userID,
		AssetID:   asset.ID,
		PaymentID: req.PaymentID,
		Type:      payment.Type,
		Status:    models.UserAccountStatusEnabled,
		Data:      string(dataBytes),
	}
	err = s.userAccountRepo.Create(ctx, userAccount)
	if err != nil {
		return nil, err
	}
	return userAccount, nil
}

// UserUpdate 用户更新用户账户
func (s *UserAccountService) UserUpdate(ctx context.Context, locale string, userID uint, id uint, req *web.UserAccountUpdateReq) error {
	userAccount, err := s.userAccountRepo.FindByIDAndUserID(ctx, id, userID)
	if err != nil {
		return err
	}

	accountSetting := constant.SettingBasicTemplate{}
	_ = s.settingRepo.GetFieldValues(models.SettingTemplateBasic, &accountSetting)
	if utils.HasStringSlice(accountSetting.Checkbox, constant.SettingBasicTemplateCheckboxWithdrawAccountPassword) {
		user, err := s.userRepo.FindByID(ctx, userID)
		if err != nil {
			return err
		}
		if !user.CompareHashAndSecurityKey(req.SecurityKey) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSecurityKeyIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
	}

	// 转换数据
	dataBytes, err := json.Marshal(req.Data)
	if err != nil {
		return err
	}
	userAccount.Data = string(dataBytes)
	return s.userAccountRepo.Update(ctx, userAccount)
}

// UserDelete 用户删除用户账户
func (s *UserAccountService) UserDelete(ctx context.Context, locale string, userID uint, id uint, req *web.UserAccountDeleteReq) error {
	accountSetting := constant.SettingBasicTemplate{}
	_ = s.settingRepo.GetFieldValues(models.SettingTemplateBasic, &accountSetting)
	if utils.HasStringSlice(accountSetting.Checkbox, constant.SettingBasicTemplateCheckboxWithdrawAccountPassword) {
		user, err := s.userRepo.FindByID(ctx, userID)
		if err != nil {
			return err
		}
		if !user.CompareHashAndSecurityKey(req.SecurityKey) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSecurityKeyIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
	}

	return s.userAccountRepo.Delete(ctx, id)
}

// Update 更新用户账户
func (s *UserAccountService) Update(ctx context.Context, id uint, req *admin.UserAccountUpdateReq) error {
	userAccount, err := s.userAccountRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if req.Name != nil {
		userAccount.Name = *req.Name
	}
	if req.Status != nil {
		userAccount.Status = *req.Status
	}
	if req.Data != nil {
		dataBytes, err := json.Marshal(req.Data)
		if err != nil {
			return err
		}
		userAccount.Data = string(dataBytes)
	}
	return s.userAccountRepo.Update(ctx, userAccount)
}

// Delete 根据ID删除用户账户
func (s *UserAccountService) Delete(ctx context.Context, id uint) error {
	return s.userAccountRepo.Delete(ctx, id)
}
