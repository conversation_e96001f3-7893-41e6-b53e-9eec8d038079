package service

import (
	"context"
	"errors"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"gin/pkg/utils"
	"time"
)

// UserSwapService 用户闪兑服务实现
type UserSwapService struct {
	logger          logger.Logger
	userSwapRepo    repo.UserSwapRepository
	managerRepo     repo.ManagerRepository
	userRepo        repo.UserRepository
	assetRepo       repo.AssetRepository
	translationRepo repo.TranslationRepository
	settingRepo     repo.SettingRepository
}

// NewUserSwapService 创建一个新的UserSwapService实例
func NewUserSwapService(logger logger.Logger, userSwapRepo repo.UserSwapRepository, managerRepo repo.ManagerRepository, userRepo repo.UserRepository, assetRepo repo.AssetRepository, translateRepo repo.TranslationRepository, settingRepo repo.SettingRepository) interfaces.IUserSwapService {
	return &UserSwapService{
		logger:          logger,
		userSwapRepo:    userSwapRepo,
		managerRepo:     managerRepo,
		userRepo:        userRepo,
		assetRepo:       assetRepo,
		translationRepo: translateRepo,
		settingRepo:     settingRepo,
	}
}

// List 根据查询条件获取用户闪兑列表, 并返回总数
func (s *UserSwapService) List(ctx context.Context, req *admin.UserSwapListReq) ([]*models.UserSwap, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("manager_id", req.ManagerID)
	queryBuilder.AddCondition("send_user_id", req.SendUserID)
	queryBuilder.AddCondition("send_asset_id", req.SendAssetID)
	queryBuilder.AddCondition("receive_asset_id", req.ReceiveAssetID)
	queryBuilder.AddCondition("type", req.Type)
	queryBuilder.AddCondition("status", req.Status)
	queryBuilder.AddConditionWithOperator("created_at", query.OpGte, req.StartTime)
	queryBuilder.AddConditionWithOperator("created_at", query.OpLte, req.EndTime)
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userSwapRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// UserList 根据查询条件获取用户闪兑列表, 并返回总数
func (s *UserSwapService) UserList(ctx context.Context, locale string, userID uint, req *web.UserSwapListReq) ([]*web.UserSwapListRes, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("send_user_id", userID)
	queryBuilder.AddCondition("type", models.UserSwapTypeFlash)
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userSwapRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	results := make([]*web.UserSwapListRes, 0)
	for _, userSwap := range result.Data {
		sendAssetSubtitle, _ := s.translationRepo.FindByKeyAndLang(ctx, userSwap.SendAsset.SubtitleField, locale)
		receiveAssetSubtitle, _ := s.translationRepo.FindByKeyAndLang(ctx, userSwap.ReceiveAsset.SubtitleField, locale)
		results = append(results, &web.UserSwapListRes{
			ID:             userSwap.ID,
			SendAssetID:    userSwap.SendAssetID,
			ReceiveAssetID: userSwap.ReceiveAssetID,
			SendAsset: web.AssetRes{
				ID:       userSwap.SendAsset.ID,
				Name:     userSwap.SendAsset.Name,
				Subtitle: sendAssetSubtitle.Value,
				Symbol:   userSwap.SendAsset.Symbol,
				Icon:     userSwap.SendAsset.Icon,
				Rate:     userSwap.SendAsset.Rate,
				Decimals: userSwap.SendAsset.Decimals,
			},
			ReceiveAsset: web.AssetRes{
				ID:       userSwap.ReceiveAsset.ID,
				Name:     userSwap.ReceiveAsset.Name,
				Subtitle: receiveAssetSubtitle.Value,
				Symbol:   userSwap.ReceiveAsset.Symbol,
				Icon:     userSwap.ReceiveAsset.Icon,
				Rate:     userSwap.ReceiveAsset.Rate,
				Decimals: userSwap.ReceiveAsset.Decimals,
			},
			SendAmount:    userSwap.SendAmount,
			ReceiveAmount: userSwap.ReceiveAmount,
			Rate:          userSwap.Rate,
			FixedFee:      userSwap.FixedFee,
			RateFee:       userSwap.RateFee,
			Type:          userSwap.Type,
			Status:        userSwap.Status,
			Reason:        userSwap.Reason,
			CreatedAt:     userSwap.CreatedAt,
			UpdatedAt:     userSwap.UpdatedAt,
		})
	}

	return results, result.Total, nil
}

// Create 创建用户闪兑
func (s *UserSwapService) Create(ctx context.Context, locale string, req *admin.UserSwapCreateReq) (*models.UserSwap, error) {
	if req.SendAssetID == req.ReceiveAssetID {
		return nil, errors.New("发送资产和接收资产不能相同")
	}

	// 获取用户
	user, err := s.userRepo.FindByID(ctx, req.SendUserID)
	if err != nil {
		return nil, err
	}

	// 获取发送资产
	sendAsset, err := s.assetRepo.FindByID(ctx, req.SendAssetID)
	if err != nil {
		return nil, err
	}

	// 获取接收资产
	receiveAsset, err := s.assetRepo.FindByID(ctx, req.ReceiveAssetID)
	if err != nil {
		return nil, err
	}

	// 计算汇率
	rate := sendAsset.Rate / receiveAsset.Rate
	receiveAmount := req.SendAmount * rate
	userSwap := &models.UserSwap{
		SendUserID:     user.ID,
		ReceiveUserID:  user.ID,
		SendAssetID:    sendAsset.ID,
		ReceiveAssetID: receiveAsset.ID,
		Rate:           rate,
		ReceiveAmount:  receiveAmount,
		Type:           models.UserSwapTypeFlash,
		Status:         models.UserSwapStatusCompleted,
		SendAmount:     req.SendAmount,
	}
	//return s.userSwapRepo.Create(ctx, locale, user, userSwap)
	return userSwap, nil
}

// UserCreate 用户创建闪兑
func (s *UserSwapService) UserCreate(ctx context.Context, locale string, userID uint, req *web.UserSwapCreateReq) error {
	if req.SendAssetID == req.ReceiveAssetID {
		systemTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSystemError.Error(), locale)
		return errors.New(systemTranslate.Value)
	}

	// 获取用户
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	// 获取支付密码
	basicTemplate := constant.SettingBasicTemplate{}
	err = s.settingRepo.GetFieldValues(models.SettingTemplateBasic, &basicTemplate)
	if err != nil {
		return err
	}
	if utils.HasStringSlice(basicTemplate.Checkbox, constant.SettingBasicTemplateCheckboxSwapPassword) {
		if !user.CompareHashAndSecurityKey(req.SecurityKey) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSecurityKeyIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
	}

	// 获取发送资产
	sendAsset, err := s.assetRepo.FindByID(ctx, req.SendAssetID)
	if err != nil {
		return err
	}

	// 获取接收资产
	receiveAsset, err := s.assetRepo.FindByID(ctx, req.ReceiveAssetID)
	if err != nil {
		return err
	}

	// 计算汇率
	rate := sendAsset.Rate / receiveAsset.Rate
	receiveAmount := req.Amount * rate
	_ = &models.UserSwap{
		SendUserID:     user.ID,
		ReceiveUserID:  user.ID,
		SendAssetID:    sendAsset.ID,
		ReceiveAssetID: receiveAsset.ID,
		Rate:           rate,
		ReceiveAmount:  receiveAmount,
		Type:           models.UserSwapTypeFlash,
		Status:         models.UserSwapStatusCompleted,
		SendAmount:     req.Amount,
	}
	//_, err = s.userSwapRepo.Create(ctx, locale, user, userSwap)
	//if err != nil {
	//	return err
	//}

	return nil
}

// Update 更新用户闪兑
func (s *UserSwapService) Update(ctx context.Context, id uint, req *admin.UserSwapUpdateReq) error {
	userSwap, err := s.userSwapRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if req.SendAmount != nil {
		userSwap.SendAmount = *req.SendAmount
	}
	if req.ReceiveAmount != nil {
		userSwap.ReceiveAmount = *req.ReceiveAmount
	}
	if req.Rate != nil {
		userSwap.Rate = *req.Rate
	}
	if req.FixedFee != nil {
		userSwap.FixedFee = *req.FixedFee
	}
	if req.RateFee != nil {
		userSwap.RateFee = *req.RateFee
	}
	if req.Reason != nil {
		userSwap.Reason = *req.Reason
	}
	if req.CreatedAt != nil {
		createdAt, err := time.Parse("2006-01-02 15:04:05", *req.CreatedAt)
		if err != nil {
			return err
		}
		userSwap.CreatedAt = createdAt
	}

	return s.userSwapRepo.Update(ctx, userSwap)
}

// Delete 根据ID删除用户闪兑
func (s *UserSwapService) Delete(ctx context.Context, id uint) error {
	return s.userSwapRepo.Delete(ctx, id)
}

// BatchDelete 批量删除用户闪兑
func (s *UserSwapService) BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error {
	return s.userSwapRepo.BulkDelete(ctx, req.IDs)
}
