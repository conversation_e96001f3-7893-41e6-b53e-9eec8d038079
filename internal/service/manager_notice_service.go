package service

import (
	"context"
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// ManagerNoticeService 管理员通知服务实现
type ManagerNoticeService struct {
	noticeRepo repo.ManagerNoticeRepository
}

// NewManagerNoticeService 创建管理员通知服务
func NewManagerNoticeService(noticeRepo repo.ManagerNoticeRepository) interfaces.IManagerNoticeService {
	return &ManagerNoticeService{
		noticeRepo: noticeRepo,
	}
}

// ListForSender 获取发送者是当前管理的通知列表
func (s *ManagerNoticeService) ListForSender(ctx context.Context, managerID uint, req *admin.ManagerNoticeListRequest) ([]*models.ManagerNotice, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("sender_id", managerID).
		AddCondition("title", req.Title).
		AddCondition("type", req.Type).
		AddCondition("is_read", req.IsRead).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).
		Build(s.noticeRepo.DB())

	result, err := s.noticeRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建管理员通知
func (s *ManagerNoticeService) Create(ctx context.Context, managerID uint, req *admin.ManagerNoticeCreateRequest) error {

	if req.Type == models.ManagerNoticeTypePersonal && req.ReceiverID == 0 {
		return fmt.Errorf("个人消息必须指定接收者")
	}

	notice := &models.ManagerNotice{
		SenderID:   managerID,
		ReceiverID: req.ReceiverID,
		Title:      req.Title,
		Content:    req.Content,
		Type:       req.Type,
		Route:      req.Route,
	}
	return s.noticeRepo.Create(ctx, notice)
}

// Update 更新管理员通知
func (s *ManagerNoticeService) Update(ctx context.Context, id uint, req *admin.ManagerNoticeUpdateRequest) error {
	notice, err := s.noticeRepo.FindByID(ctx, id)
	if err != nil || notice == nil {
		return err
	}

	if req.Title != nil {
		notice.Title = *req.Title
	}
	if req.Content != nil {
		notice.Content = *req.Content
	}
	if req.Type != nil {
		notice.Type = *req.Type
	}
	if req.Route != nil {
		notice.Route = *req.Route
	}

	return s.noticeRepo.Update(ctx, notice)
}

// Delete 删除管理员通知
func (s *ManagerNoticeService) Delete(ctx context.Context, id uint) error {
	return s.noticeRepo.Delete(ctx, id)
}

// BatchDelete 批量删除管理员通知
func (s *ManagerNoticeService) BatchDelete(ctx context.Context, req *admin.ManagerNoticeBatchDeleteRequest) error {
	return s.noticeRepo.BulkDelete(ctx, req.IDs)
}

// ListForReceiver 获取接收者是当前管理的通知列表
func (s *ManagerNoticeService) ListForReceiver(ctx context.Context, managerID uint, req *admin.ManagerNoticeListRequest) ([]*models.ManagerNotice, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("receiver_id", managerID).
		AddCondition("title", req.Title).
		AddCondition("type", req.Type).
		AddCondition("is_read", req.IsRead).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.noticeRepo.DB())

	result, err := s.noticeRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// MarkAsRead 标记管理员通知为已读
func (s *ManagerNoticeService) MarkAsRead(ctx context.Context, managerID, id uint) error {
	notice, err := s.noticeRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if notice.ReceiverID != 0 && notice.ReceiverID != managerID {
		return fmt.Errorf("无权操作")
	}
	return s.noticeRepo.MarkAsRead(ctx, id)
}

// DeleteForReceiver 删除接收者是当前管理的通知
func (s *ManagerNoticeService) DeleteForReceiver(ctx context.Context, managerID, id uint) error {
	notice, err := s.noticeRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if notice.ReceiverID != 0 && notice.ReceiverID != managerID {
		return fmt.Errorf("无权操作")
	}
	return s.noticeRepo.Delete(ctx, id)
}
