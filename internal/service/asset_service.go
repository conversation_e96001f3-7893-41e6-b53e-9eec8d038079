package service

import (
	"context"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// AssetService 资产服务
type AssetService struct {
	assetRepo repo.AssetRepository
}

// NewAssetService 创建资产服务
func NewAssetService(repo repo.AssetRepository) interfaces.IAssetService {
	return &AssetService{
		assetRepo: repo,
	}
}

// List 获取资产列表
func (s *AssetService) List(ctx context.Context, req *admin.AssetListReq) ([]*models.Asset, int64, error) {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("name", req.Name).
		AddCondition("symbol", req.Symbol).
		AddCondition("type", req.Type).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.assetRepo.DB())

	result, err := s.assetRepo.FindWithPagination(ctx, builder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建资产
func (s *AssetService) Create(ctx context.Context, req *admin.AssetCreateReq) error {
	asset := &models.Asset{
		Name:        req.Name,
		Subtitle:    req.Subtitle,
		NameNative:  req.NameNative,
		Symbol:      req.Symbol,
		Icon:        req.Icon,
		Type:        req.Type,
		Rate:        req.Rate,
		Sort:        req.Sort,
		Decimals:    req.Decimals,
		Description: req.Description,
		Data:        models.AssetData(req.Data),
	}
	return s.assetRepo.Create(ctx, asset)
}

// Update 更新资产
func (s *AssetService) Update(ctx context.Context, id uint, req *admin.AssetUpdateReq) error {
	asset, err := s.assetRepo.FindByID(ctx, id)
	if err != nil || asset == nil {
		return constant.ErrAssetNotExists
	}

	if req.Name != nil {
		asset.Name = *req.Name
	}
	if req.Symbol != nil {
		asset.Symbol = *req.Symbol
	}
	if req.Type != nil {
		asset.Type = *req.Type
	}
	if req.Status != nil {
		asset.Status = *req.Status
	}

	return s.assetRepo.Update(ctx, asset)
}

// Delete 删除资产
func (s *AssetService) Delete(ctx context.Context, id uint) error {
	return s.assetRepo.Delete(ctx, id)
}

// BatchDelete 批量删除资产
func (s *AssetService) BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error {
	return s.assetRepo.BulkDelete(ctx, req.IDs)
}
