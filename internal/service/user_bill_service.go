package service

import (
	"context"
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"strings"
	"time"
)

// UserBillService 用户账单服务实现
type UserBillService struct {
	logger          logger.Logger
	userBillRepo    repo.UserBillRepository
	managerRepo     repo.ManagerRepository
	translationRepo repo.TranslationRepository
}

// NewUserBillService 创建一个新的UserBillService实例
func NewUserBillService(logger logger.Logger, userBillRepo repo.UserBillRepository, managerRepo repo.ManagerRepository, translationRepo repo.TranslationRepository) interfaces.IUserBillService {
	return &UserBillService{
		logger:          logger,
		userBillRepo:    userBillRepo,
		managerRepo:     managerRepo,
		translationRepo: translationRepo,
	}
}

// List 根据查询条件获取用户账单列表, 并返回总数
func (s *UserBillService) List(ctx context.Context, req *admin.UserBillListReq) ([]*models.UserBill, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", req.UserID)
	queryBuilder.AddCondition("asset_id", req.AssetID)
	queryBuilder.AddCondition("type", req.Type)

	// 添加分页和排序
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))
	result, err := s.userBillRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// UserBillList 用户账单列表
func (s *UserBillService) UserBillList(ctx context.Context, locale string, userID uint, req *web.WalletBillListReq) ([]*web.WalletBillRes, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", userID)
	if req.AssetID != nil && *req.AssetID != 0 {
		queryBuilder.AddCondition("asset_id", *req.AssetID)
	}

	// 添加类型查询条件
	if req.Types != nil && *req.Types != "" {
		types := strings.Split(*req.Types, ",")
		queryBuilder.AddConditionWithOperator("type", query.OpIn, types)
	}
	queryBuilder.AddConditionWithOperator("created_at", query.OpGte, req.StartTime)
	queryBuilder.AddConditionWithOperator("created_at", query.OpLte, req.EndTime)

	// 添加分页和排序
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	// 查询用户数据
	result, err := s.userBillRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	results := make([]*web.WalletBillRes, 0)
	for _, userBill := range result.Data {
		nameTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, fmt.Sprintf(models.UserBillNameTranslateKeyPrefix, userBill.Type), locale)
		subtitleTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, userBill.Asset.SubtitleField, locale)
		results = append(results, &web.WalletBillRes{
			ID:      userBill.ID,
			AssetID: userBill.AssetID,
			Asset: web.AssetRes{
				ID:         userBill.AssetID,
				Name:       userBill.Asset.Name,
				Subtitle:   subtitleTranslate.Value,
				Icon:       userBill.Asset.Icon,
				NameNative: userBill.Asset.NameNative,
				Symbol:     userBill.Asset.Symbol,
				Rate:       userBill.Asset.Rate,
				Decimals:   userBill.Asset.Decimals,
			},
			Amount:    userBill.Amount,
			Fee:       userBill.Fee,
			Balance:   userBill.Balance,
			Type:      userBill.Type,
			Name:      nameTranslate.Value,
			CreatedAt: userBill.CreatedAt,
		})
	}

	return results, result.Total, nil
}

// Update 更新用户账单
func (s *UserBillService) Update(ctx context.Context, id uint, req *admin.UserBillUpdateReq) error {
	userBill, err := s.userBillRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if req.Amount != nil {
		userBill.Amount = *req.Amount
	}
	if req.Fee != nil {
		userBill.Fee = *req.Fee
	}
	if req.Balance != nil {
		userBill.Balance = *req.Balance
	}
	if req.CreatedAt != nil {
		createdAt, err := time.Parse("2006-01-02 15:04:05", *req.CreatedAt)
		if err != nil {
			return err
		}
		userBill.CreatedAt = createdAt
	}

	return s.userBillRepo.Update(ctx, userBill)
}

// Delete 根据ID删除用户账单
func (s *UserBillService) Delete(ctx context.Context, id uint) error {
	return s.userBillRepo.Delete(ctx, id)
}
