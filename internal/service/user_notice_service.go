package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// UserNoticeService 用户通知服务实现
type UserNoticeService struct {
	noticeRepo repo.UserNoticeRepository
}

// NewUserNoticeService 创建用户通知服务
func NewUserNoticeService(noticeRepo repo.UserNoticeRepository) interfaces.IUserNoticeService {
	return &UserNoticeService{
		noticeRepo: noticeRepo,
	}
}

// List 获取用户通知列表
func (s *UserNoticeService) List(ctx context.Context, req *admin.UserNoticeListRequest) ([]*models.UserNotice, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("sender_id", req.SenderID).
		AddCondition("receiver_id", req.ReceiverID).
		AddCondition("title", req.Title).
		AddCondition("summary", req.Summary).
		AddCondition("content", req.Content).
		AddCondition("priority", req.Priority).
		AddCondition("status", req.Status).
		AddCondition("type", req.Type).
		AddCondition("route", req.Route).
		AddCondition("is_read", req.IsRead).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.noticeRepo.DB())

	result, err := s.noticeRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// ListForReceiver 获取接收者是当前用户的通知列表
func (s *UserNoticeService) ListForReceiver(ctx context.Context, userID uint, req *web.UserNoticeListRequest) ([]*web.UserNoticeResponse, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("receiver_id", userID).
		AddCondition("priority", req.Priority).
		AddCondition("type", req.Type).
		AddCondition("is_read", req.IsRead).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.noticeRepo.DB())

	result, err := s.noticeRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	notices := make([]*web.UserNoticeResponse, 0, len(result.Data))
	for _, notice := range result.Data {
		notices = append(notices, &web.UserNoticeResponse{
			ID:       notice.ID,
			SenderID: notice.SenderID,
			Title:    notice.Title,
			Summary:  notice.Summary,
			Content:  notice.Content,
			Priority: notice.Priority,
			Status:   notice.Status,
			Type:     notice.Type,
			Route:    notice.Route,
			IsRead:   notice.IsRead,
		})
	}
	return notices, result.Total, nil
}

// Create 创建用户通知
func (s *UserNoticeService) Create(ctx context.Context, req *admin.UserNoticeCreateRequest) error {
	notice := &models.UserNotice{
		SenderID:   req.SenderID,
		ReceiverID: req.ReceiverID,
		Title:      req.Title,
		Summary:    req.Summary,
		Content:    req.Content,
		Priority:   req.Priority,
		Type:       req.Type,
		Route:      req.Route,
	}
	return s.noticeRepo.Create(ctx, notice)
}

// Update 更新用户通知
func (s *UserNoticeService) Update(ctx context.Context, id uint, req *admin.UserNoticeUpdateRequest) error {
	notice, err := s.noticeRepo.FindByID(ctx, id)
	if err != nil || notice == nil {
		return err
	}

	if req.Title != nil {
		notice.Title = *req.Title
	}
	if req.Summary != nil {
		notice.Summary = *req.Summary
	}
	if req.Content != nil {
		notice.Content = *req.Content
	}
	if req.Priority != nil {
		notice.Priority = *req.Priority
	}
	if req.Type != nil {
		notice.Type = *req.Type
	}
	if req.Route != nil {
		notice.Route = *req.Route
	}

	return s.noticeRepo.Update(ctx, notice)
}

// Delete 删除用户通知
func (s *UserNoticeService) Delete(ctx context.Context, id uint) error {
	return s.noticeRepo.Delete(ctx, id)
}

// BatchDelete 批量删除用户通知
func (s *UserNoticeService) BatchDelete(ctx context.Context, req *admin.UserNoticeBatchDeleteRequest) error {
	return s.noticeRepo.BulkDelete(ctx, req.IDs)
}
