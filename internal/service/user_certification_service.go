package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// UserCertificationService 用户认证服务实现
type UserCertificationService struct {
	logger                logger.Logger
	userCertificationRepo repo.UserCertificationRepository

	userRepo    repo.UserRepository
	managerRepo repo.ManagerRepository
}

// NewUserCertificationService 创建用户认证服务实现
func NewUserCertificationService(logger logger.Logger, userCertificationRepo repo.UserCertificationRepository, managerRepo repo.ManagerRepository, userRepo repo.UserRepository) interfaces.IUserCertificationService {
	return &UserCertificationService{
		logger:                logger,
		userCertificationRepo: userCertificationRepo,
		managerRepo:           managerRepo,
		userRepo:              userRepo,
	}
}

// List 根据查询条件获取用户认证列表, 并返回总数
func (s *UserCertificationService) List(ctx context.Context, req *admin.UserCertificationListReq) ([]*models.UserCertification, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", req.UserID)
	queryBuilder.AddLikeCondition("real_name", req.RealName)
	queryBuilder.AddLikeCondition("id_number", req.IDNumber)
	queryBuilder.AddCondition("type", req.Type)
	queryBuilder.AddCondition("status", req.Status)
	queryBuilder.SetPagination(req.Page, req.PageSize)

	result, err := s.userCertificationRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建用户认证
func (s *UserCertificationService) Create(ctx context.Context, req *admin.UserCertificationCreateReq) (*models.UserCertification, error) {
	userCertification := &models.UserCertification{
		UserID:   req.UserID,
		Mode:     models.UserCertificationModeIdentity,
		RealName: req.RealName,
		IDNumber: req.IDNumber,
		Photo1:   req.Photo1,
		Photo2:   req.Photo2,
		Type:     req.Type,
		Status:   models.UserCertificationStatusPending,
	}
	err := s.userCertificationRepo.Create(ctx, userCertification)
	if err != nil {
		return nil, err
	}
	return userCertification, nil
}

// Details 根据ID获取用户认证详情
func (s *UserCertificationService) Details(ctx context.Context, userID uint) (*web.UserCertificationRes, error) {
	userCertification, err := s.userCertificationRepo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if userCertification == nil {
		return &web.UserCertificationRes{}, nil
	} else {
		return &web.UserCertificationRes{
			ID:        userCertification.ID,
			RealName:  userCertification.RealName,
			IDNumber:  userCertification.IDNumber,
			Photo1:    userCertification.Photo1,
			Photo2:    userCertification.Photo2,
			Photo3:    userCertification.Photo3,
			Address:   userCertification.Address,
			Type:      userCertification.Type,
			Status:    userCertification.Status,
			Reason:    userCertification.Reason,
			CreatedAt: userCertification.CreatedAt,
			UpdatedAt: userCertification.UpdatedAt,
		}, nil
	}
}

// UserCreate 用户创建用户认证
func (s *UserCertificationService) UserCreate(ctx context.Context, userID uint, req *web.UserCertificationReq) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return err
	}

	// 判断用户是否已认证
	userCertification, err := s.userCertificationRepo.FindByUserID(ctx, userID)
	if err != nil {
		return err
	}
	if userCertification != nil {
		userCertification.RealName = req.RealName
		userCertification.IDNumber = req.IDNumber
		userCertification.Photo1 = req.Photo1
		userCertification.Photo2 = req.Photo2
		userCertification.Photo3 = req.Photo3
		userCertification.Address = req.Address
		userCertification.Type = models.UserCertificationTypeIdentity
		userCertification.Status = models.UserCertificationStatusPending
		err = s.userCertificationRepo.Update(ctx, userCertification)
		if err != nil {
			return err
		}
	} else {
		userCertification = &models.UserCertification{
			UserID:   userID,
			Mode:     models.UserCertificationModeIdentity,
			RealName: req.RealName,
			IDNumber: req.IDNumber,
			Photo1:   req.Photo1,
			Photo2:   req.Photo2,
			Photo3:   req.Photo3,
			Address:  req.Address,
			Type:     models.UserCertificationTypeIdentity,
		}
		err = s.userCertificationRepo.Create(ctx, userCertification)
		if err != nil {
			return err
		}
	}

	return nil
}

// Update 更新用户认证
func (s *UserCertificationService) Update(ctx context.Context, id uint, req *admin.UserCertificationUpdateReq) error {
	userCertification, err := s.userCertificationRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if req.RealName != nil {
		userCertification.RealName = *req.RealName
	}
	if req.IDNumber != nil {
		userCertification.IDNumber = *req.IDNumber
	}
	if req.Photo1 != nil {
		userCertification.Photo1 = *req.Photo1
	}
	if req.Photo2 != nil {
		userCertification.Photo2 = *req.Photo2
	}
	if req.Address != nil {
		userCertification.Address = *req.Address
	}
	if req.Status != nil {
		userCertification.Status = *req.Status
	}
	if req.Reason != nil {
		userCertification.Reason = *req.Reason
	}
	return s.userCertificationRepo.Update(ctx, userCertification)
}

// Status 审核用户认证
func (s *UserCertificationService) Status(ctx context.Context, id uint, status int8, req *admin.UserCertificationStatusReq) error {
	userCertification, err := s.userCertificationRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	// 更新状态
	switch status {
	case models.UserCertificationStatusCompleted:
		userCertification.Status = models.UserCertificationStatusCompleted
	case models.UserCertificationStatusRejected:
		userCertification.Status = models.UserCertificationStatusRejected
		userCertification.Reason = *req.Reason
	}

	return s.userCertificationRepo.Update(ctx, userCertification)
}

// Delete 根据ID删除用户认证
func (s *UserCertificationService) Delete(ctx context.Context, id uint) error {
	return s.userCertificationRepo.Delete(ctx, id)
}

// BatchDelete 批量删除用户认证
func (s *UserCertificationService) BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error {
	return s.userCertificationRepo.BulkDelete(ctx, req.IDs)
}
