package service

import (
	handler2 "gin/pkg/socket/handler"
	"log/slog"
	"time"
)

// WebSocketService WebSocket服务实现
type WebSocketService struct {
	userHub  *handler2.Hub
	adminHub *handler2.Hub
	logger   *slog.Logger
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService(logger *slog.Logger) *WebSocketService {
	// 使用默认配置创建Hub
	config := &handler2.Config{
		MaxConnections:    1000,
		HeartbeatInterval: 25 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
		BufferSize:        256,
	}

	return &WebSocketService{
		userHub:  handler2.NewHub(config),
		adminHub: handler2.NewHub(config),
		logger:   logger,
	}
}

// GetUserHub 获取用户Hub
func (s *WebSocketService) GetUserHub() *handler2.Hub {
	return s.userHub
}

// GetAdminHub 获取管理员Hub
func (s *WebSocketService) GetAdminHub() *handler2.Hub {
	return s.adminHub
}

// BroadcastToUsers 向所有用户广播消息
func (s *WebSocketService) BroadcastToUsers(msgType string, content interface{}) error {
	message := handler2.Message{
		Type:    msgType,
		Content: content,
	}
	return s.userHub.Broadcast(message)
}

// BroadcastToAdmins 向所有管理员广播消息
func (s *WebSocketService) BroadcastToAdmins(msgType string, content interface{}) error {
	message := handler2.Message{
		Type:    msgType,
		Content: content,
	}
	return s.adminHub.Broadcast(message)
}

// SendToUser 向指定用户发送消息
func (s *WebSocketService) SendToUser(ownerID uint, msgType string, content interface{}) error {
	message := handler2.Message{
		Type:    msgType,
		Content: content,
	}
	return s.userHub.SendToUser(ownerID, message)
}

// SendToAdmin 向指定管理员发送消息
func (s *WebSocketService) SendToAdmin(ownerID uint, msgType string, content interface{}) error {
	message := handler2.Message{
		Type:    msgType,
		Content: content,
	}
	return s.adminHub.SendToUser(ownerID, message)
}

// GetStats 获取统计信息
func (s *WebSocketService) GetStats() map[string]interface{} {
	userStats := s.userHub.GetStats()
	adminStats := s.adminHub.GetStats()

	return map[string]interface{}{
		"users": map[string]interface{}{
			"connections": userStats["active_connections"],
			"max":         userStats["max_connections"],
		},
		"admins": map[string]interface{}{
			"connections": adminStats["active_connections"],
			"max":         adminStats["max_connections"],
		},
		"total_connections": userStats["active_connections"].(int64) + adminStats["active_connections"].(int64),
	}
}

// SimpleMessageHandler 简化的消息处理器
type SimpleMessageHandler struct {
	service *WebSocketService
	hubType string // "user" 或 "admin"
}

// NewSimpleMessageHandler 创建简化的消息处理器
func NewSimpleMessageHandler(service *WebSocketService, hubType string) *SimpleMessageHandler {
	return &SimpleMessageHandler{
		service: service,
		hubType: hubType,
	}
}

// HandleMessage 处理收到的消息
func (h *SimpleMessageHandler) HandleMessage(conn *handler2.Connection, msg handler2.Message) error {
	// 记录日志
	h.service.logger.Info("websocket message received",
		"type", msg.Type,
		"hubType", h.hubType,
		"ownerID", conn.GetOwnerID(),
		"connectionID", conn.GetID(),
	)

	// 简单的消息处理逻辑
	switch msg.Type {
	case handler2.MessageTypeMessage:
		return h.handleChatMessage(conn, msg)
	case handler2.MessageTypeNotice:
		return h.handleNoticeMessage(conn, msg)
	default:
		return h.handleUnknownMessage(conn, msg)
	}
}

// handleChatMessage 处理聊天消息
func (h *SimpleMessageHandler) handleChatMessage(conn *handler2.Connection, msg handler2.Message) error {
	// 简单回声响应
	response := handler2.Message{
		Type: handler2.MessageTypeMessage,
		Content: map[string]interface{}{
			"from":      conn.GetOwnerID(),
			"message":   msg.Content,
			"timestamp": time.Now().Unix(),
			"hubType":   h.hubType,
			"echo":      true,
		},
	}
	return conn.SendMessage(response)
}

// handleNoticeMessage 处理通知消息
func (h *SimpleMessageHandler) handleNoticeMessage(conn *handler2.Connection, msg handler2.Message) error {
	h.service.logger.Info("notice message received",
		"ownerID", conn.GetOwnerID(),
		"hubType", h.hubType,
		"content", msg.Content,
	)
	return nil
}

// handleUnknownMessage 处理未知消息类型
func (h *SimpleMessageHandler) handleUnknownMessage(conn *handler2.Connection, msg handler2.Message) error {
	h.service.logger.Warn("unknown message type",
		"type", msg.Type,
		"ownerID", conn.GetOwnerID(),
		"hubType", h.hubType,
	)

	// 发送错误响应
	errorResponse := handler2.Message{
		Type: "error",
		Content: map[string]interface{}{
			"error": "Unknown message type",
			"type":  msg.Type,
		},
	}
	return conn.SendMessage(errorResponse)
}
