package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"time"

	"go.uber.org/zap"
)

// LoginLogService 登录日志服务
type LoginLogService struct {
	logger       logger.Logger
	loginLogRepo repo.LoginLogRepository
}

// NewLoginLogService 创建登录日志服务
func NewLoginLogService(logger logger.Logger, loginLogRepo repo.LoginLogRepository) interfaces.ILoginLogService {
	return &LoginLogService{
		logger:       logger,
		loginLogRepo: loginLogRepo,
	}
}

// List 获取登录日志列表
func (s *LoginLogService) List(ctx context.Context, req *admin.LoginLogListRequest) ([]*models.LoginLog, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("username", req.Username).
		AddCondition("ip", req.IP).
		AddCondition("status", req.Status).
		AddCondition("type", req.Type).
		AddCondition("user_id", req.UserID).
		AddCondition("manager_id", req.ManagerID).
		AddCondition("os", req.OS).
		AddCondition("device", req.Device).
		AddConditionWithOperator("created_at", query.OpGte, req.StartTime).
		AddConditionWithOperator("created_at", query.OpLte, req.EndTime).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.loginLogRepo.DB())

	result, err := s.loginLogRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	return result.Data, result.Total, nil
}

// HardDelete 硬删除登录日志
func (s *LoginLogService) HardDelete(ctx context.Context, id uint) error {
	return s.loginLogRepo.HardDelete(ctx, id)
}

// BulkHardDelete 批量硬删除登录日志
func (s *LoginLogService) BulkHardDelete(ctx context.Context, ids []uint) error {
	return s.loginLogRepo.BulkHardDelete(ctx, ids)
}

// CreateUserLoginLog 创建普通用户登录日志
func (s *LoginLogService) CreateUserLoginLog(ctx context.Context, userID uint, username, ip, userAgent string, status int8, message string) error {
	loginLog := models.NewUserLoginLog(userID, username, ip, userAgent, status, message)

	err := s.loginLogRepo.Create(ctx, loginLog)
	if err != nil {
		s.logger.Error("创建用户登录日志失败",
			zap.Uint("userID", userID),
			zap.String("username", username),
			zap.Error(err))
		return err
	}
	return nil
}

// CreateManagerLoginLog 创建管理员登录日志
func (s *LoginLogService) CreateManagerLoginLog(ctx context.Context, managerID uint, username, ip, userAgent string, status int8, message string) error {
	loginLog := models.NewManagerLoginLog(managerID, username, ip, userAgent, status, message)

	err := s.loginLogRepo.Create(ctx, loginLog)
	if err != nil {
		s.logger.Error("创建管理员登录日志失败",
			zap.Uint("managerID", managerID),
			zap.String("username", username),
			zap.Error(err))
		return err
	}
	return nil
}

// CleanupOldLogs 清理旧的登录日志
func (s *LoginLogService) CleanupOldLogs(ctx context.Context, days int) error {
	beforeTime := time.Now().AddDate(0, 0, -days)
	return s.loginLogRepo.DeleteOldLogs(ctx, beforeTime)
}
