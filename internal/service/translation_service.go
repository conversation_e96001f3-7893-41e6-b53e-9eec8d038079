package service

import (
	"context"
	"fmt"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/cache"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// TranslationService 翻译服务实现
type TranslationService struct {
	translationRepo repo.TranslationRepository
	cache           *cache.RedisClient
}

// NewTranslationService 创建翻译服务
func NewTranslationService(translationRepo repo.TranslationRepository, cache *cache.RedisClient) interfaces.ITranslationService {
	return &TranslationService{
		translationRepo: translationRepo,
		cache:           cache,
	}
}

// Create 创建翻译
func (s *TranslationService) Create(ctx context.Context, req *admin.TranslationCreateRequest) error {
	translation := &models.Translation{
		Key:    req.Key,
		Value:  req.Value,
		Lang:   req.Lang,
		Module: req.Module,
		Status: req.Status,
	}
	return s.translationRepo.Create(ctx, translation)
}

// Update 更新翻译
func (s *TranslationService) Update(ctx context.Context, id uint, req *admin.TranslationUpdateRequest) error {
	translation, err := s.translationRepo.FindByID(ctx, id)
	if err != nil || translation == nil {
		return err
	}

	if req.Value != nil {
		translation.Value = *req.Value
	}
	if req.Lang != nil {
		translation.Lang = *req.Lang
	}
	if req.Module != nil {
		translation.Module = *req.Module
	}
	if req.Status != nil {
		translation.Status = *req.Status
	}

	return s.translationRepo.Update(ctx, translation)
}

// Delete 删除翻译
func (s *TranslationService) Delete(ctx context.Context, id uint) error {
	return s.translationRepo.Delete(ctx, id)
}

// GetByID 根据ID获取翻译
func (s *TranslationService) GetByID(ctx context.Context, id uint) (*models.Translation, error) {
	return s.translationRepo.FindByID(ctx, id)
}

// List 获取翻译列表
func (s *TranslationService) List(ctx context.Context, req *admin.TranslationListRequest) ([]*models.Translation, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("key", req.Key).
		AddCondition("lang", req.Lang).
		AddCondition("module", req.Module).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.translationRepo.DB())

	result, err := s.translationRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, err
	}
	return result.Data, nil
}

// BatchDelete 批量删除翻译
func (s *TranslationService) BatchDelete(ctx context.Context, req *admin.TranslationBatchDeleteRequest) error {
	return s.translationRepo.BulkDelete(ctx, req.IDs)
}

// UpdateByKeyAndLang 根据键更新翻译
func (s *TranslationService) UpdateByKeyAndLang(ctx context.Context, key string, value string, lang string) error {
	// 更新翻译
	translation, err := s.translationRepo.FindByKeyAndLang(ctx, key, lang)
	if err != nil || translation == nil {
		zhTranslation, err := s.translationRepo.FindByKeyAndLang(ctx, key, models.LanguageDefault)
		if err != nil || zhTranslation == nil {
			return fmt.Errorf("默认中文翻译不能为空, 请先创建默认中文翻译")
		}

		return s.translationRepo.Create(ctx, &models.Translation{
			Name:   zhTranslation.Name,
			Key:    key,
			Value:  value,
			Lang:   lang,
			Module: zhTranslation.Module,
			Type:   zhTranslation.Type,
		})
	}

	return s.translationRepo.UpdateByKeyAndLang(ctx, key, value, lang)
}

// FindByKeyAndLang 根据键和语言获取翻译
func (s *TranslationService) FindByKeyAndLang(ctx context.Context, key string, lang string) (*models.Translation, error) {
	return s.translationRepo.FindByKeyAndLang(ctx, key, lang)
}

// InitsTranslations 获取初始化翻译
func (s *TranslationService) InitsTranslations(ctx context.Context, locale string) []*web.InitsTranslation {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("status", models.TranslationStatusEnabled)
	queryBuilder.AddCondition("type", models.TranslationTypeLanguage)
	queryBuilder.AddCondition("locale", locale)
	queryBuilder.AddSort("id", "DESC")
	translates, err := s.translationRepo.FindWithQuery(ctx, queryBuilder)
	if err != nil {
		return nil
	}

	translateList := make([]*web.InitsTranslation, 0)
	for _, translate := range translates {
		translateList = append(translateList, &web.InitsTranslation{
			Label: translate.Key,
			Value: translate.Value,
		})
	}
	return translateList
}
