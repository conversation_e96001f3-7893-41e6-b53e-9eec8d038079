package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IFrontMenuService 前台菜单服务接口
type IFrontMenuService interface {
	// List 获取前台菜单列表
	List(ctx context.Context, req *admin.FrontMenuListReq) ([]*models.FrontMenu, int64, error)
	// Update 更新前台菜单
	Update(ctx context.Context, id uint, req *admin.FrontMenuUpdateReq) error
	// Delete 删除前台菜单
	Delete(ctx context.Context, id uint) error

	// GetInitsFrontMenus 获取初始化菜单
	GetInitsFrontMenus(ctx context.Context, locale string) *web.InitsMenus
}
