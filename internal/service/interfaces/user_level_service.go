package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserLevelService 用户等级服务接口
type IUserLevelService interface {
	// Create 创建用户等级（后台管理）
	Create(ctx context.Context, req *admin.UserLevelCreateRequest) error
	// CreateByUser 用户创建等级（前台用户）
	CreateByUser(ctx context.Context, userID uint, req *web.CreateUserLevelRequest) error
	// Update 更新用户等级
	Update(ctx context.Context, id uint, req *admin.UserLevelUpdateRequest) error
	// Delete 删除用户等级
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取用户等级
	GetByID(ctx context.Context, id uint) (*models.UserLevel, error)
	// List 获取用户等级列表
	List(ctx context.Context, req *admin.UserLevelListRequest) ([]*models.UserLevel, int64, error)
	// BatchDelete 批量删除用户等级
	BatchDelete(ctx context.Context, req *admin.UserLevelBatchDeleteRequest) error
}
