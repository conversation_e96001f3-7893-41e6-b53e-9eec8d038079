package interfaces

import (
	"context"
	"gin/internal/dto/common"
	"gin/internal/models"
)

// IPositionService 岗位服务接口
type IPositionService interface {
	// CreatePosition 创建岗位
	CreatePosition(ctx context.Context, position *models.Position) error
	// UpdatePosition 更新岗位
	UpdatePosition(ctx context.Context, position *models.Position) error
	// DeletePosition 删除岗位
	DeletePosition(ctx context.Context, id uint64) error
	// GetPositionByID 根据ID获取岗位
	GetPositionByID(ctx context.Context, id uint64) (*models.Position, error)
	// GetAllPositions 获取所有岗位
	GetAllPositions(ctx context.Context) ([]*models.Position, error)
	// GetPositionsByDeptID 根据部门ID获取岗位
	GetPositionsByDeptID(ctx context.Context, deptID uint64) ([]*models.Position, error)
	// GetPositionsByStatus 根据状态获取岗位
	GetPositionsByStatus(ctx context.Context, status int8) ([]*models.Position, error)
	// GetPositionsByType 根据类型获取岗位
	GetPositionsByType(ctx context.Context, posType int8) ([]*models.Position, error)
	// GetPositionsByLevel 根据级别获取岗位
	GetPositionsByLevel(ctx context.Context, level int8) ([]*models.Position, error)
	// GetPositionsWithPagination 分页获取岗位
	GetPositionsWithPagination(ctx context.Context, pagination *common.Pagination) (*common.PageResult, error)
	// BatchCreatePositions 批量创建岗位
	BatchCreatePositions(ctx context.Context, positions []*models.Position) error
	// BatchDeletePositions 批量删除岗位
	BatchDeletePositions(ctx context.Context, ids []uint64) error
	// CountPositions 统计岗位总数
	CountPositions(ctx context.Context) (int64, error)
}
