package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// ICountryService 国家服务接口
type ICountryService interface {
	// Create 创建国家
	Create(ctx context.Context, req *admin.CountryCreateRequest) error
	// Update 更新国家
	Update(ctx context.Context, id uint, req *admin.CountryUpdateRequest) error
	// Delete 删除国家
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取国家
	GetByID(ctx context.Context, id uint) (*models.Country, error)
	// List 获取国家列表
	List(ctx context.Context, req *admin.CountryListRequest) ([]*models.Country, int64, error)
	// BatchDelete 批量删除国家
	BatchDelete(ctx context.Context, req *admin.CountryBatchDeleteRequest) error
	// GetInitsCountryList 获取国家列表
	GetInitsCountryList(ctx context.Context) []*web.InitsCountrys
}
