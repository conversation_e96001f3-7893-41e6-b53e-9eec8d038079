package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// ILoginLogService 登录日志服务接口
type ILoginLogService interface {
	// List 获取登录日志列表
	List(ctx context.Context, req *admin.LoginLogListRequest) ([]*models.LoginLog, int64, error)
	// HardDelete 硬删除登录日志
	HardDelete(ctx context.Context, id uint) error
	// BulkHardDelete 批量硬删除登录日志
	BulkHardDelete(ctx context.Context, ids []uint) error
	// CreateUserLoginLog 创建用户登录日志
	CreateUserLoginLog(ctx context.Context, userID uint, username, ip, userAgent string, status int8, message string) error
	// CreateManagerLoginLog 创建管理员登录日志
	CreateManagerLoginLog(ctx context.Context, managerID uint, username, ip, userAgent string, status int8, message string) error
	// CleanupOldLogs 清理指定天数之前的登录日志
	CleanupOldLogs(ctx context.Context, days int) error
}
