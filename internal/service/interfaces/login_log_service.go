package interfaces

import (
	"context"
	"gin/internal/models"
)

// ILoginLogService 登录日志服务接口
type ILoginLogService interface {
	CreateLoginLog(ctx context.Context, userID, tenantID uint64, username, ip string, status int8, message string, accessType int8) error
	CreateLoginLogWithUserAgent(ctx context.Context, userID, tenantID uint64, username, ip, userAgent string, status int8, message string, accessType int8) error
	GetLoginLogsByUserID(ctx context.Context, userID uint64, limit int) ([]*models.LoginLog, error)
	GetLatestLoginByUserID(ctx context.Context, userID uint64) (*models.LoginLog, error)
	CleanupOldLogs(ctx context.Context, days int) error
}
