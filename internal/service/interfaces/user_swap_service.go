package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserSwapService 用户闪兑服务
type IUserSwapService interface {
	// List 根据查询条件获取用户闪兑列表, 并返回总数
	List(ctx context.Context, req *admin.UserSwapListReq) ([]*models.UserSwap, int64, error)
	// UserList 根据查询条件获取用户闪兑列表, 并返回总数
	UserList(ctx context.Context, locale string, userID uint, req *web.UserSwapListReq) ([]*web.UserSwapListRes, int64, error)
	// Create 创建用户闪兑
	Create(ctx context.Context, locale string, req *admin.UserSwapCreateReq) (*models.UserSwap, error)
	// UserCreate 用户创建闪兑
	UserCreate(ctx context.Context, locale string, userID uint, req *web.UserSwapCreateReq) error
	// Update 更新用户闪兑
	Update(ctx context.Context, id uint, req *admin.UserSwapUpdateReq) error
	// Delete 根据ID删除用户闪兑
	Delete(ctx context.Context, id uint) error
	// BatchDelete 批量删除用户闪兑
	BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error
}
