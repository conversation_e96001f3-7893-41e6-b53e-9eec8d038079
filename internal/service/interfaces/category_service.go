package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// ICategoryService 分类服务接口
type ICategoryService interface {
	// List 获取分类列表
	List(ctx context.Context, req *admin.CategoryListReq) ([]*models.Category, int64, error)
	// Create 创建分类
	Create(ctx context.Context, req *admin.CategoryCreateReq) error
	// Update 更新分类
	Update(ctx context.Context, id uint, req *admin.CategoryUpdateReq) error
	// Delete 删除分类
	Delete(ctx context.Context, id uint) error
}
