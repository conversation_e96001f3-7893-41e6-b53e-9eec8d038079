package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IArticleService 文章服务接口
type IArticleService interface {
	// Create 创建文章
	Create(ctx context.Context, req *admin.ArticleCreateRequest) error
	// Update 更新文章
	Update(ctx context.Context, id uint, req *admin.ArticleUpdateRequest) error
	// Delete 删除文章
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取文章
	GetByID(ctx context.Context, id uint) (*models.Article, error)
	// List 获取文章列表
	List(ctx context.Context, req *admin.ArticleListRequest) ([]*models.Article, int64, error)
	// Details 获取文章详情
	Details(ctx context.Context, locale string, id uint) (*web.ArticleRes, error)
	// ListWithLocale 查询文章列表(带语言)
	ListWithLocale(ctx context.Context, locale string, req *web.ArticleListRequest) ([]*web.ArticleRes, int64, error)
	// BatchDelete 批量删除文章
	BatchDelete(ctx context.Context, req *admin.ArticleBatchDeleteRequest) error
	// GetNotices 获取公告
	GetNotices(ctx context.Context, locale string) []*web.ArticleNotContentRes
	// GetHelpers 获取帮助中心
	GetHelpers(ctx context.Context, locale string) []*web.ArticleRes
}
