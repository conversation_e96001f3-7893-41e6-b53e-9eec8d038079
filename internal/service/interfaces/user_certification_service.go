package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserCertificationService 用户认证服务
type IUserCertificationService interface {
	// List 根据查询条件获取用户认证列表, 并返回总数
	List(ctx context.Context, req *admin.UserCertificationListReq) ([]*models.UserCertification, int64, error)
	// Create 创建用户认证
	Create(ctx context.Context, req *admin.UserCertificationCreateReq) (*models.UserCertification, error)
	// Details 根据ID获取用户认证详情
	Details(ctx context.Context, userID uint) (*web.UserCertificationRes, error)
	// BatchDelete 批量删除用户认证
	BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error
	// UserCreate 用户创建用户认证
	UserCreate(ctx context.Context, userID uint, req *web.UserCertificationReq) error
	// Update 更新用户认证
	Update(ctx context.Context, id uint, req *admin.UserCertificationUpdateReq) error
	// Status 审核用户认证
	Status(ctx context.Context, id uint, status int8, req *admin.UserCertificationStatusReq) error
	// Delete 根据ID删除用户认证
	Delete(ctx context.Context, id uint) error
}
