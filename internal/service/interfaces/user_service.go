package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserService 用户服务接口
type IUserService interface {
	// Create 创建用户
	Create(ctx context.Context, req *admin.UserCreateRequest) error
	// Update 更新用户
	Update(ctx context.Context, id uint, req *admin.UserUpdateRequest) error
	// Delete 删除用户
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取用户
	GetByID(ctx context.Context, id uint) (*models.User, error)
	// List 获取用户列表
	List(ctx context.Context, req *admin.UserListRequest) ([]*models.User, int64, error)
	// BatchDelete 批量删除用户
	BatchDelete(ctx context.Context, req *admin.UserBatchDeleteRequest) error

	// Register 注册
	Register(ctx context.Context, req *web.RegisterReq) error
	// Login 登录
	Login(ctx context.Context, username, password string) (*models.User, string, error)
	// Logout 登出
	Logout(ctx context.Context, tokenID string, userID uint, username string, ip string, userAgent string) error
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, id uint, oldPassword, newPassword string) error
}
