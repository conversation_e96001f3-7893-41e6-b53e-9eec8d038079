package interfaces

import (
	"context"
	"gin/internal/dto/common"
	"gin/internal/models"
)

// ITenantService 租户服务接口
type ITenantService interface {
	// GetTenantByID 根据ID获取租户
	GetTenantByID(ctx context.Context, id uint64) (*models.Tenant, error)
	// GetTenantByDomain 根据域名获取租户
	GetTenantByDomain(ctx context.Context, domain string) (*models.Tenant, error)
	// GetTenantByCode 根据编码获取租户
	GetTenantByCode(ctx context.Context, code string) (*models.Tenant, error)
	// CreateTenant 创建租户
	CreateTenant(ctx context.Context, tenant *models.Tenant) error
	// UpdateTenant 更新租户
	UpdateTenant(ctx context.Context, tenant *models.Tenant) error
	// DeleteTenant 删除租户
	DeleteTenant(ctx context.Context, id uint64) error
	// ActivateTenant 激活租户
	ActivateTenant(ctx context.Context, id uint64) error
	// DeactivateTenant 停用租户
	DeactivateTenant(ctx context.Context, id uint64) error
	// ExtendExpiration 延长租户过期时间
	ExtendExpiration(ctx context.Context, id uint64, months int) error
	// SetMaxUsers 设置租户最大用户数
	SetMaxUsers(ctx context.Context, id uint64, maxUsers int) error
	// GetActiveTenants 获取所有激活的租户
	GetActiveTenants(ctx context.Context) ([]*models.Tenant, error)
	// GetInactiveTenants 获取所有未激活的租户
	GetInactiveTenants(ctx context.Context) ([]*models.Tenant, error)
	// GetExpiredTenants 获取所有已过期的租户
	GetExpiredTenants(ctx context.Context) ([]*models.Tenant, error)
	// GetExpiringTenants 获取即将过期的租户
	GetExpiringTenants(ctx context.Context, days int) ([]*models.Tenant, error)
	// CountActiveTenants 统计激活租户数
	CountActiveTenants(ctx context.Context) (int64, error)
	// SearchTenants 搜索租户
	SearchTenants(ctx context.Context, keyword string, isActive *bool, page, pageSize int) ([]*models.Tenant, int64, error)
	// ListTenant 分页查询租户
	ListTenant(ctx context.Context, pagination *common.Pagination, params map[string]interface{}) (*common.PageResult, error)
}
