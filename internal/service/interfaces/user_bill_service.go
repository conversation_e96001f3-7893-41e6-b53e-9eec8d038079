package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserBillService 用户账单服务
type IUserBillService interface {
	// List 根据查询条件获取用户账单列表, 并返回总数
	List(ctx context.Context, req *admin.UserBillListReq) ([]*models.UserBill, int64, error)
	// UserBillList 用户账单列表
	UserBillList(ctx context.Context, locale string, userID uint, req *web.WalletBillListReq) ([]*web.WalletBillRes, int64, error)
	// Update 更新用户账单
	Update(ctx context.Context, id uint, req *admin.UserBillUpdateReq) error
	// Delete 根据ID删除用户账单
	Delete(ctx context.Context, id uint) error
}
