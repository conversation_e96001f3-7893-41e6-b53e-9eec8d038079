package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IMenuService 菜单服务接口
type IMenuService interface {
	// GetAllMenus 获取所有菜单
	GetAllMenus(ctx context.Context) ([]*models.Menu, error)
	// GetMenusForUser 根据管理获取菜单
	GetMenusForUser(ctx context.Context, manager *models.Manager) ([]*models.Menu, error)
	// BuildMenuTree 构建菜单树
	BuildMenuTree(menus []*models.Menu, parentID uint) []*models.Menu
	// GetMenuByID 根据ID获取菜单
	GetMenuByID(ctx context.Context, id uint) (*models.Menu, error)
	// List 分页查询菜单列表
	List(ctx context.Context, req *admin.MenuListRequest) ([]*models.Menu, int64, error)
	// Create 创建菜单
	Create(ctx context.Context, req *admin.MenuCreateRequest) (*models.Menu, error)
	// Update 更新菜单
	Update(ctx context.Context, id uint, req *admin.MenuUpdateRequest) error
	// Delete 删除菜单
	Delete(ctx context.Context, id uint) error
	// BatchDelete 批量删除菜单
	BatchDelete(ctx context.Context, ids []uint) error
	// GetMenuPermissions 获取菜单关联的权限
	GetMenuPermissions(ctx context.Context, menuID uint) ([]*models.Permission, error)
	// AssignPermissionsToMenu 为菜单分配权限
	AssignPermissionsToMenu(ctx context.Context, menuID uint, permissionIDs []uint) error
	// RemovePermissionsFromMenu 移除菜单权限
	RemovePermissionsFromMenu(ctx context.Context, menuID uint, permissionIDs []uint) error
}
