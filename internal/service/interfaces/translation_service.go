package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// ITranslationService 翻译服务接口
type ITranslationService interface {
	// Create 创建翻译
	Create(ctx context.Context, req *admin.TranslationCreateRequest) error
	// Update 更新翻译
	Update(ctx context.Context, id uint, req *admin.TranslationUpdateRequest) error
	// Delete 删除翻译
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取翻译
	GetByID(ctx context.Context, id uint) (*models.Translation, error)
	// List 获取翻译列表
	List(ctx context.Context, req *admin.TranslationListRequest) ([]*models.Translation, error)
	// BatchDelete 批量删除翻译
	BatchDelete(ctx context.Context, req *admin.TranslationBatchDeleteRequest) error
	// UpdateByKeyAndLang 根据键更新翻译
	UpdateByKeyAndLang(ctx context.Context, key string, value string, lang string) error
	// FindByKeyAndLang 根据键和语言查询翻译
	FindByKeyAndLang(ctx context.Context, key string, lang string) (*models.Translation, error)
	// InitsTranslations 获取初始化翻译
	InitsTranslations(ctx context.Context, locale string) []*web.InitsTranslation
}
