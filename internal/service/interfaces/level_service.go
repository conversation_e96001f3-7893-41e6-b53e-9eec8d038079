package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// ILevelService 等级服务接口
type ILevelService interface {
	// Create 创建等级
	Create(ctx context.Context, req *admin.LevelCreateRequest) error
	// Update 更新等级
	Update(ctx context.Context, id uint, req *admin.LevelUpdateRequest) error
	// Delete 删除等级
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取等级
	GetByID(ctx context.Context, id uint) (*models.Level, error)
	// List 获取等级列表
	List(ctx context.Context, req *admin.LevelListRequest) ([]*models.Level, int64, error)
	// ListWithLocale 获取等级列表(带语言)
	ListWithLocale(ctx context.Context, locale string) ([]*web.LevelResponse, int64, error)
	// BatchDelete 批量删除等级
	BatchDelete(ctx context.Context, req *admin.LevelBatchDeleteRequest) error
}
