package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserAccountService 用户账户服务
type IUserAccountService interface {
	// List 根据查询条件获取用户账户列表, 并返回总数
	List(ctx context.Context, req *admin.UserAccountListReq) ([]*models.UserAccount, int64, error)
	// Create 创建用户账户
	Create(ctx context.Context, req *admin.UserAccountCreateReq) (*models.UserAccount, error)
	// Update 更新用户账户
	Update(ctx context.Context, id uint, req *admin.UserAccountUpdateReq) error
	// Delete 根据ID删除用户账户
	Delete(ctx context.Context, id uint) error
	// UserCreate 用户创建用户账户
	UserCreate(ctx context.Context, locale string, userID uint, req *web.UserAccountCreateReq) (*models.UserAccount, error)
	// UserUpdate 用户更新用户账户
	UserUpdate(ctx context.Context, locale string, userID uint, id uint, req *web.UserAccountUpdateReq) error
	// UserDelete 用户删除用户账户
	UserDelete(ctx context.Context, locale string, userID uint, id uint, req *web.UserAccountDeleteReq) error
}
