package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IOrderService 订单服务接口
type IOrderService interface {
	// List 获取订单列表
	List(ctx context.Context, req *admin.OrderListReq) ([]*models.Order, int64, error)
	// Create 创建订单
	Create(ctx context.Context, req *admin.OrderCreateReq) error
	// Update 更新订单
	Update(ctx context.Context, id uint, req *admin.OrderUpdateReq) error
	// Delete 删除订单
	Delete(ctx context.Context, id uint) error
}
