package interfaces

import (
	"context"
	"gin/internal/models"
)

// IUserSettingService 用户设置服务接口
type IUserSettingService interface {
	// GetByUserIDAndField 获取用户设置
	GetByUserIDAndField(ctx context.Context, userID uint, field string) (*models.UserSetting, error)
	// SetByUserIDAndField 设置用户设置
	SetByUserIDAndField(ctx context.Context, userID uint, field string, value string, data string) error
	// DeleteByUserIDAndField 删除用户设置
	DeleteByUserIDAndField(ctx context.Context, userID uint, field string) error
}
