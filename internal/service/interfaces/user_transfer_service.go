package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserTransferService 用户转账服务
type IUserTransferService interface {
	// List 根据查询条件获取用户转账列表, 并返回总数
	List(ctx context.Context, req *admin.UserTransferListReq) ([]*models.UserTransfer, int64, error)
	// UserList 根据查询条件获取用户转账列表, 并返回总数
	UserList(ctx context.Context, locale string, userID uint, req *web.UserTransferListReq) ([]*web.UserTransferListRes, int64, error)
	// Create 创建用户转账
	Create(ctx context.Context, locale string, req *admin.UserTransferCreateReq) (*models.UserTransfer, error)
	// UserCreate 用户创建转账
	UserCreate(ctx context.Context, locale string, userID uint, req *web.UserTransferCreateReq) error
	// Update 更新用户转账
	Update(ctx context.Context, id uint, req *admin.UserTransferUpdateReq) error
	// Delete 根据ID删除用户转账
	Delete(ctx context.Context, id uint) error
	// BatchDelete 批量删除用户转账
	BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error
}
