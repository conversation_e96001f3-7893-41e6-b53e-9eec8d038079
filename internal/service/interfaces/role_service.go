package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IRoleService 角色服务接口
type IRoleService interface {
	// GetRoleByID 根据ID获取角色
	GetRoleByID(ctx context.Context, id uint) (*models.Role, error)
	// CreateRole 创建角色
	CreateRole(ctx context.Context, role *models.Role) error
	// UpdateRole 更新角色
	UpdateRole(ctx context.Context, id uint, dto *admin.RoleUpdateRequest) error
	// DeleteRole 删除角色
	DeleteRole(ctx context.Context, id uint) error
	// BatchDeleteRole 批量删除角色
	BatchDeleteRole(ctx context.Context, ids []uint) error
	// ListRole 分页查询角色列表
	ListRole(ctx context.Context, dto *admin.RoleListRequest) ([]*models.Role, int64, error)
}
