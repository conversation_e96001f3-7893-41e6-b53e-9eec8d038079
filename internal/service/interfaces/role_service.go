package interfaces

import (
	"context"
	"gin/internal/dto/common"
	"gin/internal/models"
)

// IRoleService 角色服务接口
type IRoleService interface {
	GetRoleByID(ctx context.Context, id uint64) (*models.Role, error)
	GetRoleByCode(ctx context.Context, code string, tenantID uint64) (*models.Role, error)
	CreateRole(ctx context.Context, role *models.Role) error
	UpdateRole(ctx context.Context, role *models.Role) error
	DeleteRole(ctx context.Context, id uint64) error
	EnableRole(ctx context.Context, id uint64) error
	DisableRole(ctx context.Context, id uint64) error
	GetEnabledRoles(ctx context.Context, tenantID uint64) ([]*models.Role, error)
	GetSystemRoles(ctx context.Context) ([]*models.Role, error)
	ListRole(ctx context.Context, pagination *common.Pagination, params map[string]interface{}) (*common.PageResult, error)
}
