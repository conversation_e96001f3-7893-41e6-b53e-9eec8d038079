package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IProductService 产品服务接口
type IProductService interface {
	// List 获取产品列表
	List(ctx context.Context, req *admin.ProductListReq) ([]*models.Product, int64, error)
	// Create 创建产品
	Create(ctx context.Context, req *admin.ProductCreateReq) error
	// Update 更新产品
	Update(ctx context.Context, id uint, req *admin.ProductUpdateReq) error
	// Delete 删除产品
	Delete(ctx context.Context, id uint) error
}
