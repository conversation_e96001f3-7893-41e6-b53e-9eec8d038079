package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/cache"
	"gin/internal/models"

	"github.com/gin-gonic/gin"
)

// IManagerService 管理员用户服务接口 - 简化版
type IManagerService interface {
	// FindByID 根据ID获取管理员
	FindByID(ctx context.Context, id uint) (*models.Manager, error)
	// FindByUsername 根据用户名获取管理员
	FindByUsername(ctx context.Context, username string) (*models.Manager, error)
	// Create 创建管理员
	Create(ctx context.Context, manager *models.Manager, plainPassword string) error
	// Update 更新管理员
	Update(ctx context.Context, id uint, req *admin.ManageUpdateRequest) error
	// Delete 删除管理员
	Delete(ctx context.Context, id uint) error
	// List 获取管理员列表
	List(ctx context.Context, req *admin.ManageListRequest) ([]*models.Manager, int64, error)
	// VerifyPassword 验证密码
	VerifyPassword(ctx context.Context, username, password string) (*models.Manager, error)
	// Login 登录
	Login(c *gin.Context, username, password string, cache *cache.RedisClient) (*models.Manager, string, error)
	// Logout 登出
	Logout(ctx context.Context, tokenID string, userID uint, username string, ip string, userAgent string, cache *cache.RedisClient) error
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, id uint, oldPassword, newPassword string) error
	// ResetPassword 重置密码
	ResetPassword(ctx context.Context, id uint, newPassword string) error
	// AssignRoles 分配角色
	AssignRoles(ctx context.Context, userID uint, roleCodes []string) error
	// RemoveRoles 移除角色
	RemoveRoles(ctx context.Context, userID uint, roleCodes []string) error
	// GetUserRoles 获取用户角色
	GetUserRoles(ctx context.Context, userID uint) ([]string, error)
	// ReplaceUserRoles 替换用户角色
	ReplaceUserRoles(ctx context.Context, userID uint, roleCodes []string) error
}
