package interfaces

import (
	"context"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/cache"
	"gin/internal/models"

	"github.com/gin-gonic/gin"
)

// IManagerService 管理员用户服务接口
type IManagerService interface {
	GetManagerByID(ctx context.Context, id uint64) (*models.Manager, error)
	GetManagerByUsername(ctx context.Context, username string) (*models.Manager, error)
	CreateManager(ctx context.Context, manager *models.Manager, plainPassword string) error
	UpdateManager(ctx context.Context, manager *models.Manager) error
	DeleteManager(ctx context.Context, id uint64) error
	VerifyPassword(ctx context.Context, username, password string) (*models.Manager, error)
	ChangePassword(ctx context.Context, id uint64, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, id uint64, newPassword string) error
	EnableManager(ctx context.Context, id uint64) error
	DisableManager(ctx context.Context, id uint64) error
	Login(c *gin.Context, username, password string, cache *cache.RedisClient) (*models.Manager, string, error)
	Logout(ctx context.Context, tokenID string, userID uint64, tenantID uint64, username string, ip string, userAgent string, cache *cache.RedisClient) error
	ListManager(ctx context.Context, pagination *common.Pagination, params map[string]interface{}) (*common.PageResult, error)
}
