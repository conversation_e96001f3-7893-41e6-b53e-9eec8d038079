package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/common"
	"gin/internal/models"
)

// IAssetService 资产服务接口
type IAssetService interface {
	// List 获取资产列表
	List(ctx context.Context, req *admin.AssetListReq) ([]*models.Asset, int64, error)
	// Create 创建资产
	Create(ctx context.Context, req *admin.AssetCreateReq) error
	// Update 更新资产
	Update(ctx context.Context, id uint, req *admin.AssetUpdateReq) error
	// Delete 删除资产
	Delete(ctx context.Context, id uint) error
	// BatchDelete 批量删除资产
	BatchDelete(ctx context.Context, req *common.BatchDeleteReq) error
}
