package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IUserAssetService 用户资产服务
type IUserAssetService interface {
	// List 根据查询条件获取用户资产列表, 并返回总数
	List(ctx context.Context, req *admin.UserAssetListReq) ([]*models.UserAsset, int64, error)
	// Create 创建用户资产
	Create(ctx context.Context, req *admin.UserAssetCreateReq) (*models.UserAsset, error)
	// Update 更新用户资产
	Update(ctx context.Context, id uint, req *admin.UserAssetUpdateReq) error
	// OperationBalance 余额操作
	OperationBalance(ctx context.Context, locale string, id uint, req *admin.UserAssetOperationsReq) error
}
