package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IPaymentService 支付服务接口
type IPaymentService interface {
	// Create 创建支付订单
	Create(ctx context.Context, req *admin.PaymentCreateReq) (*models.Payment, error)
	// Update 更新支付订单
	Update(ctx context.Context, id uint, req *admin.PaymentUpdateReq) error
	// Delete 删除支付订单
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取支付订单
	GetByID(ctx context.Context, id uint) (*models.Payment, error)
	// List 获取支付订单列表
	List(ctx context.Context, req *admin.PaymentListReq) ([]*models.Payment, int64, error)
	// BatchDelete 批量删除支付订单
	BatchDelete(ctx context.Context, req *models.Payment) error
	// FindListByMode 根据模式查询支付
	FindListByMode(ctx context.Context, locale string, mode int8) (*web.WalletPaymentsRes, error)
}
