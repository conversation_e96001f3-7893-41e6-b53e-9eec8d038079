package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IOperateLogService 操作日志服务接口
type IOperateLogService interface {
	// List 获取操作日志列表
	List(ctx context.Context, req *admin.OperateLogListRequest) ([]*models.OperateLog, int64, error)
	// HardDelete 硬删除操作日志
	HardDelete(ctx context.Context, id uint) error
	// BulkHardDelete 批量硬删除操作日志
	BulkHardDelete(ctx context.Context, ids []uint) error
	// CreateUserOperateLog 创建用户操作日志
	CreateUserOperateLog(ctx context.Context, userID uint, username, module, description, reqPath, reqParams, resResult string, status int8, errorMsg string) error
	// CreateManagerOperateLog 创建管理员操作日志
	CreateManagerOperateLog(ctx context.Context, managerID uint, username, module, description, reqPath, reqParams, resResult string, status int8, errorMsg string) error
	// CleanupOldLogs 清理指定天数之前的操作日志
	CleanupOldLogs(ctx context.Context, days int) error
}
