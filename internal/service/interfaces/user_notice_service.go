package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserNoticeService 用户通知服务接口
type IUserNoticeService interface {
	// List 获取用户通知列表
	List(ctx context.Context, req *admin.UserNoticeListRequest) ([]*models.UserNotice, int64, error)
	// ListForReceiver 获取接收者是当前用户的通知列表
	ListForReceiver(ctx context.Context, userID uint, req *web.UserNoticeListRequest) ([]*web.UserNoticeResponse, int64, error)
	// Create 创建用户通知
	Create(ctx context.Context, req *admin.UserNoticeCreateRequest) error
	// Update 更新用户通知
	Update(ctx context.Context, id uint, req *admin.UserNoticeUpdateRequest) error
	// Delete 删除用户通知
	Delete(ctx context.Context, id uint) error
	// BatchDelete 批量删除用户通知
	BatchDelete(ctx context.Context, req *admin.UserNoticeBatchDeleteRequest) error
}
