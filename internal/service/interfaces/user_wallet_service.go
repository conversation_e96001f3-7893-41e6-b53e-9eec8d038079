package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// IUserWalletService 用户钱包服务
type IUserWalletService interface {
	// List 根据查询条件获取用户钱包列表, 并返回总数
	List(ctx context.Context, walletType int8, req *admin.UserWalletListReq) ([]*models.UserWallet, int64, error)
	// UserWalletList 用户钱包列表
	UserWalletList(ctx context.Context, userID uint, req *web.WalletListReq) ([]*web.WalletOrderRes, int64, error)
	// UserCreateDeposit 用户创建充值订单
	UserCreateDeposit(ctx context.Context, locale string, userID uint, req *web.WalletDepositReq) error
	// UpdateDepositStatus 更新充值状态
	UpdateDepositStatus(ctx context.Context, locale string, id uint, status int8, req *admin.UserWalletStatusReq) error
	// UserCreateWithdraw 用户创建提现订单
	UserCreateWithdraw(ctx context.Context, locale string, userID uint, req *web.WalletWithdrawReq) error
	// UpdateWithdrawStatus 更新提现状态
	UpdateWithdrawStatus(ctx context.Context, locale string, id uint, status int8, req *admin.UserWalletStatusReq) error
	// Create 创建用户钱包
	Create(ctx context.Context, locale string, accountID uint, req *admin.UserWalletCreateReq) (*models.UserWallet, error)
	// Update 更新用户钱包
	Update(ctx context.Context, id uint, walletType int8, req *admin.UserWalletUpdateReq) error
	// Delete 根据ID删除用户钱包
	Delete(ctx context.Context, id uint) error
}
