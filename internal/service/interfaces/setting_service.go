package interfaces

import (
	"context"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// ISettingService 应用设置服务
type ISettingService interface {
	// List 获取应用设置列表
	List(ctx context.Context, queryReq *admin.SettingListReq) ([]*models.Setting, int64, error)
	// GetByField 跟据字段获取应用设置
	GetByField(field models.SettingField, value interface{}) error
	// GetByFieldString 获取字符串类型的应用设置
	GetByFieldString(field models.SettingField) string
	// GetNoticeTips 获取公告提示词
	GetNoticeTips(ctx context.Context, locale string) *constant.SettingNoticeTips
	// GetTemplateRegister 获取模版注册配置
	GetTemplateRegister(ctx context.Context, locale string) *constant.SettingRegisterTemplate
	// GetTemplateLogin 获取模版登录配置
	GetTemplateLogin(ctx context.Context, locale string) *constant.SettingLoginTemplate
	// GetKeywords 获取关键词
	GetKeywords(ctx context.Context, locale string) string
	// GetCopyright 获取版权
	GetCopyright(ctx context.Context, locale string) string
	// GetIntroduce 获取介绍
	GetIntroduce(ctx context.Context, locale string) string
	// Update 更新应用设置
	Update(ctx context.Context, id uint, body *admin.SettingUpdateReq) error
	// UpdateData 更新应用设置数据
	UpdateData(ctx context.Context, id uint, body *admin.SettingDataUpdateReq) error
}
