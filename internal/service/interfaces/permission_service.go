package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IPermissionService 权限服务接口
type IPermissionService interface {
	// GetAllPermissions 获取所有权限
	GetAllPermissions(ctx context.Context) ([]*models.Permission, error)
	// GetPermissionsByRoleCode 根据角色编码获取权限
	GetPermissionsByRoleCode(ctx context.Context, roleCode string) ([]*models.Permission, error)
	// Create 创建权限
	Create(ctx context.Context, req *admin.PermissionCreateRequest) (*models.Permission, error)
	// Update 更新权限
	Update(ctx context.Context, id uint, req *admin.PermissionUpdateRequest) error
	// GetPermissionByID 根据ID获取权限
	GetPermissionByID(ctx context.Context, id uint) (*models.Permission, error)
	// List 分页查询权限列表
	List(ctx context.Context, req *admin.PermissionListRequest) ([]*models.Permission, int64, error)
	// AssignPermissionsToRole 为角色分配权限
	AssignPermissionsToRole(ctx context.Context, roleCode string, permIds []uint) error
	// RemovePermissionsFromRole 移除角色权限
	RemovePermissionsFromRole(ctx context.Context, roleCode string, permIds []uint) error
}
