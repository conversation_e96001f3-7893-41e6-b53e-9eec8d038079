package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
)

// IManagerNoticeService 管理员通知服务接口
type IManagerNoticeService interface {
	// Create 创建管理员通知
	Create(ctx context.Context, managerID uint, req *admin.ManagerNoticeCreateRequest) error
	// Update 更新管理员通知
	Update(ctx context.Context, id uint, req *admin.ManagerNoticeUpdateRequest) error
	// Delete 删除管理员通知
	Delete(ctx context.Context, id uint) error
	// BatchDelete 批量删除管理员通知
	BatchDelete(ctx context.Context, req *admin.ManagerNoticeBatchDeleteRequest) error
	// ListForSender 获取发送者是当前管理的通知列表
	ListForSender(ctx context.Context, managerID uint, req *admin.ManagerNoticeListRequest) ([]*models.ManagerNotice, int64, error)
	// ListForReceiver 获取接收者是当前管理的通知列表
	ListForReceiver(ctx context.Context, managerID uint, req *admin.ManagerNoticeListRequest) ([]*models.ManagerNotice, int64, error)
	// MarkAsRead 标记管理员通知为已读
	MarkAsRead(ctx context.Context, managerID, id uint) error
	// DeleteForReceiver 删除接收者是当前管理的通知
	DeleteForReceiver(ctx context.Context, managerID, id uint) error
}
