package interfaces

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
)

// ILanguageService 语言服务接口
type ILanguageService interface {
	// Create 创建语言
	Create(ctx context.Context, req *admin.LanguageCreateRequest) error
	// Update 更新语言
	Update(ctx context.Context, id uint, req *admin.LanguageUpdateRequest) error
	// Delete 删除语言
	Delete(ctx context.Context, id uint) error
	// GetByID 根据ID获取语言
	GetByID(ctx context.Context, id uint) (*models.Language, error)
	// List 获取语言列表
	List(ctx context.Context, req *admin.LanguageListRequest) ([]*models.Language, int64, error)
	// BatchDelete 批量删除语言
	BatchDelete(ctx context.Context, req *admin.LanguageBatchDeleteRequest) error
	// GetInitsLanguages 获取语言列表
	GetInitsLanguages(ctx context.Context) []*web.InitsLanguages
}
