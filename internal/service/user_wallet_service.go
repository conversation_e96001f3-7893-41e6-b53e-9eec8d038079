package service

import (
	"context"
	"errors"
	"fmt"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"gin/pkg/utils"
	"github.com/goccy/go-json"
	"time"
)

// UserWalletServiceImpl 用户钱包服务实现
type UserWalletServiceImpl struct {
	logger          logger.Logger
	userWalletRepo  repo.UserWalletRepository
	assetRepo       repo.AssetRepository
	managerRepo     repo.ManagerRepository
	userRepo        repo.UserRepository
	userAssetRepo   repo.UserAssetRepository
	userAccountRepo repo.UserAccountRepository
	paymentRepo     repo.PaymentRepository
	translationRepo repo.TranslationRepository
	settingRepo     repo.SettingRepository
}

// NewUserWalletService 创建用户钱包服务实现
func NewUserWalletService(
	logger logger.Logger,
	userWalletRepo repo.UserWalletRepository,
	assetRepo repo.AssetRepository,
	managerRepo repo.ManagerRepository,
	userRepo repo.UserRepository,
	userAssetRepo repo.UserAssetRepository,
	userAccountRepo repo.UserAccountRepository,
	paymentRepo repo.PaymentRepository,
	translationRepo repo.TranslationRepository,
	settingRepo repo.SettingRepository,
) interfaces.IUserWalletService {
	return &UserWalletServiceImpl{
		logger:          logger,
		userWalletRepo:  userWalletRepo,
		assetRepo:       assetRepo,
		managerRepo:     managerRepo,
		userRepo:        userRepo,
		userAssetRepo:   userAssetRepo,
		userAccountRepo: userAccountRepo,
		paymentRepo:     paymentRepo,
		translationRepo: translationRepo,
		settingRepo:     settingRepo,
	}
}

// List 根据查询条件获取用户钱包列表, 并返回总数
func (s *UserWalletServiceImpl) List(ctx context.Context, walletType int8, req *admin.UserWalletListReq) ([]*models.UserWallet, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("manager_id", req.ManagerID)
	queryBuilder.AddCondition("user_id", req.UserID)
	queryBuilder.AddCondition("asset_id", req.AssetID)
	queryBuilder.AddCondition("type", walletType)
	queryBuilder.AddCondition("status", req.Status)
	queryBuilder.AddConditionWithOperator("created_at", query.OpGte, req.StartTime)
	queryBuilder.AddConditionWithOperator("created_at", query.OpLte, req.EndTime)
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userWalletRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// UserWalletList 用户钱包列表
func (s *UserWalletServiceImpl) UserWalletList(ctx context.Context, userID uint, req *web.WalletListReq) ([]*web.WalletOrderRes, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", userID)
	queryBuilder.AddCondition("asset_id", req.AssetID)
	queryBuilder.AddCondition("type", req.Type)
	queryBuilder.AddCondition("status", req.Status)

	// 添加分页和排序
	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	// 查询用户数据
	result, err := s.userWalletRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	results := make([]*web.WalletOrderRes, 0)
	for _, userWallet := range result.Data {
		results = append(results, &web.WalletOrderRes{
			ID:            userWallet.ID,
			AssetID:       userWallet.AssetID,
			OrderSN:       userWallet.OrderSN,
			Amount:        userWallet.Amount,
			ArrivalAmount: userWallet.ArrivalAmount,
			FixedFee:      userWallet.FixedFee,
			RateFee:       userWallet.RateFee,
			Type:          userWallet.Type,
			Status:        userWallet.Status,
			Reason:        userWallet.Reason,
			CreatedAt:     userWallet.CreatedAt,
			UpdatedAt:     userWallet.UpdatedAt,
		})
	}
	return results, result.Total, nil
}

// UserCreateDeposit 用户创建充值
func (s *UserWalletServiceImpl) UserCreateDeposit(ctx context.Context, locale string, userID uint, req *web.WalletDepositReq) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	// 获取支付密码
	basicTemplate := constant.SettingBasicTemplate{}
	if err = s.settingRepo.GetFieldValues(models.SettingTemplateBasic, &basicTemplate); err != nil {
		return err
	}
	if utils.HasStringSlice(basicTemplate.Checkbox, constant.SettingBasicTemplateCheckboxDepositPassword) {
		if !user.CompareHashAndSecurityKey(req.SecurityKey) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSecurityKeyIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
	}

	// 获取支付信息 并且是 充值的
	payment, err := s.paymentRepo.FindByID(ctx, req.PaymentID)
	if err != nil {
		return err
	}

	// 判断支付方式是否是充值, 如果不是, 那么返回系统错误
	if payment.Mode != models.PaymentModeDeposit {
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSystemError.Error(), locale)
		return errors.New(translate.Value)
	}

	// 判断充值金额范围
	if req.Amount < payment.MinAmount || req.Amount > payment.MaxAmount {
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrDepositAmountRange.Error(), locale)
		return fmt.Errorf("%s[min:%f,max:%f]", translate.Value, payment.MinAmount, payment.MaxAmount)
	}

	// 创建充值订单
	depositFee := req.Amount*payment.RateFee/100 + payment.FixedFee
	userWallet := &models.UserWallet{
		UserID:        user.ID,
		AssetID:       req.AssetID,
		Type:          models.UserWalletTypeDeposit,
		Amount:        req.Amount,
		ArrivalAmount: req.Amount - depositFee,
		FixedFee:      payment.FixedFee,
		RateFee:       payment.RateFee,
		OrderSN:       utils.NewRandomizer().OrderID("SN"),
		Status:        models.UserWalletStatusPending,
		Data:          payment.Data,
	}

	return s.userWalletRepo.Create(ctx, userWallet)
}

// UpdateDepositStatus 更新充值状态
func (s *UserWalletServiceImpl) UpdateDepositStatus(ctx context.Context, locale string, id uint, status int8, req *admin.UserWalletStatusReq) error {
	walletOrder, err := s.userWalletRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取用户钱包失败: %w", err)
	}

	// 获取用户
	user, err := s.userRepo.FindByID(ctx, walletOrder.UserID)
	if err != nil {
		return fmt.Errorf("获取用户失败: %w", err)
	}

	// 获取用户资产
	userAsset, err := s.userAssetRepo.FindByUserIDAndAssetIDWithCreate(ctx, user, walletOrder.AssetID)
	if err != nil {
		return fmt.Errorf("获取用户资产失败: %w", err)
	}
	fmt.Println(userAsset)
	walletOrder.Reason = req.Reason
	//return s.userWalletRepo.StatusDeposit(ctx, locale, status, userAsset, walletOrder)
	return nil
}

// Create 创建提现
func (s *UserWalletServiceImpl) Create(ctx context.Context, locale string, accountID uint, req *admin.UserWalletCreateReq) (*models.UserWallet, error) {
	// 获取用户信息
	user, err := s.userRepo.FindByID(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// 获取用户资产, 如果用户资产不存在, 那么创建用户资产
	asset, err := s.assetRepo.FindByID(ctx, req.AssetID)
	if err != nil || asset == nil {
		return nil, constant.ErrAssetNotExists
	}
	userAsset, err := s.userAssetRepo.FindByUserIDAndAssetIDWithCreate(ctx, user, asset.ID)
	if err != nil {
		return nil, fmt.Errorf("创建用户资产失败: %w", err)
	}
	fmt.Println(userAsset)

	// 获取用户提现账户, 如果为0, 那么选择一个提现账户
	var account *models.UserAccount
	if accountID > 0 {
		account, err = s.userAccountRepo.FindByIDAndAssetID(ctx, accountID, req.AssetID)
		if err != nil {
			return nil, fmt.Errorf("获取用户提现账户失败: %w", err)
		}
	} else {
		// 获取用户提现账户, 如果为0, 那么选择一个提现账户
		account, err = s.userAccountRepo.FindByUserIDAndAssetID(ctx, user.ID, req.AssetID)
		if err != nil {
			return nil, fmt.Errorf("获取用户提现账户失败: %w", err)
		}
	}
	fmt.Println(account)

	// 创建提现订单
	//withdrawFee := req.Amount*account.Payment.RateFee/100 + account.Payment.FixedFee
	//userWallet := &models.UserWallet{
	//	UserID:        user.ID,
	//	AssetID:       req.AssetID,
	//	Type:          models.UserWalletTypeWithdrawal,
	//	Amount:        req.Amount,
	//	ArrivalAmount: req.Amount - withdrawFee,
	//	FixedFee:      account.Payment.FixedFee,
	//	RateFee:       account.Payment.RateFee,
	//	SourceID:      accountID,
	//	OrderSN:       utils.NewRandomizer().OrderID("SN"),
	//	Status:        models.UserWalletStatusPending,
	//	Data:          account.Data,
	//}
	//return s.userWalletRepo.CreateWithdraw(ctx, locale, userAsset, userWallet)
	return nil, nil
}

// UserCreateWithdraw 用户创建提现
func (s *UserWalletServiceImpl) UserCreateWithdraw(ctx context.Context, locale string, userID uint, req *web.WalletWithdrawReq) error {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}

	// 获取支付密码
	basicTemplate := constant.SettingBasicTemplate{}
	err = s.settingRepo.GetFieldValues(models.SettingTemplateBasic, &basicTemplate)
	if err != nil {
		return err
	}
	if utils.HasStringSlice(basicTemplate.Checkbox, constant.SettingBasicTemplateCheckboxWithdrawPassword) {
		if !user.CompareHashAndSecurityKey(req.SecurityKey) {
			translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrSecurityKeyIncorrect.Error(), locale)
			return errors.New(translate.Value)
		}
	}

	// 获取用户提现账户
	account, err := s.userAccountRepo.FindByIDAndUserID(ctx, req.AccountID, user.ID)
	if err != nil {
		return fmt.Errorf("获取用户提现账户失败: %w", err)
	}

	// 判断提现金额范围
	if req.Amount < account.Payment.MinAmount || req.Amount > account.Payment.MaxAmount {
		translate, _ := s.translationRepo.FindByKeyAndLang(ctx, constant.ErrDepositAmountRange.Error(), locale)
		return fmt.Errorf("%s[min:%f,max:%f]", translate.Value, account.Payment.MinAmount, account.Payment.MaxAmount)
	}

	// 获取用户资产
	//userAsset, err := s.userAssetRepo.FindByUserIDAndAssetIDWithCreate(ctx, user, account.AssetID)
	//if err != nil {
	//	return fmt.Errorf("获取用户资产失败: %w", err)
	//}
	//
	//// 创建提现订单
	//withdrawFee := req.Amount*account.Payment.RateFee/100 + account.Payment.FixedFee
	//userWallet := &models.UserWallet{
	//	UserID:        user.ID,
	//	AssetID:       account.AssetID,
	//	Type:          models.UserWalletTypeWithdrawal,
	//	Amount:        req.Amount,
	//	ArrivalAmount: req.Amount - withdrawFee,
	//	FixedFee:      account.Payment.FixedFee,
	//	RateFee:       account.Payment.RateFee,
	//	SourceID:      account.ID,
	//	OrderSN:       utils.NewRandomizer().OrderID("SN"),
	//	Status:        models.UserWalletStatusPending,
	//	Data:          account.Data,
	//}
	//_, err = s.userWalletRepo.CreateWithdraw(ctx, locale, userAsset, userWallet)
	//if err != nil {
	//	return err
	//}
	return nil
}

// UpdateWithdrawStatus 更新提现状态
func (s *UserWalletServiceImpl) UpdateWithdrawStatus(ctx context.Context, locale string, id uint, status int8, req *admin.UserWalletStatusReq) error {
	walletOrder, err := s.userWalletRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取用户钱包失败: %w", err)
	}

	// 获取用户
	user, err := s.userRepo.FindByID(ctx, walletOrder.UserID)
	if err != nil {
		return fmt.Errorf("获取用户失败: %w", err)
	}

	// 获取用户资产
	userAsset, err := s.userAssetRepo.FindByUserIDAndAssetIDWithCreate(ctx, user, walletOrder.AssetID)
	if err != nil {
		return fmt.Errorf("获取用户资产失败: %w", err)
	}
	fmt.Println(userAsset)
	walletOrder.Reason = req.Reason
	//return s.userWalletRepo.StatusWithdraw(ctx, locale, status, userAsset, walletOrder)
	return nil
}

// Update 更新用户钱包
func (s *UserWalletServiceImpl) Update(ctx context.Context, id uint, walletType int8, req *admin.UserWalletUpdateReq) error {
	userWallet, err := s.userWalletRepo.FindByIDAndType(ctx, id, walletType)
	if err != nil {
		return fmt.Errorf("获取用户钱包失败: %w", err)
	}
	if req.Proof != nil {
		userWallet.Proof = *req.Proof
	}
	if req.Amount != nil {
		userWallet.Amount = *req.Amount
	}
	if req.FixedFee != nil {
		userWallet.FixedFee = *req.FixedFee
	}
	if req.RateFee != nil {
		userWallet.RateFee = *req.RateFee
	}
	if req.Status != nil {
		userWallet.Status = *req.Status
	}
	if req.Reason != nil {
		userWallet.Reason = *req.Reason
	}
	if req.CreatedAt != nil {
		createdAt, err := time.Parse("2006-01-02 15:04:05", *req.CreatedAt)
		if err != nil {
			return err
		}
		userWallet.CreatedAt = createdAt
	}
	if req.UpdatedAt != nil {
		updatedAt, err := time.Parse("2006-01-02 15:04:05", *req.UpdatedAt)
		if err != nil {
			return err
		}
		userWallet.UpdatedAt = updatedAt
	}
	if req.Data != nil {
		dataBytes, err := json.Marshal(req.Data)
		if err != nil {
			return err
		}
		userWallet.Data = string(dataBytes)
	}
	return s.userWalletRepo.Update(ctx, userWallet)
}

// Delete 根据ID删除用户钱包
func (s *UserWalletServiceImpl) Delete(ctx context.Context, id uint) error {
	return s.userWalletRepo.Delete(ctx, id)
}
