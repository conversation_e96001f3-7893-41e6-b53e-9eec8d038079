package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// ProductService 产品服务实现
type ProductService struct {
	productRepo repo.ProductRepository
}

// NewProductService 创建产品服务
func NewProductService(productRepo repo.ProductRepository) interfaces.IProductService {
	return &ProductService{
		productRepo: productRepo,
	}
}

// List 获取产品列表
func (s *ProductService) List(ctx context.Context, req *admin.ProductListReq) ([]*models.Product, int64, error) {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("name", req.Name).
		AddCondition("category_id", req.CategoryID).
		Build(s.productRepo.DB())

	result, err := s.productRepo.FindWithPagination(ctx, builder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建产品
func (s *ProductService) Create(ctx context.Context, req *admin.ProductCreateReq) error {
	product := &models.Product{
		Name:        req.Name,
		Description: req.Description,
		CategoryID:  req.CategoryID,
	}
	return s.productRepo.Create(ctx, product)
}

// Update 更新产品
func (s *ProductService) Update(ctx context.Context, id uint, req *admin.ProductUpdateReq) error {
	product, err := s.productRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return nil
	}

	if req.Name != nil {
		product.Name = *req.Name
	}
	if req.Description != nil {
		product.Description = *req.Description
	}
	if req.CategoryID != nil {
		product.CategoryID = *req.CategoryID
	}
	return s.productRepo.Update(ctx, product)
}

// Delete 删除产品
func (s *ProductService) Delete(ctx context.Context, id uint) error {
	return s.productRepo.Delete(ctx, id)
}
