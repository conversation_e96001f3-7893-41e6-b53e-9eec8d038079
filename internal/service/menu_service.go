package service

import (
	"context"
	"errors"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"sort"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// MenuService 菜单服务实现
type MenuService struct {
	logger             logger.Logger
	menuRepo           repo.MenuRepository
	menuPermissionRepo repo.MenuPermissionRepository
	permissionRepo     repo.PermissionRepository
	enforcer           casbin.IEnforcer
}

// NewMenuService 创建菜单服务
func NewMenuService(
	logger logger.Logger,
	menuRepo repo.MenuRepository,
	menuPermissionRepo repo.MenuPermissionRepository,
	permissionRepo repo.PermissionRepository,
	enforcer casbin.IEnforcer,
) interfaces.IMenuService {
	return &MenuService{
		logger:             logger,
		menuRepo:           menuRepo,
		menuPermissionRepo: menuPermissionRepo,
		permissionRepo:     permissionRepo,
		enforcer:           enforcer,
	}
}

// GetAllMenus 获取所有菜单
func (s *MenuService) GetAllMenus(ctx context.Context) ([]*models.Menu, error) {
	menus, err := s.menuRepo.FindAllMenus(ctx)
	if err != nil {
		s.logger.Error("获取所有菜单失败", zap.Error(err))
		return nil, err
	}
	return menus, nil
}

// GetMenusForUser 根据用户获取菜单
func (s *MenuService) GetMenusForUser(ctx context.Context, manager *models.Manager) ([]*models.Menu, error) {
	// 获取用户角色权限
	if s.enforcer == nil {
		return []*models.Menu{}, nil
	}

	userIDStr := strconv.FormatUint(uint64(manager.ID), 10)
	roles, _ := s.enforcer.GetEnforcer().GetRolesForUser(userIDStr)

	if len(roles) == 0 {
		roles, _ = s.enforcer.GetEnforcer().GetRolesForUser(manager.Username)
	}

	var allPermissions [][]string
	for _, role := range roles {
		if rolePerms, err := s.enforcer.GetEnforcer().GetPermissionsForUser(role); err == nil {
			allPermissions = append(allPermissions, rolePerms...)
		}
	}

	// 提取权限代码
	codeSet := make(map[string]bool)
	for _, perm := range allPermissions {
		if len(perm) >= 2 {
			if code := strings.TrimSpace(perm[1]); code != "" && !strings.HasPrefix(code, "/") {
				codeSet[code] = true
			}
		}
	}

	permCodes := make([]string, 0, len(codeSet))
	for code := range codeSet {
		permCodes = append(permCodes, code)
	}

	if len(permCodes) == 0 {
		return []*models.Menu{}, nil
	}

	// 根据权限代码获取菜单
	return s.menuRepo.FindMenusByPermissionCodes(ctx, permCodes)
}

// BuildMenuTree 构建菜单树
func (s *MenuService) BuildMenuTree(menus []*models.Menu, parentID uint) []*models.Menu {
	menuMap := make(map[uint]*models.Menu)
	for _, menu := range menus {
		menuMap[menu.ID] = menu
	}

	var result []*models.Menu
	for _, menu := range menus {
		if menu.ParentID == parentID {
			// 递归构建子菜单
			var children []*models.Menu
			for _, childMenu := range menus { // 遍历原始切片而不是map
				if childMenu.ParentID == menu.ID {
					childMenu.Children = s.BuildMenuTree(menus, childMenu.ID)
					children = append(children, childMenu)
				}
			}

			// 对子菜单按sort字段排序
			sort.Slice(children, func(i, j int) bool {
				return children[i].Sort < children[j].Sort
			})

			menu.Children = children
			result = append(result, menu)
		}
	}

	// 对同级菜单按sort字段排序
	sort.Slice(result, func(i, j int) bool {
		return result[i].Sort < result[j].Sort
	})

	return result
}

// GetMenuByID 根据ID获取菜单
func (s *MenuService) GetMenuByID(ctx context.Context, id uint) (*models.Menu, error) {
	return s.menuRepo.FindByID(ctx, id)
}

// List 分页查询菜单列表
func (s *MenuService) List(ctx context.Context, req *admin.MenuListRequest) ([]*models.Menu, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("name", req.Name).
		AddCondition("code", req.Code).
		AddCondition("type", req.Type).
		AddCondition("status", req.Status).
		AddCondition("parent_id", req.ParentID).
		AddCondition("path", req.Path).
		Build(s.menuRepo.DB())

	result, err := s.menuRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建菜单
func (s *MenuService) Create(ctx context.Context, req *admin.MenuCreateRequest) (*models.Menu, error) {
	// 验证菜单
	if err := s.validateMenu(ctx, req.Name, req.Code, req.Path, 0); err != nil {
		return nil, err
	}

	menu := &models.Menu{
		Name:      req.Name,
		Code:      req.Code,
		Type:      req.Type,
		Status:    req.Status,
		ParentID:  req.ParentID,
		Sort:      req.Sort,
		Path:      req.Path,
		Component: req.Component,
		Redirect:  req.Redirect,
		Remark:    req.Remark,
		IsSystem:  false,
	}

	// 设置默认状态
	if menu.Status == 0 {
		menu.Status = models.MenuStatusEnabled
	}

	// 处理元数据
	if req.Meta != nil {
		menu.Meta = req.Meta.ConvertToMenuMeta()
	}

	// 创建菜单
	if err := s.menuRepo.Create(ctx, menu); err != nil {
		s.logger.Error("创建菜单失败", zap.Error(err))
		return nil, err
	}

	// 关联权限
	if len(req.PermissionIDs) > 0 {
		if err := s.menuPermissionRepo.BatchCreateMenuPermissions(ctx, menu.ID, req.PermissionIDs); err != nil {
			s.logger.Error("关联菜单权限失败", zap.Uint("menuId", menu.ID), zap.Error(err))
			// 不回滚菜单创建，只记录错误
		}
	}

	return menu, nil
}

// Update 更新菜单
func (s *MenuService) Update(ctx context.Context, id uint, req *admin.MenuUpdateRequest) error {
	// 获取现有菜单
	menu, err := s.menuRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if menu == nil {
		return errors.New("菜单不存在")
	}

	// 验证菜单
	name := menu.Name
	code := menu.Code
	path := menu.Path
	if req.Name != nil {
		name = *req.Name
	}
	if req.Code != nil {
		code = *req.Code
	}
	if req.Path != nil {
		path = *req.Path
	}

	if err := s.validateMenu(ctx, name, code, path, id); err != nil {
		return err
	}

	// 更新字段
	if req.Name != nil {
		menu.Name = *req.Name
	}
	if req.Code != nil {
		menu.Code = *req.Code
	}
	if req.Type != nil {
		menu.Type = *req.Type
	}
	if req.Status != nil {
		menu.Status = *req.Status
	}
	if req.ParentID != nil {
		menu.ParentID = *req.ParentID
	}
	if req.Sort != nil {
		menu.Sort = *req.Sort
	}
	if req.Path != nil {
		menu.Path = *req.Path
	}
	if req.Component != nil {
		menu.Component = *req.Component
	}
	if req.Redirect != nil {
		menu.Redirect = *req.Redirect
	}
	if req.Remark != nil {
		menu.Remark = *req.Remark
	}

	// 处理元数据
	if req.Meta != nil {
		menu.Meta = req.Meta.ConvertToMenuMeta()
	}

	// 更新菜单
	if err := s.menuRepo.Update(ctx, menu); err != nil {
		s.logger.Error("更新菜单失败", zap.Uint("id", id), zap.Error(err))
		return err
	}

	// 更新权限关联
	if req.PermissionIDs != nil {
		if err := s.menuPermissionRepo.UpdateMenuPermissions(ctx, id, req.PermissionIDs); err != nil {
			s.logger.Error("更新菜单权限关联失败", zap.Uint("menuId", id), zap.Error(err))
			// 不回滚菜单更新，只记录错误
		}
	}

	return nil
}

// Delete 删除菜单
func (s *MenuService) Delete(ctx context.Context, id uint) error {
	// 检查是否存在子菜单
	children, err := s.menuRepo.FindChildrenByParentID(ctx, id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return errors.New("存在子菜单，无法删除")
	}

	// 删除菜单权限关联
	if err := s.menuPermissionRepo.DeleteByMenuID(ctx, id); err != nil {
		s.logger.Error("删除菜单权限关联失败", zap.Uint("menuId", id), zap.Error(err))
	}

	// 删除菜单
	return s.menuRepo.Delete(ctx, id)
}

// BatchDelete 批量删除菜单
func (s *MenuService) BatchDelete(ctx context.Context, ids []uint) error {
	for _, id := range ids {
		if err := s.Delete(ctx, id); err != nil {
			s.logger.Error("批量删除菜单失败", zap.Uint("id", id), zap.Error(err))
			return err
		}
	}
	return nil
}

// GetMenuPermissions 获取菜单关联的权限
func (s *MenuService) GetMenuPermissions(ctx context.Context, menuID uint) ([]*models.Permission, error) {
	return s.menuPermissionRepo.GetPermissionsByMenuID(ctx, menuID)
}

// AssignPermissionsToMenu 为菜单分配权限
func (s *MenuService) AssignPermissionsToMenu(ctx context.Context, menuID uint, permissionIDs []uint) error {
	return s.menuPermissionRepo.UpdateMenuPermissions(ctx, menuID, permissionIDs)
}

// RemovePermissionsFromMenu 移除菜单权限
func (s *MenuService) RemovePermissionsFromMenu(ctx context.Context, menuID uint, permissionIDs []uint) error {
	for _, permID := range permissionIDs {
		if err := s.menuPermissionRepo.DeleteByMenuAndPermission(ctx, menuID, permID); err != nil {
			s.logger.Error("移除菜单权限失败",
				zap.Uint("menuId", menuID),
				zap.Uint("permissionId", permID),
				zap.Error(err))
			return err
		}
	}
	return nil
}

// validateMenu 验证菜单
func (s *MenuService) validateMenu(ctx context.Context, name, code, path string, excludeID uint) error {
	// 验证名称唯一性
	if existing, err := s.menuRepo.FindByName(ctx, name); err != nil {
		return err
	} else if existing != nil && existing.ID != excludeID {
		return errors.New("菜单名称已存在")
	}

	// 验证编码唯一性
	if existing, err := s.menuRepo.FindByCode(ctx, code); err != nil {
		return err
	} else if existing != nil && existing.ID != excludeID {
		return errors.New("菜单编码已存在")
	}

	// 验证路径唯一性（如果有路径）
	if path != "" {
		if existing, err := s.menuRepo.FindByPath(ctx, path); err != nil {
			return err
		} else if existing != nil && existing.ID != excludeID {
			return errors.New("菜单路径已存在")
		}
	}

	return nil
}
