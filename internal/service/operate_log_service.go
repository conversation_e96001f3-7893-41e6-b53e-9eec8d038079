package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"go.uber.org/zap"
	"time"
)

// OperateLogService 操作日志服务
type OperateLogService struct {
	logger         logger.Logger
	operateLogRepo repo.OperateLogRepository
}

// NewOperateLogService 创建操作日志服务
func NewOperateLogService(logger logger.Logger, operateLogRepo repo.OperateLogRepository) interfaces.IOperateLogService {
	return &OperateLogService{
		logger:         logger,
		operateLogRepo: operateLogRepo,
	}
}

// List 获取操作日志列表
func (s *OperateLogService) List(ctx context.Context, req *admin.OperateLogListRequest) ([]*models.OperateLog, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("username", req.Username).
		AddCondition("module", req.Module).
		AddCondition("type", req.Type).
		AddCondition("mode", req.Mode).
		AddCondition("user_id", req.UserID).
		AddCondition("manager_id", req.ManagerID).
		AddConditionWithOperator("created_at", query.OpGte, req.StartTime).
		AddConditionWithOperator("created_at", query.OpLte, req.EndTime).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.operateLogRepo.DB())

	result, err := s.operateLogRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	return result.Data, result.Total, nil
}

// HardDelete 硬删除操作日志
func (s *OperateLogService) HardDelete(ctx context.Context, id uint) error {
	return s.operateLogRepo.HardDelete(ctx, id)
}

// BulkHardDelete 批量硬删除操作日志
func (s *OperateLogService) BulkHardDelete(ctx context.Context, ids []uint) error {
	return s.operateLogRepo.BulkHardDelete(ctx, ids)
}

// CreateUserOperateLog 创建用户操作日志
func (s *OperateLogService) CreateUserOperateLog(ctx context.Context, userID uint, username, module, description, reqPath, reqParams, resResult string, status int8, errorMsg string) error {
	operateLog := models.NewUserOperateLog(userID, username, module, description, reqPath, reqParams, resResult, status, errorMsg)

	err := s.operateLogRepo.Create(ctx, operateLog)
	if err != nil {
		s.logger.Error("创建用户操作日志失败",
			zap.Uint("userID", userID),
			zap.String("username", username),
			zap.Error(err))
		return err
	}
	return nil
}

// CreateManagerOperateLog 创建管理员操作日志
func (s *OperateLogService) CreateManagerOperateLog(ctx context.Context, managerID uint, username, module, description, reqPath, reqParams, resResult string, status int8, errorMsg string) error {
	operateLog := models.NewManagerOperateLog(managerID, username, module, description, reqPath, reqParams, resResult, status, errorMsg)

	err := s.operateLogRepo.Create(ctx, operateLog)
	if err != nil {
		s.logger.Error("创建管理员操作日志失败",
			zap.Uint("managerID", managerID),
			zap.String("username", username),
			zap.Error(err))
		return err
	}
	return nil
}

// CleanupOldLogs 清理指定天数之前的操作日志
func (s *OperateLogService) CleanupOldLogs(ctx context.Context, days int) error {
	beforeTime := time.Now().AddDate(0, 0, -days)
	return s.operateLogRepo.DeleteOldLogs(ctx, beforeTime)
}
