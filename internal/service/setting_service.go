package service

import (
	"context"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"github.com/goccy/go-json"
)

// SettingService 应用设置服务实现
type SettingService struct {
	logger          logger.Logger
	settingRepo     repo.SettingRepository
	translationRepo repo.TranslationRepository
}

// NewSettingService 创建应用设置服务
func NewSettingService(logger logger.Logger, settingRepo repo.SettingRepository, translationRepo repo.TranslationRepository) interfaces.ISettingService {
	return &SettingService{
		logger:          logger,
		settingRepo:     settingRepo,
		translationRepo: translationRepo,
	}
}

// List 获取应用设置列表
func (s *SettingService) List(ctx context.Context, req *admin.SettingListReq) ([]*models.Setting, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("group_id", req.GroupID)
	queryBuilder.AddLikeCondition("name", req.Name)

	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.settingRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	return result.Data, result.Total, nil
}

// GetByField 获取应用设置
func (s *SettingService) GetByField(field models.SettingField, value interface{}) error {
	return s.settingRepo.GetFieldValues(field, value)
}

// GetByFieldString 获取字符串类型的应用设置
func (s *SettingService) GetByFieldString(field models.SettingField) string {
	setting, err := s.settingRepo.FindByField(context.Background(), string(field))
	if err != nil {
		return ""
	}
	return setting.Value
}

// GetNoticeTips 获取公告提示词
func (s *SettingService) GetNoticeTips(ctx context.Context, locale string) *constant.SettingNoticeTips {
	setting := &constant.SettingNoticeTips{}
	if err := s.GetByField(models.SettingTipsNotice, setting); err != nil {
		return nil
	}
	// 获取公告提示词
	noticeTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsNotice, locale)
	setting.Notice = noticeTranslate.Value

	// 获取用户冻结提示词
	userFreezeTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsUserFreeze, locale)
	setting.UserFreeze = userFreezeTranslate.Value

	// 获取用户信用分提示词
	userScoreTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsUserScore, locale)
	setting.UserScore = userScoreTranslate.Value

	return setting
}

// GetTemplateRegister 获取模版注册配置
func (s *SettingService) GetTemplateRegister(ctx context.Context, locale string) *constant.SettingRegisterTemplate {
	setting := &constant.SettingRegisterTemplate{}
	if err := s.GetByField(models.SettingTemplateRegister, setting); err != nil {
		return nil
	}

	// 获取注册提示词
	tipTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsRegister, locale)
	setting.Tips = tipTranslate.Value
	return setting
}

// GetTemplateLogin 获取模版登录配置
func (s *SettingService) GetTemplateLogin(ctx context.Context, locale string) *constant.SettingLoginTemplate {
	setting := &constant.SettingLoginTemplate{}
	if err := s.GetByField(models.SettingTemplateLogin, setting); err != nil {
		return nil
	}

	// 获取登录提示词
	tipTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsLogin, locale)
	setting.Tips = tipTranslate.Value
	return setting
}

// GetKeywords 获取关键词
func (s *SettingService) GetKeywords(ctx context.Context, locale string) string {
	keywordsTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsSiteDesc, locale)
	return keywordsTranslate.Value
}

// GetCopyright 获取版权
func (s *SettingService) GetCopyright(ctx context.Context, locale string) string {
	copyrightTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsSiteCopyright, locale)
	return copyrightTranslate.Value
}

// GetIntroduce 获取介绍
func (s *SettingService) GetIntroduce(ctx context.Context, locale string) string {
	introduceTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, models.SettingTipsSiteIntroduce, locale)
	return introduceTranslate.Value
}

// Update 更新配置
func (s *SettingService) Update(ctx context.Context, id uint, req *admin.SettingUpdateReq) error {
	settings, err := s.settingRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if req.Name != nil {
		settings.Name = *req.Name
	}
	if err = s.settingRepo.Update(ctx, settings); err != nil {
		return err
	}
	return nil
}

// UpdateData 更新应用设置数据
func (s *SettingService) UpdateData(ctx context.Context, id uint, req *admin.SettingDataUpdateReq) error {
	settings, err := s.settingRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	switch settings.Type {
	case models.SettingTypeObject:
		valueBytes, err := json.Marshal(req.Value)
		if err != nil {
			return err
		}
		settings.Value = string(valueBytes)
	case models.SettingTypeArray:
		valueBytes, err := json.Marshal(req.Value)
		if err != nil {
			return err
		}
		settings.Value = string(valueBytes)
	default:
		settings.Value = req.Value.(string)
	}
	if err := s.settingRepo.Update(ctx, settings); err != nil {
		return err
	}
	return nil
}
