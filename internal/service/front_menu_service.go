package service

import (
	"context"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// FrontMenuService 前台菜单服务实现
type FrontMenuService struct {
	frontMenuRepo   repo.FrontMenuRepository
	translationRepo interfaces.ITranslationService
}

// NewFrontMenuService 创建前台菜单服务
func NewFrontMenuService(
	frontMenuRepo repo.FrontMenuRepository,
	translationRepo interfaces.ITranslationService,
) interfaces.IFrontMenuService {
	return &FrontMenuService{
		frontMenuRepo:   frontMenuRepo,
		translationRepo: translationRepo,
	}
}

// List 获取前台菜单列表
func (s *FrontMenuService) List(ctx context.Context, req *admin.FrontMenuListReq) ([]*models.FrontMenu, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("parent_id", req.ParentID).
		AddCondition("name", req.Name).
		AddCondition("subtitle", req.Subtitle).
		AddCondition("route", req.Route).
		AddCondition("sort", req.Sort).
		AddCondition("mode", req.Mode).
		AddCondition("type", req.Type).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.frontMenuRepo.DB())

	result, err := s.frontMenuRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Update 更新前台菜单
func (s *FrontMenuService) Update(ctx context.Context, id uint, req *admin.FrontMenuUpdateReq) error {
	// 获取现有菜单
	menu, err := s.frontMenuRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if menu == nil {
		return constant.ErrFrontMenuNotExists
	}

	// 更新字段
	if req.ParentID != nil {
		menu.ParentID = *req.ParentID
	}
	if req.Name != nil {
		menu.Name = *req.Name
	}
	if req.Subtitle != nil {
		menu.Subtitle = *req.Subtitle
	}
	if req.Icon != nil {
		menu.Icon = *req.Icon
	}
	if req.ActiveIcon != nil {
		menu.ActiveIcon = *req.ActiveIcon
	}
	if req.Route != nil {
		menu.Route = *req.Route
	}
	if req.Sort != nil {
		menu.Sort = *req.Sort
	}
	if req.Mode != nil {
		menu.Mode = *req.Mode
	}
	if req.Type != nil {
		menu.Type = *req.Type
	}
	if req.Status != nil {
		menu.Status = *req.Status
	}

	return s.frontMenuRepo.Update(ctx, menu)
}

// Delete 删除前台菜单
func (s *FrontMenuService) Delete(ctx context.Context, id uint) error {
	return s.frontMenuRepo.Delete(ctx, id)
}

// GetInitsFrontMenus 获取初始化菜单
func (s *FrontMenuService) GetInitsFrontMenus(ctx context.Context, locale string) *web.InitsMenus {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("status", models.FrontMenuStatusEnabled)
	builder.AddSort("sort", "ASC")
	menus, err := s.frontMenuRepo.FindWithQuery(ctx, builder)
	if err != nil {
		return nil
	}

	data := &web.InitsMenus{
		Tabbar:        make([]*web.InitsMenuItems, 0),
		Users:         make([]*web.InitsMenuItems, 0),
		UsersQuick:    make([]*web.InitsMenuItems, 0),
		HomeQuick:     make([]*web.InitsMenuItems, 0),
		DesktopNavbar: make([]*web.InitsMenuItems, 0),
		DesktopUsers:  make([]*web.InitsMenuItems, 0),
	}

	// 只支持两层数据
	for _, menu := range menus {
		nameTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, menu.NameField, locale)
		menu.Name = nameTranslate.Value
		subtitleTranslate, _ := s.translationRepo.FindByKeyAndLang(ctx, menu.SubtitleField, locale)
		menu.Subtitle = subtitleTranslate.Value

		menuItem := &web.InitsMenuItems{
			Icon:       menu.Icon,
			ActiveIcon: menu.ActiveIcon,
			Label:      menu.Name,
			Route:      menu.Route,
			Subtitle:   menu.Subtitle,
			Children:   make([]*web.InitsMenuItems, 0),
		}
		for _, child := range menu.Children {
			nameTranslate, _ = s.translationRepo.FindByKeyAndLang(ctx, child.NameField, locale)
			child.Name = nameTranslate.Value
			subtitleTranslate, _ = s.translationRepo.FindByKeyAndLang(ctx, child.SubtitleField, locale)
			child.Subtitle = subtitleTranslate.Value
			menuItem.Children = append(menuItem.Children, &web.InitsMenuItems{
				Icon:       child.Icon,
				ActiveIcon: child.ActiveIcon,
				Label:      child.Name,
				Route:      child.Route,
				Subtitle:   child.Subtitle,
			})
		}

		if menu.Mode == models.FrontMenuModeDesktop {
			switch menu.Type {
			case models.FrontMenuTypeTabbar:
				data.DesktopNavbar = append(data.DesktopNavbar, menuItem)
			case models.FrontMenuTypeUsers:
				data.DesktopUsers = append(data.DesktopUsers, menuItem)
			}
		} else {
			switch menu.Type {
			case models.FrontMenuTypeTabbar:
				data.Tabbar = append(data.Tabbar, menuItem)
			case models.FrontMenuTypeUsers:
				data.Users = append(data.Users, menuItem)
			case models.FrontMenuTypeUsersQuick:
				data.UsersQuick = append(data.UsersQuick, menuItem)
			case models.FrontMenuTypeHomeQuick:
				data.HomeQuick = append(data.HomeQuick, menuItem)
			}
		}
	}

	return data
}
