package service

import (
	"context"
	"gin/internal/constant"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"time"
)

// UserLevelService 用户等级服务实现
type UserLevelService struct {
	userLevelRepo repo.UserLevelRepository
	levelRepo     repo.LevelRepository
}

// NewUserLevelService 创建用户等级服务
func NewUserLevelService(repo repo.UserLevelRepository, levelRepo repo.LevelRepository) interfaces.IUserLevelService {
	return &UserLevelService{
		userLevelRepo: repo,
		levelRepo:     levelRepo,
	}
}

// Create 创建用户等级
func (s *UserLevelService) Create(ctx context.Context, req *admin.UserLevelCreateRequest) error {
	level, err := s.levelRepo.FindByID(ctx, req.LevelID)
	if err != nil {
		return err
	}
	if level == nil {
		return constant.ErrLevelNotExists
	}

	// 创建用户等级
	userLevel := &models.UserLevel{
		UserID:  req.UserID,
		LevelID: req.LevelID,
		Type:    req.Type,
		Status:  req.Status,
		ExpiredAt: func() time.Time {
			if req.ExpiredAt == "" {
				return time.Time{}
			}
			t, err := time.Parse("2006-01-02 15:04:05", req.ExpiredAt)
			if err != nil {
				return time.Time{}
			}
			return t
		}(),
		Data: models.UserLevelData{
			Level: *level,
		},
	}
	return s.userLevelRepo.Create(ctx, userLevel)
}

// CreateByUser 用户创建等级
func (s *UserLevelService) CreateByUser(ctx context.Context, userID uint, req *web.CreateUserLevelRequest) error {
	if userID == 0 {
		return constant.ErrUserNotExists
	}

	return s.Create(ctx, &admin.UserLevelCreateRequest{
		UserID:  userID,
		LevelID: req.LevelID,
	})
}

// Update 更新用户等级
func (s *UserLevelService) Update(ctx context.Context, id uint, req *admin.UserLevelUpdateRequest) error {
	userLevel, err := s.userLevelRepo.FindByID(ctx, id)
	if err != nil || userLevel == nil {
		return err
	}

	if req.UserID != nil {
		userLevel.UserID = *req.UserID
	}
	if req.LevelID != nil {
		userLevel.LevelID = *req.LevelID
	}
	if req.Type != nil {
		userLevel.Type = *req.Type
	}
	if req.Status != nil {
		userLevel.Status = *req.Status
	}
	if req.ExpiredAt != nil {
		t, err := time.Parse("2006-01-02 15:04:05", *req.ExpiredAt)
		if err != nil {
			return err
		}
		userLevel.ExpiredAt = t
	}

	return s.userLevelRepo.Update(ctx, userLevel)
}

// Delete 删除用户等级
func (s *UserLevelService) Delete(ctx context.Context, id uint) error {
	return s.userLevelRepo.Delete(ctx, id)
}

// GetByID 根据ID获取用户等级
func (s *UserLevelService) GetByID(ctx context.Context, id uint) (*models.UserLevel, error) {
	return s.userLevelRepo.FindByID(ctx, id)
}

// List 获取用户等级列表
func (s *UserLevelService) List(ctx context.Context, req *admin.UserLevelListRequest) ([]*models.UserLevel, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("user_id", req.UserID).
		AddCondition("level_id", req.LevelID).
		AddCondition("type", req.Type).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.userLevelRepo.DB())

	result, err := s.userLevelRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// BatchDelete 批量删除用户等级
func (s *UserLevelService) BatchDelete(ctx context.Context, req *admin.UserLevelBatchDeleteRequest) error {
	return s.userLevelRepo.BulkDelete(ctx, req.IDs)
}
