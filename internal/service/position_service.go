package service

import (
	"context"
	"errors"
	"gin/internal/dto/common"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository"
	"gin/internal/service/interfaces"

	"go.uber.org/zap"
)

// PositionService 岗位服务接口
type PositionService struct {
	positionRepo repository.PositionRepo
}

// NewPositionService 创建岗位服务
func NewPositionService(positionRepo repository.PositionRepo) interfaces.IPositionService {
	return &PositionService{
		positionRepo: positionRepo,
	}
}

// CreatePosition 创建岗位
func (s *PositionService) CreatePosition(ctx context.Context, position *models.Position) error {
	// 检查岗位编码是否已存在
	existing, err := s.positionRepo.FindByCode(ctx, position.Code)
	if err != nil {
		logger.Error("检查岗位编码是否存在失败", zap.String("code", position.Code), zap.Error(err))
		return err
	}
	if existing != nil {
		return errors.New("岗位编码已存在")
	}

	// 创建岗位
	err = s.positionRepo.Create(position)
	if err != nil {
		logger.Error("创建岗位失败", zap.String("name", position.Name), zap.Error(err))
		return err
	}

	return nil
}

// UpdatePosition 更新岗位
func (s *PositionService) UpdatePosition(ctx context.Context, position *models.Position) error {
	// 检查岗位是否存在
	var existing models.Position
	err := s.positionRepo.WithContext(ctx).FindByID(position.ID, &existing)
	if err != nil {
		logger.Error("检查岗位是否存在失败", zap.Uint64("id", position.ID), zap.Error(err))
		return err
	}

	// 检查岗位编码是否已被其他岗位使用
	if position.Code != existing.Code {
		existingByCode, err := s.positionRepo.FindByCode(ctx, position.Code)
		if err != nil {
			logger.Error("检查岗位编码是否存在失败", zap.String("code", position.Code), zap.Error(err))
			return err
		}
		if existingByCode != nil && existingByCode.ID != position.ID {
			return errors.New("岗位编码已被其他岗位使用")
		}
	}

	// 更新岗位
	err = s.positionRepo.Update(position)
	if err != nil {
		logger.Error("更新岗位失败", zap.Uint64("id", position.ID), zap.Error(err))
		return err
	}

	return nil
}

// DeletePosition 删除岗位
func (s *PositionService) DeletePosition(ctx context.Context, id uint64) error {
	// 检查岗位是否存在
	var existing models.Position
	err := s.positionRepo.WithContext(ctx).FindByID(id, &existing)
	if err != nil {
		logger.Error("检查岗位是否存在失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}

	// 删除岗位
	err = s.positionRepo.Delete(id)
	if err != nil {
		logger.Error("删除岗位失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}

	return nil
}

// GetPositionByID 根据ID获取岗位
func (s *PositionService) GetPositionByID(ctx context.Context, id uint64) (*models.Position, error) {
	var position models.Position
	err := s.positionRepo.WithContext(ctx).FindByID(id, &position)
	if err != nil {
		logger.Error("根据ID获取岗位失败", zap.Uint64("id", id), zap.Error(err))
		return nil, err
	}
	return &position, nil
}

// GetAllPositions 获取所有岗位
func (s *PositionService) GetAllPositions(ctx context.Context) ([]*models.Position, error) {
	var positions []*models.Position
	err := s.positionRepo.WithContext(ctx).FindAll(&positions)
	if err != nil {
		logger.Error("获取所有岗位失败", zap.Error(err))
		return nil, err
	}
	return positions, nil
}

// GetPositionsByDeptID 根据部门ID获取岗位
func (s *PositionService) GetPositionsByDeptID(ctx context.Context, deptID uint64) ([]*models.Position, error) {
	positions, err := s.positionRepo.FindByDeptID(ctx, deptID)
	if err != nil {
		logger.Error("根据部门ID获取岗位失败", zap.Uint64("deptID", deptID), zap.Error(err))
		return nil, err
	}
	return positions, nil
}

// GetPositionsByStatus 根据状态获取岗位
func (s *PositionService) GetPositionsByStatus(ctx context.Context, status int8) ([]*models.Position, error) {
	positions, err := s.positionRepo.FindByStatus(ctx, status)
	if err != nil {
		logger.Error("根据状态获取岗位失败", zap.Int8("status", status), zap.Error(err))
		return nil, err
	}
	return positions, nil
}

// GetPositionsByType 根据类型获取岗位
func (s *PositionService) GetPositionsByType(ctx context.Context, posType int8) ([]*models.Position, error) {
	positions, err := s.positionRepo.FindByType(ctx, posType)
	if err != nil {
		logger.Error("根据类型获取岗位失败", zap.Int8("posType", posType), zap.Error(err))
		return nil, err
	}
	return positions, nil
}

// GetPositionsByLevel 根据级别获取岗位
func (s *PositionService) GetPositionsByLevel(ctx context.Context, level int8) ([]*models.Position, error) {
	positions, err := s.positionRepo.FindByLevel(ctx, level)
	if err != nil {
		logger.Error("根据级别获取岗位失败", zap.Int8("level", level), zap.Error(err))
		return nil, err
	}
	return positions, nil
}

// GetPositionsWithPagination 分页获取岗位
func (s *PositionService) GetPositionsWithPagination(ctx context.Context, pagination *common.Pagination) (*common.PageResult, error) {
	var positions []*models.Position
	result, err := s.positionRepo.WithContext(ctx).FindWithPagination(pagination, nil, &positions)
	if err != nil {
		logger.Error("分页获取岗位失败", zap.Error(err))
		return nil, err
	}
	return result, nil
}

// BatchCreatePositions 批量创建岗位
func (s *PositionService) BatchCreatePositions(ctx context.Context, positions []*models.Position) error {
	err := s.positionRepo.BatchCreate(ctx, positions)
	if err != nil {
		logger.Error("批量创建岗位失败", zap.Int("count", len(positions)), zap.Error(err))
		return err
	}
	return nil
}

// BatchDeletePositions 批量删除岗位
func (s *PositionService) BatchDeletePositions(ctx context.Context, ids []uint64) error {
	err := s.positionRepo.BatchDelete(ctx, ids)
	if err != nil {
		logger.Error("批量删除岗位失败", zap.Uint64s("ids", ids), zap.Error(err))
		return err
	}
	return nil
}

// CountPositions 统计岗位总数
func (s *PositionService) CountPositions(ctx context.Context) (int64, error) {
	count, err := s.positionRepo.WithContext(ctx).Count(nil)
	if err != nil {
		logger.Error("统计岗位总数失败", zap.Error(err))
		return 0, err
	}
	return count, nil
}

// CountPositionsByDeptID 统计部门岗位数
func (s *PositionService) CountPositionsByDeptID(ctx context.Context, deptID uint64) (int64, error) {
	count, err := s.positionRepo.CountByDeptID(ctx, deptID)
	if err != nil {
		logger.Error("统计部门岗位数失败", zap.Uint64("deptID", deptID), zap.Error(err))
		return 0, err
	}
	return count, nil
}
