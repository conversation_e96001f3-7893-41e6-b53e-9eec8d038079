package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// UserAssetService 用户资产服务实现
type UserAssetService struct {
	logger        logger.Logger
	userAssetRepo repo.UserAssetRepository

	userRepo        repo.UserRepository
	userBillService interfaces.IUserBillService
	managerRepo     repo.ManagerRepository
}

// NewUserAssetService 创建一个新的UserAssetService实例
func NewUserAssetService(logger logger.Logger, userAssetRepo repo.UserAssetRepository, userRepo repo.UserRepository, userBillService interfaces.IUserBillService, managerRepo repo.ManagerRepository) interfaces.IUserAssetService {
	return &UserAssetService{
		logger:          logger,
		userAssetRepo:   userAssetRepo,
		userRepo:        userRepo,
		userBillService: userBillService,
		managerRepo:     managerRepo,
	}
}

// List 根据查询条件获取用户资产列表, 并返回总数
func (s *UserAssetService) List(ctx context.Context, req *admin.UserAssetListReq) ([]*models.UserAsset, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("manager_id", req.ManagerID)
	queryBuilder.AddCondition("user_id", req.UserID)
	queryBuilder.AddCondition("asset_id", req.AssetID)

	queryBuilder.SetPagination(req.Page, req.PageSize)
	queryBuilder.AddSort(req.OrderBy, query.SortDirection(req.Order))

	result, err := s.userAssetRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// Create 创建用户资产
func (s *UserAssetService) Create(ctx context.Context, req *admin.UserAssetCreateReq) (*models.UserAsset, error) {
	//user, err := s.userRepo.FindByID(ctx, req.UserID)
	//if err != nil {
	//	return nil, err
	//}
	//return s.userAssetRepo.FindByUserIDAndAssetIDWithCreate(ctx, user, req.AssetID)
	return nil, nil
}

// Update 更新用户资产
func (s *UserAssetService) Update(ctx context.Context, id uint, req *admin.UserAssetUpdateReq) error {
	userAsset, err := s.userAssetRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	if req.AvailableAmount != nil {
		userAsset.AvailableAmount = *req.AvailableAmount
	}
	if req.FrozenAmount != nil {
		userAsset.FrozenAmount = *req.FrozenAmount
	}
	if req.ProfitAmount != nil {
		userAsset.ProfitAmount = *req.ProfitAmount
	}
	if req.SumDepositAmount != nil {
		userAsset.SumDepositAmount = *req.SumDepositAmount
	}
	if req.SumWithdrawAmount != nil {
		userAsset.SumWithdrawAmount = *req.SumWithdrawAmount
	}

	return s.userAssetRepo.Update(ctx, userAsset)
}

// OperationBalance 更新余额
func (s *UserAssetService) OperationBalance(ctx context.Context, locale string, id uint, req *admin.UserAssetOperationsReq) error {
	// 后台操作账单, 不需要手续费
	//return s.userAssetRepo.OperationBalance(ctx, locale, id, req.Type, req.WriteBill, req.Amount, 0)
	return nil
}
