package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// LevelService 等级服务实现
type LevelService struct {
	levelRepo          repo.LevelRepository
	translationService interfaces.ITranslationService
}

// NewLevelService 创建等级服务
func NewLevelService(repo repo.LevelRepository, translationService interfaces.ITranslationService) interfaces.ILevelService {
	return &LevelService{
		levelRepo:          repo,
		translationService: translationService,
	}
}

// Create 创建等级
func (s *LevelService) Create(ctx context.Context, req *admin.LevelCreateRequest) error {
	level := &models.Level{
		Name:        req.Name,
		Icon:        req.Icon,
		Symbol:      req.Symbol,
		Type:        req.Type,
		Sort:        req.Sort,
		Amount:      req.Amount,
		Discount:    req.Discount,
		Days:        req.Days,
		Status:      req.Status,
		Description: req.Description,
	}
	return s.levelRepo.Create(ctx, level)
}

// Update 更新等级
func (s *LevelService) Update(ctx context.Context, id uint, req *admin.LevelUpdateRequest) error {
	level, err := s.levelRepo.FindByID(ctx, id)
	if err != nil || level == nil {
		return err
	}

	if req.Name != nil {
		level.Name = *req.Name
	}
	if req.Icon != nil {
		level.Icon = *req.Icon
	}
	if req.Symbol != nil {
		level.Symbol = *req.Symbol
	}
	if req.Type != nil {
		level.Type = *req.Type
	}
	if req.Sort != nil {
		level.Sort = *req.Sort
	}
	if req.Amount != nil {
		level.Amount = *req.Amount
	}
	if req.Discount != nil {
		level.Discount = *req.Discount
	}
	if req.Days != nil {
		level.Days = *req.Days
	}
	if req.Status != nil {
		level.Status = *req.Status
	}
	if req.Description != nil {
		level.Description = *req.Description
	}

	return s.levelRepo.Update(ctx, level)
}

// Delete 删除等级
func (s *LevelService) Delete(ctx context.Context, id uint) error {
	return s.levelRepo.Delete(ctx, id)
}

// GetByID 根据ID获取等级
func (s *LevelService) GetByID(ctx context.Context, id uint) (*models.Level, error) {
	return s.levelRepo.FindByID(ctx, id)
}

// List 获取等级列表
func (s *LevelService) List(ctx context.Context, req *admin.LevelListRequest) ([]*models.Level, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("name", req.Name).
		AddCondition("symbol", req.Symbol).
		AddCondition("type", req.Type).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.levelRepo.DB())

	result, err := s.levelRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// ListWithLocale 获取用户等级列表(带语言)
func (s *LevelService) ListWithLocale(ctx context.Context, locale string) ([]*web.LevelResponse, int64, error) {
	levels, err := s.levelRepo.FindAll(ctx)
	if err != nil {
		return nil, 0, err
	}

	levelResponses := make([]*web.LevelResponse, len(levels))

	// 翻译
	for i, level := range levels {
		nameTranslation, err := s.translationService.FindByKeyAndLang(ctx, level.NameField, locale)
		if err != nil {
			return nil, 0, err
		}
		descriptionTranslation, err := s.translationService.FindByKeyAndLang(ctx, level.DescriptionField, locale)
		if err != nil {
			return nil, 0, err
		}
		levelResponses[i] = &web.LevelResponse{
			ID:          level.ID,
			Name:        nameTranslation.Value,
			Description: descriptionTranslation.Value,
			Icon:        level.Icon,
			Symbol:      level.Symbol,
			Type:        level.Type,
			Sort:        level.Sort,
			Amount:      level.Amount,
			Discount:    level.Discount,
			Days:        level.Days,
			Status:      level.Status,
		}
	}
	return levelResponses, int64(len(levelResponses)), nil
}

// BatchDelete 批量删除等级
func (s *LevelService) BatchDelete(ctx context.Context, req *admin.LevelBatchDeleteRequest) error {
	return s.levelRepo.BulkDelete(ctx, req.IDs)
}
