package service

import (
	"context"
	"errors"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/casbin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
	"gin/pkg/utils"

	"go.uber.org/zap"
)

// PermissionService 权限服务实现
type PermissionService struct {
	logger   logger.Logger
	permRepo repo.PermissionRepository
	enforcer casbin.IEnforcer
}

// NewPermissionService 创建权限服务
func NewPermissionService(logger logger.Logger, permRepo repo.PermissionRepository, enforcer casbin.IEnforcer) interfaces.IPermissionService {
	return &PermissionService{
		logger:   logger,
		permRepo: permRepo,
		enforcer: enforcer,
	}
}

// GetAllPermissions 获取所有权限
func (s *PermissionService) GetAllPermissions(ctx context.Context) ([]*models.Permission, error) {
	permissions, err := s.permRepo.FindAllByStatus(ctx)
	if err != nil {
		s.logger.Error("获取权限失败", zap.Error(err))
		return nil, err
	}
	return permissions, nil
}

// GetPermissionsByRoleCode 根据角色编码获取权限
func (s *PermissionService) GetPermissionsByRoleCode(ctx context.Context, roleCode string) ([]*models.Permission, error) {
	if s.enforcer == nil {
		return []*models.Permission{}, nil
	}

	// 从Casbin获取权限策略
	permissions, err := s.enforcer.GetPermissionsForRole(roleCode)
	if err != nil {
		s.logger.Error("获取角色权限失败", zap.String("roleCode", roleCode), zap.Error(err))
		return nil, err
	}

	// 提取权限代码
	codeSet := make(map[string]bool)
	for _, perm := range permissions {
		if len(perm) >= 2 {
			if code := perm[1]; code != "" {
				codeSet[code] = true
			}
		}
	}

	codes := make([]string, 0, len(codeSet))
	for code := range codeSet {
		codes = append(codes, code)
	}

	// 批量获取权限
	result := make([]*models.Permission, 0)
	for _, code := range codes {
		if perm, err := s.permRepo.FindByCode(ctx, code); err == nil && perm != nil {
			result = append(result, perm)
		}
	}
	return result, nil
}

// Create 创建权限
func (s *PermissionService) Create(ctx context.Context, req *admin.PermissionCreateRequest) (*models.Permission, error) {
	// 验证权限
	if err := s.validatePermission(ctx, req.Name, req.Code, req.API, req.Method, 0); err != nil {
		return nil, err
	}

	permission := &models.Permission{
		Name:     req.Name,
		Code:     req.Code,
		Resource: req.Resource,
		Action:   req.Action,
		Method:   req.Method,
		API:      req.API,
		Status:   req.Status,
		Remark:   req.Remark,
		IsSystem: false,
	}

	// 设置默认状态
	if permission.Status == 0 {
		permission.Status = models.PermissionStatusEnabled // 启用
	}

	// 创建权限
	if err := s.permRepo.Create(ctx, permission); err != nil {
		s.logger.Error("创建权限失败", zap.Error(err))
		return nil, err
	}

	return permission, nil
}

// Update 更新权限
func (s *PermissionService) Update(ctx context.Context, id uint, req *admin.PermissionUpdateRequest) error {
	// 获取现有权限
	permission, err := s.permRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if permission == nil {
		return errors.New("权限不存在")
	}

	// 验证权限
	name := permission.Name
	code := permission.Code
	api := permission.API
	method := permission.Method
	if req.Name != nil {
		name = *req.Name
	}
	if req.Code != nil {
		code = *req.Code
	}
	if req.API != nil {
		api = *req.API
	}
	if req.Method != nil {
		method = *req.Method
	}

	if err := s.validatePermission(ctx, name, code, api, method, id); err != nil {
		return err
	}

	// 更新字段
	if req.Name != nil {
		permission.Name = *req.Name
	}
	if req.Code != nil {
		permission.Code = *req.Code
	}
	if req.Resource != nil {
		permission.Resource = *req.Resource
	}
	if req.Action != nil {
		permission.Action = *req.Action
	}
	if req.Method != nil {
		permission.Method = *req.Method
	}
	if req.API != nil {
		permission.API = *req.API
	}
	if req.Status != nil {
		permission.Status = *req.Status
	}
	if req.Remark != nil {
		permission.Remark = *req.Remark
	}

	// 更新权限
	if err := s.permRepo.Update(ctx, permission); err != nil {
		s.logger.Error("更新权限失败", zap.Uint("id", id), zap.Error(err))
		return err
	}

	return nil
}

// GetPermissionByID 根据ID获取权限
func (s *PermissionService) GetPermissionByID(ctx context.Context, id uint) (*models.Permission, error) {
	return s.permRepo.FindByID(ctx, id)
}

// List 分页查询权限列表
func (s *PermissionService) List(ctx context.Context, req *admin.PermissionListRequest) ([]*models.Permission, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddLikeCondition("name", req.Name).
		AddLikeCondition("code", req.Code).
		AddLikeCondition("resource", req.Resource).
		AddLikeCondition("action", req.Action).
		AddLikeCondition("method", req.Method).
		AddLikeCondition("api", req.API).
		AddCondition("is_system", req.IsSystem).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).
		Build(s.permRepo.DB())

	result, err := s.permRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// AssignPermissionsToRole 为角色分配权限
func (s *PermissionService) AssignPermissionsToRole(ctx context.Context, roleCode string, permIds []uint) error {
	// 1. 清除角色现有权限
	permissions, err := s.enforcer.GetPermissionsForRole(roleCode)
	if err != nil {
		return err
	}

	for _, permission := range permissions {
		params := make([]interface{}, len(permission))
		for i, p := range permission {
			params[i] = p
		}
		_, _ = s.enforcer.RemovePolicy(params...)
	}

	// 2. 为角色添加新权限
	for _, permId := range permIds {
		perm, err := s.permRepo.FindByID(ctx, permId)
		if err != nil {
			s.logger.Error("根据ID获取权限失败", zap.Uint("permId", permId), zap.Error(err))
			continue
		}
		if perm == nil {
			continue
		}

		// 根据HTTP方法确定Casbin操作类型
		action := utils.ConvertMethodToAction(perm.Method)

		// 添加API权限策略（用于后端权限验证中间件）
		if perm.API != "" && perm.Method != "" {
			if _, err = s.enforcer.AddPolicy(roleCode, perm.API, action); err != nil {
				s.logger.Error("添加角色API权限失败",
					zap.String("roleCode", roleCode),
					zap.String("api", perm.API),
					zap.String("action", action),
					zap.Error(err))
			}
		}

		// 添加权限代码策略（用于前端权限控制）
		if _, err = s.enforcer.AddPolicy(roleCode, perm.Code, action); err != nil {
			s.logger.Error("添加角色权限失败",
				zap.String("roleCode", roleCode),
				zap.String("permCode", perm.Code),
				zap.String("action", action),
				zap.Error(err))
		}
	}

	return nil
}

// RemovePermissionsFromRole 移除角色权限
func (s *PermissionService) RemovePermissionsFromRole(ctx context.Context, roleCode string, permIds []uint) error {
	// 从Casbin中移除指定权限
	for _, permId := range permIds {
		perm, err := s.permRepo.FindByID(ctx, permId)
		if err != nil {
			s.logger.Error("根据ID获取权限失败", zap.Uint("permId", permId), zap.Error(err))
			continue
		}
		if perm == nil {
			continue
		}

		// 根据HTTP方法确定Casbin操作类型
		action := utils.ConvertMethodToAction(perm.Method)

		// 移除API权限策略（用于后端权限验证中间件）
		if perm.API != "" && perm.Method != "" {
			if _, err = s.enforcer.RemovePolicy(roleCode, perm.API, action); err != nil {
				s.logger.Error("移除角色API权限失败",
					zap.String("roleCode", roleCode),
					zap.String("api", perm.API),
					zap.String("action", action),
					zap.Error(err))
			}
		}

		// 移除权限代码策略（用于前端权限控制）
		if _, err = s.enforcer.RemovePolicy(roleCode, perm.Code, action); err != nil {
			s.logger.Error("移除角色权限失败",
				zap.String("roleCode", roleCode),
				zap.String("permCode", perm.Code),
				zap.String("action", action),
				zap.Error(err))
		}
	}

	return nil
}

// validatePermission 统一验证权限
func (s *PermissionService) validatePermission(ctx context.Context, name, code, api, method string, excludeID uint) error {
	// 验证权限名称唯一性
	if existing, err := s.permRepo.FindByName(ctx, name); err != nil {
		return err
	} else if existing != nil && existing.ID != excludeID {
		return errors.New("权限名称已存在")
	}

	// 验证权限代码唯一性
	if existing, err := s.permRepo.FindByCode(ctx, code); err != nil {
		return err
	} else if existing != nil && existing.ID != excludeID {
		return errors.New("权限代码已存在")
	}

	// 验证API唯一性（如果有API和方法）
	if api != "" && method != "" {
		if existing, err := s.permRepo.FindByAPI(ctx, api, method); err != nil {
			return err
		} else if existing != nil && existing.ID != excludeID {
			return errors.New("API权限已存在")
		}
	}

	return nil
}
