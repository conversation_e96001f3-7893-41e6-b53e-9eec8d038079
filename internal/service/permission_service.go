package service

import (
	"context"
	"errors"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository"
	"gin/internal/service/interfaces"

	"go.uber.org/zap"
)

// PermissionService 权限服务实现
type PermissionService struct {
	permRepo repository.PermissionRepo
}

// NewPermissionService 创建权限服务
func NewPermissionService(permRepo repository.PermissionRepo) interfaces.IPermissionService {
	return &PermissionService{
		permRepo: permRepo,
	}
}

// GetAllPermissionMenus 获取所有菜单
func (s *PermissionService) GetAllPermissionMenus(ctx context.Context) ([]*models.Permission, error) {
	menus, err := s.permRepo.FindAllMenus(ctx)
	if err != nil {
		logger.Error("获取所有菜单失败", zap.Error(err))
		return nil, err
	}
	return menus, nil
}

// GetMenusByPermissions 根据权限代码获取菜单
func (s *PermissionService) GetMenusByPermissions(ctx context.Context, permCodes []string) ([]*models.Permission, error) {
	if len(permCodes) == 0 {
		return nil, errors.New("权限代码列表不能为空")
	}

	menus, err := s.permRepo.FindMenusByPermissions(ctx, permCodes)
	if err != nil {
		logger.Error("根据权限代码获取菜单失败", zap.Strings("permCodes", permCodes), zap.Error(err))
		return nil, err
	}
	return menus, nil
}

// BuildPermissionMenuTree 构建菜单树
func (s *PermissionService) BuildPermissionMenuTree(menus []*models.Permission, parentID uint64) []*models.Permission {
	var tree []*models.Permission
	for _, menu := range menus {
		if menu.ParentID == parentID {
			children := s.BuildPermissionMenuTree(menus, menu.ID)
			if len(children) > 0 {
				menu.Children = children
			}
			tree = append(tree, menu)
		}
	}
	return tree
}

// CreatePermission 创建权限
func (s *PermissionService) CreatePermission(ctx context.Context, permission *models.Permission) error {
	// 检查权限代码是否已存在
	existing, err := s.permRepo.FindByCode(ctx, permission.Code)
	if err != nil {
		logger.Error("检查权限代码是否存在失败", zap.String("code", permission.Code), zap.Error(err))
		return err
	}
	if existing != nil {
		return errors.New("权限代码已存在")
	}

	// 如果是API权限，检查API是否已存在
	if permission.Type == 3 && permission.API != "" {
		existing, err = s.permRepo.FindByAPI(ctx, permission.API, permission.Method)
		if err != nil {
			logger.Error("检查API是否存在失败", zap.String("api", permission.API), zap.Error(err))
			return err
		}
		if existing != nil {
			return errors.New("API权限已存在")
		}
	}

	// 创建权限
	err = s.permRepo.Create(permission)
	if err != nil {
		logger.Error("创建权限失败", zap.String("name", permission.Name), zap.Error(err))
		return err
	}

	return nil
}

// UpdatePermission 更新权限
func (s *PermissionService) UpdatePermission(ctx context.Context, permission *models.Permission) error {
	// 检查权限是否存在
	var existing models.Permission
	err := s.permRepo.FindByID(permission.ID, &existing)
	if err != nil {
		logger.Error("检查权限是否存在失败", zap.Uint64("id", permission.ID), zap.Error(err))
		return err
	}

	// 检查权限代码是否已被其他权限使用
	if permission.Code != existing.Code {
		existingByCode, err := s.permRepo.FindByCode(ctx, permission.Code)
		if err != nil {
			logger.Error("检查权限代码是否存在失败", zap.String("code", permission.Code), zap.Error(err))
			return err
		}
		if existingByCode != nil && existingByCode.ID != permission.ID {
			return errors.New("权限代码已被其他权限使用")
		}
	}

	// 如果是API权限，检查API是否已被其他权限使用
	if permission.Type == 3 && permission.API != "" &&
		(permission.API != existing.API || permission.Method != existing.Method) {
		existingByAPI, err := s.permRepo.FindByAPI(ctx, permission.API, permission.Method)
		if err != nil {
			logger.Error("检查API是否存在失败", zap.String("api", permission.API), zap.Error(err))
			return err
		}
		if existingByAPI != nil && existingByAPI.ID != permission.ID {
			return errors.New("API权限已被其他权限使用")
		}
	}

	// 更新权限
	err = s.permRepo.Update(permission)
	if err != nil {
		logger.Error("更新权限失败", zap.Uint64("id", permission.ID), zap.Error(err))
		return err
	}

	return nil
}

// DeletePermission 删除权限
func (s *PermissionService) DeletePermission(ctx context.Context, id uint64) error {
	// 检查是否有子权限
	children, err := s.permRepo.FindByParentID(ctx, id)
	if err != nil {
		logger.Error("检查子权限失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}
	if len(children) > 0 {
		return errors.New("请先删除子权限")
	}

	// 删除权限
	err = s.permRepo.Delete(id)
	if err != nil {
		logger.Error("删除权限失败", zap.Uint64("id", id), zap.Error(err))
		return err
	}

	return nil
}

// GetPermissionByID 根据ID获取权限
func (s *PermissionService) GetPermissionByID(ctx context.Context, id uint64) (*models.Permission, error) {
	var permission models.Permission
	err := s.permRepo.WithContext(ctx).FindByID(id, &permission)
	if err != nil {
		logger.Error("根据ID获取权限失败", zap.Uint64("id", id), zap.Error(err))
		return nil, err
	}
	return &permission, nil
}

// GetPermissionsByType 根据类型获取权限
func (s *PermissionService) GetPermissionsByType(ctx context.Context, permType int) ([]*models.Permission, error) {
	permissions, err := s.permRepo.FindByType(ctx, permType)
	if err != nil {
		logger.Error("根据类型获取权限失败", zap.Int("type", permType), zap.Error(err))
		return nil, err
	}
	return permissions, nil
}

// GetPermissionsByRoleID 根据角色ID获取权限
func (s *PermissionService) GetPermissionsByRoleID(ctx context.Context, roleID uint64) ([]*models.Permission, error) {
	permissions, err := s.permRepo.FindByRoleID(ctx, roleID)
	if err != nil {
		logger.Error("根据角色ID获取权限失败", zap.Uint64("roleID", roleID), zap.Error(err))
		return nil, err
	}
	return permissions, nil
}
