package service

import (
	"context"
	"gin/internal/dto/admin"
	"gin/internal/dto/web"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"
)

// CountryService 国家服务实现
type CountryService struct {
	countryRepo repo.CountryRepository
}

// NewCountryService 创建国家服务
func NewCountryService(repo repo.CountryRepository) interfaces.ICountryService {
	return &CountryService{
		countryRepo: repo,
	}
}

// Create 创建国家
func (s *CountryService) Create(ctx context.Context, req *admin.CountryCreateRequest) error {
	country := &models.Country{
		NameZh:     req.NameZh,
		NameNative: req.NameNative,
		Icon:       req.Icon,
		ISO2:       req.ISO2,
		Sort:       req.Sort,
		Code:       req.Code,
		Status:     models.CountryStatusEnabled,
	}
	return s.countryRepo.Create(ctx, country)
}

// Update 更新国家
func (s *CountryService) Update(ctx context.Context, id uint, req *admin.CountryUpdateRequest) error {
	country, err := s.countryRepo.FindByID(ctx, id)
	if err != nil || country == nil {
		return err
	}

	if req.NameZh != nil {
		country.NameZh = *req.NameZh
	}
	if req.NameNative != nil {
		country.NameNative = *req.NameNative
	}
	if req.Icon != nil {
		country.Icon = *req.Icon
	}
	if req.ISO2 != nil {
		country.ISO2 = *req.ISO2
	}
	if req.Sort != nil {
		country.Sort = *req.Sort
	}
	if req.Code != nil {
		country.Code = *req.Code
	}

	return s.countryRepo.Update(ctx, country)
}

// Delete 删除国家
func (s *CountryService) Delete(ctx context.Context, id uint) error {
	return s.countryRepo.Delete(ctx, id)
}

// GetByID 根据ID获取国家
func (s *CountryService) GetByID(ctx context.Context, id uint) (*models.Country, error) {
	return s.countryRepo.FindByID(ctx, id)
}

// List 分页查询国家列表
func (s *CountryService) List(ctx context.Context, req *admin.CountryListRequest) ([]*models.Country, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("name_zh", req.NameZh).
		AddCondition("name_native", req.NameNative).
		AddCondition("iso2", req.ISO2).
		AddCondition("code", req.Code).
		AddCondition("status", req.Status).
		SetPagination(req.Pagination.Page, req.Pagination.PageSize).
		AddSort(req.Pagination.OrderBy, query.SortDirection(req.Pagination.Order)).Build(s.countryRepo.DB())

	result, err := s.countryRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}
	return result.Data, result.Total, nil
}

// BatchDelete 批量删除国家
func (s *CountryService) BatchDelete(ctx context.Context, req *admin.CountryBatchDeleteRequest) error {
	if len(req.IDs) == 0 {
		return nil
	}
	return s.countryRepo.BulkDelete(ctx, req.IDs)
}

// GetInitsCountryList 获取国家列表
func (s *CountryService) GetInitsCountryList(ctx context.Context) []*web.InitsCountrys {
	builder := query.NewEnhancedQueryBuilder()
	builder.AddCondition("status", models.CountryStatusEnabled).
		Build(s.countryRepo.DB())

	countryList, err := s.countryRepo.FindWithQuery(ctx, builder)
	if err != nil {
		return nil
	}

	result := make([]*web.InitsCountrys, len(countryList))
	for i, country := range countryList {
		result[i] = &web.InitsCountrys{
			ID:    country.ID,
			Icon:  country.Icon,
			Label: country.NameZh,
			ISO2:  country.ISO2,
			Code:  country.Code,
		}
	}
	return result
}
