package service

import (
	"context"
	"errors"
	"gin/internal/dto/admin"
	"gin/internal/infrastructure/logger"
	"gin/internal/models"
	"gin/internal/repository/query"
	"gin/internal/repository/repo"
	"gin/internal/service/interfaces"

	"go.uber.org/zap"
)

// RoleService 角色服务实现
type RoleService struct {
	logger      logger.Logger
	roleRepo    repo.RoleRepository
	permService interfaces.IPermissionService
}

// NewRoleService 创建角色服务
func NewRoleService(logger logger.Logger, repo repo.RoleRepository, permService interfaces.IPermissionService) interfaces.IRoleService {
	return &RoleService{
		logger:      logger,
		roleRepo:    repo,
		permService: permService,
	}
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(ctx context.Context, id uint) (*models.Role, error) {
	role, err := s.roleRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if role != nil {
		// 使用权限服务直接通过角色编码获取完整权限信息
		permissions, err := s.permService.GetPermissionsByRoleCode(ctx, role.Code)
		if err != nil {
			return nil, err
		}

		// 将权限对象的ID提取出来设置到角色的Permissions字段
		role.Permissions = make([]uint, len(permissions))
		for i, perm := range permissions {
			role.Permissions[i] = perm.ID
		}

	}
	return role, nil
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, role *models.Role) error {
	// 检查角色编码是否已存在
	existingRole, err := s.roleRepo.FindByCode(ctx, role.Code)
	if err != nil {
		s.logger.Error("检查角色编码是否存在失败", zap.String("code", role.Code), zap.Error(err))
		return err
	}
	if existingRole != nil {
		return errors.New("角色编码已存在")
	}

	// 检查角色名称是否已存在
	existingRole, err = s.roleRepo.FindByName(ctx, role.Name)
	if err != nil {
		s.logger.Error("检查角色名称是否存在失败", zap.String("name", role.Name), zap.Error(err))
		return err
	}
	if existingRole != nil {
		return errors.New("角色名称已存在")
	}

	// 创建角色
	err = s.roleRepo.Create(ctx, role)
	if err != nil {
		s.logger.Error("创建角色失败", zap.String("name", role.Name), zap.Error(err))
		return err
	}

	return nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(ctx context.Context, id uint, dto *admin.RoleUpdateRequest) error {
	// 检查角色是否存在
	existingRole, err := s.roleRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.Error("检查角色是否存在失败", zap.Uint("id", id), zap.Error(err))
		return err
	}
	if existingRole == nil {
		return errors.New("角色不存在")
	}

	// 系统内置角色编码修改限制检查
	if dto.Code != nil && existingRole.IsSystem && existingRole.Code != *dto.Code {
		return errors.New("系统内置角色不允许修改编码")
	}

	// 检查角色编码是否已被其他角色使用
	if dto.Code != nil && *dto.Code != existingRole.Code {
		otherRole, err := s.roleRepo.FindByCode(ctx, *dto.Code)
		if err != nil {
			return err
		}
		if otherRole != nil && otherRole.ID != id {
			return errors.New("角色编码已被其他角色使用")
		}
	}

	// 检查角色名称是否已被其他角色使用
	if dto.Name != nil && *dto.Name != existingRole.Name {
		otherRole, err := s.roleRepo.FindByName(ctx, *dto.Name)
		if err != nil {
			return err
		}
		if otherRole != nil && otherRole.ID != id {
			return errors.New("角色名称已被其他角色使用")
		}
	}

	// 基于现有角色对象进行更新，只修改传入的字段
	role := *existingRole // 复制现有角色对象

	// 只更新传入的字段
	if dto.Name != nil {
		role.Name = *dto.Name
	}
	if dto.Code != nil {
		role.Code = *dto.Code
	}
	if dto.Status != nil {
		role.Status = *dto.Status
	}
	if dto.Sort != nil {
		role.Sort = *dto.Sort
	}
	if dto.Remark != nil {
		role.Remark = *dto.Remark
	}
	if dto.DataScope != nil {
		role.DataScope = *dto.DataScope
	}

	// 系统内置角色不允许修改系统标志
	if existingRole.IsSystem {
		role.IsSystem = true
	}

	// 更新角色
	err = s.roleRepo.Update(ctx, &role)
	if err != nil {
		s.logger.Error("更新角色失败", zap.Uint("id", id), zap.Error(err))
		return err
	}

	// 更新角色权限
	if dto.Permissions != nil {
		// 从Casbin中移除旧权限
		if err = s.permService.RemovePermissionsFromRole(ctx, role.Code, existingRole.Permissions); err != nil {
			return err
		}

		// 为角色添加新权限
		if err = s.permService.AssignPermissionsToRole(ctx, role.Code, dto.Permissions); err != nil {
			return err
		}
	}

	return nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(ctx context.Context, id uint) error {
	// 检查角色是否存在
	existingRole, err := s.roleRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.Error("检查角色是否存在失败", zap.Uint("id", id), zap.Error(err))
		return err
	}
	if existingRole == nil {
		return errors.New("角色不存在")
	}

	// 系统内置角色不能删除
	if existingRole.IsSystem {
		return errors.New("不能删除系统内置角色")
	}

	// 删除角色
	err = s.roleRepo.Delete(ctx, id)
	if err != nil {
		s.logger.Error("删除角色失败", zap.Uint("id", id), zap.Error(err))
		return err
	}

	return nil
}

// BatchDeleteRole 批量删除角色
func (s *RoleService) BatchDeleteRole(ctx context.Context, ids []uint) error {
	err := s.roleRepo.BulkDelete(ctx, ids)
	if err != nil {
		s.logger.Error("批量删除角色失败", zap.Uints("ids", ids), zap.Error(err))
		return err
	}
	return nil
}

// ListRole 分页查询角色列表
func (s *RoleService) ListRole(ctx context.Context, dto *admin.RoleListRequest) ([]*models.Role, int64, error) {
	queryBuilder := query.NewEnhancedQueryBuilder()
	queryBuilder.AddCondition("name", dto.Name).
		AddCondition("code", dto.Code).
		AddCondition("status", dto.Status).
		AddConditionWithOperator("created_at", query.OpGte, dto.StartTime).
		AddConditionWithOperator("created_at", query.OpLte, dto.EndTime).
		AddSort(dto.Pagination.OrderBy, query.SortDirection(dto.Pagination.Order)).
		SetPagination(dto.Pagination.Page, dto.Pagination.PageSize).Build(s.roleRepo.DB())

	result, err := s.roleRepo.FindWithPagination(ctx, queryBuilder)
	if err != nil {
		return nil, 0, err
	}

	// 为每个角色加载权限信息
	for _, role := range result.Data {
		// 使用权限服务直接通过角色编码获取完整权限信息
		permissions, err := s.permService.GetPermissionsByRoleCode(ctx, role.Code)
		if err != nil {
			return nil, 0, err
		}

		// 将权限对象的ID提取出来设置到角色的Permissions字段
		role.Permissions = make([]uint, len(permissions))
		for i, perm := range permissions {
			role.Permissions[i] = perm.ID
		}

	}

	return result.Data, result.Total, nil
}
